// هذا الملف يجب إزالته تدريجياً واستبداله بـ Zikr في أي مكان يستخدم AzkarCategory
// يمكن الاحتفاظ به مؤقتاً للتوافق الخلفي مع الشفرة القديمة

class AzkarCategory {
  final String id;
  final String name;
  final String description;
  final int count;
  final String? iconName;
  final List<Zikr> items;
  final bool _hasSubcategoriesFlag;
  final List<AzkarCategory>? subcategories;

  AzkarCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.count,
    this.iconName,
    required this.items,
    bool hasSubcategories = false,
    this.subcategories,
  }) : _hasSubcategoriesFlag = hasSubcategories;

  factory AzkarCategory.fromJson(Map<String, dynamic> json) {
    List<Zikr> items = [];

    if (json['items'] != null) {
      items = List<Zikr>.from(json['items'].map((item) => Zikr.fromJson(item)));
    }

    List<AzkarCategory>? subcategories;
    if (json['subcategories'] != null) {
      subcategories = List<AzkarCategory>.from(
          json['subcategories'].map((sub) => AzkarCategory.fromJson(sub)));
    }

    return AzkarCategory(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      count: json['count'] ?? 0,
      iconName: json['iconName'],
      items: items,
      hasSubcategories: json['hasSubcategories'] ?? false,
      subcategories: subcategories,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'count': count,
      'iconName': iconName,
      'items': items.map((item) => item.toJson()).toList(),
      'hasSubcategories': hasSubcategories,
      'subcategories': subcategories?.map((sub) => sub.toJson()).toList(),
    };
  }

  String get imageUrl => 'assets/images/$id.jpg';

  List<Zikr> get azkar => items;

  bool get hasSubcategories =>
      _hasSubcategoriesFlag ||
      (subcategories != null && subcategories!.isNotEmpty);
}

class Zikr {
  final String id;
  final String text;
  final int count;
  final String? source;
  final String? fadl;

  Zikr({
    required this.id,
    required this.text,
    required this.count,
    this.source,
    this.fadl,
  });

  factory Zikr.fromJson(Map<String, dynamic> json) {
    return Zikr(
      id: json['id'],
      text: json['text'],
      count: json['count'],
      source: json['source'],
      fadl: json['fadl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'count': count,
      'source': source,
      'fadl': fadl,
    };
  }
}
