import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:convert';
import 'dart:ui';
import 'dart:math' as math;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:share_plus/share_plus.dart';
import 'package:provider/provider.dart';
import '../utils/app_colors.dart';
import '../models/zikr.dart';
import '../widgets/zikr_item_card.dart';
import '../utils/icon_helper.dart';
import '../database/database_helper.dart';
import '../widgets/animated_check_mark.dart';
import '../widgets/filter_button.dart';
import 'favorites/providers/favorites_provider.dart';

part 'components/azkar_details_app_bar.dart';
part 'components/azkar_details_dialogs.dart';
part 'components/azkar_details_info_card.dart';
part 'components/azkar_details_progress.dart';
part 'components/share_dialog.dart';

class AzkarDetailsScreen extends StatefulWidget {
  final String category;
  final List<ZikrItem>? azkarItems;

  const AzkarDetailsScreen({
    Key? key,
    required this.category,
    required this.azkarItems,
  }) : super(key: key);

  @override
  State<AzkarDetailsScreen> createState() => _AzkarDetailsScreenState();
}

class _AzkarDetailsScreenState extends State<AzkarDetailsScreen>
    with SingleTickerProviderStateMixin {
  late Future<Zikr?> _categoryFuture;
  late AnimationController _animationController;
  late ScrollController _scrollController;

  // للأذكار التي تتطلب تكرار
  late Map<String, int> _completedCounts;

  // للمفضلة
  late Set<String> _favorites;

  // لتتبع حالة المفضلة للقسم كاملاً
  bool _isCategoryFavorite = false;

  // لتتبع الإكمال
  late Set<String> _completedAzkar;

  // لتتبع حالة التصفية
  bool _showFavoritesOnly = false;
  bool _showUncompletedOnly = false;
  bool _isSearchMode = false;
  String _searchQuery = '';

  // لتتبع حالة شريط التطبيق
  bool _isAppBarCollapsed = false;

  // متغير جديد لتتبع حالة إبراز شريط التقدم
  bool _isProgressBarHighlighted = false;

  @override
  void initState() {
    super.initState();

    try {
      // طباعة معلومات التشخيص
      debugPrint('بدء تهيئة شاشة تفاصيل الأذكار');
      debugPrint('اسم الفئة: ${widget.category}');
      debugPrint('هل تم تمرير عناصر الأذكار؟ ${widget.azkarItems != null}');
      if (widget.azkarItems != null) {
        debugPrint('عدد عناصر الأذكار المُمررة: ${widget.azkarItems!.length}');
      }

      // تهيئة المتغيرات
      _favorites = <String>{};
      _completedAzkar = <String>{};
      _completedCounts = <String, int>{};
      _isCategoryFavorite = false;
      _isProgressBarHighlighted = false;
      _showFavoritesOnly = false;
      _showUncompletedOnly = false;
      _isSearchMode = false;
      _searchQuery = '';
      _isAppBarCollapsed = false;

      // تحميل بيانات الفئة
      _categoryFuture = _loadAzkarCategory(widget.category);

      // إعداد وحدات التحكم
      _animationController = AnimationController(
        vsync: this,
        // تقليل مدة الرسوم المتحركة لتحسين الأداء
        duration: const Duration(milliseconds: 600),
      );

      _scrollController = ScrollController();
      _scrollController.addListener(_onScroll);

      // بدء الحركة - تقليل مدة التأخير لتحسين الأداء
      Future.delayed(const Duration(milliseconds: 50), () {
        if (mounted) {
          _animationController.forward();
        }
      });

      // استرجاع بيانات المفضلة وتحديث حالة المفضلة للقسم كاملاً
      _loadFavorites();

      debugPrint('تم تهيئة شاشة تفاصيل الأذكار بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة شاشة تفاصيل الأذكار: $e');
    }
  }

  void _onScroll() {
    if (_scrollController.hasClients) {
      final scrollOffset = _scrollController.offset;
      final isCollapsed = scrollOffset > 120;

      if (isCollapsed != _isAppBarCollapsed) {
        setState(() {
          _isAppBarCollapsed = isCollapsed;
        });
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();

    // حفظ المفضلة فقط
    _saveFavorites();

    super.dispose();
  }

  // تحميل بيانات فئة الأذكار
  Future<Zikr?> _loadAzkarCategory(String categoryName) async {
    try {
      // طباعة معلومات التشخيص
      debugPrint('بدء تحميل فئة الأذكار: $categoryName');
      debugPrint('هل تم تمرير عناصر الأذكار؟ ${widget.azkarItems != null}');

      if (widget.azkarItems != null) {
        // إذا تم تمرير عناصر الأذكار، استخدمها مباشرة
        debugPrint(
            'استخدام عناصر الأذكار المُمررة: ${widget.azkarItems!.length} عنصر');
        return Zikr(
          id: 'custom',
          name: categoryName,
          description: 'قائمة الأذكار',
          count: widget.azkarItems!.length,
          items: widget.azkarItems,
        );
      }

      // تحميل الأذكار من ملف JSON
      debugPrint('تحميل الأذكار من ملف JSON');
      final String response =
          await rootBundle.loadString('assets/data/azkar.json');
      final data = await json.decode(response);

      if (data['categories'] != null) {
        final categories = List<Zikr>.from(
            data['categories'].map((category) => Zikr.fromJson(category)));

        debugPrint('تم تحميل ${categories.length} فئة من ملف JSON');

        // البحث في الفئات الرئيسية
        for (final category in categories) {
          if (category.name == categoryName) {
            debugPrint(
                'تم العثور على الفئة في الفئات الرئيسية: ${category.name}');
            debugPrint('عدد العناصر: ${category.items?.length ?? 0}');
            return category;
          }

          // البحث في الفئات الفرعية إذا كانت موجودة
          if (category.subcategories != null) {
            for (final subcategory in category.subcategories!) {
              if (subcategory.name == categoryName) {
                debugPrint(
                    'تم العثور على الفئة في الفئات الفرعية: ${subcategory.name}');
                debugPrint('عدد العناصر: ${subcategory.items?.length ?? 0}');
                return subcategory;
              }
            }
          }
        }

        // إذا لم يتم العثور على الفئة، ابحث عن فئة افتراضية
        debugPrint(
            'لم يتم العثور على الفئة: $categoryName، البحث عن فئة افتراضية');

        // استخدام أذكار الصباح كفئة افتراضية
        if (categoryName.contains('صباح') || categoryName == 'morning') {
          for (final category in categories) {
            if (category.name == 'أذكار الصباح') {
              debugPrint('استخدام أذكار الصباح كفئة افتراضية');
              return category;
            }
          }
        }

        // استخدام أذكار المساء كفئة افتراضية
        if (categoryName.contains('مساء') || categoryName == 'evening') {
          for (final category in categories) {
            if (category.name == 'أذكار المساء') {
              debugPrint('استخدام أذكار المساء كفئة افتراضية');
              return category;
            }
          }
        }

        // إذا لم يتم العثور على أي فئة، استخدم أول فئة
        if (categories.isNotEmpty) {
          debugPrint('استخدام أول فئة كفئة افتراضية: ${categories[0].name}');
          return categories[0];
        }

        throw Exception('فئة الأذكار غير موجودة');
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في تحميل فئة الأذكار: $e');
      if (mounted) {
        _showSnackBar(
          'حدث خطأ أثناء تحميل الأذكار',
          icon: Icons.error_outline,
          color: Colors.red.shade700,
        );
      }

      // إنشاء فئة فارغة لتجنب الأخطاء
      return Zikr(
        id: 'error',
        name: categoryName,
        description: 'حدث خطأ أثناء تحميل الأذكار',
        count: 0,
        items: [],
      );
    }
  }

  // إظهار إشعار للمستخدم - مُحسّن لتجنب مشكلة ParentDataWidget
  void _showSnackBar(String message, {IconData? icon, Color? color}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: LayoutBuilder(
          builder: (context, constraints) {
            return Row(
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
              children: [
                if (icon != null) ...[
                  Icon(icon, color: Colors.white, size: 20),
                  const SizedBox(width: 12),
                ],
                Expanded(
                  child: Text(
                    message,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                    textDirection: TextDirection
                        .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                    textAlign: TextAlign.right, // محاذاة النص إلى اليمين
                  ),
                ),
              ],
            );
          },
        ),
        backgroundColor: color ??
            AppColors.getAzkarColor(
                Theme.of(context).brightness == Brightness.dark),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(12),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  // تحميل المفضلة من قاعدة البيانات و SharedPreferences - تم تحسينها للتوافق مع شاشة المفضلة
  Future<void> _loadFavorites() async {
    try {
      debugPrint('بدء تحميل المفضلة...');

      // التحقق من أن المكون لا يزال مرتبطًا بشجرة العناصر
      if (!mounted) {
        debugPrint('تم إلغاء تحميل المفضلة لأن المكون غير مرتبط بشجرة العناصر');
        return;
      }

      // تحميل من SharedPreferences للتوافق مع الكود القديم
      final prefs = await SharedPreferences.getInstance();
      final favoritesSet = prefs.getStringList('favorites') ?? [];

      // طباعة معرفات العناصر المحملة من SharedPreferences للتشخيص
      debugPrint('تم تحميل ${favoritesSet.length} عنصر من SharedPreferences');

      // تحميل من قاعدة البيانات
      final dbHelper = DatabaseHelper();
      final favorites = await dbHelper.getFavorites();

      // تحويل المفضلة من قاعدة البيانات إلى مجموعة من المعرفات
      final dbFavorites = favorites
          .where((fav) => fav['item_type'] == 'azkar')
          .map((fav) => fav['item_id'].toString())
          .toSet();

      // طباعة معرفات العناصر المحملة من قاعدة البيانات للتشخيص
      debugPrint('تم تحميل ${dbFavorites.length} عنصر من قاعدة البيانات');

      // التحقق من التوافق بين SharedPreferences وقاعدة البيانات
      final Set<String> combinedFavorites = <String>{};

      // إضافة العناصر من SharedPreferences
      combinedFavorites.addAll(favoritesSet);

      // إضافة العناصر من قاعدة البيانات
      // التحقق من عدم وجود تكرار باستخدام hashCode
      for (var dbId in dbFavorites) {
        bool alreadyExists = false;
        for (var id in combinedFavorites) {
          if (id == dbId ||
              id.hashCode.toString() == dbId ||
              id == dbId.hashCode.toString()) {
            alreadyExists = true;
            break;
          }
        }

        if (!alreadyExists) {
          combinedFavorites.add(dbId);
        }
      }

      // التحقق مرة أخرى من أن المكون لا يزال مرتبطًا بشجرة العناصر
      if (!mounted) {
        debugPrint('تم إلغاء تحديث المفضلة لأن المكون غير مرتبط بشجرة العناصر');
        return;
      }

      // تحديث الحالة
      setState(() {
        _favorites.clear();
        _favorites.addAll(combinedFavorites);
      });

      // تحديث حالة المفضلة للقسم كاملاً بعد تحميل المفضلة
      Future.microtask(() {
        if (mounted) {
          _updateCategoryFavoriteStatus();
        }
      });

      // مزامنة SharedPreferences مع قاعدة البيانات إذا كان هناك اختلاف
      if (favoritesSet.length != combinedFavorites.length) {
        await _saveFavorites();
        debugPrint('تمت مزامنة المفضلة بين SharedPreferences وقاعدة البيانات');
      }

      debugPrint(
          'تم تحميل المفضلة بنجاح. العدد الإجمالي: ${_favorites.length}');
    } catch (e) {
      debugPrint('خطأ في تحميل المفضلة: $e');

      // التأكد من تهيئة المفضلة حتى في حالة حدوث خطأ
      if (mounted) {
        setState(() {
          if (_favorites.isEmpty) {
            _favorites = <String>{};
          }
        });
      }
    }
  }

  // تحديث حالة المفضلة للقسم كاملاً
  void _updateCategoryFavoriteStatus() async {
    try {
      // التحقق من أن المكون لا يزال مرتبطًا بشجرة العناصر
      if (!mounted) {
        debugPrint(
            'تم إلغاء تحديث حالة المفضلة للقسم لأن المكون غير مرتبط بشجرة العناصر');
        return;
      }

      // التحقق من أن _favorites تم تهيئته
      if (_favorites.isEmpty) {
        debugPrint('تحذير: _favorites فارغ في _updateCategoryFavoriteStatus');
        setState(() {
          _isCategoryFavorite = false;
        });
        return;
      }

      // انتظار اكتمال تحميل الفئة
      final category = await _categoryFuture;

      // التحقق من وجود الفئة وعناصرها
      if (category == null) {
        debugPrint('تحذير: الفئة فارغة في _updateCategoryFavoriteStatus');
        if (mounted) {
          setState(() {
            _isCategoryFavorite = false;
          });
        }
        return;
      }

      // التحقق من وجود عناصر في الفئة
      final items = category.items;
      if (items == null || items.isEmpty) {
        debugPrint('تحذير: لا توجد عناصر في الفئة ${category.name}');
        if (mounted) {
          setState(() {
            _isCategoryFavorite = false;
          });
        }
        return;
      }

      // التحقق من أن جميع الأذكار في القسم موجودة في المفضلة
      bool allFavorite = true;
      for (var zikr in items) {
        if (!_favorites.contains(zikr.id)) {
          allFavorite = false;
          break;
        }
      }

      // التحقق مرة أخرى من أن المكون لا يزال مرتبطًا بشجرة العناصر
      if (mounted) {
        setState(() {
          _isCategoryFavorite = allFavorite;
        });
        debugPrint('تم تحديث حالة المفضلة للقسم كاملاً: $_isCategoryFavorite');
      }
    } catch (e) {
      debugPrint('خطأ في تحديث حالة المفضلة للقسم: $e');
      if (mounted) {
        setState(() {
          _isCategoryFavorite = false;
        });
      }
    }
  }

  // إضافة/إزالة القسم كاملاً من المفضلة
  void _toggleCategoryFavorite(Zikr category) async {
    try {
      // التحقق من وجود عناصر في الفئة
      final items = category.items;
      if (items == null || items.isEmpty) {
        _showSnackBar('لا توجد أذكار في هذا القسم',
            icon: Icons.info_outline, color: Colors.blue);
        return;
      }

      // تحديد الإجراء (إضافة أو إزالة) بناءً على الحالة الحالية
      final bool shouldAdd = !_isCategoryFavorite;

      // إظهار تعليقات مرئية فورية
      if (shouldAdd) {
        _showSnackBar('تمت إضافة ${category.name} للمفضلة',
            icon: Icons.favorite, color: Colors.pink);
      } else {
        _showSnackBar('تم إزالة ${category.name} من المفضلة',
            icon: Icons.favorite_border);
      }

      // تحديث حالة واجهة المستخدم فورًا
      setState(() {
        _isCategoryFavorite = shouldAdd;
      });

      // تحديث المفضلة لكل ذكر في القسم
      for (var zikr in items) {
        if (shouldAdd) {
          // إضافة إلى المفضلة إذا لم يكن موجودًا بالفعل
          if (!_favorites.contains(zikr.id)) {
            _favorites.add(zikr.id);
          }
        } else {
          // إزالة من المفضلة
          _favorites.remove(zikr.id);
        }
      }

      // حفظ التغييرات
      await _saveFavorites();

      // تحديث قاعدة البيانات
      final dbHelper = DatabaseHelper();
      for (var zikr in items) {
        if (shouldAdd) {
          await dbHelper.addFavorite(zikr.id, 'azkar');
        } else {
          await dbHelper.removeFromFavorites(zikr.id, itemType: 'azkar');
        }
      }

      // تحديث قائمة المفضلة في FavoritesProvider
      if (mounted) {
        try {
          final favoritesProvider =
              Provider.of<FavoritesProvider>(context, listen: false);

          await Future.delayed(const Duration(milliseconds: 300));

          if (mounted) {
            debugPrint('جاري تحديث مزود المفضلة...');
            favoritesProvider.loadFavorites();
          }
        } catch (e) {
          debugPrint('خطأ في تحديث مزود المفضلة: $e');
        }
      }

      // تأثير اهتزاز للتفاعل
      HapticFeedback.mediumImpact();

      debugPrint('تم تحديث حالة المفضلة للقسم كاملاً بنجاح');
    } catch (e) {
      // في حالة حدوث خطأ، التراجع عن التغيير في واجهة المستخدم
      debugPrint('خطأ في تحديث المفضلة للقسم كاملاً: $e');

      if (mounted) {
        // التراجع عن الحالة
        setState(() {
          _isCategoryFavorite = !_isCategoryFavorite;
        });

        // إظهار رسالة خطأ
        _showSnackBar('حدث خطأ أثناء تحديث المفضلة',
            icon: Icons.error_outline, color: Colors.red);
      }
    }
  }

  // حفظ المفضلة في SharedPreferences - تم تحسينها للتوافق مع شاشة المفضلة
  Future<void> _saveFavorites() async {
    try {
      // عمل نسخة محلية من المفضلة لتجنب مشاكل التزامن
      final List<String> favoritesToSave = List<String>.from(_favorites);

      // طباعة معلومات التشخيص
      debugPrint(
          'جاري حفظ ${favoritesToSave.length} عنصر مفضل في SharedPreferences');

      // طباعة معرفات العناصر المحفوظة للتشخيص
      for (var id in favoritesToSave) {
        debugPrint('معرف محفوظ: $id, hashCode: ${id.hashCode}');
      }

      // حفظ في SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList('favorites', favoritesToSave);

      // مزامنة مع قاعدة البيانات
      final dbHelper = DatabaseHelper();

      // التحقق من العناصر الموجودة في قاعدة البيانات
      final favorites = await dbHelper.getFavorites();
      final dbFavorites = favorites
          .where((fav) => fav['item_type'] == 'azkar')
          .map((fav) => fav['item_id'].toString())
          .toSet();

      // إضافة العناصر الجديدة إلى قاعدة البيانات
      for (var id in favoritesToSave) {
        if (!dbFavorites.contains(id) &&
            !dbFavorites.contains(id.hashCode.toString())) {
          await dbHelper.addFavorite(id, 'azkar');
          debugPrint('تمت إضافة عنصر جديد إلى قاعدة البيانات: $id');
        }
      }

      // إزالة العناصر المحذوفة من قاعدة البيانات
      for (var dbId in dbFavorites) {
        bool found = false;
        for (var id in favoritesToSave) {
          if (id == dbId || id.hashCode.toString() == dbId) {
            found = true;
            break;
          }
        }

        if (!found) {
          try {
            int numericId = int.parse(dbId);
            await dbHelper.removeFromFavorites(numericId, itemType: 'azkar');
            debugPrint('تمت إزالة عنصر من قاعدة البيانات: $dbId');
          } catch (e) {
            debugPrint('خطأ في إزالة عنصر من قاعدة البيانات: $e');
          }
        }
      }

      debugPrint('تم حفظ المفضلة بنجاح');
    } catch (e) {
      debugPrint('خطأ في حفظ المفضلة: $e');
    }
  }

  // إضافة/إزالة من المفضلة - تم تحسينها للتوافق مع شاشة المفضلة
  void _toggleFavorite(String zikrId) async {
    // حفظ مرجع محلي للمعرف
    final String currentZikrId = zikrId;
    final bool currentIsFavorite = _favorites.contains(currentZikrId);

    debugPrint(
        'تبديل حالة المفضلة للذكر: $currentZikrId, الحالة الحالية: $currentIsFavorite');

    // تحديث حالة واجهة المستخدم فورًا لاستجابة أسرع
    setState(() {
      if (currentIsFavorite) {
        _favorites.remove(currentZikrId);
      } else {
        _favorites.add(currentZikrId);
      }
    });

    // إظهار تعليقات مرئية فورية
    if (currentIsFavorite) {
      _showSnackBar('تم الإزالة من المفضلة', icon: Icons.favorite_border);
    } else {
      _showSnackBar('تمت الإضافة للمفضلة',
          icon: Icons.favorite, color: Colors.pink);
    }

    try {
      // تحديث SharedPreferences أولاً
      if (mounted) {
        await _saveFavorites();
        debugPrint('تم تحديث SharedPreferences بنجاح');
      }

      // تحديث قاعدة البيانات
      final dbHelper = DatabaseHelper();
      if (currentIsFavorite) {
        // استخدام المعرف الأصلي للتوافق مع طريقة التخزين في قاعدة البيانات
        await dbHelper.removeFromFavorites(currentZikrId, itemType: 'azkar');
        debugPrint('تمت إزالة الذكر من قاعدة البيانات: $currentZikrId');
      } else {
        // استخدام المعرف الأصلي للتوافق مع طريقة التخزين في قاعدة البيانات
        await dbHelper.addFavorite(currentZikrId, 'azkar');
        debugPrint('تمت إضافة الذكر إلى قاعدة البيانات: $currentZikrId');
      }

      // تحديث حالة المفضلة للقسم كاملاً
      _updateCategoryFavoriteStatus();

      // تحديث قائمة المفضلة في FavoritesProvider
      if (mounted) {
        try {
          // الحصول على مزود المفضلة بشكل آمن
          final favoritesProvider =
              Provider.of<FavoritesProvider>(context, listen: false);

          // استخدام تأخير بسيط لضمان اكتمال العمليات السابقة
          await Future.delayed(const Duration(milliseconds: 300));

          if (mounted) {
            debugPrint('جاري تحديث مزود المفضلة...');
            favoritesProvider.loadFavorites();
          }
        } catch (e) {
          debugPrint('خطأ في تحديث مزود المفضلة: $e');
        }
      }

      // تأثير اهتزاز للتفاعل
      HapticFeedback.lightImpact();
    } catch (e) {
      // في حالة حدوث خطأ، التراجع عن التغيير في واجهة المستخدم
      debugPrint('خطأ في تحديث المفضلة: $e');

      if (mounted) {
        // التراجع عن الحالة
        setState(() {
          if (currentIsFavorite) {
            _favorites.add(currentZikrId);
          } else {
            _favorites.remove(currentZikrId);
          }
        });

        // إظهار رسالة خطأ
        _showSnackBar('حدث خطأ أثناء تحديث المفضلة',
            icon: Icons.error_outline, color: Colors.red);
      }
    }
  }

  // تعديل دالة incrementCounter لتتعامل مع الأذكار بشكل صحيح
  void _incrementCounter(String zikrId, int totalCount) {
    setState(() {
      // تخزين العدادات في الذاكرة المؤقتة فقط
      _completedCounts[zikrId] = (_completedCounts[zikrId] ?? 0) + 1;
      final currentCount = _completedCounts[zikrId] ?? 0;

      // إذا وصل للعدد المطلوب
      if (currentCount >= totalCount) {
        // إضافة الذكر إلى قائمة المكتملة إذا لم يكن موجوداً بالفعل
        if (!_completedAzkar.contains(zikrId)) {
          _completedAzkar.add(zikrId);

          // تأثير اهتزاز للإشعار بالإنجاز
          HapticFeedback.lightImpact();

          // تأثير مرئي لإظهار التقدم
          _highlightProgressBar(scrollToTop: false);

          // حفظ حالة الإكمال
          _saveCompletion(zikrId);

          // فحص اكتمال جميع الأذكار
          _checkAllAzkarCompleted();
        }
      } else {
        // تأثير اهتزاز خفيف عند كل نقرة
        HapticFeedback.lightImpact();
      }
    });
  }

  // دالة جديدة لفحص اكتمال جميع الأذكار
  void _checkAllAzkarCompleted() async {
    try {
      final category = await _categoryFuture;
      if (category == null) {
        debugPrint('تحذير: الفئة فارغة في _checkAllAzkarCompleted');
        return;
      }

      // التحقق من وجود عناصر في الفئة
      final items = category.items;
      if (items == null || items.isEmpty) {
        debugPrint('تحذير: لا توجد عناصر في الفئة ${category.name}');
        return;
      }

      // التحقق من أن جميع الأذكار مكتملة
      if (_completedAzkar.length == items.length) {
        // تأثير اهتزاز قوي عند اكتمال جميع الأذكار
        HapticFeedback.heavyImpact();

        // التمرير إلى الأعلى عند إكمال جميع الأذكار
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            0,
            duration: const Duration(milliseconds: 800),
            curve: Curves.easeOutCubic,
          );
        }

        // عرض مربع حوار الإكمال بعد فترة قصيرة (للسماح بالتمرير أولاً)
        if (mounted) {
          Future.delayed(const Duration(milliseconds: 900), () {
            if (mounted) {
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (dialogContext) => const _CompletionDialog(),
              );
            }
          });
        }
      }
    } catch (e) {
      debugPrint('خطأ في فحص اكتمال جميع الأذكار: $e');
    }
  }

  // دالة جديدة لإبراز شريط التقدم عند إكمال ذكر
  void _highlightProgressBar({bool scrollToTop = false}) {
    // إنشاء تأثير وميض على شريط التقدم
    _isProgressBarHighlighted = true;
    Future.delayed(const Duration(milliseconds: 600), () {
      if (mounted) {
        setState(() {
          _isProgressBarHighlighted = false;
        });
      }
    });

    // التمرير فقط إذا كانت المعلمة scrollToTop صحيحة
    if (scrollToTop &&
        _scrollController.hasClients &&
        _scrollController.offset > 100) {
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeOutCubic,
      );
    }
  }

  // تعديل أيضاً دالة إعادة تعيين العداد
  void _resetCounter(String zikrId) {
    setState(() {
      _completedCounts[zikrId] = 0; // نعيد ضبط العداد في الذاكرة المؤقتة فقط
      _completedAzkar.remove(zikrId); // نزيل الذكر من قائمة المكتملة
    });
  }

  // حفظ معلومات إكمال الذكر
  Future<void> _saveCompletion(String zikrId) async {
    try {
      final dbHelper = DatabaseHelper();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final category = await _categoryFuture;

      if (category != null) {
        // استخدام معرف الفئة أو معرف بديل إذا كان فارغًا
        final categoryId =
            category.id.isNotEmpty ? category.id : 'unknown_category';

        await dbHelper.saveZikrCompletion(
          categoryId,
          'zikr_item',
          timestamp,
        );

        debugPrint('تم حفظ إكمال الذكر: $zikrId في الفئة: $categoryId');
      } else {
        debugPrint('تحذير: لم يتم حفظ إكمال الذكر لأن الفئة فارغة');
      }
    } catch (e) {
      debugPrint('خطأ في حفظ الإكمال: $e');
    }
  }

  // عرض مربع حوار التصفية
  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return _FilterBottomSheet(
          showFavoritesOnly: _showFavoritesOnly,
          showUncompletedOnly: _showUncompletedOnly,
          onFavoritesFilterChanged: (value) {
            setState(() {
              _showFavoritesOnly = value;
            });
          },
          onUncompletedFilterChanged: (value) {
            setState(() {
              _showUncompletedOnly = value;
            });
          },
          onResetFilters: () {
            setState(() {
              _showFavoritesOnly = false;
              _showUncompletedOnly = false;
              _searchQuery = '';
            });
          },
        );
      },
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
    );
  }

  // تصفية الأذكار حسب الفلاتر
  List<ZikrItem> _filterAzkarItems(List<ZikrItem> items) {
    return items.where((item) {
      // تصفية حسب المفضلة
      if (_showFavoritesOnly && !_favorites.contains(item.id)) {
        return false;
      }

      // تصفية حسب غير المكتملة
      if (_showUncompletedOnly && _completedAzkar.contains(item.id)) {
        return false;
      }

      // تصفية حسب البحث
      if (_searchQuery.isNotEmpty) {
        return item.text.contains(_searchQuery) ||
            (item.fadl?.contains(_searchQuery) ?? false) ||
            (item.source?.contains(_searchQuery) ?? false);
      }

      return true;
    }).toList();
  }

  // تبديل وضع البحث
  void _toggleSearchMode() {
    setState(() {
      _isSearchMode = !_isSearchMode;
      if (!_isSearchMode) {
        _searchQuery = '';
      }
    });
  }

  // تحديث استعلام البحث
  void _updateSearchQuery(String query) {
    setState(() {
      _searchQuery = query;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FutureBuilder<Zikr?>(
        future: _categoryFuture,
        builder: (context, snapshot) {
          // طباعة معلومات التشخيص
          debugPrint('حالة الاتصال: ${snapshot.connectionState}');
          if (snapshot.hasError) {
            debugPrint('خطأ: ${snapshot.error}');
          }
          if (snapshot.hasData) {
            debugPrint(
                'البيانات: ${snapshot.data?.name}, عدد العناصر: ${snapshot.data?.items?.length ?? 0}');
          }

          if (snapshot.connectionState == ConnectionState.waiting) {
            return _buildLoadingScreen();
          } else if (snapshot.hasError) {
            return _buildErrorScreen(snapshot.error);
          } else if (!snapshot.hasData || snapshot.data == null) {
            // عرض شاشة الخطأ مباشرة
            return _buildErrorScreen(
                Exception('لا توجد بيانات للفئة ${widget.category}'));
          } else {
            try {
              final category = snapshot.data!;
              return _buildAzkarScreen(category);
            } catch (e) {
              debugPrint('خطأ في بناء شاشة الأذكار: $e');
              return _buildErrorScreen(e);
            }
          }
        },
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildFloatingActionButton() {
    if (_isSearchMode) return const SizedBox.shrink();

    return FutureBuilder<Zikr?>(
      future: _categoryFuture,
      builder: (context, snapshot) {
        if (!snapshot.hasData) return const SizedBox.shrink();

        // زر التصفية
        return AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: CurvedAnimation(
                parent: _animationController,
                curve: const Interval(0.6, 1.0),
              ),
              child: FloatingActionButton.extended(
                onPressed: _showFilterDialog,
                icon: Icon(
                  _showFavoritesOnly || _showUncompletedOnly
                      ? Icons.filter_list_alt
                      : Icons.filter_list,
                ),
                label: Text(
                  _showFavoritesOnly || _showUncompletedOnly
                      ? 'تعديل التصفية'
                      : 'تصفية الأذكار',
                ),
                backgroundColor: AppColors.azkarColor,
                foregroundColor: Colors.white,
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildLoadingScreen() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFFEEF7F6),
            Colors.white,
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // زخرفة متحركة - مُحسّنة للأداء
            RepaintBoundary(
              child: TweenAnimationBuilder<double>(
                tween: Tween<double>(begin: 0, end: 1),
                // تقليل مدة الرسوم المتحركة لتحسين الأداء
                duration: const Duration(milliseconds: 1500),
                // استخدام منحنى أبسط لتحسين الأداء
                curve: Curves.easeOut,
                builder: (context, value, child) {
                  return Transform.scale(
                    // تقليل مدى التكبير لتحسين الأداء
                    scale: 0.9 + (value * 0.1),
                    child: Opacity(
                      opacity: value,
                      child: SvgPicture.asset(
                        'assets/images/p2.svg',
                        width: 120,
                        height: 120,
                        colorFilter: ColorFilter.mode(
                          Theme.of(context).brightness == Brightness.dark
                              ? AppColors.getAzkarColor(true)
                                  .withAlpha(179) // 0.7 * 255 = ~179
                              : AppColors.getAzkarColor(false)
                                  .withAlpha(179), // 0.7 * 255 = ~179
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  );
                },
                onEnd: () {
                  // تكرار التأثير
                  setState(() {});
                },
              ),
            ),
            const SizedBox(height: 32),
            const SizedBox(
              width: 40,
              height: 40,
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppColors.azkarColor,
                ),
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'جاري تحميل الأذكار...',
              style: TextStyle(
                fontSize: 18,
                color: AppColors.azkarColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorScreen(Object? error) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFFFFF5F5),
            Colors.white,
          ],
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // شريط العودة
            Align(
              alignment: Alignment.topLeft,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.arrow_back),
                  tooltip: 'العودة',
                ),
              ),
            ),

            Expanded(
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.all(32.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.error_outline,
                          size: 40,
                          color: Colors.red.shade700,
                        ),
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'حدث خطأ أثناء تحميل البيانات',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.red.shade700,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        error?.toString() ??
                            'تعذر الوصول إلى البيانات المطلوبة',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[700],
                        ),
                      ),
                      const SizedBox(height: 32),
                      ElevatedButton.icon(
                        onPressed: () {
                          setState(() {
                            _categoryFuture =
                                _loadAzkarCategory(widget.category);
                          });
                        },
                        icon: const Icon(Icons.refresh),
                        label: const Text('إعادة المحاولة'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.getAzkarColor(
                              Theme.of(context).brightness == Brightness.dark),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAzkarScreen(Zikr category) {
    // طباعة معلومات التشخيص
    debugPrint('بناء شاشة الأذكار للفئة: ${category.name}');

    // التحقق من وجود عناصر في الفئة
    if (category.items == null) {
      debugPrint('تحذير: عناصر الفئة فارغة في _buildAzkarScreen');
      return _buildErrorScreen(Exception('لا توجد أذكار في هذه الفئة'));
    }

    final allItems = category.items ?? [];
    debugPrint('عدد العناصر في الفئة: ${allItems.length}');

    if (allItems.isEmpty) {
      debugPrint('تحذير: قائمة الأذكار فارغة في _buildAzkarScreen');
      return _buildErrorScreen(Exception('لا توجد أذكار في هذه الفئة'));
    }

    final filteredItems = _filterAzkarItems(allItems);

    return Stack(
      children: [
        CustomScrollView(
          controller: _scrollController,
          physics: const BouncingScrollPhysics(),
          slivers: [
            // شريط التطبيق - إظهار العنوان فقط هنا
            _buildAzkarAppBar(
              category: category,
              isSearchMode: _isSearchMode,
              onSearchModeToggle: _toggleSearchMode,
              onSearchQueryChanged: _updateSearchQuery,
              searchQuery: _searchQuery,
              context: context,
              showTitle: true, // عرض العنوان في شريط التطبيق فقط
            ),

            // مؤشر التقدم
            SliverToBoxAdapter(
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                child: _buildProgressBar(
                  total: allItems.length,
                  completed: _completedAzkar.length,
                  context: context,
                  isHighlighted: _isProgressBarHighlighted,
                ),
              ),
            ),

            // معلومات الفئة - إخفاء العنوان
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: _buildCategoryInfoCard(
                  category: category,
                  totalAzkar: allItems.length,
                  completedAzkar: _completedAzkar.length,
                  filteredCount: filteredItems.length,
                  hasFilters: _showFavoritesOnly ||
                      _showUncompletedOnly ||
                      _searchQuery.isNotEmpty,
                  context: context,
                  showTitle: false, // إخفاء العنوان في بطاقة المعلومات
                ),
              ),
            ),

            // رسالة في حالة عدم وجود نتائج للتصفية
            if (filteredItems.isEmpty && allItems.isNotEmpty)
              SliverToBoxAdapter(
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.all(32.0),
                    child: Column(
                      children: [
                        Icon(
                          Icons.filter_alt_off,
                          size: 48,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد نتائج تطابق التصفية الحالية',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[600],
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        TextButton.icon(
                          onPressed: () {
                            setState(() {
                              _showFavoritesOnly = false;
                              _showUncompletedOnly = false;
                              _searchQuery = '';
                            });
                          },
                          icon: const Icon(Icons.refresh),
                          label: const Text('إعادة ضبط التصفية'),
                          style: TextButton.styleFrom(
                            foregroundColor: AppColors.azkarColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

            // قائمة الأذكار
            if (filteredItems.isNotEmpty)
              SliverPadding(
                padding: const EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 16.0),
                sliver: SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final zikr = filteredItems[index];
                      return _buildZikrItemWithAnimation(zikr, index);
                    },
                    childCount: filteredItems.length,
                  ),
                ),
              ),

            // مساحة للزر العائم
            const SliverToBoxAdapter(
              child: SizedBox(height: 80),
            ),
          ],
        ),

        // شريط علوي عائم - إخفاء العنوان
        if (_isAppBarCollapsed && !_isSearchMode)
          _buildFloatingStatusBar(
            category: category,
            totalAzkar: allItems.length,
            completedAzkar: _completedAzkar.length,
            hasFilters: _showFavoritesOnly ||
                _showUncompletedOnly ||
                _searchQuery.isNotEmpty,
            onFilterPressed: _showFilterDialog,
            showTitle: false, // إخفاء العنوان في الشريط العائم
          ),
      ],
    );
  }

  Widget _buildZikrItemWithAnimation(ZikrItem zikr, int index) {
    // استخدام RepaintBoundary لتحسين أداء الرسوم المتحركة
    return RepaintBoundary(
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          // تقليل التأخير بين العناصر لتحسين الأداء
          final delay = index * 0.03;
          // تقليل مدى الحركة لتحسين الأداء
          const begin = Offset(0, 0.1);
          const end = Offset.zero;

          final slideAnimation = Tween<Offset>(begin: begin, end: end).animate(
            CurvedAnimation(
              parent: _animationController,
              curve: Interval(
                delay.clamp(0.0, 0.8),
                // تقليل مدة الحركة لتحسين الأداء
                (delay + 0.2).clamp(0.0, 1.0),
                // استخدام منحنى أبسط لتحسين الأداء
                curve: Curves.easeOut,
              ),
            ),
          );

          final fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
            CurvedAnimation(
              parent: _animationController,
              curve: Interval(
                delay.clamp(0.0, 0.8),
                // تقليل مدة الحركة لتحسين الأداء
                (delay + 0.15).clamp(0.0, 1.0),
                // استخدام منحنى أبسط لتحسين الأداء
                curve: Curves.easeOut,
              ),
            ),
          );

          return FadeTransition(
            opacity: fadeAnimation,
            child: SlideTransition(
              position: slideAnimation,
              child: Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: ZikrItemCard(
                  zikr: zikr,
                  index: index + 1,
                  isFavorite: _favorites.contains(zikr.id),
                  onFavoriteToggle: _toggleFavorite,
                  onCounterIncrement: _incrementCounter,
                  onCounterReset: _resetCounter,
                  completedCounts: _completedCounts,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  // شريط علوي عائم يظهر عند التمرير - مُحسّن للأداء
  Widget _buildFloatingStatusBar({
    required Zikr category,
    required int totalAzkar,
    required int completedAzkar,
    required bool hasFilters,
    required VoidCallback onFilterPressed,
    bool showTitle = false,
  }) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      // استخدام RepaintBoundary لتحسين أداء الرسوم المتحركة
      child: RepaintBoundary(
        child: ClipRect(
          child: BackdropFilter(
            // تقليل قيمة التمويه لتحسين الأداء
            filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
            child: Container(
              padding: EdgeInsets.only(
                top: MediaQuery.of(context).padding.top,
                bottom: 8,
                left: 8,
                right: 8,
              ),
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey.shade900.withAlpha(217) // 0.85 * 255 = ~217
                    : Colors.white.withAlpha(217), // 0.85 * 255 = ~217
                // تبسيط الظلال لتحسين الأداء
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(13), // 0.05 * 255 = ~13
                    // تقليل قيمة التمويه لتحسين الأداء
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                textDirection:
                    TextDirection.rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                children: [
                  // استخدام مكون FilterButton
                  FilterButton(
                    hasActiveFilters: hasFilters,
                    onPressed: onFilterPressed,
                    label: hasFilters ? 'تعديل' : 'تصفية',
                    icon:
                        hasFilters ? Icons.filter_list_alt : Icons.filter_list,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment:
                          CrossAxisAlignment.end, // تغيير المحاذاة إلى اليمين
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (showTitle)
                          Text(
                            category.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign:
                                TextAlign.right, // محاذاة النص إلى اليمين
                            textDirection: TextDirection
                                .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                          ),
                        if (!showTitle)
                          Row(
                            textDirection: TextDirection
                                .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                            children: [
                              Icon(
                                Icons.format_list_numbered,
                                size: 16,
                                color: Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? const Color(
                                        0xFFAAAAAA) /* TasbihColors.darkTextSecondary */
                                    : Colors.grey[700],
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'عدد الأذكار: $totalAzkar',
                                style: TextStyle(
                                  fontSize:
                                      MediaQuery.of(context).size.width < 360
                                          ? 12
                                          : 14,
                                  fontWeight: FontWeight.w500,
                                  color: Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? const Color(
                                          0xFFE0E0E0) /* TasbihColors.darkTextColor */
                                      : Colors.grey[800],
                                ),
                                textDirection: TextDirection
                                    .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                              ),
                            ],
                          ),
                        const SizedBox(height: 4),
                        Row(
                          textDirection: TextDirection
                              .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                          children: [
                            Expanded(
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(4),
                                child: LinearProgressIndicator(
                                  value: totalAzkar > 0
                                      ? completedAzkar / totalAzkar
                                      : 0,
                                  minHeight: 4,
                                  backgroundColor: Theme.of(context)
                                              .brightness ==
                                          Brightness.dark
                                      ? const Color(
                                          0xFF16213E) /* TasbihColors.darkBackgroundSecondary */
                                      : Colors.grey[200],
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    AppColors.getAzkarColor(
                                        Theme.of(context).brightness ==
                                            Brightness.dark),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed: () => Navigator.of(context).pop(),
                    tooltip: 'العودة',
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
