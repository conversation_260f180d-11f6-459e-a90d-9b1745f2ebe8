// نموذج الورد اليومي

import 'package:flutter/material.dart';
import 'dhikr_model.dart';

/// نموذج عنصر الورد (ذكر واحد في الورد)
class WirdItemModel {
  final int id;
  final DhikrModel dhikr;
  final int targetCount;
  final int order;
  int currentCount;
  bool isCompleted;

  WirdItemModel({
    required this.id,
    required this.dhikr,
    required this.targetCount,
    required this.order,
    this.currentCount = 0,
    this.isCompleted = false,
  });

  /// نسخ العنصر مع تحديث بعض القيم
  WirdItemModel copyWith({
    int? id,
    DhikrModel? dhikr,
    int? targetCount,
    int? order,
    int? currentCount,
    bool? isCompleted,
  }) {
    return WirdItemModel(
      id: id ?? this.id,
      dhikr: dhikr ?? this.dhikr,
      targetCount: targetCount ?? this.targetCount,
      order: order ?? this.order,
      currentCount: currentCount ?? this.currentCount,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'dhikrId': dhikr.id,
      'targetCount': targetCount,
      'order': order,
      'currentCount': currentCount,
      'isCompleted': isCompleted ? 1 : 0,
      // حفظ معلومات الذكر للاستخدام في حالة الطوارئ
      'dhikrName': dhikr.name,
      'dhikrArabicText': dhikr.arabicText,
      'dhikrTransliteration': dhikr.transliteration,
      'dhikrTranslation': dhikr.translation,
      'dhikrIsDefault': dhikr.isDefault ? 1 : 0,
    };
  }

  /// إنشاء نموذج من Map
  factory WirdItemModel.fromMap(Map<String, dynamic> map, DhikrModel? dhikr) {
    // إذا كان الذكر غير موجود، استخدم المعلومات المحفوظة في الخريطة
    final DhikrModel actualDhikr = dhikr ??
        DhikrModel(
          id: map['dhikrId'] as int,
          name: map['dhikrName'] as String? ?? 'ذكر غير معروف',
          count: map['targetCount'] as int,
          arabicText: map['dhikrArabicText'] as String? ?? '',
          transliteration: map['dhikrTransliteration'] as String? ?? '',
          translation: map['dhikrTranslation'] as String? ?? '',
          isDefault: (map['dhikrIsDefault'] as int?) == 1,
        );

    return WirdItemModel(
      id: map['id'] as int,
      dhikr: actualDhikr,
      targetCount: map['targetCount'] as int,
      order: map['order'] as int,
      currentCount: map['currentCount'] as int? ?? 0,
      isCompleted: (map['isCompleted'] as int?) == 1,
    );
  }

  /// حساب نسبة الإكمال
  double get completionPercentage {
    if (targetCount == 0) return 0;
    return currentCount / targetCount;
  }
}

/// نموذج الورد اليومي
class WirdModel {
  final int id;
  final String name;
  final List<WirdItemModel> items;
  final TimeOfDay? reminderTime;
  final bool isActive;
  final DateTime? lastUsedDate;
  final int completedCount;

  WirdModel({
    required this.id,
    required this.name,
    required this.items,
    this.reminderTime,
    this.isActive = true,
    this.lastUsedDate,
    this.completedCount = 0,
  });

  /// نسخ الورد مع تحديث بعض القيم
  WirdModel copyWith({
    int? id,
    String? name,
    List<WirdItemModel>? items,
    TimeOfDay? reminderTime,
    bool? isActive,
    DateTime? lastUsedDate,
    int? completedCount,
  }) {
    return WirdModel(
      id: id ?? this.id,
      name: name ?? this.name,
      items: items ?? this.items,
      reminderTime: reminderTime ?? this.reminderTime,
      isActive: isActive ?? this.isActive,
      lastUsedDate: lastUsedDate ?? this.lastUsedDate,
      completedCount: completedCount ?? this.completedCount,
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'reminderHour': reminderTime?.hour,
      'reminderMinute': reminderTime?.minute,
      'isActive': isActive ? 1 : 0,
      'lastUsedDate': lastUsedDate?.millisecondsSinceEpoch,
      'completedCount': completedCount,
    };
  }

  /// إنشاء نموذج من Map
  factory WirdModel.fromMap(
      Map<String, dynamic> map, List<WirdItemModel> items) {
    TimeOfDay? reminderTime;
    if (map['reminderHour'] != null && map['reminderMinute'] != null) {
      reminderTime = TimeOfDay(
        hour: map['reminderHour'] as int,
        minute: map['reminderMinute'] as int,
      );
    }

    DateTime? lastUsedDate;
    if (map['lastUsedDate'] != null) {
      lastUsedDate =
          DateTime.fromMillisecondsSinceEpoch(map['lastUsedDate'] as int);
    }

    return WirdModel(
      id: map['id'] as int,
      name: map['name'] as String,
      items: items,
      reminderTime: reminderTime,
      isActive: (map['isActive'] as int?) == 1,
      lastUsedDate: lastUsedDate,
      completedCount: map['completedCount'] as int? ?? 0,
    );
  }

  /// الحصول على العنصر الحالي (غير المكتمل)
  WirdItemModel? get currentItem {
    for (var item in items) {
      if (!item.isCompleted) {
        return item;
      }
    }
    return null;
  }

  /// التحقق مما إذا كان الورد مكتملاً
  bool get isCompleted {
    return items.every((item) => item.isCompleted);
  }

  /// حساب نسبة إكمال الورد
  double get completionPercentage {
    if (items.isEmpty) return 0;

    int completedItems = items.where((item) => item.isCompleted).length;
    return completedItems / items.length;
  }

  /// حساب إجمالي عدد التسبيحات في الورد
  int get totalDhikrCount {
    return items.fold(0, (sum, item) => sum + item.targetCount);
  }

  /// حساب عدد التسبيحات المكتملة في الورد
  int get completedDhikrCount {
    return items.fold(0, (sum, item) => sum + item.currentCount);
  }
}
