/*
import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:flutter/material.dart';
import 'package:wahaj_alsaalik/utils/constants.dart';
import 'package:wahaj_alsaalik/main.dart';

// Note: هذا الملف غير مستخدم حالياً، تم نقل وظائفه إلى main.dart
// يمكن استخدامه في المستقبل إذا أردنا فصل المنطق

class NotificationController {
  // ملاحظة: تم تعليق هذا المتغير لأنه غير مستخدم حالياً
  // سيتم استخدامه في المستقبل عند فصل منطق الإشعارات عن main.dart
  // static final NotificationService _notificationService = NotificationService();

  // مفتاح التنقل العام
  static GlobalKey<NavigatorState>? navigatorKey;

  // ضبط مفتاح التنقل
  static void setNavigatorKey(GlobalKey<NavigatorState> key) {
    navigatorKey = key;
  }

  // معالجة أفعال الإشعارات
  @pragma('vm:entry-point')
  static Future<void> onActionReceivedMethod(
      ReceivedAction receivedAction) async {
    // استدعاء الدالة المقابلة في main.dart
    await onActionReceivedMethod(receivedAction);
  }

  @pragma('vm:entry-point')
  static Future<void> onNotificationCreatedMethod(
      ReceivedNotification receivedNotification) async {
    debugPrint('تم إنشاء الإشعار: ${receivedNotification.id}');
  }

  @pragma('vm:entry-point')
  static Future<void> onNotificationDisplayedMethod(
      ReceivedNotification receivedNotification) async {
    debugPrint('تم عرض الإشعار: ${receivedNotification.id}');
  }

  @pragma('vm:entry-point')
  static Future<void> onDismissActionReceivedMethod(
      ReceivedAction receivedAction) async {
    debugPrint('تم تجاهل الإشعار: ${receivedAction.id}');
  }

  // دوال للتنقل
  static void navigateToAzkarPage(String type) {
    if (MyApp.navigatorKey.currentState != null) {
      MyApp.navigatorKey.currentState!.pushNamed(
        AppConstants.azkarDetailsRoute,
        arguments: type,
      );
    }
  }

  static void navigateToNotificationsPage() {
    if (MyApp.navigatorKey.currentState != null) {
      MyApp.navigatorKey.currentState!.pushNamed('/notifications');
    }
  }
}
*/