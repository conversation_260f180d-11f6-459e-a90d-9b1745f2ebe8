// إنشاء ملف لقسم البيانات

import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../../../services/database_helper.dart';
import '../widgets/setting_item.dart';
import '../utils/dialog_utils.dart';
import 'package:flutter_svg/flutter_svg.dart';

class DataSection extends StatefulWidget {
  final Color settingsColor;
  final Function(Widget, {double delay}) buildSlideAnimation;
  final Function(BuildContext, String, IconData) buildSectionTitle;

  const DataSection({
    super.key,
    required this.settingsColor,
    required this.buildSlideAnimation,
    required this.buildSectionTitle,
  });

  @override
  State<DataSection> createState() => _DataSectionState();
}

class _DataSectionState extends State<DataSection> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  String _appVersion = '';

  @override
  void initState() {
    super.initState();
    _getAppVersion();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        widget.buildSectionTitle(context, 'البيانات', Icons.storage_outlined),

        // مسح البيانات
        widget.buildSlideAnimation(
          SettingItem(
            title: 'مسح البيانات',
            icon: Icons.delete_outline,
            iconColor: Colors.red,
            subtitle: 'حذف جميع المفضلات والإعدادات',
            onTap: _clearAppData,
          ),
          delay: 0.7,
        ),

        const SizedBox(height: 16),

        // معلومات التطبيق
        widget.buildSlideAnimation(
          GestureDetector(
            onTap: () => _handleAppInfoTap(),
            child: Container(
              margin:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              padding: const EdgeInsets.all(20.0),
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.black12
                    : Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: widget.settingsColor.withAlpha(15),
                    blurRadius: 10,
                    spreadRadius: 0,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(
                  color: widget.settingsColor.withAlpha(30),
                  width: 1,
                ),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // شعار التطبيق المحسن
                    Container(
                      width: 90,
                      height: 90,
                      decoration: BoxDecoration(
                        color: widget.settingsColor.withAlpha(20),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: widget.settingsColor.withAlpha(30),
                            blurRadius: 15,
                            spreadRadius: 1,
                            offset: const Offset(0, 3),
                          ),
                        ],
                        border: Border.all(
                          color: widget.settingsColor.withAlpha(40),
                          width: 1.5,
                        ),
                      ),
                      child: Center(
                        child: SvgPicture.asset(
                          'assets/images/p2.svg',
                          width: 50,
                          height: 50,
                          colorFilter: ColorFilter.mode(
                            widget.settingsColor,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // اسم التطبيق بتصميم محسن
                    ShaderMask(
                      shaderCallback: (bounds) => LinearGradient(
                        colors: [
                          widget.settingsColor,
                          widget.settingsColor.withAlpha(200),
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ).createShader(bounds),
                      child: const Text(
                        'وهج السالك',
                        style: TextStyle(
                          fontSize: 26,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          letterSpacing: 0.5,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    const SizedBox(height: 8),

                    // رقم الإصدار
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: widget.settingsColor.withAlpha(20),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: widget.settingsColor.withAlpha(30),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        'الإصدار $_appVersion',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).textTheme.bodyMedium?.color,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    const SizedBox(height: 20),

                    // معلومات المطور
                    Text(
                      'تم التطوير بواسطة فريق وهج السالك',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).textTheme.bodyMedium?.color,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 8),

                    // حقوق النشر
                    Text(
                      'جميع الحقوق محفوظة © ${DateTime.now().year}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context)
                            .textTheme
                            .bodySmall
                            ?.color
                            ?.withAlpha(153),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
          delay: 0.8,
        ),
      ],
    );
  }

  // الحصول على إصدار التطبيق
  Future<void> _getAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _appVersion = packageInfo.version;
      });
    } catch (e) {
      // استخدم إصدار افتراضي إذا حدث خطأ
      setState(() {
        _appVersion = '1.0.0';
      });
    }
  }

  // معالجة النقر على معلومات التطبيق
  void _handleAppInfoTap() {
    // تم إزالة وضع المطور
  }

  // مسح بيانات التطبيق
  Future<void> _clearAppData() async {
    DialogUtils.showCustomDialog(
      context,
      title: 'مسح البيانات',
      content:
          'هل أنت متأكد من رغبتك في مسح جميع البيانات المحفوظة؟ لا يمكن التراجع عن هذا الإجراء.',
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        TextButton(
          onPressed: () async {
            Navigator.pop(context);
            await _databaseHelper.clearAllData();
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم مسح البيانات بنجاح')),
              );
            }
          },
          child: const Text('مسح', style: TextStyle(color: Colors.red)),
        ),
      ],
    );
  }
}
