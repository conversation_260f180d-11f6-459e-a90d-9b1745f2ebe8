// خدمة التحقق من أذونات الإشعارات - تم تحديثها لاستخدام النظام الجديد
// تم تعطيل هذا الملف لأن وظائفه تم دمجها في local_notification_service.dart و notification_manager.dart

import 'package:flutter/material.dart';
// import 'package:awesome_notifications/awesome_notifications.dart'; // تم تعطيل استخدام هذه المكتبة
import 'package:shared_preferences/shared_preferences.dart';

import 'notification_manager.dart';

/// خدمة للتحقق من أذونات الإشعارات وطلبها بطريقة احترافية
/// ملاحظة: هذه الخدمة تم استبدالها بـ NotificationManager و LocalNotificationService
/// يتم الاحتفاظ بها للتوافق مع الكود القديم فقط
@Deprecated('استخدم NotificationManager بدلاً من ذلك')
class NotificationPermissionService {
  // نمط singleton للوصول لنفس النسخة من الخدمة في جميع أنحاء التطبيق
  static final NotificationPermissionService _instance =
      NotificationPermissionService._internal();

  factory NotificationPermissionService() => _instance;

  NotificationPermissionService._internal() {
    debugPrint(
        'تحذير: NotificationPermissionService تم استبداله بـ NotificationManager');
  }

  // مفتاح تخزين حالة طلب الأذونات
  static const String _permissionRequestedKey =
      'notification_permission_requested';

  // مفتاح تخزين حالة تفعيل الإشعارات
  static const String _notificationsEnabledKey = 'notifications_enabled';

  // مدير الإشعارات الجديد
  final NotificationManager _notificationManager = NotificationManager();

  /// التحقق مما إذا كان قد تم طلب الأذونات من قبل
  Future<bool> hasRequestedPermission() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_permissionRequestedKey) ?? false;
  }

  /// تعيين حالة طلب الأذونات
  Future<void> setPermissionRequested(bool requested) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_permissionRequestedKey, requested);
  }

  /// التحقق من حالة أذونات الإشعارات
  Future<bool> checkNotificationPermissions() async {
    // استخدام النظام الجديد
    return await _notificationManager.checkNotificationPermissions();
  }

  /// طلب أذونات الإشعارات
  Future<bool> requestNotificationPermissions() async {
    // استخدام النظام الجديد
    final result = await _notificationManager.requestNotificationPermissions();

    // تسجيل أنه تم طلب الأذونات
    await setPermissionRequested(true);

    return result;
  }

  /// التحقق من حالة تفعيل الإشعارات
  Future<bool> getNotificationsEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_notificationsEnabledKey) ?? true; // افتراضياً مفعلة
    } catch (e) {
      debugPrint('خطأ في الحصول على حالة تفعيل الإشعارات: $e');
      return true; // افتراضياً مفعلة
    }
  }

  /// تفعيل أو تعطيل الإشعارات
  Future<void> setNotificationsEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_notificationsEnabledKey, enabled);
      debugPrint('تم ${enabled ? 'تفعيل' : 'تعطيل'} الإشعارات بنجاح');
    } catch (e) {
      debugPrint('خطأ في تفعيل/تعطيل الإشعارات: $e');
    }
  }

  /// فتح إعدادات التطبيق في نظام التشغيل
  Future<void> openAppSettings() async {
    // استخدام النظام الجديد - توجيه الطلب إلى مدير الإشعارات
    try {
      // طلب الأذونات من خلال مدير الإشعارات
      await _notificationManager.requestNotificationPermissions();

      // ملاحظة: لا يوجد طريقة مباشرة لفتح إعدادات التطبيق في flutter_local_notifications
      // لذلك نكتفي بطلب الأذونات من خلال مدير الإشعارات
      debugPrint('تم طلب أذونات الإشعارات من خلال مدير الإشعارات');
    } catch (e) {
      debugPrint('خطأ في فتح إعدادات التطبيق: $e');
    }
  }

  /// التحقق من الأذونات وطلبها إذا لزم الأمر
  /// يرجع true إذا كانت الأذونات ممنوحة، وfalse إذا كانت مرفوضة
  Future<bool> checkAndRequestPermissions() async {
    final isAllowed = await checkNotificationPermissions();
    if (!isAllowed) {
      return await requestNotificationPermissions();
    }
    return true;
  }
}
