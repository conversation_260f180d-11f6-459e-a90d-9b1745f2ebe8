import 'package:flutter/material.dart';
// import 'package:flutter/services.dart'; // No es necesario ya que está incluido en material.dart
import 'package:flutter_svg/flutter_svg.dart';
import '../utils/app_colors.dart';
import '../models/poem.dart';
import '../services/database_helper.dart';
// import '../services/native_share_service.dart'; // Comentado temporalmente
import 'poem_detail_screen.dart';

class PoemsScreen extends StatefulWidget {
  const PoemsScreen({super.key});

  @override
  State<PoemsScreen> createState() => _PoemsScreenState();
}

class _PoemsScreenState extends State<PoemsScreen>
    with SingleTickerProviderStateMixin {
  // للتحكم في الحركات
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  // للبحث
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  String _searchQuery = "";

  // للتصفية
  String _selectedCategory = "الكل";
  String _selectedEra = "الكل";
  List<String> _categories = ["الكل"];
  List<String> _eras = ["الكل"];

  // قائمة القصائد
  late Future<List<Poem>> _poems;
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  @override
  void initState() {
    super.initState();

    // تحميل القصائد
    _poems = _loadPoems();

    // إعداد الحركات
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
      ),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.3, 1.0, curve: Curves.easeOut),
      ),
    );

    // بدء الحركة
    Future.delayed(const Duration(milliseconds: 200), () {
      _animationController.forward();
    });

    // مراقبة البحث
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  // تحميل القصائد
  Future<List<Poem>> _loadPoems() async {
    try {
      final poems = await _databaseHelper.getPoems();

      // استخراج الفئات والعصور المتاحة
      final categories = poems.map((p) => p.category).toSet().toList();
      final eras = poems.map((p) => p.era).toSet().toList();

      setState(() {
        _categories = ["الكل", ...categories];
        _eras = ["الكل", ...eras];
      });

      return poems;
    } catch (e) {
      // عرض رسالة خطأ إذا فشل التحميل
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء تحميل القصائد: $e')),
        );
      }
      return [];
    }
  }

  // تصفية وبحث القصائد
  List<Poem> _filterAndSearchPoems(List<Poem> poems) {
    var filteredPoems = poems;

    // تصفية حسب الفئة
    if (_selectedCategory != "الكل") {
      filteredPoems = filteredPoems
          .where((poem) => poem.category == _selectedCategory)
          .toList();
    }

    // تصفية حسب العصر
    if (_selectedEra != "الكل") {
      filteredPoems =
          filteredPoems.where((poem) => poem.era == _selectedEra).toList();
    }

    // البحث
    if (_searchQuery.isNotEmpty) {
      filteredPoems = filteredPoems
          .where((poem) =>
              poem.title.contains(_searchQuery) ||
              poem.poet.contains(_searchQuery) ||
              poem.content.contains(_searchQuery))
          .toList();
    }

    return filteredPoems;
  }

  // إضافة إلى المفضلة
  void _toggleFavorite(Poem poem) async {
    try {
      // تحديث الواجهة فورًا لتحسين تجربة المستخدم
      final bool newFavoriteState = !poem.isFavorite;

      // تحديث الواجهة فورًا قبل الاتصال بقاعدة البيانات
      setState(() {
        // تحديث القصيدة في القائمة الحالية
        _updatePoemInList(poem.id, newFavoriteState);
      });

      // استخدام معرف القصيدة الأصلي مباشرة
      dynamic poemId = poem.id;
      debugPrint('استخدام معرف القصيدة الأصلي: $poemId');

      // محاولة تحويل المعرف إلى رقم إذا كان نصياً
      try {
        if (poemId is String) {
          poemId = int.parse(poemId);
        }
      } catch (e) {
        // إذا فشل التحويل، استخدم المعرف كما هو
        debugPrint('لا يمكن تحويل المعرف إلى رقم: $poemId');
      }

      // تنفيذ عملية قاعدة البيانات
      if (!newFavoriteState) {
        // إذا كانت الحالة الجديدة غير مفضلة
        await _databaseHelper.removeFavorite(poemId, 'poem');
        debugPrint('تمت إزالة القصيدة من المفضلة: $poemId');
      } else {
        // إذا كانت الحالة الجديدة مفضلة
        await _databaseHelper.addFavorite(poemId, 'poem');
        debugPrint('تمت إضافة القصيدة إلى المفضلة: $poemId');
      }

      // إعادة تحميل القصائد للتأكد من تحديث الحالة بشكل صحيح
      // لكن لا نعيد تحميل القصائد فورًا لتجنب الوميض
      // بدلاً من ذلك، نعيد تحميلها بعد فترة قصيرة
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            _poems = _loadPoems();
          });
        }
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(newFavoriteState
                ? 'تمت الإضافة إلى المفضلة'
                : 'تمت الإزالة من المفضلة'),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            duration: const Duration(seconds: 1), // تقليل مدة الإشعار
          ),
        );
      }
    } catch (e) {
      // في حالة حدوث خطأ، نعيد تحميل القصائد للتأكد من عرض الحالة الصحيحة
      setState(() {
        _poems = _loadPoems();
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    }
  }

  // تحديث القصيدة في القائمة الحالية
  void _updatePoemInList(dynamic poemId, bool isFavorite) async {
    // الحصول على القائمة الحالية من القصائد
    final List<Poem> currentPoems = await _poems;

    // البحث عن القصيدة وتحديثها
    for (int i = 0; i < currentPoems.length; i++) {
      if (currentPoems[i].id == poemId) {
        // إنشاء نسخة جديدة من القصيدة مع تحديث حالة المفضلة
        currentPoems[i] = currentPoems[i].copyWith(isFavorite: isFavorite);
        break;
      }
    }

    // تحديث المستقبل بالقائمة المحدثة
    _poems = Future.value(currentPoems);
  }

  // مشاركة القصيدة - تم تعطيلها مؤقتاً
  // void _sharePoem(Poem poem) async {
  //   HapticFeedback.lightImpact();
  //   // تم تعطيل المشاركة مؤقتاً
  //   // await NativeShareService.sharePoem(poem);
  //   if (mounted) {
  //     ScaffoldMessenger.of(context).showSnackBar(
  //       const SnackBar(
  //         content: Text('تم تعطيل ميزة المشاركة مؤقتاً'),
  //         behavior: SnackBarBehavior.floating,
  //       ),
  //     );
  //   }
  // }

  // بناء حقل البحث
  Widget _buildSearchField() {
    return TextField(
      controller: _searchController,
      autofocus: true,
      decoration: InputDecoration(
        hintText: 'ابحث عن قصيدة أو شاعر...',
        hintStyle: TextStyle(
          color: Theme.of(context).colorScheme.primary.withAlpha(150),
        ),
        border: InputBorder.none,
      ),
      style: TextStyle(
        color: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  // بناء شريط التصفية
  Widget _buildFilterBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان التصفية
          const Row(
            children: [
              Icon(
                Icons.filter_list,
                size: 18,
                color: AppColors.poemsColor,
              ),
              SizedBox(width: 8),
              Text(
                'تصفية القصائد',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.poemsColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // أشرطة التصفية
          Row(
            children: [
              // تصفية حسب الفئة
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    color: AppColors.poemsColor.withAlpha(25),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppColors.poemsColor.withAlpha(50),
                      width: 1,
                    ),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: _selectedCategory,
                      isExpanded: true,
                      icon: const Icon(
                        Icons.arrow_drop_down,
                        color: AppColors.poemsColor,
                      ),
                      items: _categories.map((String category) {
                        return DropdownMenuItem<String>(
                          value: category,
                          child: Text(
                            category,
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  Theme.of(context).textTheme.bodyMedium?.color,
                            ),
                          ),
                        );
                      }).toList(),
                      onChanged: (String? newValue) {
                        if (newValue != null) {
                          setState(() {
                            _selectedCategory = newValue;
                          });
                        }
                      },
                      hint: const Text(
                        'الفئة',
                        style: TextStyle(
                          color: AppColors.poemsColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),

              // تصفية حسب العصر
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    color: AppColors.poemsColor.withAlpha(25),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppColors.poemsColor.withAlpha(50),
                      width: 1,
                    ),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: _selectedEra,
                      isExpanded: true,
                      icon: const Icon(
                        Icons.arrow_drop_down,
                        color: AppColors.poemsColor,
                      ),
                      items: _eras.map((String era) {
                        return DropdownMenuItem<String>(
                          value: era,
                          child: Text(
                            era,
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  Theme.of(context).textTheme.bodyMedium?.color,
                            ),
                          ),
                        );
                      }).toList(),
                      onChanged: (String? newValue) {
                        if (newValue != null) {
                          setState(() {
                            _selectedEra = newValue;
                          });
                        }
                      },
                      hint: const Text(
                        'العصر',
                        style: TextStyle(
                          color: AppColors.poemsColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بناء بطاقة القصيدة
  Widget _buildPoemCard(Poem poem) {
    return Card(
      elevation: 2,
      shadowColor: AppColors.poemsColor.withAlpha(50),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PoemDetailScreen(poem: poem),
            ),
          );
        },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان القصيدة والشاعر
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // أيقونة القصيدة
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppColors.poemsColor.withAlpha(25),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.menu_book,
                        color: AppColors.poemsColor,
                        size: 20,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // عنوان القصيدة والشاعر
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          poem.title,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color:
                                Theme.of(context).textTheme.titleLarge?.color,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          poem.poet,
                          style: TextStyle(
                            fontSize: 14,
                            color: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.color
                                ?.withAlpha(180),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // أزرار الإجراءات
                  Row(
                    children: [
                      // زر المفضلة
                      IconButton(
                        icon: Icon(
                          poem.isFavorite
                              ? Icons.favorite
                              : Icons.favorite_border,
                          color: poem.isFavorite ? Colors.red : Colors.grey,
                        ),
                        onPressed: () => _toggleFavorite(poem),
                        tooltip: poem.isFavorite
                            ? 'إزالة من المفضلة'
                            : 'إضافة إلى المفضلة',
                      ),

                      // زر المشاركة - تم تعطيله مؤقتاً
                      // IconButton(
                      //   icon: const Icon(
                      //     Icons.share,
                      //     color: Colors.grey,
                      //   ),
                      //   onPressed: () => _sharePoem(poem),
                      //   tooltip: 'مشاركة',
                      // ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // بيت من القصيدة
              if (poem.verses.isNotEmpty)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.poemsColor.withAlpha(15),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    poem.verses.first,
                    style: TextStyle(
                      fontSize: 14,
                      height: 1.6,
                      color: Theme.of(context).textTheme.bodyMedium?.color,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ),

              const SizedBox(height: 12),

              // معلومات إضافية
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // الفئة
                  Chip(
                    label: Text(
                      poem.category,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.white,
                      ),
                    ),
                    backgroundColor: AppColors.poemsColor,
                    padding: EdgeInsets.zero,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),

                  // العصر
                  Text(
                    poem.era,
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).textTheme.bodySmall?.color,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        physics: const BouncingScrollPhysics(),
        slivers: [
          // شريط التطبيق مع العنوان وأيقونة البحث
          SliverAppBar(
            expandedHeight: _isSearching ? 0 : 220.0,
            pinned: true,
            floating: true,
            stretch: true,
            elevation: 0,
            backgroundColor: Theme.of(context).scaffoldBackgroundColor,
            title: _isSearching
                ? _buildSearchField()
                : Text(
                    'المنهل الرواي - محمد هزاع باعلوي',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
            actions: [
              IconButton(
                icon: Icon(
                  _isSearching ? Icons.close : Icons.search,
                  color: Theme.of(context).colorScheme.primary,
                ),
                onPressed: () {
                  setState(() {
                    _isSearching = !_isSearching;
                    if (!_isSearching) {
                      _searchController.clear();
                      _searchQuery = "";
                    }
                  });
                },
              ),
            ],
            flexibleSpace: _isSearching
                ? null
                : FlexibleSpaceBar(
                    background: Stack(
                      children: [
                        // خلفية متدرجة
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                AppColors.poemsColor.withAlpha(38),
                                Theme.of(context).scaffoldBackgroundColor,
                              ],
                            ),
                          ),
                        ),
                        // زخرفة إسلامية
                        Positioned(
                          top: -20,
                          right: -20,
                          child: Opacity(
                            opacity: 0.1,
                            child: SvgPicture.asset(
                              'assets/images/p1.svg',
                              width: 200,
                              height: 200,
                              colorFilter: const ColorFilter.mode(
                                AppColors.poemsColor,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                        ),
                        // صورة دائرية توضيحية
                        Positioned(
                          bottom: 15,
                          right: 20,
                          child: Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: AppColors.poemsColor.withAlpha(38),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.poemsColor.withAlpha(38),
                                  blurRadius: 20,
                                  spreadRadius: 5,
                                ),
                              ],
                            ),
                            child: const Center(
                              child: Icon(
                                Icons.menu_book,
                                size: 40,
                                color: AppColors.poemsColor,
                              ),
                            ),
                          ),
                        ),
                        // عنوان الصفحة وشرح
                        Positioned(
                          bottom: 20,
                          left: 20,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    width: 4,
                                    height: 20,
                                    decoration: BoxDecoration(
                                      color: AppColors.poemsColor,
                                      borderRadius: BorderRadius.circular(2),
                                    ),
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    'المنهل الرواي',
                                    style: Theme.of(context)
                                        .textTheme
                                        .headlineSmall!
                                        .copyWith(
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.poemsColor,
                                        ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'مجموعة قصائد للشاعر محمد هزاع باعلوي',
                                style: TextStyle(
                                  color: AppColors.poemsColor.withAlpha(200),
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
          ),

          // شريط التصفية
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(0, 16, 0, 16),
              child: SlideTransition(
                position: _slideAnimation,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: _buildFilterBar(),
                ),
              ),
            ),
          ),

          // قائمة القصائد
          FutureBuilder<List<Poem>>(
            future: _poems,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const SliverFillRemaining(
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                );
              } else if (snapshot.hasError) {
                return SliverFillRemaining(
                  child: Center(
                    child: Text('حدث خطأ: ${snapshot.error}'),
                  ),
                );
              } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                return const SliverFillRemaining(
                  child: Center(
                    child: Text('لا توجد قصائد متاحة'),
                  ),
                );
              } else {
                final filteredPoems = _filterAndSearchPoems(snapshot.data!);

                if (filteredPoems.isEmpty) {
                  return const SliverFillRemaining(
                    child: Center(
                      child: Text('لا توجد نتائج مطابقة للبحث'),
                    ),
                  );
                }

                return SliverPadding(
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                  sliver: SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final poem = filteredPoems[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 16),
                          child: SlideTransition(
                            position: _slideAnimation,
                            child: FadeTransition(
                              opacity: _fadeAnimation,
                              child: _buildPoemCard(poem),
                            ),
                          ),
                        );
                      },
                      childCount: filteredPoems.length,
                    ),
                  ),
                );
              }
            },
          ),
        ],
      ),
    );
  }
}
