// خدمة الإشعارات الفاخرة باستخدام awesome_notifications
/*
import 'dart:math';
import 'dart:convert';
import 'dart:typed_data'; // للتعامل مع Int64List
import 'package:flutter/material.dart';
import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../main.dart'; // استيراد للوصول إلى MyApp.navigatorKey
import '../../../utils/constants.dart'; // استيراد للوصول إلى مسارات التطبيق
import '../models/user_activity_model.dart';
import '../screens/wird_list_screen.dart';
import '../utils/tasbih_colors.dart'; // استيراد للوصول إلى ألوان المسبحة

/// خدمة لإدارة إشعارات العودة للمستخدمين المتأخرين والتذكير بالأوراد
/// تستخدم مكتبة awesome_notifications للحصول على إشعارات فاخرة ومتقدمة
class AwesomeTasbihNotificationService {
  static final AwesomeTasbihNotificationService _instance =
      AwesomeTasbihNotificationService._internal();

  factory AwesomeTasbihNotificationService() => _instance;

  AwesomeTasbihNotificationService._internal();

  /// معرف قناة إشعارات العودة
  static const String _returnChannelKey = 'tasbih_reminders';

  /// اسم قناة إشعارات العودة
  static const String _returnChannelName = 'تذكيرات المسبحة';

  /// وصف قناة إشعارات العودة
  static const String _returnChannelDescription =
      'إشعارات تذكير للعودة إلى المسبحة';

  /// معرف قناة إشعارات الأوراد
  static const String _wirdChannelKey = 'wird_reminders';

  /// اسم قناة إشعارات الأوراد
  static const String _wirdChannelName = 'تذكيرات الأوراد';

  /// وصف قناة إشعارات الأوراد
  static const String _wirdChannelDescription =
      'إشعارات تذكير بالأوراد اليومية';

  /// مفتاح تخزين بيانات نشاط المستخدم
  static const String _userActivityKey = 'tasbih_user_activity';

  /// نمط اهتزاز فاخر للإشعارات المهمة - نمط متموج
  static final Int64List _luxuryVibrationPattern =
      Int64List.fromList([0, 100, 200, 300, 400, 500, 400, 300, 200, 100]);

  // تم تعليق هذا المتغير لأنه غير مستخدم حاليًا
  // يمكن استخدامه في المستقبل عند إضافة قناة إشعارات للإشعارات العاجلة
  // static final Int64List _urgentVibrationPattern =
  //     Int64List.fromList([0, 100, 50, 100, 50, 100, 50, 200, 100, 300]);

  /// نمط اهتزاز فاخر للتذكيرات - نمط ناعم ومتدرج
  static final Int64List _reminderVibrationPattern =
      Int64List.fromList([0, 50, 100, 150, 200, 250, 300, 350, 400, 450]);

  /// معرف إشعار اختبار الإشعارات
  static const int _testNotificationId = 9999;

  /// تهيئة خدمة الإشعارات
  Future<void> initialize() async {
    debugPrint('جاري تهيئة خدمة الإشعارات الفاخرة...');

    // تهيئة مكتبة الإشعارات
    await AwesomeNotifications().initialize(
      // لا نستخدم أيقونة مخصصة للإشعارات، نستخدم أيقونة التطبيق الافتراضية
      null,
      [
        // قناة إشعارات العودة للمسبحة - بتصميم فاخر
        NotificationChannel(
          channelKey: _returnChannelKey,
          channelName: _returnChannelName,
          channelDescription: _returnChannelDescription,
          defaultColor: TasbihColors.primary,
          ledColor: TasbihColors.primary,
          // إضافة وقت تشغيل وإيقاف LED لتجنب الخطأ
          ledOnMs: 500,
          ledOffMs: 500,
          importance: NotificationImportance.High,
          playSound: false, // تعطيل الصوت بناءً على طلب المستخدم
          enableVibration: true,
          // استخدام نمط الاهتزاز الفاخر للإشعارات المهمة
          vibrationPattern: _luxuryVibrationPattern,
          channelShowBadge: true,
        ),

        // قناة إشعارات الأوراد - بتصميم فاخر
        NotificationChannel(
          channelKey: _wirdChannelKey,
          channelName: _wirdChannelName,
          channelDescription: _wirdChannelDescription,
          defaultColor: TasbihColors.primary,
          ledColor: TasbihColors.primary,
          // إضافة وقت تشغيل وإيقاف LED لتجنب الخطأ
          ledOnMs: 500,
          ledOffMs: 500,
          importance: NotificationImportance.High,
          playSound: false, // تعطيل الصوت بناءً على طلب المستخدم
          enableVibration: true,
          // استخدام نمط الاهتزاز الفاخر للتذكيرات
          vibrationPattern: _reminderVibrationPattern,
          channelShowBadge: true,
        ),
      ],
      debug: true,
    );

    // تسجيل معالج النقر على الإشعارات
    AwesomeNotifications().setListeners(
      onActionReceivedMethod: _onActionReceivedMethod,
    );

    // طلب الأذونات اللازمة للإشعارات
    await requestNotificationPermissions();

    debugPrint('تم تهيئة خدمة الإشعارات الفاخرة بنجاح');
  }

  /// معالجة النقر على الإشعار
  @pragma('vm:entry-point')
  static Future<void> _onActionReceivedMethod(
      ReceivedAction receivedAction) async {
    debugPrint('تم النقر على الإشعار: ${receivedAction.payload}');

    try {
      // التحقق من نوع الإشعار من خلال الـ payload إذا كان متوفراً
      if (receivedAction.payload != null &&
          receivedAction.payload!.isNotEmpty) {
        final String notificationType = receivedAction.payload!['type'] ?? '';
        debugPrint('نوع الإشعار: $notificationType');

        // التحقق من نوع الإشعار
        if (notificationType == 'tasbih_return') {
          // إشعار العودة للمسبحة
          debugPrint('الانتقال إلى شاشة المسبحة');
          _navigateToTasbihScreen();
          return;
        } else if (notificationType == 'wird_reminder') {
          // إشعار تذكير بالورد
          final String wirdIdStr = receivedAction.payload!['wird_id'] ?? '-1';
          final int wirdId = int.tryParse(wirdIdStr) ?? -1;
          final String wirdName = receivedAction.payload!['wird_name'] ?? '';
          debugPrint('الانتقال إلى شاشة تشغيل الورد: $wirdId - $wirdName');

          if (wirdId > 0) {
            _navigateToWirdPlayerScreen(wirdId);
            return;
          }
        }
      }

      // إذا لم يكن هناك payload أو لم يتم التعرف على النوع، نفترض أنه إشعار عودة للمسبحة
      debugPrint(
          'لم يتم التعرف على نوع الإشعار، الانتقال إلى شاشة المسبحة افتراضياً');
      _navigateToTasbihScreen();
    } catch (e) {
      debugPrint('خطأ في معالجة النقر على الإشعار: $e');
    }
  }

  /// الانتقال إلى شاشة المسبحة
  static void _navigateToTasbihScreen() {
    // استخدام مفتاح التنقل العام للتطبيق
    if (MyApp.navigatorKey.currentState != null) {
      // الانتقال إلى شاشة المسبحة
      MyApp.navigatorKey.currentState!.pushNamedAndRemoveUntil(
        AppConstants.tasbihRoute, // مسار صفحة المسبحة
        (route) => route.isFirst, // الإبقاء على الصفحة الرئيسية فقط في المكدس
      );
      debugPrint('تم طلب فتح صفحة المسبحة');
    }
  }

  /// الانتقال إلى شاشة تشغيل الورد
  static void _navigateToWirdPlayerScreen(int wirdId) {
    // استخدام مفتاح التنقل العام للتطبيق
    if (MyApp.navigatorKey.currentState != null) {
      debugPrint('الانتقال إلى شاشة المسبحة أولاً');
      // الانتقال إلى شاشة المسبحة أولاً
      MyApp.navigatorKey.currentState!.pushNamedAndRemoveUntil(
        AppConstants.tasbihRoute,
        (route) => route.isFirst,
      );

      // ثم الانتقال مباشرة إلى شاشة تشغيل الورد
      Future.delayed(const Duration(milliseconds: 500), () {
        if (MyApp.navigatorKey.currentState != null) {
          debugPrint(
              'الانتقال إلى شاشة قائمة الأوراد مع تنشيط الورد رقم $wirdId');
          MyApp.navigatorKey.currentState!.push(
            MaterialPageRoute(
              builder: (context) => WirdListScreen(wirdIdToActivate: wirdId),
            ),
          );
        }
      });
    }
  }

  /// تسجيل نشاط المستخدم
  Future<void> recordUserActivity() async {
    try {
      // الحصول على بيانات نشاط المستخدم الحالية
      final UserActivity activity = await _getUserActivity();

      // تحديث بيانات النشاط
      final UserActivity updatedActivity = UserActivity(
        lastUsedDate: DateTime.now(),
        daysSinceLastUsed: 0,
        notificationCount: activity.notificationCount,
        notificationsEnabled: activity.notificationsEnabled,
        preferredHour: activity.preferredHour,
        lastNotificationDate: activity.lastNotificationDate,
      );

      // حفظ بيانات النشاط المحدثة
      await _saveUserActivity(updatedActivity);

      // إلغاء الإشعارات الحالية لأن المستخدم نشط الآن
      await AwesomeNotifications().cancelAll();

      debugPrint('تم تسجيل نشاط المستخدم بنجاح');
    } catch (e) {
      debugPrint('خطأ في تسجيل نشاط المستخدم: $e');
    }
  }

  /// الحصول على بيانات نشاط المستخدم
  Future<UserActivity> _getUserActivity() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? activityJson = prefs.getString(_userActivityKey);

      if (activityJson != null && activityJson.isNotEmpty) {
        return UserActivity.fromJson(jsonDecode(activityJson));
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على بيانات نشاط المستخدم: $e');
    }

    // إذا لم يتم العثور على بيانات، إرجاع بيانات افتراضية
    return UserActivity(
      lastUsedDate: DateTime.now(),
      daysSinceLastUsed: 0,
      notificationCount: 0,
      notificationsEnabled: true,
      preferredHour: 20, // الساعة 8 مساءً كوقت افتراضي
      lastNotificationDate: DateTime.now().subtract(const Duration(days: 1)),
    );
  }

  /// حفظ بيانات نشاط المستخدم
  Future<void> _saveUserActivity(UserActivity activity) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String activityJson = jsonEncode(activity.toJson());
      await prefs.setString(_userActivityKey, activityJson);
      debugPrint('تم حفظ بيانات نشاط المستخدم بنجاح');
    } catch (e) {
      debugPrint('خطأ في حفظ بيانات نشاط المستخدم: $e');
    }
  }

  /// جدولة إشعار تذكير بالورد
  Future<void> scheduleWirdReminder({
    required int wirdId,
    required String wirdName,
    required TimeOfDay reminderTime,
    bool isDaily = true,
  }) async {
    try {
      debugPrint(
          'جاري جدولة إشعار تذكير للورد: $wirdName في الساعة ${reminderTime.hour}:${reminderTime.minute}');

      // تحويل TimeOfDay إلى DateTime لليوم الحالي
      final now = DateTime.now();
      final scheduledTime = DateTime(
        now.year,
        now.month,
        now.day,
        reminderTime.hour,
        reminderTime.minute,
      );

      // إذا كان الوقت المحدد قد مر لليوم الحالي، نجدول للغد
      final DateTime finalScheduledTime = scheduledTime.isBefore(now)
          ? scheduledTime.add(const Duration(days: 1))
          : scheduledTime;

      // تنسيق الوقت للعرض
      final String timeStr =
          '${reminderTime.hour}:${reminderTime.minute.toString().padLeft(2, '0')}';

      // الحصول على رسالة عشوائية للتذكير بالورد
      final String message = _getRandomWirdReminderMessage(wirdName);

      // معرف الإشعار
      final int notificationId =
          5000 + wirdId; // نستخدم نطاق 5000+ للإشعارات المجدولة

      // إلغاء أي إشعارات سابقة لنفس الورد
      await AwesomeNotifications().cancel(notificationId);
      debugPrint('تم إلغاء أي إشعارات سابقة للورد بمعرف $notificationId');

      // إنشاء إشعار مجدول
      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: notificationId,
          channelKey: _wirdChannelKey,
          title: 'تذكير بالورد',
          body: message,
          notificationLayout: NotificationLayout.BigText,
          color: TasbihColors.primary,
          category: NotificationCategory.Reminder,
          wakeUpScreen: true,
          fullScreenIntent: true,
          criticalAlert: true,
          payload: {
            'type': 'wird_reminder',
            'wird_id': wirdId.toString(),
            'wird_name': wirdName,
          },
        ),
        schedule: isDaily
            ? NotificationCalendar(
                hour: reminderTime.hour,
                minute: reminderTime.minute,
                second: 0,
                repeats: true,
                preciseAlarm: true,
              )
            : NotificationCalendar.fromDate(
                date: finalScheduledTime,
                preciseAlarm: true,
              ),
      );

      // تم إزالة الإشعار التأكيدي الفوري لتحسين تجربة المستخدم

      debugPrint(
          'تمت جدولة إشعار الورد بنجاح للوقت $timeStr ${isDaily ? 'يومياً' : ''}');
    } catch (e) {
      debugPrint('خطأ في جدولة إشعار تذكير بالورد: $e');
      rethrow;
    }
  }

  /// إلغاء إشعار تذكير بالورد
  Future<void> cancelWirdReminder(int wirdId) async {
    try {
      // إلغاء الإشعار العادي
      final notificationId = 5000 + wirdId;
      await AwesomeNotifications().cancel(notificationId);

      // لم يعد هناك إشعار تأكيدي للإلغاء

      debugPrint('تم إلغاء جميع إشعارات الورد بنجاح');
    } catch (e) {
      debugPrint('خطأ في إلغاء إشعار تذكير بالورد: $e');
    }
  }

  /// إرسال إشعار تجريبي للورد
  Future<void> sendTestWirdNotification(
    int wirdId,
    String wirdName,
    TimeOfDay testTime,
  ) async {
    try {
      final timeStr =
          '${testTime.hour}:${testTime.minute.toString().padLeft(2, '0')}';
      debugPrint('جاري إرسال إشعار تجريبي للورد $wirdName في الساعة $timeStr');

      // للاختبار، نضيف 10 ثواني فقط للوقت الحالي - وقت قصير للتأكد من ظهور الإشعار فوراً
      final scheduledTime = DateTime.now().add(const Duration(seconds: 10));
      debugPrint(
          'سيظهر الإشعار التجريبي بعد 10 ثوانٍ في: ${scheduledTime.toString()}');

      // الحصول على رسالة عشوائية للتذكير بالورد
      final message = _getRandomWirdReminderMessage(wirdName);

      // إنشاء معرفات فريدة للإشعارات التجريبية
      final testNotificationId =
          9000 + wirdId; // نستخدم نطاق 9000+ للإشعارات التجريبية

      // إلغاء أي إشعارات تجريبية سابقة
      await AwesomeNotifications().cancel(testNotificationId);

      // إرسال إشعار فوري مع نمط اهتزاز فاخر للإشعارات العاجلة
      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: testNotificationId,
          channelKey: _wirdChannelKey,
          title: 'اختبار تذكير الورد',
          body: message,
          notificationLayout: NotificationLayout.BigText,
          color: TasbihColors.primary,
          category: NotificationCategory.Reminder,
          wakeUpScreen: true,
          fullScreenIntent: true,
          criticalAlert: true,
          payload: {
            'type': 'wird_reminder',
            'wird_id': wirdId.toString(),
            'wird_name': wirdName,
            'is_test': 'true',
          },
        ),
      );

      // جدولة إشعار بعد 10 ثواني
      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: testNotificationId + 500,
          channelKey: _wirdChannelKey,
          title: 'اختبار تذكير الورد (مجدول)',
          body: message,
          notificationLayout: NotificationLayout.BigText,
          color: TasbihColors.primary,
          category: NotificationCategory.Reminder,
          wakeUpScreen: true,
          fullScreenIntent: true,
          criticalAlert: true,
          payload: {
            'type': 'wird_reminder',
            'wird_id': wirdId.toString(),
            'wird_name': wirdName,
            'is_test': 'true',
          },
        ),
        schedule: NotificationCalendar.fromDate(
          date: scheduledTime,
          preciseAlarm: true,
        ),
      );

      debugPrint('تم إرسال إشعار تجريبي للورد بنجاح');
    } catch (e) {
      debugPrint('خطأ في إرسال إشعار تجريبي للورد: $e');
      rethrow;
    }
  }

  /// الحصول على رسالة عشوائية للتذكير بالورد
  String _getRandomWirdReminderMessage(String wirdName) {
    final random = Random();

    // قائمة رسائل التذكير بالورد الفاخرة
    final List<String> messages = [
      'حان وقت ورد "$wirdName" 🌟 لحظات من السكينة والروحانية في انتظارك',
      'تذكير بورد "$wirdName" 🕌 استمتع بلحظات من الذكر والدعاء',
      'وقت الورد! "$wirdName" 📿 جدد صلتك بالله من خلال هذا الورد المبارك',
      'ورد "$wirdName" في انتظارك ✨ لحظات من النور والسكينة',
      'حان موعد ورد "$wirdName" 🤲 اغتنم هذه اللحظات المباركة',
      'تذكير بموعد ورد "$wirdName" 💫 لحظات روحانية تنتظرك',
      'وقت الورد! "$wirdName" 🌙 أضئ قلبك بنور الذكر',
    ];

    // اختيار رسالة عشوائية
    return messages[random.nextInt(messages.length)];
  }

  /// جدولة إشعارات العودة للمستخدمين المتأخرين
  Future<void> scheduleReturnNotifications() async {
    try {
      debugPrint('جاري التحقق من نشاط المستخدم لجدولة إشعارات العودة...');

      // الحصول على بيانات نشاط المستخدم
      final UserActivity activity = await _getUserActivity();

      // التحقق مما إذا كان المستخدم غير نشط
      if (activity.daysSinceLastUsed >= 3) {
        debugPrint(
            'المستخدم غير نشط منذ ${activity.daysSinceLastUsed} أيام، جاري جدولة إشعار عودة');
        await _scheduleReturnNotification(activity);
      } else {
        debugPrint('المستخدم نشط، لا داعي لإشعارات العودة');
      }
    } catch (e) {
      debugPrint('خطأ في جدولة إشعارات العودة: $e');
    }
  }

  /// جدولة إشعار عودة للمستخدم
  Future<void> _scheduleReturnNotification(UserActivity activity) async {
    try {
      // تحديد نوع الرسالة بناءً على مدة عدم النشاط
      String messageType;
      if (activity.daysSinceLastUsed >= 30) {
        messageType = 'long_absence';
      } else if (activity.daysSinceLastUsed >= 14) {
        messageType = 'medium_absence';
      } else if (activity.daysSinceLastUsed >= 7) {
        messageType = 'short_absence';
      } else {
        messageType = 'recent_absence';
      }

      // الحصول على رسالة عشوائية للعودة
      final String message = _getRandomReturnMessage(messageType);

      // تحديد وقت الإشعار (بعد ساعة من الآن)
      final DateTime scheduledTime =
          DateTime.now().add(const Duration(hours: 1));

      // معرف الإشعار
      final int notificationId = 4000 + activity.notificationCount;

      // إلغاء أي إشعارات سابقة
      await AwesomeNotifications().cancel(notificationId);

      // جدولة إشعار عودة مع نمط اهتزاز فاخر
      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: notificationId,
          channelKey: _returnChannelKey,
          title: 'وهج السالك',
          body: message,
          notificationLayout: NotificationLayout.BigText,
          color: TasbihColors.primary,
          category: NotificationCategory.Reminder,
          wakeUpScreen: true,
          fullScreenIntent: true,
          criticalAlert: true,
          payload: {
            'type': 'tasbih_return',
            'message_type': messageType,
            'days_inactive': activity.daysSinceLastUsed.toString(),
          },
        ),
        schedule: NotificationCalendar.fromDate(date: scheduledTime),
      );

      // تحديث بيانات نشاط المستخدم
      final UserActivity updatedActivity = activity.copyWith(
        lastNotificationDate: DateTime.now(),
        notificationCount: activity.notificationCount + 1,
      );

      // حفظ بيانات النشاط المحدثة
      await _saveUserActivity(updatedActivity);

      debugPrint(
          'تمت جدولة إشعار العودة بنجاح للساعة ${scheduledTime.hour}:${scheduledTime.minute}');
    } catch (e) {
      debugPrint('خطأ في جدولة إشعار العودة: $e');
    }
  }

  // تم تعليق هذه الدالة لأنها غير مستخدمة حاليًا
  // يمكن استخدامها في المستقبل لإنشاء أنماط اهتزاز ديناميكية
  //
  // /// إنشاء نمط اهتزاز فاخر ديناميكي
  // /// يمكن استخدامه لإنشاء أنماط اهتزاز مختلفة بناءً على المعلمات
  // static Int64List _createLuxuryVibrationPattern({
  //   int intensity = 5, // شدة الاهتزاز (1-10)
  //   int duration = 1000, // مدة الاهتزاز بالمللي ثانية
  //   String pattern = 'wave', // نمط الاهتزاز: wave, pulse, crescendo
  // }) {
  //   final List<int> vibrationPattern = [0]; // بداية بصفر دائماً
  //   final int step =
  //       duration ~/ (intensity * 2); // حساب الخطوة بناءً على الشدة والمدة
  //
  //   switch (pattern) {
  //     case 'wave': // نمط متموج
  //       for (int i = 1; i <= intensity; i++) {
  //         vibrationPattern.add(i * step);
  //         vibrationPattern.add((intensity - i + 1) * step);
  //       }
  //       break;
  //
  //     case 'pulse': // نمط نبضات
  //       for (int i = 1; i <= intensity; i++) {
  //         vibrationPattern.add(step);
  //         vibrationPattern.add(step ~/ 2);
  //       }
  //       break;
  //
  //     case 'crescendo': // نمط متصاعد
  //       for (int i = 1; i <= intensity * 2; i++) {
  //         vibrationPattern.add(i * step ~/ 2);
  //       }
  //       break;
  //
  //     default: // نمط افتراضي
  //       for (int i = 1; i <= intensity; i++) {
  //         vibrationPattern.add(step * i);
  //       }
  //   }
  //
  //   return Int64List.fromList(vibrationPattern);
  // }

  /// الحصول على رسالة عشوائية للعودة
  String _getRandomReturnMessage(String messageType) {
    final random = Random();

    // قائمة رسائل العودة الفاخرة حسب نوع الغياب
    Map<String, List<String>> messagesByType = {
      'recent_absence': [
        'اشتقنا لك! 🌟 عد إلى المسبحة لتجديد الذكر والدعاء',
        'لحظات من السكينة تنتظرك في المسبحة 🕌 عد إلينا',
        'مرت أيام قليلة منذ آخر ذكر، هل تعود للمسبحة الآن؟ 📿',
        '"من ذكرني في نفسه ذكرته في نفسي" ✨ عد للذكر والدعاء',
      ],
      'short_absence': [
        'أسبوع دون ذكر! 🌙 عد إلى المسبحة لتجديد صلتك بالله',
        'قال تعالى: "ألا بذكر الله تطمئن القلوب" 💫 عد للذكر والدعاء',
        'أيام من الغياب عن الذكر، هل تعود للمسبحة الآن؟ 🤲',
        'اشتاقت المسبحة إليك! 🌟 عد لتنعم بلحظات من السكينة والروحانية',
      ],
      'medium_absence': [
        'مضى وقت طويل منذ آخر ذكر! 🕌 عد إلى المسبحة لتجديد روحك',
        'قال ﷺ: "مثل الذي يذكر ربه والذي لا يذكر ربه مثل الحي والميت" 📿 عد للذكر',
        'أسبوعان دون ذكر، ألا تشتاق للحظات الروحانية؟ ✨ عد للمسبحة',
        'الذكر حياة للقلوب 💫 عد إلى المسبحة لتحيي قلبك بالذكر',
      ],
      'long_absence': [
        'مضى شهر دون ذكر! 🌙 عد إلى المسبحة لتجديد صلتك بالله',
        'قال تعالى: "فاذكروني أذكركم" 🤲 عد للذكر والدعاء',
        'اشتقنا إليك كثيراً! 🌟 عد إلى المسبحة لتنعم بلحظات من السكينة',
        'الذكر جنة القلوب ونور الأرواح ✨ عد إلى المسبحة لتنعم بهذا النور',
      ],
    };

    // الحصول على قائمة الرسائل المناسبة لنوع الغياب
    final List<String> messages =
        messagesByType[messageType] ?? messagesByType['recent_absence']!;

    // اختيار رسالة عشوائية
    return messages[random.nextInt(messages.length)];
  }

  /// إرسال إشعار اختباري
  Future<void> sendTestNotification() async {
    try {
      debugPrint('جاري إرسال إشعار اختباري...');

      // إلغاء أي إشعارات اختبارية سابقة
      await AwesomeNotifications().cancel(_testNotificationId);

      // إرسال إشعار اختباري
      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: _testNotificationId,
          channelKey: _returnChannelKey,
          title: 'اختبار الإشعارات',
          body: 'هذا إشعار اختباري للتأكد من عمل نظام الإشعارات بشكل صحيح ✨',
          notificationLayout: NotificationLayout.BigText,
          color: TasbihColors.primary,
          category: NotificationCategory.Reminder,
          wakeUpScreen: true,
          fullScreenIntent: true,
          payload: {
            'type': 'test_notification',
          },
        ),
      );

      debugPrint('تم إرسال إشعار اختباري بنجاح');
    } catch (e) {
      debugPrint('خطأ في إرسال إشعار اختباري: $e');
      rethrow;
    }
  }

  /// إلغاء جميع الإشعارات
  Future<void> cancelAllNotifications() async {
    try {
      debugPrint('جاري إلغاء جميع الإشعارات...');
      await AwesomeNotifications().cancelAll();
      debugPrint('تم إلغاء جميع الإشعارات بنجاح');
    } catch (e) {
      debugPrint('خطأ في إلغاء جميع الإشعارات: $e');
      rethrow;
    }
  }

  /// التحقق من حالة أذونات الإشعارات
  Future<bool> checkNotificationPermissions() async {
    try {
      final isAllowed = await AwesomeNotifications().isNotificationAllowed();
      debugPrint(
          'حالة أذونات الإشعارات: ${isAllowed ? 'مسموح بها' : 'غير مسموح بها'}');
      return isAllowed;
    } catch (e) {
      debugPrint('خطأ في التحقق من حالة أذونات الإشعارات: $e');
      return false;
    }
  }

  /// طلب أذونات الإشعارات
  Future<bool> requestNotificationPermissions() async {
    try {
      final isAllowed = await AwesomeNotifications().isNotificationAllowed();
      if (!isAllowed) {
        final result =
            await AwesomeNotifications().requestPermissionToSendNotifications();
        debugPrint(
            'نتيجة طلب أذونات الإشعارات: ${result ? 'تم السماح' : 'تم الرفض'}');

        // تسجيل حالة الأذونات في التخزين المحلي
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('notification_permission_requested', true);

        return result;
      }
      return true;
    } catch (e) {
      debugPrint('خطأ في طلب أذونات الإشعارات: $e');
      return false;
    }
  }

  /// تفعيل أو تعطيل الإشعارات
  Future<void> toggleNotifications(bool enabled) async {
    try {
      // الحصول على بيانات نشاط المستخدم الحالية
      final UserActivity activity = await _getUserActivity();

      // تحديث حالة تفعيل الإشعارات
      final UserActivity updatedActivity = activity.copyWith(
        notificationsEnabled: enabled,
      );

      // حفظ بيانات النشاط المحدثة
      await _saveUserActivity(updatedActivity);

      debugPrint('تم ${enabled ? 'تفعيل' : 'تعطيل'} الإشعارات بنجاح');

      // إذا تم تعطيل الإشعارات، قم بإلغاء جميع الإشعارات الحالية
      if (!enabled) {
        await cancelAllNotifications();
      }
    } catch (e) {
      debugPrint('خطأ في تفعيل/تعطيل الإشعارات: $e');
      rethrow;
    }
  }

  /// الحصول على حالة تفعيل الإشعارات
  Future<bool> getNotificationsEnabled() async {
    try {
      // الحصول على بيانات نشاط المستخدم
      final UserActivity activity = await _getUserActivity();
      return activity.notificationsEnabled;
    } catch (e) {
      debugPrint('خطأ في الحصول على حالة تفعيل الإشعارات: $e');
      return true; // القيمة الافتراضية هي تفعيل الإشعارات
    }
  }

  /// تعيين الوقت المفضل للإشعارات
  Future<void> setPreferredNotificationTime(int hour) async {
    try {
      // التحقق من صحة الساعة
      if (hour < 0 || hour > 23) {
        throw Exception('الساعة يجب أن تكون بين 0 و 23');
      }

      // الحصول على بيانات نشاط المستخدم الحالية
      final UserActivity activity = await _getUserActivity();

      // تحديث الوقت المفضل
      final UserActivity updatedActivity = activity.copyWith(
        preferredHour: hour,
      );

      // حفظ بيانات النشاط المحدثة
      await _saveUserActivity(updatedActivity);

      debugPrint('تم تعيين الوقت المفضل للإشعارات إلى الساعة $hour بنجاح');
    } catch (e) {
      debugPrint('خطأ في تعيين الوقت المفضل للإشعارات: $e');
      rethrow;
    }
  }

  /// الحصول على الوقت المفضل للإشعارات
  Future<int> getPreferredNotificationTime() async {
    try {
      // الحصول على بيانات نشاط المستخدم
      final UserActivity activity = await _getUserActivity();
      return activity.preferredHour;
    } catch (e) {
      debugPrint('خطأ في الحصول على الوقت المفضل للإشعارات: $e');
      return 20; // القيمة الافتراضية هي الساعة 8 مساءً
    }
  }

  /// إرسال إشعار تأكيدي لتفعيل إشعارات العودة
  Future<void> sendReturnNotificationConfirmation() async {
    try {
      debugPrint('جاري إرسال إشعار تأكيدي لتفعيل إشعارات العودة...');

      // إلغاء أي إشعارات تأكيدية سابقة
      await AwesomeNotifications().cancel(9500);

      // إرسال إشعار تأكيدي
      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: 9500,
          channelKey: _returnChannelKey,
          title: 'تم تفعيل إشعارات العودة',
          body: 'سنذكرك بالعودة إلى المسبحة في حال عدم استخدامها لفترة طويلة ✨',
          notificationLayout: NotificationLayout.Default,
          color: TasbihColors.primary,
          category: NotificationCategory.Reminder,
        ),
      );

      debugPrint('تم إرسال إشعار تأكيدي لتفعيل إشعارات العودة بنجاح');
    } catch (e) {
      debugPrint('خطأ في إرسال إشعار تأكيدي لتفعيل إشعارات العودة: $e');
      rethrow;
    }
  }
}
 */