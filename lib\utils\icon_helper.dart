import 'package:flutter/material.dart';
import 'svg_icon_helper.dart';

class IconHelper {
  /// الحصول على أيقونة مناسبة لكل فئة
  /// يدعم كل من أيقونات SVG والأيقونات التقليدية
  static IconData getIconForCategory(String categoryName,
      {String? iconName, String? iconPath}) {
    // إذا كان هناك مسار SVG، لا نحتاج لإرجاع IconData
    // هذه الدالة محفوظة للتوافق مع النظام القديم
    if (iconName != null) {
      // تحويل اسم الأيقونة إلى أيقونة فعلية
      return _mapStringToIcon(iconName);
    }

    // التعرف على الأيقونة بناءً على اسم الفئة
    if (categoryName.contains('الصباح')) {
      return Icons.wb_sunny_outlined;
    } else if (categoryName.contains('المساء')) {
      return Icons.nights_stay_outlined;
    } else if (categoryName.contains('النوم')) {
      return Icons.hotel_outlined;
    } else if (categoryName.contains('المسجد')) {
      return Icons.account_balance_outlined;
    } else if (categoryName.contains('الصلاة')) {
      return Icons.mosque_outlined;
    } else if (categoryName.contains('الطعام')) {
      return Icons.restaurant_outlined;
    } else if (categoryName.contains('الفجر')) {
      return Icons.brightness_2_outlined;
    } else if (categoryName.contains('الظهر')) {
      return Icons.wb_sunny;
    } else if (categoryName.contains('العصر')) {
      return Icons.wb_twilight;
    } else if (categoryName.contains('المغرب')) {
      return Icons.nightlight;
    } else if (categoryName.contains('العشاء')) {
      return Icons.dark_mode_outlined;
    } else if (categoryName.contains('الركوع')) {
      return Icons.keyboard_arrow_down_outlined;
    } else if (categoryName.contains('السجود')) {
      return Icons.arrow_downward_outlined;
    }

    // أيقونة افتراضية
    return Icons.menu_book_outlined;
  }

  /// تحويل اسم الأيقونة إلى IconData
  static IconData _mapStringToIcon(String iconName) {
    // إذا كان الاسم يبدأ بـ 'e' فهو رمز مباشر من MaterialIcons
    if (iconName.startsWith('e')) {
      try {
        int codePoint = int.parse('0x${iconName.substring(1)}');
        return IconData(
          codePoint,
          fontFamily: 'MaterialIcons',
        );
      } catch (e) {
        // في حالة حدوث خطأ، استخدم الأيقونة الافتراضية
        return Icons.format_quote;
      }
    }

    // الأيقونات المعروفة
    switch (iconName) {
      case 'wb_sunny_outlined':
        return Icons.wb_sunny_outlined;
      case 'nights_stay_outlined':
        return Icons.nights_stay_outlined;
      case 'hotel_outlined':
        return Icons.hotel_outlined;
      case 'account_balance_outlined':
        return Icons.account_balance_outlined;
      case 'mosque_outlined':
        return Icons.mosque_outlined;
      case 'restaurant_outlined':
        return Icons.restaurant_outlined;
      case 'brightness_2_outlined':
        return Icons.brightness_2_outlined;
      case 'wb_sunny':
        return Icons.wb_sunny;
      case 'wb_twilight':
        return Icons.wb_twilight;
      case 'nightlight':
        return Icons.nightlight;
      case 'dark_mode_outlined':
        return Icons.dark_mode_outlined;
      case 'keyboard_arrow_down_outlined':
        return Icons.keyboard_arrow_down_outlined;
      case 'arrow_downward_outlined':
        return Icons.arrow_downward_outlined;
      // أيقونات قسم الأدعية
      case 'volunteer_activism':
        return Icons.volunteer_activism;
      case 'volunteer_activism_rounded':
        return Icons.volunteer_activism_rounded;
      case 'volunteer_activism_outlined':
        return Icons.volunteer_activism_outlined;
      case 'menu_book':
        return Icons.menu_book;
      case 'menu_book_rounded':
        return Icons.menu_book_rounded;
      case 'menu_book_outlined':
        return Icons.menu_book_outlined;
      // أيقونات قسم الصلاة على النبي
      case 'auto_awesome':
        return Icons.auto_awesome;
      case 'auto_awesome_rounded':
        return Icons.auto_awesome_rounded;
      case 'auto_awesome_outlined':
        return Icons.auto_awesome_outlined;
      case 'auto_awesome_motion':
        return Icons.auto_awesome_motion;
      case 'auto_awesome_motion_rounded':
        return Icons.auto_awesome_motion_rounded;
      case 'auto_awesome_motion_outlined':
        return Icons.auto_awesome_motion_outlined;
      // أيقونات إضافية
      case 'school':
        return Icons.school;
      case 'school_outlined':
        return Icons.school_outlined;
      case 'school_rounded':
        return Icons.school_rounded;
      case 'event':
        return Icons.event;
      case 'event_outlined':
        return Icons.event_outlined;
      case 'event_rounded':
        return Icons.event_rounded;
      default:
        return Icons.menu_book_outlined;
    }
  }

  /// الحصول على ويدجت أيقونة مناسبة للفئة (يدعم SVG والأيقونات التقليدية)
  static Widget getIconWidget({
    required String categoryName,
    String? iconPath,
    String? iconName,
    required double size,
    required Color color,
    String section = 'general',
  }) {
    return SvgIconHelper.getIconWidget(
      categoryName: categoryName,
      iconPath: iconPath,
      iconName: iconName,
      size: size,
      color: color,
    );
  }
}
