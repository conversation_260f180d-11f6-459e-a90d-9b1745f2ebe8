// نموذج الذكر
class DhikrModel {
  final int id;
  final String name;
  int count;
  final bool
      isDefault; // إضافة خاصية لتحديد ما إذا كان الذكر افتراضيًا أم مخصصًا
  final String arabicText; // نص الذكر بالعربية
  final String transliteration; // النطق اللاتيني للذكر
  final String translation; // ترجمة الذكر

  DhikrModel({
    required this.id,
    required this.name,
    required this.count,
    this.isDefault = false, // القيمة الافتراضية هي false (ذكر مخصص)
    this.arabicText = '', // القيمة الافتراضية هي نص فارغ
    this.transliteration = '', // القيمة الافتراضية هي نص فارغ
    this.translation = '', // القيمة الافتراضية هي نص فارغ
  });

  factory DhikrModel.fromMap(Map<String, dynamic> map) {
    return DhikrModel(
      id: map['id'] as int,
      name: map['name'] as String,
      count: map['count'] as int,
      isDefault: map['isDefault'] as bool? ?? false,
      arabicText: map['arabicText'] as String? ?? '',
      transliteration: map['transliteration'] as String? ?? '',
      translation: map['translation'] as String? ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'count': count,
      'isDefault': isDefault,
      'arabicText': arabicText,
      'transliteration': transliteration,
      'translation': translation,
    };
  }

  DhikrModel copyWith({
    int? id,
    String? name,
    int? count,
    bool? isDefault,
    String? arabicText,
    String? transliteration,
    String? translation,
  }) {
    return DhikrModel(
      id: id ?? this.id,
      name: name ?? this.name,
      count: count ?? this.count,
      isDefault: isDefault ?? this.isDefault,
      arabicText: arabicText ?? this.arabicText,
      transliteration: transliteration ?? this.transliteration,
      translation: translation ?? this.translation,
    );
  }
}
