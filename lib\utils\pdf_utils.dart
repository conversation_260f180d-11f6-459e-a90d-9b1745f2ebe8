import 'dart:io';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';

class PdfUtils {
  // استخراج PDF مضمن في الحزمة إلى مجلد مؤقت
  static Future<String> extractAssetPdf(String assetPath) async {
    try {
      final ByteData data = await rootBundle.load(assetPath);
      final List<int> bytes = data.buffer.asUint8List();
      
      final Directory tempDir = await getTemporaryDirectory();
      final String tempPath = tempDir.path;
      
      // استخدام اسم الملف الأصلي
      final String fileName = assetPath.split('/').last;
      final String filePath = '$tempPath/$fileName';
      
      final File file = File(filePath);
      await file.writeAsBytes(bytes);
      
      return filePath;
    } catch (e) {
      throw Exception('فشل استخراج ملف PDF: $e');
    }
  }
} 