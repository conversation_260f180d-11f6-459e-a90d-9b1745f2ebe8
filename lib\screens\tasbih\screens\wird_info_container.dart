import 'package:flutter/material.dart';
import '../models/dhikr_model.dart';
import '../utils/tasbih_colors.dart';

/// مكون لعرض معلومات الذكر بشكل احترافي
class WirdInfoContainer extends StatelessWidget {
  final DhikrModel dhikr;

  const WirdInfoContainer({Key? key, required this.dhikr}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            TasbihColors.primary.withAlpha(20),
            TasbihColors.primary.withAlpha(5),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.0),
        border: Border.all(color: TasbihColors.primary.withAlpha(50), width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(5),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // عنوان معلومات الذكر
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: TasbihColors.primary.withAlpha(30),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.info_outline, color: TasbihColors.primary, size: 16),
                SizedBox(width: 6),
                Text(
                  'معلومات الذكر',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: TasbihColors.primary,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // نص الذكر بالعربية
          if (dhikr.arabicText.isNotEmpty) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(5),
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Text(
                dhikr.arabicText,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],

          // تم إزالة قسم النطق والترجمة

          // عدد التكرار المقترح
          if (dhikr.count > 0) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: TasbihColors.primary.withAlpha(15),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: TasbihColors.primary.withAlpha(30)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.repeat,
                      color: TasbihColors.primary, size: 16),
                  const SizedBox(width: 6),
                  Text(
                    'العدد المقترح: ${dhikr.count}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: TasbihColors.primary,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
