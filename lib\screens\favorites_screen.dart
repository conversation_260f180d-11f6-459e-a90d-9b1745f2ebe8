import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'favorites/providers/favorites_provider.dart';
import 'favorites/favorites_screen.dart' as favorites_content;

/// شاشة المفضلة الرئيسية
///
/// تم تقسيم الشاشة إلى مكونات منفصلة لتسهيل التطوير والصيانة
class FavoritesScreenWrapper extends StatefulWidget {
  const FavoritesScreenWrapper({super.key});

  @override
  State<FavoritesScreenWrapper> createState() => _FavoritesScreenWrapperState();
}

class _FavoritesScreenWrapperState extends State<FavoritesScreenWrapper> {
  // إنشاء مزود المفضلة الذي يبقى طوال فترة حياة الويدجت
  final FavoritesProvider _favoritesProvider = FavoritesProvider();
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    // استخدام تأخير بسيط لتحميل المفضلة بعد اكتمال بناء الواجهة
    _initializeFavorites();
  }

  void _initializeFavorites() {
    if (!mounted) return;

    // تأخير بسيط لضمان اكتمال بناء الواجهة
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted && !_isInitialized) {
        debugPrint('Initializing favorites from wrapper');
        _favoritesProvider.loadFavorites();
        _isInitialized = true;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _favoritesProvider,
      child: const favorites_content.FavoritesScreen(),
    );
  }
}

// للتوافق مع الكود القديم
class FavoritesScreen extends StatelessWidget {
  const FavoritesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const FavoritesScreenWrapper();
  }
}
