// مدير التحقق من الأذونات - تم تحديثه لاستخدام نظام الإشعارات الجديد

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/notification_manager.dart'; // استخدام مدير الإشعارات الجديد
import '../screens/permissions/notification_permission_screen.dart';
// تم إزالة استيراد notification_permission_service.dart لأنه لم يعد مستخدماً

/// مدير للتحقق من الأذونات المختلفة وطلبها عند الحاجة
class PermissionManager {
  // نمط singleton للوصول لنفس النسخة من المدير في جميع أنحاء التطبيق
  static final PermissionManager _instance = PermissionManager._internal();

  factory PermissionManager() => _instance;

  PermissionManager._internal();

  // استخدام مدير الإشعارات الجديد
  final NotificationManager _notificationManager = NotificationManager();

  // مفتاح تخزين حالة طلب الأذونات
  static const String _permissionRequestedKey =
      'notification_permission_requested';

  // تم إزالة الخدمة القديمة واستبدالها بمدير الإشعارات الجديد
  // @Deprecated('استخدم _notificationManager بدلاً من ذلك')
  // final NotificationPermissionService _notificationPermissionService =
  //     NotificationPermissionService();

  /// التحقق من أذونات الإشعارات عند بدء التطبيق - تم تعليقه مؤقتاً
  /// يعرض شاشة طلب الأذونات إذا لم يتم طلبها من قبل
  Future<void> checkNotificationPermissionsOnStartup(
      BuildContext context) async {
    /*
    try {
      // التحقق مما إذا كان قد تم طلب الأذونات من قبل
      final prefs = await SharedPreferences.getInstance();
      final hasRequested = prefs.getBool(_permissionRequestedKey) ?? false;

      // إذا لم يتم طلب الأذونات من قبل، نطلب الأذونات مباشرة بدون عرض شاشة خاصة
      if (!hasRequested) {
        // طلب الأذونات مباشرة بدون استخدام Navigator
        await _notificationManager.requestNotificationPermissions();

        // تسجيل أنه تم طلب الأذونات
        await prefs.setBool(_permissionRequestedKey, true);
      } else {
        // إذا تم طلب الأذونات من قبل، التحقق من حالتها الحالية
        await _notificationManager.checkNotificationPermissions();
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من أذونات الإشعارات: $e');
    }
    */
  }

  /// التحقق من أذونات الإشعارات قبل جدولة الإشعارات
  /// يرجع true إذا كانت الأذونات ممنوحة، وfalse إذا كانت مرفوضة
  Future<bool> checkNotificationPermissionsBeforeScheduling(
      BuildContext context) async {
    // التحقق من حالة الأذونات الحالية
    final isAllowed = await _notificationManager.checkNotificationPermissions();

    // إذا كانت الأذونات ممنوحة، إرجاع true
    if (isAllowed) {
      return true;
    }

    // التحقق مما إذا كان قد تم طلب الأذونات من قبل
    final prefs = await SharedPreferences.getInstance();
    final hasRequested = prefs.getBool(_permissionRequestedKey) ?? false;

    // إذا لم يتم طلب الأذونات من قبل، عرض شاشة طلب الأذونات
    if (!hasRequested) {
      // التأكد من أن السياق لا يزال صالحاً
      if (!context.mounted) return false;

      // عرض شاشة طلب الأذونات
      return await _showPermissionScreen(context);
    } else {
      // إذا تم طلب الأذونات من قبل ولكنها مرفوضة، عرض حوار لفتح إعدادات التطبيق
      // التأكد من أن السياق لا يزال صالحاً
      if (!context.mounted) return false;

      return await _showPermissionDialog(context);
    }
  }

  /// عرض شاشة طلب الأذونات
  /// يرجع true إذا تم منح الأذونات، وfalse إذا تم رفضها
  Future<bool> _showPermissionScreen(BuildContext context) async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => NotificationPermissionScreen(
          onPermissionGranted: () {
            Navigator.pop(context, true);
          },
          onPermissionDenied: () {
            Navigator.pop(context, false);
          },
        ),
      ),
    );

    return result ?? false;
  }

  /// عرض حوار لفتح إعدادات التطبيق
  /// يرجع true إذا تم النقر على "فتح الإعدادات"، وfalse إذا تم النقر على "إلغاء"
  Future<bool> _showPermissionDialog(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الإشعارات غير مفعلة'),
        content: const Text(
          'لتلقي إشعارات الأذكار والأوراد، يرجى تفعيل الإشعارات من إعدادات التطبيق.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context, false);
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              await _notificationManager.requestNotificationPermissions();
              if (context.mounted) {
                Navigator.pop(context, true);
              }
            },
            child: const Text('فتح الإعدادات'),
          ),
        ],
      ),
    );

    return result ?? false;
  }
}
