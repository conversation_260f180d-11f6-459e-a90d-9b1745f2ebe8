// متحكم الشاشة الرئيسية

import 'package:flutter/material.dart';
import '../../../models/daily_wisdom.dart';

class HomeController extends ChangeNotifier {
  // حالة القسم الغير جاهز
  final Map<String, bool> _availableSections = {
    'azkar': true,
    'favorites': true,
    'tasbih': true,
    'duas': true, // قسم الأدعية
    'prophetPrayers': true, // قسم الصلاة على النبي
    'books': false, // غير جاهز
    'poems': false, // غير جاهز
  };

  // معلومات للعرض
  String _greeting = '';
  List<DailyWisdom> _wisdomQuotes = [];
  int _currentWisdomPage = 0;
  bool _isLoading = true;
  bool _showBooksSection = false;
  bool _showPoemsSection = false;

  // Getters
  Map<String, bool> get availableSections => _availableSections;
  String get greeting => _greeting;
  List<DailyWisdom> get wisdomQuotes => _wisdomQuotes;
  int get currentWisdomPage => _currentWisdomPage;
  bool get isLoading => _isLoading;
  bool get showBooksSection => _showBooksSection;
  bool get showPoemsSection => _showPoemsSection;

  // للتحقق مما إذا كان القسم متاحًا
  bool isSectionAvailable(String sectionKey) =>
      _availableSections[sectionKey] ?? false;

  // بنّاء
  HomeController() {
    _updateGreeting();
    _loadWisdomQuotes();
  }

  // تحميل البيانات الأولية
  // Esta función no se utiliza actualmente, pero se mantiene comentada por si se necesita en el futuro
  // Future<void> _loadData() async {
  //   _updateGreeting();
  //   _loadWisdomQuotes();
  // }

  // تحديث التحية بناءً على وقت اليوم
  void _updateGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 5) {
      _greeting = 'تصبح على خير';
    } else if (hour < 12) {
      _greeting = 'صباح الخير';
    } else if (hour < 17) {
      _greeting = 'مساء الخير';
    } else if (hour < 22) {
      _greeting = 'مساء النور';
    } else {
      _greeting = 'ليلة طيبة';
    }
    notifyListeners();
  }

  // تحميل حكم اليوم
  Future<void> _loadWisdomQuotes() async {
    _isLoading = true;
    notifyListeners();

    try {
      _wisdomQuotes = await DailyWisdom.getAllQuotes();
      _isLoading = false;
    } catch (e) {
      _isLoading = false;
      // يمكن هنا إضافة معالجة الأخطاء
    }

    notifyListeners();
  }

  // تحديث صفحة الحكمة الحالية
  void setCurrentWisdomPage(int page) {
    _currentWisdomPage = page;
    notifyListeners();
  }

  // تحديث حالة عرض الأقسام
  void updateSectionsVisibility({bool? showBooks, bool? showPoems}) {
    if (showBooks != null) _showBooksSection = showBooks;
    if (showPoems != null) _showPoemsSection = showPoems;
    notifyListeners();
  }

  // إعادة تحميل البيانات
  void refresh() {
    _loadWisdomQuotes();
    _updateGreeting();
  }
}
