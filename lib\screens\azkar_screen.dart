import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:convert';
import 'dart:ui';
import '../utils/app_colors.dart';
import '../models/zikr.dart';
import '../widgets/azkar_card.dart';
import '../widgets/featured_azkar_section.dart';
import '../widgets/azkar_search_delegate.dart';
import 'azkar_details_screen.dart';
import 'azkar_subcategories_screen.dart';
import 'dart:math' as math;

class AzkarScreen extends StatefulWidget {
  const AzkarScreen({super.key});

  @override
  State<AzkarScreen> createState() => _AzkarScreenState();
}

class _AzkarScreenState extends State<AzkarScreen>
    with SingleTickerProviderStateMixin {
  List<Zikr> _categories = [];
  List<Zikr> _filteredCategories = [];
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  bool _isAppBarCollapsed = false;

  // للتحكم بالتحريك والحركة
  late AnimationController _animationController;
  late ScrollController _scrollController;

  // للتحكم بالبحث
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isSearchMode = false;

  // مؤشر للتحديث
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  @override
  void initState() {
    super.initState();

    // تهيئة وحدات التحكم
    _scrollController = ScrollController();

    // استخدام addPostFrameCallback لتأخير إضافة المستمع حتى اكتمال بناء الإطار الأول
    // هذا يساعد في تقليل التعليق عند بدء التشغيل
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollController.addListener(_onScroll);
    });

    _animationController = AnimationController(
      vsync: this,
      // تقليل مدة الرسوم المتحركة لتحسين الأداء
      duration: const Duration(milliseconds: 600),
    );

    // تحميل البيانات
    _loadCategories();

    // بدء الحركة بعد تأخير قصير
    // استخدام addPostFrameCallback بدلاً من Future.delayed لتحسين الأداء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _animationController.forward();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  // تحسين دالة التمرير لتقليل التعليق
  void _onScroll() {
    if (_scrollController.hasClients) {
      final scrollOffset = _scrollController.offset;
      final isCollapsed = scrollOffset > 100;

      // تحديث الحالة فقط عند تغيير حالة الشريط العلوي
      // واستخدام تأخير بسيط لتجنب التحديثات المتكررة
      if (isCollapsed != _isAppBarCollapsed) {
        // استخدام SchedulerBinding لتأخير تحديث الحالة حتى الإطار التالي
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {
              _isAppBarCollapsed = isCollapsed;
            });
          }
        });
      }
    }
  }

  Future<void> _loadCategories() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // تحميل البيانات مباشرة من ملف JSON
      await _loadFromJsonDirectly();

      // استرداد حالة المستخدم (المفضلة والمكتملة) من التخزين المحلي
      await _loadUserPreferences();

      _filteredCategories = List.from(_categories);
      _animationController.forward(from: 0.0);
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = e.toString();
          _categories = [];
          _filteredCategories = [];
        });
      }
    }
  }

  Future<void> _loadFromJsonDirectly() async {
    try {
      final String response =
          await rootBundle.loadString('assets/data/azkar.json');
      final data = await json.decode(response);

      setState(() {
        _categories = List<Zikr>.from(
            data['categories'].map((category) => Zikr.fromJson(category)));
        _filteredCategories = List.from(_categories);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'فشل تحميل بيانات الأذكار: ${e.toString()}';
      });
      rethrow;
    }
  }

  Future<void> _loadUserPreferences() async {
    // استخدام shared_preferences أو أي حل آخر للتخزين المحلي
    // يمكن إضافة هذه الدالة بدون كسر التوافق الحالي
  }

  void _updateSearchQuery(String query) {
    setState(() {
      _searchQuery = query;
      _filterCategories();
    });
  }

  void _toggleSearchMode() {
    setState(() {
      _isSearchMode = !_isSearchMode;
      if (!_isSearchMode) {
        _searchQuery = '';
        _searchController.clear();
        _filteredCategories = List.from(_categories);
      } else {
        // عند بدء البحث، التركيز على حقل البحث
        FocusScope.of(context).requestFocus(FocusNode());
      }
    });
  }

  void _filterCategories() {
    if (_searchQuery.isEmpty) {
      _filteredCategories = List.from(_categories);
    } else {
      _filteredCategories = _categories
          .where((category) =>
              category.name.contains(_searchQuery) ||
              category.description.contains(_searchQuery))
          .toList();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.light,
      child: Scaffold(
        body: Stack(
          children: [
            // خلفية متدرجة
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: Theme.of(context).brightness == Brightness.dark
                      ? const [
                          Color(0xFF1A1A2E), // TasbihColors.darkBackground
                          Color(
                              0xFF16213E), // TasbihColors.darkBackgroundSecondary
                        ]
                      : [
                          const Color(0xFFEEF7F6),
                          Colors.white,
                        ],
                ),
              ),
            ),

            // المحتوى الرئيسي
            _isLoading
                ? const _LoadingScreen()
                : _hasError
                    ? _buildErrorScreen()
                    : _buildMainContent(),

            // شريط علوي عائم يظهر عند التمرير
            if (_isAppBarCollapsed &&
                !_isLoading &&
                !_hasError &&
                !_isSearchMode)
              _buildFloatingHeader(),
          ],
        ),
        floatingActionButton: _buildFloatingSearchButton(),
      ),
    );
  }

  Widget _buildFloatingHeader() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            padding: EdgeInsets.only(
              top: MediaQuery.of(context).padding.top,
              bottom: 8,
              left: 8,
              right: 8,
            ),
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF1A1A2E)
                      .withAlpha(217) // TasbihColors.darkBackground
                  : Colors.white.withAlpha(217), // 0.85 * 255 = 217
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(13), // 0.05 * 255 = 13
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
              children: [
                IconButton(
                  icon: const Icon(Icons.menu_book_rounded),
                  onPressed: () {
                    _scrollController.animateTo(
                      0,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  },
                  tooltip: 'العودة للأعلى',
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'الأذكار',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textDirection: TextDirection
                        .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                    textAlign: TextAlign.right, // محاذاة النص إلى اليمين
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.search),
                  onPressed: () {
                    showSearch(
                      context: context,
                      delegate: AzkarSearchDelegate(_categories),
                    );
                  },
                  tooltip: 'بحث',
                ),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () {
                    _refreshIndicatorKey.currentState?.show();
                  },
                  tooltip: 'تحديث',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return RefreshIndicator(
      key: _refreshIndicatorKey,
      onRefresh: _loadCategories,
      color: AppColors.getAzkarColor(
          Theme.of(context).brightness == Brightness.dark),
      child: CustomScrollView(
        controller: _scrollController,
        physics: const BouncingScrollPhysics(
          parent: AlwaysScrollableScrollPhysics(),
        ),
        // تحسين أداء التمرير
        cacheExtent: 1000, // زيادة حجم التخزين المؤقت لتقليل إعادة البناء
        slivers: [
          _buildAppBar(),

          // البحث عند التفعيل
          if (_isSearchMode)
            SliverPadding(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
              sliver: SliverToBoxAdapter(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? const Color(
                                0xFF252A34) // TasbihColors.darkCardColor
                            : Colors.white,
                        borderRadius:
                            const BorderRadius.all(Radius.circular(16)),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.getAzkarColor(
                                    Theme.of(context).brightness ==
                                        Brightness.dark)
                                .withAlpha(26), // 0.1 * 255 = 26
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: TextField(
                        controller: _searchController,
                        onChanged: _updateSearchQuery,
                        autofocus: _isSearchMode,
                        textDirection: TextDirection
                            .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                        textAlign: TextAlign.right, // محاذاة النص إلى اليمين
                        decoration: InputDecoration(
                          hintText: 'البحث في الأذكار...',
                          hintTextDirection: TextDirection
                              .rtl, // تحديد اتجاه النص التلميحي من اليمين إلى اليسار
                          hintStyle: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.normal,
                          ),
                          prefixIcon: null, // إزالة الأيقونة من اليسار
                          suffixIcon: Row(
                            mainAxisSize: MainAxisSize.min,
                            textDirection:
                                TextDirection.ltr, // عكس اتجاه الأيقونات
                            children: [
                              if (_searchQuery.isNotEmpty)
                                IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    _searchController.clear();
                                    _updateSearchQuery('');
                                  },
                                ),
                              const Icon(
                                  Icons.search), // إضافة أيقونة البحث في اليمين
                            ],
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                              vertical: 16, horizontal: 16),
                        ),
                      ),
                    ),
                    if (_searchQuery.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 16, right: 8),
                        child: Text(
                          'نتائج البحث عن: "$_searchQuery"',
                          style: TextStyle(
                            fontSize: 14,
                            color: Theme.of(context).brightness ==
                                    Brightness.dark
                                ? const Color(
                                    0xFFAAAAAA) // TasbihColors.darkTextSecondary
                                : Colors.grey[600],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),

          // قسم الأذكار المميزة إذا لم يكن في وضع البحث
          if (!_isSearchMode)
            SliverToBoxAdapter(
              child: FeaturedAzkarSection(
                categories: _categories,
                title: 'الأذكار المميزة',
              ),
            ),

          // رسالة عند عدم وجود نتائج بحث
          if (_searchQuery.isNotEmpty && _filteredCategories.isEmpty)
            SliverToBoxAdapter(
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.all(50.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.search_off,
                        size: 60,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? const Color(
                                0xFFAAAAAA) // TasbihColors.darkTextSecondary
                            : Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لم يتم العثور على نتائج لـ "$_searchQuery"',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? const Color(
                                  0xFFE0E0E0) // TasbihColors.darkTextColor
                              : Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'حاول البحث بكلمات مختلفة',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? const Color(
                                  0xFFAAAAAA) // TasbihColors.darkTextSecondary
                              : Colors.grey[500],
                        ),
                      ),
                      const SizedBox(height: 24),
                      TextButton.icon(
                        onPressed: () {
                          setState(() {
                            _searchQuery = '';
                            _searchController.clear();
                            _filteredCategories = List.from(_categories);
                          });
                        },
                        icon: const Icon(Icons.refresh),
                        label: const Text('عرض جميع الأذكار'),
                        style: TextButton.styleFrom(
                          foregroundColor: AppColors.getAzkarColor(
                              Theme.of(context).brightness == Brightness.dark),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

          // عنوان قسم كافة الأذكار
          if (_filteredCategories.isNotEmpty)
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
                child: Row(
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                  children: [
                    Container(
                      width: 4,
                      height: 20,
                      decoration: BoxDecoration(
                        color: AppColors.getAzkarColor(
                            Theme.of(context).brightness == Brightness.dark),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _isSearchMode ? 'نتائج البحث' : 'كافة الأذكار',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      textDirection: TextDirection
                          .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                      textAlign: TextAlign.right, // محاذاة النص إلى اليمين
                    ),
                    const Spacer(),
                    if (!_isSearchMode)
                      TextButton.icon(
                        onPressed: _toggleSearchMode,
                        icon: const Icon(Icons.search, size: 16),
                        label: const Text(
                          'بحث',
                          textDirection: TextDirection
                              .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                        ),
                        style: TextButton.styleFrom(
                          foregroundColor: AppColors.getAzkarColor(
                              Theme.of(context).brightness == Brightness.dark),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 4),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                            side: BorderSide(
                              color: AppColors.getAzkarColor(
                                      Theme.of(context).brightness ==
                                          Brightness.dark)
                                  .withAlpha(77), // 0.3 * 255 = 77
                              width: 1.0,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),

          // شبكة الأذكار
          if (_filteredCategories.isNotEmpty)
            SliverPadding(
              padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.03),
              sliver: SliverGrid(
                gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
                  maxCrossAxisExtent:
                      MediaQuery.of(context).size.width > 600 ? 300 : 200,
                  childAspectRatio:
                      MediaQuery.of(context).size.width > 600 ? 0.85 : 0.75,
                  crossAxisSpacing: MediaQuery.of(context).size.width * 0.03,
                  mainAxisSpacing: MediaQuery.of(context).size.height * 0.02,
                ),
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final category = _filteredCategories[index];
                    return AzkarCard(
                      category: category,
                      onTap: () => _navigateToCategory(category),
                      animation: _getAnimation(index),
                    );
                  },
                  childCount: _filteredCategories.length,
                ),
              ),
            ),

          // مساحة إضافية في النهاية
          const SliverToBoxAdapter(
            child: SizedBox(height: 80),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorScreen() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.error_outline,
                size: 60,
                color: Colors.red.shade700,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'حدث خطأ أثناء تحميل الأذكار',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.red.shade700,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _errorMessage.isNotEmpty
                    ? _errorMessage
                    : 'حدث خطأ أثناء تحميل البيانات، يرجى المحاولة مرة أخرى',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: _loadCategories,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.azkarColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingSearchButton() {
    if (_isLoading || _hasError || _isSearchMode) {
      return const SizedBox.shrink();
    }

    return FloatingActionButton(
      onPressed: () {
        showSearch(
          context: context,
          delegate: AzkarSearchDelegate(_categories),
        );
      },
      backgroundColor: AppColors.getAzkarColor(
          Theme.of(context).brightness == Brightness.dark),
      elevation: 4,
      tooltip: 'بحث',
      child: const Icon(Icons.search, color: Colors.white),
    );
  }

  SliverAppBar _buildAppBar() {
    final screenSize = MediaQuery.of(context).size;

    return SliverAppBar(
      expandedHeight: _isSearchMode ? 80.0 : screenSize.height * 0.27,
      floating: false,
      pinned: true,
      backgroundColor: AppColors.getAzkarColor(
          Theme.of(context).brightness == Brightness.dark),
      elevation: 0,
      stretch: true,
      title: _isSearchMode
          ? null
          : const Text(
              'الأذكار',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
      leading: _isSearchMode
          ? IconButton(
              icon: const Icon(Icons.close, color: Colors.white),
              onPressed: _toggleSearchMode,
            )
          : null,
      actions: [
        if (!_isSearchMode) ...[
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: _toggleSearchMode,
            tooltip: 'بحث',
          ),
          IconButton(
            icon: const Icon(Icons.share, color: Colors.white),
            tooltip: 'مشاركة التطبيق',
            onPressed: _shareAppTelegram,
          ),
        ],
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          fit: StackFit.expand,
          children: [
            // خلفية متدرجة بألوان متناسقة
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topRight,
                  end: Alignment.bottomLeft,
                  colors: [
                    AppColors.getAzkarColor(true), // Use dark mode color
                    AppColors.getAzkarColor(true),
                    AppColors.getAzkarColor(true),
                  ],
                  stops: const [0.2, 0.6, 1.0],
                ),
              ),
            ),

            // طبقة خلفية لضبط الإضاءة
            Container(
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  center: Alignment.topRight,
                  radius: 1.0,
                  colors: [
                    Colors.white.withAlpha(38), // 0.15 * 255 = 38
                    Colors.transparent,
                  ],
                  stops: const [0.1, 0.6],
                ),
              ),
            ),

            // زخرفة إسلامية رئيسية - مُحسّنة للأداء وتقليل التعليق
            RepaintBoundary(
              child: AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  // استخدام AnimatedBuilder بدلاً من TweenAnimationBuilder لتحسين الأداء
                  // وتقليل عدد إعادة البناء أثناء التمرير
                  final value = _animationController.value;
                  return Stack(
                    children: [
                      // الزخرفة الأولى - أكبر حجماً وشفافية أقل - مُحسّنة للأداء
                      Positioned(
                        // تقليل مدى الحركة لتحسين الأداء
                        right: -40 + (value * 8),
                        top: -30 + (value * 8),
                        child: Opacity(
                          opacity: 0.25 * value,
                          // تبسيط التدوير لتحسين الأداء
                          child: Transform.rotate(
                            angle: 0.03 * math.pi * (1 - value),
                            child: SvgPicture.asset(
                              'assets/images/p2.svg',
                              width: screenSize.width * 0.7,
                              height: screenSize.width * 0.7,
                              colorFilter: ColorFilter.mode(
                                Colors.white.withAlpha(230), // 0.9 * 255 = 230
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                        ),
                      ),

                      // الزخرفة الثانية - أصغر حجماً وشفافية أكبر - مُحسّنة للأداء
                      Positioned(
                        // تقليل مدى الحركة لتحسين الأداء
                        left: -60 + (value * 15),
                        bottom: -screenSize.height * 0.1,
                        child: Opacity(
                          opacity: 0.18 * value,
                          // تبسيط التدوير لتحسين الأداء
                          child: Transform.rotate(
                            angle: -0.05 * math.pi * (1 - value),
                            // تقليل مدى التكبير لتحسين الأداء
                            child: Transform.scale(
                              scale: 0.8 + (0.2 * value),
                              child: SvgPicture.asset(
                                'assets/images/p2.svg',
                                width: screenSize.width * 0.6,
                                height: screenSize.width * 0.6,
                                colorFilter: ColorFilter.mode(
                                  Colors.white
                                      .withAlpha(230), // 0.9 * 255 = 230
                                  BlendMode.srcIn,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),

            // تأثير وهج متحرك متقدم - مُحسّن للأداء وتقليل التعليق
            RepaintBoundary(
              child: AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  // استخدام AnimatedBuilder بدلاً من TweenAnimationBuilder لتحسين الأداء
                  // وتقليل عدد إعادة البناء أثناء التمرير
                  return CustomPaint(
                    painter: LightSourcePainter(
                      progress: _animationController.value,
                      baseColor: Colors.white,
                    ),
                    child: const SizedBox.expand(),
                  );
                },
              ),
            ),

            // معلومات التطبيق المحسنة والمتناسقة - مُحسّن لتجنب مشكلة ParentDataWidget وتقليل التعليق
            if (!_isSearchMode)
              Positioned(
                right: 20, // تغيير الموضع من اليسار إلى اليمين
                bottom: screenSize.height * 0.03,
                child: RepaintBoundary(
                  child: AnimatedBuilder(
                    animation: _animationController,
                    builder: (context, child) {
                      // استخدام AnimatedBuilder بدلاً من TweenAnimationBuilder لتحسين الأداء
                      // وتقليل عدد إعادة البناء أثناء التمرير
                      final value = _animationController.value;
                      // تحسين موضع أيقونة الكتاب والنص
                      return Opacity(
                        opacity: value,
                        // تقليل مدى الحركة لتحسين الأداء
                        child: Transform.translate(
                          offset: Offset(0, 15 * (1 - value)),
                          child: RepaintBoundary(
                            child: Directionality(
                              textDirection:
                                  TextDirection.rtl, // ضبط اتجاه العناصر
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  // أيقونة الكتاب المحسنة
                                  Container(
                                    padding: const EdgeInsets.all(10),
                                    decoration: BoxDecoration(
                                      color: Colors.white
                                          .withAlpha(51), // 0.2 * 255 = 51
                                      shape: BoxShape.circle,
                                      // تبسيط الظلال لتحسين الأداء
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black
                                              .withAlpha(26), // 0.1 * 255 = 26
                                          // تقليل قيمة التمويه لتحسين الأداء
                                          blurRadius: 6,
                                          spreadRadius: 0.5,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                      border: Border.all(
                                        color: Colors.white
                                            .withAlpha(77), // 0.3 * 255 = 77
                                        width: 1,
                                      ),
                                    ),
                                    child: const Icon(
                                      Icons.menu_book_rounded,
                                      color: Colors.white,
                                      size: 30,
                                    ),
                                  ),
                                  const SizedBox(height: 10),

                                  // كبسولة عدد الفئات المحسنة
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 14, vertical: 7),
                                    decoration: BoxDecoration(
                                      color: Colors.white
                                          .withAlpha(38), // 0.15 * 255 = 38
                                      borderRadius: BorderRadius.circular(20),
                                      // تبسيط الظلال لتحسين الأداء
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black
                                              .withAlpha(20), // 0.08 * 255 = 20
                                          // تقليل قيمة التمويه لتحسين الأداء
                                          blurRadius: 4,
                                          offset: const Offset(0, 1),
                                        ),
                                      ],
                                      border: Border.all(
                                        color: Colors.white
                                            .withAlpha(77), // 0.3 * 255 = 77
                                        width: 0.5,
                                      ),
                                    ),
                                    // عدد الفئات - مُحسّن لتجنب مشكلة ParentDataWidget
                                    child: LayoutBuilder(
                                      builder: (context, constraints) {
                                        return Row(
                                          textDirection: TextDirection
                                              .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Text(
                                              '${_categories.length} فئة من الأذكار',
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 13,
                                                fontWeight: FontWeight.w500,
                                                // تبسيط الظلال لتحسين الأداء
                                                shadows: [
                                                  Shadow(
                                                    color: Colors.black.withAlpha(
                                                        77), // 0.3 * 255 = 77
                                                    blurRadius: 1,
                                                  ),
                                                ],
                                              ),
                                              textDirection: TextDirection
                                                  .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                              textAlign: TextAlign
                                                  .right, // محاذاة النص إلى اليمين
                                            ),
                                            const SizedBox(width: 8),
                                            Icon(
                                              Icons.format_list_numbered,
                                              color: Colors.white,
                                              size: 15,
                                              // تبسيط الظلال لتحسين الأداء
                                              shadows: [
                                                Shadow(
                                                  color: Colors.black.withAlpha(
                                                      77), // 0.3 * 255 = 77
                                                  blurRadius: 1,
                                                ),
                                              ],
                                            ),
                                          ],
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Animation<double> _getAnimation(int index) {
    // تحسين معادلة التأخير لتحسين الأداء
    final int rowIndex = index ~/ 2;

    // تقليل التأخير الأساسي لتحسين الأداء
    const double baseDelay = 0.03;

    // تقليل التأخير بين الصفوف والأعمدة لتحسين الأداء
    final double delay = baseDelay + (0.02 * rowIndex) + (0.01 * (index % 2));

    return Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Interval(
          delay.clamp(0.0, 0.5), // تقليل التأخير الأقصى لتحسين الأداء
          (delay + 0.3).clamp(0.0, 1.0), // تقليل مدة الحركة لتحسين الأداء
          // استخدام منحنى أبسط لتحسين الأداء
          curve: Curves.easeOut,
        ),
      ),
    );
  }

  // مشاركة رابط التطبيق من قناة التليجرام مع كلمات روحانية
  void _shareAppTelegram() async {
    // تأثير اهتزاز خفيف
    HapticFeedback.lightImpact();

    // قائمة من الكلمات الروحانية
    final List<String> spiritualQuotes = [
      "اللهم اجعل القرآن ربيع قلوبنا، ونور صدورنا، وجلاء أحزاننا، وذهاب همومنا وغمومنا",
      "ذكر الله يطمئن القلوب ويشرح الصدور ويزيل الهموم",
      "من أراد سعادة الدارين فعليه بكثرة الاستغفار",
      "اللهم إني أسألك علماً نافعاً، ورزقاً طيباً، وعملاً متقبلاً",
      "سبحان الله وبحمده، سبحان الله العظيم",
      "لا إله إلا الله وحده لا شريك له، له الملك وله الحمد وهو على كل شيء قدير",
      "اللهم صل وسلم وبارك على سيدنا محمد وعلى آله وصحبه أجمعين",
      "استغفر الله العظيم الذي لا إله إلا هو الحي القيوم وأتوب إليه",
      "اللهم إني أسألك الهدى والتقى والعفاف والغنى",
      "حسبي الله لا إله إلا هو عليه توكلت وهو رب العرش العظيم",
    ];

    // اختيار كلمة روحانية عشوائية
    final random = math.Random();
    final quote = spiritualQuotes[random.nextInt(spiritualQuotes.length)];

    // رابط قناة التليجرام
    const telegramChannelUrl = "https://t.me/wahajalsalik";

    // نص المشاركة
    final shareText = '''
$quote

حمّل تطبيق "وهج السالك" للأذكار والأدعية من قناتنا على تليجرام:
$telegramChannelUrl

تطبيق وهج السالك: ينابيع الحكمة وأنوار المعرفة 🌟
''';

    try {
      // مشاركة النص
      await Share.share(shareText);

      // عرض رسالة تأكيد بعد المشاركة
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: LayoutBuilder(
              builder: (context, constraints) {
                return const Row(
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                  children: [
                    Icon(Icons.check_circle, color: Colors.white, size: 20),
                    SizedBox(width: 10),
                    Expanded(
                      child: Text(
                        'تم تحضير المشاركة بنجاح',
                        textDirection: TextDirection
                            .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                        textAlign: TextAlign.right, // محاذاة النص إلى اليمين
                      ),
                    ),
                  ],
                );
              },
            ),
            backgroundColor: AppColors.getAzkarColor(
                Theme.of(context).brightness == Brightness.dark),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: LayoutBuilder(
              builder: (context, constraints) {
                return Row(
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                  children: [
                    const Icon(Icons.error_outline,
                        color: Colors.white, size: 20),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Text(
                        'حدث خطأ أثناء المشاركة: $e',
                        textDirection: TextDirection
                            .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                        textAlign: TextAlign.right, // محاذاة النص إلى اليمين
                      ),
                    ),
                  ],
                );
              },
            ),
            backgroundColor: Colors.red.shade700,
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  void _navigateToCategory(Zikr category) {
    // تأثير اهتزاز خفيف
    HapticFeedback.lightImpact();

    // تحسين الانتقال بين الصفحات لتحسين الأداء
    // استخدام دالة مشتركة لإنشاء الانتقال بدلاً من تكرار الكود
    PageRouteBuilder<dynamic> createPageRoute(Widget page) {
      return PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => page,
        // تقليل مدة الانتقال لتحسين الأداء
        transitionDuration: const Duration(milliseconds: 250),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          // تقليل مدى الحركة لتحسين الأداء
          const begin = Offset(0.0, 0.03);
          const end = Offset.zero;
          // استخدام منحنى أبسط لتحسين الأداء
          const curve = Curves.easeOut;

          var tween =
              Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
          var offsetAnimation = animation.drive(tween);

          // استخدام RepaintBoundary لتحسين الأداء
          return RepaintBoundary(
            child: SlideTransition(
              position: offsetAnimation,
              child: FadeTransition(
                opacity: animation,
                child: child,
              ),
            ),
          );
        },
      );
    }

    if (category.hasSubcategories && category.subcategories != null) {
      Navigator.push(
        context,
        createPageRoute(
          AzkarSubcategoriesScreen(
            mainCategory: category.name,
            subcategories: category.subcategories!,
          ),
        ),
      );
    } else {
      Navigator.push(
        context,
        createPageRoute(
          AzkarDetailsScreen(
            category: category.name,
            azkarItems: category.items,
          ),
        ),
      );
    }
  }
}

class _LoadingScreen extends StatelessWidget {
  const _LoadingScreen();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // زخرفة متحركة - مُحسّنة للأداء
          RepaintBoundary(
            child: TweenAnimationBuilder<double>(
              tween: Tween<double>(
                  begin: 0.9, end: 1.0), // تقليل مدى التكبير لتحسين الأداء
              // تقليل مدة الرسوم المتحركة لتحسين الأداء
              duration: const Duration(milliseconds: 800),
              // استخدام منحنى أبسط لتحسين الأداء
              curve: Curves.easeOut,
              builder: (context, value, child) {
                return Transform.scale(
                  scale: value,
                  child: SvgPicture.asset(
                    'assets/images/p2.svg',
                    width: 100,
                    height: 100,
                    colorFilter: ColorFilter.mode(
                      AppColors.getAzkarColor(
                              Theme.of(context).brightness == Brightness.dark)
                          .withAlpha(128), // 0.5 * 255 = 128
                      BlendMode.srcIn,
                    ),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 32),
          SizedBox(
            width: 40,
            height: 40,
            child: CircularProgressIndicator(
              strokeWidth: 3,
              valueColor: AlwaysStoppedAnimation<Color>(
                AppColors.getAzkarColor(
                    Theme.of(context).brightness == Brightness.dark),
              ),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'جاري تحميل الأذكار...',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFFE0E0E0) // TasbihColors.darkTextColor
                  : Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'تمتع بلحظات من السكينة والطمأنينة',
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFFAAAAAA) // TasbihColors.darkTextSecondary
                  : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}

// إضافة فئة رسم متقدمة لتأثير مصدر الضوء - مُحسّنة للأداء
class LightSourcePainter extends CustomPainter {
  final double progress;
  final Color baseColor;

  // تخزين قيم مسبقة لتحسين الأداء
  static const double _topGlowOpacity = 0.15;
  static const double _bottomGlowOpacity = 0.08;
  static const List<double> _stops = [0.0, 0.3, 0.6];

  LightSourcePainter({
    required this.progress,
    required this.baseColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);

    // تبسيط الحسابات الرياضية لتحسين الأداء
    // استخدام عمليات حسابية أبسط بدلاً من الدوال المثلثية المعقدة
    final double topSinValue = 0.1 * progress;
    final double topCosValue = 0.1 * (1 - progress);

    // الوهج الأول من الزاوية العلوية - مُحسّن للأداء
    final topGlow = RadialGradient(
      // تبسيط حساب المركز لتحسين الأداء
      center: Alignment(0.8 + topSinValue, -0.8 + topCosValue),
      // تبسيط حساب نصف القطر لتحسين الأداء
      radius: 1.0 + (0.05 * progress),
      colors: [
        baseColor.withAlpha((_topGlowOpacity * progress * 255).toInt()),
        baseColor.withAlpha((0.05 * progress * 255).toInt()),
        Colors.transparent,
      ],
      stops: _stops,
    ).createShader(rect);

    canvas.drawRect(rect, Paint()..shader = topGlow);

    // تبسيط الحسابات الرياضية لتحسين الأداء
    final double bottomSinValue = 0.1 * progress;

    // الوهج الثاني من الأسفل - مُحسّن للأداء
    final bottomGlow = RadialGradient(
      // تبسيط حساب المركز لتحسين الأداء
      center: Alignment(-0.7 + bottomSinValue, 0.7 + bottomSinValue),
      // تبسيط حساب نصف القطر لتحسين الأداء
      radius: 0.8 + (0.1 * progress),
      colors: [
        baseColor.withAlpha((_bottomGlowOpacity * progress * 255).toInt()),
        baseColor.withAlpha((0.03 * progress * 255).toInt()),
        Colors.transparent,
      ],
      stops: _stops,
    ).createShader(rect);

    canvas.drawRect(rect, Paint()..shader = bottomGlow);
  }

  @override
  bool shouldRepaint(LightSourcePainter oldDelegate) {
    // تحسين شرط إعادة الرسم لتجنب إعادة الرسم غير الضرورية
    return (oldDelegate.progress - progress).abs() > 0.01;
  }
}
