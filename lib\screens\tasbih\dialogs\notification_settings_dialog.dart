// مربع حوار إعدادات الإشعارات

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/tasbih_provider.dart';
import '../utils/tasbih_colors.dart';

/// عرض مربع حوار إعدادات الإشعارات
Future<void> showNotificationSettingsDialog(BuildContext context) async {
  // الحصول على مزود المسبحة من السياق الحالي
  final provider = Provider.of<TasbihProvider>(context, listen: false);

  return showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => NotificationSettingsDialog(provider: provider),
  );
}

/// مربع حوار إعدادات الإشعارات
class NotificationSettingsDialog extends StatefulWidget {
  final TasbihProvider provider;

  const NotificationSettingsDialog({
    Key? key,
    required this.provider,
  }) : super(key: key);

  @override
  State<NotificationSettingsDialog> createState() =>
      _NotificationSettingsDialogState();
}

class _NotificationSettingsDialogState
    extends State<NotificationSettingsDialog> {
  bool _notificationsEnabled = true;
  int _preferredHour = 19; // الافتراضي: 7 مساءً
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  /// تحميل إعدادات الإشعارات
  Future<void> _loadSettings() async {
    try {
      // استخدام مزود المسبحة من الويدجيت
      final provider = widget.provider;

      final notificationsEnabled =
          await provider.getReturnNotificationsEnabled();
      final preferredHour = await provider.getPreferredNotificationTime();

      if (mounted) {
        setState(() {
          _notificationsEnabled = notificationsEnabled;
          _preferredHour = preferredHour;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل إعدادات الإشعارات: $e');

      // التأكد من أن الويدجيت لا يزال مرفقاً بالشجرة
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // إظهار رسالة خطأ
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء تحميل الإعدادات'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// تحديث إعدادات الإشعارات
  Future<void> _updateSettings() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // استخدام مزود المسبحة من الويدجيت
      final provider = widget.provider;

      // التحقق من أذونات الإشعارات أولاً إذا كان المستخدم يريد تفعيلها
      if (_notificationsEnabled) {
        final hasPermission =
            await provider.checkNotificationPermissions(context);
        if (!hasPermission) {
          // إذا لم يتم منح الأذونات، نعرض رسالة للمستخدم
          if (mounted) {
            setState(() {
              _isLoading = false;
            });

            // عرض رسالة تحذير
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content:
                    const Text('يجب منح أذونات الإشعارات لتلقي إشعارات العودة'),
                backgroundColor: Colors.orange,
                action: SnackBarAction(
                  label: 'طلب الأذونات',
                  textColor: Colors.white,
                  onPressed: () async {
                    // محاولة طلب الأذونات مرة أخرى
                    await provider.requestNotificationPermissions();
                  },
                ),
              ),
            );

            return;
          }
        }
      }

      // تحديث الإعدادات
      await provider.toggleReturnNotifications(_notificationsEnabled);
      await provider.setPreferredNotificationTime(_preferredHour);

      // جدولة الإشعارات إذا تم تفعيلها
      bool notificationScheduled = true;
      if (_notificationsEnabled) {
        try {
          // إرسال إشعار تأكيدي منفصل للتأكد من ظهوره
          await provider.sendReturnNotificationConfirmation();

          // جدولة الإشعارات المستقبلية
          if (mounted) {
            await provider.scheduleReturnNotifications(context: context);
          }
        } catch (notificationError) {
          debugPrint('خطأ في جدولة الإشعارات: $notificationError');
          notificationScheduled = false;
        }
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // إظهار رسالة نجاح أو تحذير
        if (_notificationsEnabled && !notificationScheduled) {
          // إظهار رسالة تحذير إذا فشلت جدولة الإشعارات
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                  'تم حفظ الإعدادات ولكن قد تكون هناك مشكلة في جدولة الإشعارات'),
              backgroundColor: Colors.orange,
              action: SnackBarAction(
                label: 'إعادة المحاولة',
                textColor: Colors.white,
                onPressed: () {
                  // إعادة محاولة جدولة الإشعارات
                  if (mounted) {
                    provider.scheduleReturnNotifications(context: context);
                  }
                },
              ),
            ),
          );
        } else {
          // إظهار رسالة نجاح
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حفظ إعدادات الإشعارات'),
              backgroundColor: TasbihColors.primary,
            ),
          );
        }

        Navigator.pop(context);
      }
    } catch (e) {
      debugPrint('خطأ في تحديث إعدادات الإشعارات: $e');

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // إظهار رسالة خطأ
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('حدث خطأ أثناء حفظ الإعدادات'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: _updateSettings,
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      decoration: BoxDecoration(
        color: isDarkMode ? TasbihColors.darkCardColor : Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: _isLoading
          ? const Center(
              child: Padding(
                padding: EdgeInsets.all(32.0),
                child: CircularProgressIndicator(),
              ),
            )
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // مقبض السحب
                    Center(
                      child: Container(
                        height: 4,
                        width: 40,
                        margin: const EdgeInsets.only(bottom: 20),
                        decoration: BoxDecoration(
                          color: Colors.grey[400],
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),

                    // العنوان
                    Text(
                      'إعدادات إشعارات العودة',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: TasbihColors.primary,
                      ),
                    ),

                    const SizedBox(height: 8),

                    // الوصف
                    Text(
                      'تذكيرات للعودة إلى المسبحة بعد فترة من عدم الاستخدام',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: isDarkMode ? Colors.white70 : Colors.black54,
                      ),
                    ),

                    const SizedBox(height: 24),

                    // تفعيل الإشعارات
                    SwitchListTile(
                      title: const Text('تفعيل إشعارات العودة'),
                      subtitle: const Text(
                          'تلقي تذكيرات للعودة إلى المسبحة بعد فترة من عدم الاستخدام'),
                      value: _notificationsEnabled,
                      onChanged: (value) {
                        setState(() {
                          _notificationsEnabled = value;
                        });
                      },
                      activeColor: TasbihColors.primary,
                      contentPadding: EdgeInsets.zero,
                    ),

                    const SizedBox(height: 16),

                    // الوقت المفضل للإشعارات
                    if (_notificationsEnabled) ...[
                      Text(
                        'الوقت المفضل للإشعارات',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),

                      const SizedBox(height: 8),

                      Text(
                        'اختر الوقت الذي تفضل تلقي الإشعارات فيه',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: isDarkMode ? Colors.white70 : Colors.black54,
                        ),
                      ),

                      const SizedBox(height: 16),

                      // اختيار الوقت
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color:
                              isDarkMode ? Colors.grey[800] : Colors.grey[100],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: DropdownButton<int>(
                          value: _preferredHour,
                          isExpanded: true,
                          underline: const SizedBox(),
                          dropdownColor:
                              isDarkMode ? Colors.grey[800] : Colors.grey[100],
                          items: [
                            for (int hour = 0; hour < 24; hour++)
                              DropdownMenuItem(
                                value: hour,
                                child: Text(_formatHour(hour)),
                              ),
                          ],
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _preferredHour = value;
                              });
                            }
                          },
                        ),
                      ),
                    ],

                    const SizedBox(height: 32),

                    // أزرار الإجراءات
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => Navigator.pop(context),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              side:
                                  const BorderSide(color: TasbihColors.primary),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text('إلغاء'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _updateSettings,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: TasbihColors.primary,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text('حفظ'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  /// تنسيق الساعة
  String _formatHour(int hour) {
    final period = hour < 12 ? 'صباحاً' : 'مساءً';
    final displayHour = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    return '$displayHour $period';
  }
}
