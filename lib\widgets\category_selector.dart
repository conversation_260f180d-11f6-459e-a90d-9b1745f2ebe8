import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/app_colors.dart';

class CategorySelector extends StatefulWidget {
  final List<String> categories;
  final String? selectedCategory;
  final ValueChanged<String> onCategorySelected;
  final String allCategoriesLabel;

  const CategorySelector({
    Key? key,
    required this.categories,
    required this.selectedCategory,
    required this.onCategorySelected,
    this.allCategoriesLabel = 'الكل',
  }) : super(key: key);

  @override
  State<CategorySelector> createState() => _CategorySelectorState();
}

class _CategorySelectorState extends State<CategorySelector>
    with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  late AnimationController _animationController;
  final GlobalKey _allCategoriesKey = GlobalKey();

  // للتحكم في فترة الظهور الأولي
  bool _isInitialRender = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // شغل التحريك بعد بناء الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _animationController.forward();
      _scrollToSelectedCategory();

      // تعيين الحالة الأولية بعد التحميل
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          setState(() {
            _isInitialRender = false;
          });
        }
      });
    });
  }

  @override
  void didUpdateWidget(CategorySelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedCategory != widget.selectedCategory) {
      // تمرير لتأخير التمرير حتى يتم بناء القائمة
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToSelectedCategory();
      });
    }
  }

  void _scrollToSelectedCategory() {
    if (widget.selectedCategory == null) {
      // تمرير إلى "الكل"
      final RenderBox? renderBox =
          _allCategoriesKey.currentContext?.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        final position = renderBox.localToGlobal(Offset.zero).dx;
        _scrollController.animateTo(
          position - 16, // تعديل للهوامش
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOutCubic,
        );
      }
      return;
    }

    final index = widget.categories.indexOf(widget.selectedCategory!);
    if (index >= 0) {
      final estimatedPosition = index * 110.0; // تقدير متوسط عرض العنصر
      _scrollController.animateTo(
        estimatedPosition,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOutCubic,
      );
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 * 255 = 12.75 ≈ 13
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(0, (1 - _animationController.value) * 20),
            child: Opacity(
              opacity: _animationController.value,
              child: child,
            ),
          );
        },
        child: ListView(
          controller: _scrollController,
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          physics: const BouncingScrollPhysics(),
          children: [
            // زر "الكل"
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
              child: _CategoryButton(
                key: _allCategoriesKey,
                label: widget.allCategoriesLabel,
                icon: Icons.apps_rounded,
                isSelected: widget.selectedCategory == null,
                isInitialRender: _isInitialRender,
                animationIndex: 0,
                onTap: () {
                  HapticFeedback.lightImpact();
                  widget.onCategorySelected('');
                },
              ),
            ),

            // أزرار الفئات
            ...List.generate(
              widget.categories.length,
              (index) {
                final category = widget.categories[index];
                return Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                  child: _CategoryButton(
                    label: category,
                    icon: _getCategoryIcon(category),
                    isSelected: category == widget.selectedCategory,
                    isInitialRender: _isInitialRender,
                    animationIndex: index + 1,
                    onTap: () {
                      HapticFeedback.lightImpact();
                      widget.onCategorySelected(category);
                    },
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'رواية':
        return Icons.auto_stories;
      case 'ديني':
        return Icons.mosque;
      case 'تاريخ':
        return Icons.history_edu;
      case 'فلسفة':
        return Icons.psychology;
      case 'علمي':
        return Icons.science;
      case 'أدب':
        return Icons.menu_book;
      default:
        return Icons.category;
    }
  }
}

class _CategoryButton extends StatefulWidget {
  final String label;
  final IconData icon;
  final bool isSelected;
  final bool isInitialRender;
  final int animationIndex;
  final VoidCallback onTap;

  const _CategoryButton({
    Key? key,
    required this.label,
    required this.icon,
    required this.isSelected,
    required this.isInitialRender,
    required this.animationIndex,
    required this.onTap,
  }) : super(key: key);

  @override
  State<_CategoryButton> createState() => _CategoryButtonState();
}

class _CategoryButtonState extends State<_CategoryButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _pressController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _pressController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(
        parent: _pressController,
        curve: Curves.easeOutCubic,
      ),
    );
  }

  @override
  void dispose() {
    _pressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final Color categoryColor = AppColors.getCategoryColor(widget.label);

    // تعديل: استخدام حل بديل لإنشاء التأخير
    final Widget animatedButton = GestureDetector(
      onTapDown: (_) {
        setState(() {
          _isPressed = true;
        });
        _pressController.forward();
      },
      onTapUp: (_) {
        setState(() {
          _isPressed = false;
        });
        _pressController.reverse();
      },
      onTapCancel: () {
        setState(() {
          _isPressed = false;
        });
        _pressController.reverse();
      },
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: _pressController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: child,
          );
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOutCubic,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: widget.isSelected
                ? categoryColor
                : _isPressed
                    ? categoryColor.withAlpha(26) // 0.1 * 255 = 25.5 ≈ 26
                    : Colors.grey[100],
            borderRadius: BorderRadius.circular(30),
            boxShadow: widget.isSelected
                ? [
                    BoxShadow(
                      color:
                          categoryColor.withAlpha(77), // 0.3 * 255 = 76.5 ≈ 77
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                      spreadRadius: 0,
                    )
                  ]
                : null,
            border: Border.all(
              color: widget.isSelected
                  ? categoryColor
                  : Colors.grey.withAlpha(77), // 0.3 * 255 = 76.5 ≈ 77
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                widget.icon,
                size: 16,
                color: widget.isSelected ? Colors.white : categoryColor,
              ),
              const SizedBox(width: 8),
              Text(
                widget.label,
                style: TextStyle(
                  color: widget.isSelected ? Colors.white : Colors.black87,
                  fontWeight:
                      widget.isSelected ? FontWeight.bold : FontWeight.w500,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ),
    );

    if (!widget.isInitialRender) {
      return TweenAnimationBuilder<double>(
        tween: Tween<double>(begin: 0.0, end: 1.0),
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOutCubic,
        builder: (context, value, child) {
          return Transform.translate(
            offset: Offset(0, 20 * (1 - value)),
            child: Opacity(
              opacity: value,
              child: child,
            ),
          );
        },
        child: animatedButton,
      );
    }

    // تحكم في تأخير الظهور الأولي
    return AnimatedOpacity(
      opacity: 1.0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOut,
      onEnd: () {
        // يمكن أن تضيف منطق بعد اكتمال الرسوم المتحركة هنا
      },
      child: animatedButton,
    );
  }
}
