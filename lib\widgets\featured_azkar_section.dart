import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/zikr.dart';
import '../utils/app_colors.dart';
import '../utils/icon_helper.dart';
import '../screens/azkar_details_screen.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../screens/tasbih/utils/tasbih_colors.dart';

class FeaturedAzkarSection extends StatefulWidget {
  final List<Zikr> categories;
  final String title;

  const FeaturedAzkarSection({
    super.key,
    required this.categories,
    this.title = 'الأذكار المميزة',
  });

  @override
  State<FeaturedAzkarSection> createState() => _FeaturedAzkarSectionState();
}

/// معلومات الذكر المميز
class _FeaturedZikrInfo {
  final String categoryId;
  final String categoryName;
  final ZikrItem zikr;

  _FeaturedZikrInfo({
    required this.categoryId,
    required this.categoryName,
    required this.zikr,
  });
}

class _FeaturedAzkarSectionState extends State<FeaturedAzkarSection>
    with SingleTickerProviderStateMixin {
  late final PageController _pageController;
  late final AnimationController _animController;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(
      viewportFraction: 0.85,
      initialPage: 0,
    );

    _animController = AnimationController(
      vsync: this,
      // تقليل مدة الرسوم المتحركة لتحسين الأداء
      duration: const Duration(milliseconds: 300),
    );

    _pageController.addListener(() {
      int next = _pageController.page!.round();
      if (_currentPage != next) {
        setState(() {
          _currentPage = next;
        });
      }
    });

    _animController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animController.dispose();
    super.dispose();
  }

  /// الحصول على أيقونة مناسبة للذكر
  IconData _getIconForZikr(Zikr category) {
    // إذا كانت الفئة تحتوي على أذكار مميزة، نتحقق من أيقونات الأذكار الفردية
    if (category.items != null && category.items!.isNotEmpty) {
      // نبحث عن أول ذكر له أيقونة مخصصة
      for (final item in category.items!) {
        if (item.iconName != null) {
          return IconHelper.getIconForCategory('', iconName: item.iconName);
        }
      }
    }

    // إذا لم نجد أيقونة مخصصة للأذكار، نستخدم أيقونة الفئة
    return IconHelper.getIconForCategory(
      category.name,
      iconName: category.iconName,
    );
  }

  /// استخراج الأذكار المميزة من جميع الفئات
  List<_FeaturedZikrInfo> _extractFeaturedZikrs(List<Zikr> categories) {
    final List<_FeaturedZikrInfo> featuredZikrs = [];

    // تسجيل عدد الفئات للتشخيص
    debugPrint('عدد الفئات الكلي: ${categories.length}');
    int featuredCategoriesCount = 0;
    int featuredSubcategoriesCount = 0;
    int featuredItemsCount = 0;

    for (final category in categories) {
      // تسجيل ما إذا كانت الفئة مميزة
      if (category.isFeatured) {
        featuredCategoriesCount++;
        debugPrint('فئة مميزة: ${category.name} (${category.id})');
      }

      // إذا كان القسم مميز، نضيف جميع الأذكار فيه كأذكار مميزة
      if (category.isFeatured && category.items != null) {
        for (final item in category.items!) {
          featuredZikrs.add(
            _FeaturedZikrInfo(
              categoryId: category.id,
              categoryName: category.name,
              zikr: item,
            ),
          );
        }
      }
      // إذا لم يكن القسم مميز، نبحث عن الأذكار المميزة فقط
      else if (category.items != null) {
        for (final item in category.items!) {
          if (item.isFeatured) {
            featuredItemsCount++;
            debugPrint('ذكر مميز: ${item.id} في فئة ${category.name}');
            featuredZikrs.add(
              _FeaturedZikrInfo(
                categoryId: category.id,
                categoryName: category.name,
                zikr: item,
              ),
            );
          }
        }
      }

      // التعامل مع الأقسام الفرعية
      if (category.subcategories != null) {
        for (final subcategory in category.subcategories!) {
          // تسجيل ما إذا كانت الفئة الفرعية مميزة
          if (subcategory.isFeatured) {
            featuredSubcategoriesCount++;
            debugPrint(
                'فئة فرعية مميزة: ${subcategory.name} (${subcategory.id}) في فئة ${category.name}');
          }

          // إذا كان القسم الفرعي مميز، نضيف جميع الأذكار فيه كأذكار مميزة
          if (subcategory.isFeatured && subcategory.items != null) {
            for (final item in subcategory.items!) {
              featuredZikrs.add(
                _FeaturedZikrInfo(
                  categoryId: subcategory.id,
                  categoryName: '${category.name} - ${subcategory.name}',
                  zikr: item,
                ),
              );
            }
          }
          // إذا لم يكن القسم الفرعي مميز، نبحث عن الأذكار المميزة فقط
          else if (subcategory.items != null) {
            for (final item in subcategory.items!) {
              if (item.isFeatured) {
                featuredItemsCount++;
                debugPrint(
                    'ذكر مميز: ${item.id} في فئة فرعية ${subcategory.name}');
                featuredZikrs.add(
                  _FeaturedZikrInfo(
                    categoryId: subcategory.id,
                    categoryName: '${category.name} - ${subcategory.name}',
                    zikr: item,
                  ),
                );
              }
            }
          }
        }
      }
    }

    // تسجيل إحصائيات الأذكار المميزة
    debugPrint('إحصائيات الأذكار المميزة:');
    debugPrint('- عدد الفئات المميزة: $featuredCategoriesCount');
    debugPrint('- عدد الفئات الفرعية المميزة: $featuredSubcategoriesCount');
    debugPrint('- عدد الأذكار الفردية المميزة: $featuredItemsCount');
    debugPrint('- إجمالي الأذكار المميزة المستخرجة: ${featuredZikrs.length}');

    return featuredZikrs;
  }

  /// تحويل قائمة الأذكار المميزة إلى فئات للعرض
  List<Zikr> _convertFeaturedZikrsToCategories(
      List<_FeaturedZikrInfo> featuredZikrs) {
    // التحقق من وجود أذكار مميزة
    if (featuredZikrs.isEmpty) {
      debugPrint('لا توجد أذكار مميزة للتحويل');
      return [];
    }

    debugPrint('بدء تحويل ${featuredZikrs.length} ذكر مميز إلى فئات');

    // تجميع الأذكار المميزة حسب الفئة
    final Map<String, List<_FeaturedZikrInfo>> groupedZikrs = {};

    for (final featuredZikr in featuredZikrs) {
      if (featuredZikr.categoryName.isEmpty ||
          featuredZikr.categoryId.isEmpty) {
        debugPrint(
            'تم تخطي ذكر مميز بسبب بيانات غير صالحة: ${featuredZikr.zikr.id}');
        continue;
      }

      if (!groupedZikrs.containsKey(featuredZikr.categoryName)) {
        groupedZikrs[featuredZikr.categoryName] = [];
      }
      groupedZikrs[featuredZikr.categoryName]!.add(featuredZikr);
    }

    // التحقق من وجود مجموعات بعد التصفية
    if (groupedZikrs.isEmpty) {
      debugPrint('لا توجد مجموعات أذكار مميزة صالحة بعد التصفية');
      return [];
    }

    debugPrint('تم تجميع الأذكار المميزة في ${groupedZikrs.length} مجموعة');

    // تحويل المجموعات إلى فئات
    final List<Zikr> featuredCategories = [];

    groupedZikrs.forEach((categoryName, zikrs) {
      // التحقق من وجود أذكار في المجموعة
      if (zikrs.isEmpty) {
        debugPrint('تم تخطي مجموعة فارغة: $categoryName');
        return;
      }

      try {
        // إنشاء قائمة من الأذكار المميزة في هذه الفئة
        final List<ZikrItem> items = zikrs.map((z) => z.zikr).toList();

        // التحقق من وجود معرف للفئة
        final String categoryId = zikrs.first.categoryId.isNotEmpty
            ? zikrs.first.categoryId
            : 'unknown_${categoryName.hashCode}';

        debugPrint('إنشاء فئة مميزة: $categoryName مع ${items.length} ذكر');

        // إنشاء فئة جديدة تحتوي على الأذكار المميزة فقط
        featuredCategories.add(
          Zikr(
            id: 'featured_$categoryId',
            name: categoryName,
            description: 'أذكار مميزة من $categoryName',
            count: items.length,
            iconName: 'star_outlined', // استخدام أيقونة النجمة للأذكار المميزة
            items: items,
            isFeatured: true, // تعيين الفئة كمميزة
          ),
        );
      } catch (e) {
        debugPrint('خطأ أثناء إنشاء فئة الأذكار المميزة: $e');
      }
    });

    // التحقق من وجود فئات بعد التحويل
    if (featuredCategories.isEmpty) {
      debugPrint('لا توجد فئات أذكار مميزة بعد التحويل');
      return [];
    }

    // ترتيب الفئات حسب عدد الأذكار المميزة (تنازلياً)
    featuredCategories
        .sort((a, b) => b.items!.length.compareTo(a.items!.length));

    // عرض جميع الفئات المميزة بدون تحديد عدد معين
    return featuredCategories;
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // التحقق من وجود فئات
    if (widget.categories.isEmpty) {
      debugPrint('لا توجد فئات لعرض الأذكار المميزة');
      return const SizedBox.shrink(); // إرجاع مساحة فارغة إذا لم تكن هناك فئات
    }

    // استخراج الأذكار المميزة من جميع الفئات
    List<_FeaturedZikrInfo> featuredZikrs = [];
    try {
      featuredZikrs = _extractFeaturedZikrs(widget.categories);
      debugPrint('تم استخراج ${featuredZikrs.length} ذكر مميز');
    } catch (e) {
      debugPrint('خطأ أثناء استخراج الأذكار المميزة: $e');
    }

    // إذا لم تكن هناك أذكار مميزة، نعرض الفئات التي تحتوي على خاصية isFeatured
    List<Zikr> featuredCategories = [];
    try {
      if (featuredZikrs.isEmpty) {
        // إذا لم تكن هناك أذكار مميزة، نبحث عن الفئات التي تحتوي على خاصية isFeatured
        featuredCategories =
            widget.categories.where((category) => category.isFeatured).toList();

        // إضافة الفئات الفرعية التي تحتوي على خاصية isFeatured
        for (var category in widget.categories) {
          if (category.subcategories != null) {
            for (var subcategory in category.subcategories!) {
              if (subcategory.isFeatured) {
                // إنشاء نسخة من الفئة الفرعية كفئة رئيسية
                featuredCategories.add(
                  Zikr(
                    id: subcategory.id,
                    name: '${category.name} - ${subcategory.name}',
                    description: subcategory.description,
                    count: subcategory.count,
                    iconName: subcategory.iconName,
                    items: subcategory.items,
                    isFeatured: true,
                  ),
                );
              }
            }
          }
        }

        // إذا لم نجد أي فئات مميزة، نعرض رسالة توضيحية
        if (featuredCategories.isEmpty) {
          debugPrint('لم يتم العثور على أي فئات مميزة في ملف JSON');
        }
      } else {
        // استخدام الأذكار المميزة التي تم استخراجها
        featuredCategories = _convertFeaturedZikrsToCategories(featuredZikrs);
      }

      debugPrint('تم إنشاء ${featuredCategories.length} فئة مميزة');
    } catch (e) {
      debugPrint('خطأ أثناء تحويل الأذكار المميزة إلى فئات: $e');
      // في حالة حدوث خطأ، نستخدم الفئات المميزة فقط
      featuredCategories =
          widget.categories.where((category) => category.isFeatured).toList();
    }

    // التحقق من وجود فئات للعرض
    if (featuredCategories.isEmpty) {
      debugPrint('لا توجد فئات للعرض في قسم الأذكار المميزة');
      return const SizedBox.shrink(); // إرجاع مساحة فارغة إذا لم تكن هناك فئات
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment
          .end, // تغيير المحاذاة إلى اليمين لدعم اللغة العربية
      children: [
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: screenSize.width * 0.04,
            vertical: screenSize.height * 0.01,
          ),
          child: Row(
            textDirection:
                TextDirection.rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // عنوان مع تأثير ظل خفيف
              ShaderMask(
                shaderCallback: (bounds) => LinearGradient(
                  colors: [
                    AppColors.azkarColor,
                    AppColors.azkarColor.withBlue(
                        (AppColors.azkarColor.b + 30).clamp(0, 255).toInt()),
                  ],
                ).createShader(bounds),
                child: Text(
                  widget.title,
                  style: TextStyle(
                    fontSize: isTablet ? 24 : screenSize.width * 0.045,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode
                        ? const Color(0xFFE0E0E0)
                        : Colors.white, // TasbihColors.darkTextColor
                  ),
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                  textAlign: TextAlign.right, // محاذاة النص إلى اليمين
                ),
              ),

              // زر المزيد معطل مؤقتاً
              // _buildAnimatedMoreButton(
              //     context, isDarkMode, isTablet, screenSize),
            ],
          ),
        ),

        // مؤشر الصفحات المصغر
        Padding(
          padding: EdgeInsets.symmetric(horizontal: screenSize.width * 0.04),
          child: _buildPageIndicator(featuredCategories.length, isDarkMode),
        ),

        SizedBox(height: screenSize.height * 0.01),

        // عرض الأذكار المميزة بحجم متناسب مع الشاشة
        SizedBox(
          height: isTablet ? screenSize.height * 0.3 : screenSize.height * 0.35,
          child: PageView.builder(
            controller: _pageController,
            itemCount: featuredCategories.length,
            onPageChanged: (index) {
              HapticFeedback.lightImpact(); // تأثير اهتزاز خفيف عند التغيير
            },
            itemBuilder: (context, index) {
              final category = featuredCategories[index];

              // التحقق الآمن من قيمة _pageController.page
              double pageOffset = 0.0;
              if (_pageController.hasClients &&
                  _pageController.positions.isNotEmpty) {
                try {
                  final currentPage = _pageController.page;
                  if (currentPage != null) {
                    pageOffset = (index - currentPage).abs();
                  }
                } catch (e) {
                  // تجاهل الخطأ واستخدام قيمة افتراضية آمنة
                }
              }

              final scale = 1 - (pageOffset * 0.1).clamp(0.0, 0.2);
              final isCurrentPage = index == _currentPage;

              // استخدام RepaintBoundary لتحسين الأداء وتقليل مدة الرسوم المتحركة
              return RepaintBoundary(
                child: TweenAnimationBuilder<double>(
                  tween: Tween<double>(
                      begin: 0.9, end: 1.0), // تقليل مدى التكبير لتحسين الأداء
                  curve: Curves.easeOut, // استخدام منحنى أبسط لتحسين الأداء
                  duration: const Duration(
                      milliseconds: 350), // تقليل المدة لتحسين الأداء
                  builder: (context, value, child) {
                    return Transform.scale(
                      scale: scale * value,
                      child: Opacity(
                        opacity: scale.clamp(0.8,
                            1.0), // زيادة الحد الأدنى للشفافية لتحسين المظهر
                        child: _buildEnhancedAzkarCard(
                          context,
                          category,
                          index,
                          isCurrentPage,
                          isDarkMode,
                        ),
                      ),
                    );
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPageIndicator(int pageCount, bool isDarkMode) {
    // استخدام RepaintBoundary لتحسين الأداء
    return RepaintBoundary(
      child: SizedBox(
        height: 8,
        child: Row(
          textDirection:
              TextDirection.rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(pageCount, (index) {
            final isCurrentPage = index == _currentPage;
            return AnimatedContainer(
              // تقليل مدة الرسوم المتحركة لتحسين الأداء
              duration: const Duration(milliseconds: 200),
              margin: const EdgeInsets.symmetric(horizontal: 2),
              height: 8,
              width: isCurrentPage ? 24 : 8,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: isCurrentPage
                    ? AppColors.getAzkarColor(isDarkMode)
                    : (isDarkMode
                        ? AppColors.getAzkarColor(isDarkMode)
                            .withAlpha(77) // 0.3 * 255 = 77
                        : AppColors.getAzkarColor(isDarkMode)
                            .withAlpha(51)), // 0.2 * 255 = 51
                // تبسيط الظلال لتحسين الأداء - فقط للصفحة الحالية
                boxShadow: isCurrentPage
                    ? [
                        BoxShadow(
                          color: AppColors.getAzkarColor(isDarkMode)
                              .withAlpha(77), // 0.3 * 255 = 77
                          // تقليل قيمة التمويه لتحسين الأداء
                          blurRadius: 3,
                          offset: const Offset(0, 1),
                        )
                      ]
                    : null,
              ),
            );
          }),
        ),
      ),
    );
  }

  // بناء زر استعراض الأذكار المتحرك - مُحسّن للأداء
  Widget _buildExploreButton({
    required BuildContext context,
    required bool isDarkMode,
    required bool isTablet,
    required Size screenSize,
    required VoidCallback onTap,
  }) {
    // استخدام RepaintBoundary لتحسين الأداء
    return RepaintBoundary(
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(
              vertical: screenSize.height * 0.012,
            ),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? AppColors.getAzkarColor(isDarkMode)
                      .withAlpha(38) // 0.15 * 255 = 38
                  : AppColors.getAzkarColor(isDarkMode)
                      .withAlpha(20), // 0.08 * 255 = 20
              borderRadius: BorderRadius.circular(screenSize.width * 0.04),
              boxShadow: [
                BoxShadow(
                  color: (isDarkMode
                          ? AppColors.getAzkarColor(isDarkMode)
                          : AppColors.getAzkarColor(isDarkMode))
                      .withAlpha(13), // 0.05 * 255 = 13
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Row(
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'استعراض الأذكار',
                  style: TextStyle(
                    fontSize: isTablet ? 16 : screenSize.width * 0.035,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode
                        ? AppColors.getAzkarColor(isDarkMode)
                            .withAlpha(230) // 0.9 * 255 = 230
                        : AppColors.getAzkarColor(isDarkMode),
                  ),
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                  textAlign: TextAlign.center, // محاذاة النص في المنتصف
                ),
                SizedBox(width: screenSize.width * 0.02),
                // استخدام أيقونة ثابتة بدلاً من الرسوم المتحركة لتحسين الأداء
                Icon(
                  Icons.arrow_forward,
                  size: isTablet ? 18 : screenSize.width * 0.04,
                  color: isDarkMode
                      ? AppColors.getAzkarColor(isDarkMode)
                          .withAlpha(230) // 0.9 * 255 = 230
                      : AppColors.getAzkarColor(isDarkMode),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // // بناء زر المزيد المتحرك
  // Widget _buildAnimatedMoreButton(
  //     BuildContext context, bool isDarkMode, bool isTablet, Size screenSize) {
  //   return StatefulBuilder(
  //     builder: (context, setState) {
  //       // استخدام متغير حالة للتحكم في تأثير الضغط
  //       bool isPressed = false;
  //       return TweenAnimationBuilder<double>(
  //         tween: Tween<double>(begin: 0.0, end: 1.0),
  //         duration: const Duration(milliseconds: 300),
  //         builder: (context, value, child) {
  //           return GestureDetector(
  //             onTapDown: (_) => setState(() => isPressed = true),
  //             onTapUp: (_) {
  //               setState(() => isPressed = false);
  //               // الانتقال للأسفل إلى قسم كافة الأذكار
  //               HapticFeedback.lightImpact();
  //               // يمكن إضافة الإجراء المطلوب هنا
  //             },
  //             onTapCancel: () => setState(() => isPressed = false),
  //             child: MouseRegion(
  //               cursor: SystemMouseCursors.click,
  //               child: AnimatedScale(
  //                 scale: isPressed ? 0.95 : 1.0,
  //                 duration: const Duration(milliseconds: 150),
  //                 child: Container(
  //                   padding: EdgeInsets.symmetric(
  //                     horizontal: screenSize.width * 0.03,
  //                     vertical: screenSize.height * 0.01,
  //                   ),
  //                   decoration: BoxDecoration(
  //                     color: isDarkMode
  //                         ? AppColors.getAzkarColor(isDarkMode)
  //                             .withAlpha(38) // 0.15 * 255 = 38
  //                         : AppColors.getAzkarColor(isDarkMode)
  //                             .withAlpha(20), // 0.08 * 255 = 20
  //                     borderRadius:
  //                         BorderRadius.circular(screenSize.width * 0.04),
  //                     boxShadow: [
  //                       BoxShadow(
  //                         color: (isDarkMode
  //                                 ? AppColors.getAzkarColor(isDarkMode)
  //                                 : AppColors.getAzkarColor(isDarkMode))
  //                             .withAlpha(13), // 0.05 * 255 = 13
  //                         blurRadius: 3,
  //                         offset: const Offset(0, 1),
  //                       ),
  //                     ],
  //                   ),
  //                   child: Row(
  //                     children: [
  //                       Text(
  //                         'المزيد',
  //                         style: TextStyle(
  //                           fontSize: isTablet ? 16 : screenSize.width * 0.035,
  //                           fontWeight: FontWeight.bold,
  //                           color: isDarkMode
  //                               ? AppColors.getAzkarColor(isDarkMode)
  //                               : AppColors.getAzkarColor(isDarkMode),
  //                         ),
  //                       ),
  //                       SizedBox(width: screenSize.width * 0.01),
  //                       Icon(
  //                         Icons.arrow_forward_ios,
  //                         size: isTablet ? 14 : screenSize.width * 0.03,
  //                         color: isDarkMode
  //                             ? AppColors.getAzkarColor(isDarkMode)
  //                             : AppColors.getAzkarColor(isDarkMode),
  //                       ),
  //                     ],
  //                   ),
  //                 ),
  //               ),
  //             ),
  //           );
  //         },
  //       );
  //     },
  //   );
  // }

  Widget _buildEnhancedAzkarCard(BuildContext context, Zikr category, int index,
      bool isCurrentPage, bool isDarkMode) {
    // التحقق من صحة البيانات
    if (category.id.isEmpty) {
      debugPrint('تم تخطي بطاقة ذكر بسبب معرف فارغ');
      return const SizedBox.shrink(); // إرجاع مساحة فارغة إذا كان المعرف فارغاً
    }

    // التحقق مما إذا كانت هذه فئة أذكار مميزة
    final bool isFeaturedCategory = category.id.startsWith('featured_');
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;

    // استخدام RepaintBoundary لتحسين الأداء
    return RepaintBoundary(
      child: GestureDetector(
        onTap: () {
          // استخدام اهتزاز أخف لتحسين الأداء
          HapticFeedback.lightImpact();
          if (category.hasSubcategories && category.subcategories != null) {
            // التعامل مع الفئات الفرعية
            debugPrint(
                'الفئة تحتوي على أقسام فرعية: ${category.subcategories!.length}');
            // يمكن إضافة التنقل إلى الأقسام الفرعية هنا
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('سيتم دعم الأقسام الفرعية قريباً'),
                duration: Duration(seconds: 2),
              ),
            );
          } else {
            // التحقق من وجود عناصر قبل الانتقال
            if (category.items == null || category.items!.isEmpty) {
              debugPrint(
                  'لا يمكن الانتقال: لا توجد عناصر في الفئة ${category.name}');
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('لا توجد أذكار في هذه الفئة'),
                  duration: Duration(seconds: 2),
                ),
              );
            } else {
              debugPrint(
                  'الانتقال إلى تفاصيل الفئة: ${category.name} مع ${category.items!.length} ذكر');
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => AzkarDetailsScreen(
                    category: category.name,
                    azkarItems: category.items,
                  ),
                ),
              );
            }
          }
        },
        child: Hero(
          tag: 'featured_${category.id}',
          child: Card(
            margin: EdgeInsets.symmetric(
              horizontal: screenSize.width * 0.02,
              vertical: screenSize.height * 0.01,
            ),
            elevation: isCurrentPage ? 8 : 4,
            shadowColor: isCurrentPage
                ? (isDarkMode
                    ? AppColors.getAzkarColor(isDarkMode).withAlpha(
                        102) // AppColors.azkarColorDark مع شفافية 0.4
                    : AppColors.getAzkarColor(isDarkMode)
                        .withAlpha(102)) // 0.4 * 255 = 102
                : (isDarkMode
                    ? AppColors.getAzkarColor(isDarkMode)
                        .withAlpha(51) // AppColors.azkarColorDark مع شفافية 0.2
                    : AppColors.getAzkarColor(isDarkMode)
                        .withAlpha(51)), // 0.2 * 255 = 51
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(screenSize.width * 0.05),
              side: isCurrentPage
                  ? BorderSide(
                      color: isDarkMode
                          ? AppColors.getAzkarColor(isDarkMode)
                              .withAlpha(128) // 0.5 * 255 = 128
                          : AppColors.getAzkarColor(isDarkMode)
                              .withAlpha(77), // 0.3 * 255 = 77
                      width: 1.5,
                    )
                  : BorderSide.none,
            ),
            color: isDarkMode
                ? const Color(
                    0xFF252A34) // استخدام لون البطاقات المتوافق مع TasbihColors.darkCardColor
                : Colors.white,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(screenSize.width * 0.05),
              child: Container(
                decoration: BoxDecoration(
                  color: isDarkMode ? const Color(0xFF1A1A2E) : Colors.white,
                  gradient: LinearGradient(
                    begin: Alignment.topRight,
                    end: Alignment.bottomLeft,
                    colors: isDarkMode
                        ? const [
                            Color(0xFF1A1A2E), // TasbihColors.darkBackground
                            Color(
                                0xFF16213E), // TasbihColors.darkBackgroundSecondary
                          ]
                        : [
                            Colors.white,
                            Colors.grey.shade50,
                          ],
                  ),
                  boxShadow: [
                    // إضافة ظل خفيف داخلي متوافق مع قسم المسبحة
                    BoxShadow(
                      color: isDarkMode
                          ? Colors.black.withAlpha(isCurrentPage ? 40 : 20)
                          : Colors.black.withAlpha(isCurrentPage ? 20 : 10),
                      blurRadius: 15,
                      spreadRadius: -5, // قيمة سالبة لجعل الظل داخلياً
                      offset: const Offset(0, 0),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // الجزء العلوي مع الخلفية الجميلة
                    Expanded(
                      flex: 6,
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topRight,
                            end: Alignment.bottomLeft,
                            colors: isDarkMode
                                ? [
                                    TasbihColors
                                        .primary, // استخدام لون المسبحة الأساسي
                                    TasbihColors.primary.withBlue(
                                      (TasbihColors.primary.b + 30)
                                          .clamp(0, 255)
                                          .toInt(),
                                    ),
                                  ]
                                : [
                                    TasbihColors
                                        .primary, // استخدام لون المسبحة الأساسي
                                    TasbihColors.primary.withBlue(
                                      (TasbihColors.primary.b + 30)
                                          .clamp(0, 255)
                                          .toInt(),
                                    ),
                                  ],
                          ),
                        ),
                        child: Stack(
                          children: [
                            // زخرفة إسلامية متحركة - مُحسّنة للأداء وتجنب مشكلة ParentDataWidget
                            Positioned(
                              right: isCurrentPage ? -15 : -20,
                              bottom: isCurrentPage ? -15 : -25,
                              child: AnimatedOpacity(
                                // تقليل مدة الرسوم المتحركة لتحسين الأداء
                                duration: const Duration(milliseconds: 600),
                                // استخدام منحنى أبسط لتحسين الأداء
                                curve: Curves.easeOut,
                                // استخدام قيمة ثابتة للشفافية بدلاً من الرسوم المتحركة لتحسين الأداء
                                opacity: 0.18,
                                child: AnimatedContainer(
                                  duration: const Duration(milliseconds: 600),
                                  curve: Curves.easeOut,
                                  transform: Matrix4.translationValues(
                                    isCurrentPage ? 0 : -5,
                                    isCurrentPage ? 0 : -10,
                                    0,
                                  ),
                                  child: SvgPicture.asset(
                                    'assets/images/p2.svg',
                                    width: screenSize.width * 0.4,
                                    height: screenSize.width * 0.4,
                                    colorFilter: const ColorFilter.mode(
                                      Colors.white,
                                      BlendMode.srcIn,
                                    ),
                                  ),
                                ),
                              ),
                            ),

                            // تأثير توهج للجزء العلوي
                            Positioned(
                              top: 0,
                              left: 0,
                              right: 0,
                              height: 60,
                              child: Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    colors: [
                                      Colors.white
                                          .withAlpha(51), // 0.2 * 255 = 51
                                      Colors.transparent,
                                    ],
                                  ),
                                ),
                              ),
                            ),

                            // محتوى البطاقة
                            Padding(
                              padding: EdgeInsets.all(screenSize.width * 0.045),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  // أيقونة الفئة مع تأثير
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      // شارة الأذكار - مُحسّنة للأداء
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: screenSize.width * 0.025,
                                          vertical: screenSize.height * 0.006,
                                        ),
                                        decoration: BoxDecoration(
                                          color: isFeaturedCategory
                                              ? (isCurrentPage
                                                  ? Colors.amber.withAlpha(
                                                      64) // 0.25 * 255 = 64
                                                  : Colors.amber.withAlpha(
                                                      38)) // 0.15 * 255 = 38
                                              : (isCurrentPage
                                                  ? Colors.white.withAlpha(
                                                      64) // 0.25 * 255 = 64
                                                  : Colors.white.withAlpha(
                                                      38)), // 0.15 * 255 = 38
                                          borderRadius: BorderRadius.circular(
                                              screenSize.width * 0.04),
                                          boxShadow: isCurrentPage
                                              ? [
                                                  const BoxShadow(
                                                    color: Colors.black12,
                                                    // تقليل قيمة التمويه لتحسين الأداء
                                                    blurRadius: 3,
                                                    offset: Offset(0, 1),
                                                  )
                                                ]
                                              : null,
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              isFeaturedCategory
                                                  ? Icons.star
                                                  : Icons.format_list_numbered,
                                              color: Colors.white,
                                              size: isTablet
                                                  ? 14
                                                  : screenSize.width * 0.03,
                                            ),
                                            SizedBox(
                                                width: screenSize.width * 0.01),
                                            Text(
                                              isFeaturedCategory
                                                  ? 'أذكار مميزة'
                                                  : (category.hasSubcategories
                                                      ? '${category.subcategories?.length ?? 0} أقسام'
                                                      : '${category.items?.length ?? 0} ذكر'),
                                              style: TextStyle(
                                                fontSize: isTablet
                                                    ? 14
                                                    : screenSize.width * 0.03,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),

                                      // أيقونة الفئة
                                      Container(
                                        width: isTablet
                                            ? 50
                                            : screenSize.width * 0.11,
                                        height: isTablet
                                            ? 50
                                            : screenSize.width * 0.11,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: isFeaturedCategory
                                              ? Colors.amber.withAlpha(100)
                                              : Colors.white24,
                                          boxShadow: [
                                            BoxShadow(
                                              color: isFeaturedCategory
                                                  ? Colors.amber.withAlpha(50)
                                                  : Colors.black.withAlpha(
                                                      26), // 0.1 * 255 = 26
                                              blurRadius: 4,
                                              offset: const Offset(0, 2),
                                            ),
                                          ],
                                        ),
                                        child: Icon(
                                          isFeaturedCategory
                                              ? Icons.star
                                              : _getIconForZikr(category),
                                          color: isFeaturedCategory
                                              ? Colors.amber
                                              : Colors.white,
                                          size: isTablet
                                              ? 28
                                              : screenSize.width * 0.06,
                                        ),
                                      ),
                                    ],
                                  ),

                                  // اسم الفئة
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        category.name,
                                        style: TextStyle(
                                          fontSize: isTablet
                                              ? 26
                                              : screenSize.width * 0.055,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                          shadows: [
                                            Shadow(
                                              color: Colors.black.withAlpha(80),
                                              offset: const Offset(0, 1),
                                              blurRadius: 3,
                                            ),
                                          ],
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      SizedBox(
                                          height: screenSize.height * 0.005),

                                      // خط تحت العنوان للتمييز - مُحسّن للأداء
                                      Container(
                                        width: isCurrentPage
                                            ? screenSize.width * 0.2
                                            : screenSize.width * 0.15,
                                        height: 3,
                                        decoration: BoxDecoration(
                                          color: isFeaturedCategory
                                              ? Colors.amber.withAlpha(179)
                                              : Colors.white.withAlpha(
                                                  179), // 0.7 * 255 = 179
                                          borderRadius:
                                              BorderRadius.circular(1.5),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // الجزء السفلي مع المعلومات
                    Expanded(
                      flex: 5,
                      child: Padding(
                        padding: EdgeInsets.all(screenSize.width * 0.045),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // وصف الفئة
                            Text(
                              category.description,
                              style: TextStyle(
                                fontSize:
                                    isTablet ? 16 : screenSize.width * 0.033,
                                color: isDarkMode
                                    ? const Color(
                                        0xFFE0E0E0) // TasbihColors.darkTextColor
                                    : Colors.grey[700],
                                height: 1.3,
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),

                            // زر استعراض محسن
                            _buildExploreButton(
                              context: context,
                              isDarkMode: isDarkMode,
                              isTablet: isTablet,
                              screenSize: screenSize,
                              onTap: () {
                                HapticFeedback.mediumImpact();
                                if (category.hasSubcategories &&
                                    category.subcategories != null) {
                                  // التعامل مع الفئات الفرعية
                                  debugPrint(
                                      'الفئة تحتوي على أقسام فرعية: ${category.subcategories!.length}');
                                  // يمكن إضافة التنقل إلى الأقسام الفرعية هنا
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                          'سيتم دعم الأقسام الفرعية قريباً'),
                                      duration: Duration(seconds: 2),
                                    ),
                                  );
                                } else {
                                  // التحقق من وجود عناصر قبل الانتقال
                                  if (category.items == null ||
                                      category.items!.isEmpty) {
                                    debugPrint(
                                        'لا يمكن الانتقال: لا توجد عناصر في الفئة ${category.name}');
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content:
                                            Text('لا توجد أذكار في هذه الفئة'),
                                        duration: Duration(seconds: 2),
                                      ),
                                    );
                                  } else {
                                    debugPrint(
                                        'الانتقال إلى تفاصيل الفئة: ${category.name} مع ${category.items!.length} ذكر');
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            AzkarDetailsScreen(
                                          category: category.name,
                                          azkarItems: category.items,
                                        ),
                                      ),
                                    );
                                  }
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
