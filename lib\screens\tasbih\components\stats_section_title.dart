// عنوان قسم الإحصائيات

import 'package:flutter/material.dart';
import '../utils/tasbih_colors.dart';

class StatsSectionTitle extends StatelessWidget {
  final String title;
  final IconData? icon;
  final VoidCallback? onTap;
  final Widget? trailing;

  const StatsSectionTitle({
    Key? key,
    required this.title,
    this.icon,
    this.onTap,
    this.trailing,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final textColor = isDarkMode ? Colors.white : Colors.black87;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              if (icon != null) ...[
                Icon(
                  icon,
                  color: TasbihColors.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
              ],
              Text(
                title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: textColor,
                ),
              ),
            ],
          ),
          if (trailing != null)
            trailing!
          else if (onTap != null)
            InkWell(
              onTap: onTap,
              borderRadius: BorderRadius.circular(20),
              child: const Padding(
                padding: EdgeInsets.all(4.0),
                child: Icon(
                  Icons.arrow_forward_ios,
                  color: TasbihColors.primary,
                  size: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
