import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../../../models/favorite_item.dart';
import '../../../models/favorite.dart';
import '../../../models/azkar_item.dart';
import '../../../models/book.dart';
import '../../../models/poem.dart';
import '../../../models/zikr.dart';
import '../../../models/dua.dart';
import '../../../database/database_helper.dart';
import '../../../services/favorites_service.dart';
import '../components/sort_options_dialog.dart';
import '../load_poem_helper.dart';

class FavoritesProvider extends ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // حالة المفضلة
  List<FavoriteItem> _items = [];
  bool _isLoading = true;
  String _error = '';
  bool _disposed = false; // متغير لتتبع ما إذا كان المزود قد تم التخلص منه
  DateTime _loadingStartTime = DateTime.now(); // وقت بدء التحميل

  // حالة التصفية والبحث
  String _currentFilter = 'all'; // all, books, poems, azkar
  String _searchQuery = '';

  // الحصول على العناصر المفلترة
  List<FavoriteItem> get filteredItems {
    return _filterAndSearchItems(_items);
  }

  // حالة التحميل
  bool get isLoading => _isLoading;

  // رسالة الخطأ
  String get error => _error;

  // الفلتر الحالي
  String get currentFilter => _currentFilter;

  // استعلام البحث
  String get searchQuery => _searchQuery;

  // تعيين فلتر جديد
  void setFilter(String filter) {
    _currentFilter = filter;
    notifyListeners();
  }

  // تعيين استعلام بحث جديد
  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  // تحميل المفضلة - تم تحسينها للتعامل مع الأخطاء وتسريع التحميل
  Future<void> loadFavorites() async {
    // إعادة تعيين حالة التحميل إذا كانت عالقة لأكثر من 3 ثواني
    if (_isLoading) {
      // التحقق من وقت بدء التحميل
      final now = DateTime.now();
      final loadingDuration = now.difference(_loadingStartTime);

      if (loadingDuration.inSeconds > 3) {
        // إذا كان التحميل مستمرًا لأكثر من 3 ثواني، أعد تعيين الحالة
        debugPrint('إعادة تعيين حالة التحميل لأنها عالقة لأكثر من 3 ثواني');
        _isLoading = false;
      } else {
        debugPrint('تجاهل طلب تحميل المفضلة لأنها قيد التحميل بالفعل');
        return;
      }
    }

    debugPrint('بدء تحميل المفضلة...');
    _isLoading = true;
    _error = '';
    _loadingStartTime = DateTime.now(); // تحديث وقت بدء التحميل
    notifyListeners();

    try {
      // تحسين: استخدام تأخير أقصر لتسريع التحميل
      await Future.delayed(const Duration(milliseconds: 50));

      // تحسين: تقليل وقت الانتظار الأقصى للتحميل
      const timeout = Duration(seconds: 3);

      // تحميل المفضلة مع وقت انتظار أقصى
      final items = await _fetchFavoriteItems().timeout(timeout, onTimeout: () {
        debugPrint('انتهى وقت تحميل المفضلة');
        return [];
      });

      // تصفية العناصر لعرض الأذكار فقط
      final azkarItems = items.where((item) => item.type == 'azkar').toList();
      debugPrint(
          'تم استرجاع ${items.length} عنصر، منها ${azkarItems.length} ذكر');

      // التحقق من أن المكون لا يزال مرتبطًا بشجرة العناصر
      if (!_disposed) {
        // تحسين: تخزين جميع العناصر ولكن عرض الأذكار فقط
        _items = items;
        _isLoading = false;
        _error = '';
        notifyListeners();
        debugPrint('تم تحميل المفضلة بنجاح مع ${_items.length} عنصر');
      }
    } catch (e, stackTrace) {
      debugPrint('خطأ في تحميل المفضلة: $e');
      debugPrint('تفاصيل الخطأ: $stackTrace');

      // التحقق من أن المكون لا يزال مرتبطًا بشجرة العناصر
      if (!_disposed) {
        _items = []; // تعيين قائمة فارغة في حالة الخطأ
        _isLoading = false;
        _error = 'حدث خطأ أثناء تحميل المفضلة: $e';
        notifyListeners();
      }
    } finally {
      // ضمان تعيين _isLoading إلى false حتى في حالة حدوث خطأ غير متوقع
      if (_isLoading && !_disposed) {
        _isLoading = false;
        notifyListeners();
        debugPrint('تم إنهاء حالة التحميل في finally');
      }
    }
  }

  // ترتيب العناصر
  void sortItems(SortOption sortOption) {
    switch (sortOption) {
      case SortOption.newest:
        _items.sort((a, b) => b.addedDate.compareTo(a.addedDate));
        break;
      case SortOption.oldest:
        _items.sort((a, b) => a.addedDate.compareTo(b.addedDate));
        break;
      case SortOption.alphabetical:
        _items.sort((a, b) => a.title.compareTo(b.title));
        break;
    }
    notifyListeners();
  }

  // حذف عنصر من المفضلة - تم تحسينه لمعالجة مشكلة عدم الحذف
  Future<bool> removeFromFavorites(FavoriteItem favoriteItem) async {
    try {
      debugPrint(
          'بدء حذف العنصر من المفضلة: ${favoriteItem.id} (النوع: ${favoriteItem.type})');

      // استخدام المعرف الأصلي كما هو أولاً
      String originalId = favoriteItem.id;

      // محاولة الحذف باستخدام المعرف الأصلي
      bool success = await FavoritesService.removeFromFavorites(
          originalId, favoriteItem.type);

      // إذا فشل الحذف، حاول تحويل المعرف إلى رقم
      if (!success) {
        try {
          // محاولة تحويل معرف العنصر من String إلى int
          int numericId = int.parse(favoriteItem.id);
          debugPrint('محاولة الحذف باستخدام المعرف الرقمي: $numericId');
          success = await FavoritesService.removeFromFavorites(
              numericId, favoriteItem.type);
        } catch (e) {
          // إذا لم يكن المعرف رقمًا، استخدم hashCode كبديل
          int hashId = favoriteItem.id.hashCode;
          debugPrint('محاولة الحذف باستخدام hashCode للمعرف: $hashId');
          success = await FavoritesService.removeFromFavorites(
              hashId, favoriteItem.type);
        }
      }

      // إذا كان العنصر من نوع الأذكار، قم بتحديث SharedPreferences أيضًا
      if (favoriteItem.type == 'azkar') {
        try {
          final prefs = await SharedPreferences.getInstance();
          final favorites = prefs.getStringList('favorites') ?? [];
          debugPrint(
              'قائمة المفضلة في SharedPreferences قبل الحذف: $favorites');

          // حذف العنصر بالمعرف الأصلي
          final updatedFavorites =
              favorites.where((id) => id != favoriteItem.id).toList();

          // حذف العنصر بالمعرف الرقمي إذا أمكن
          try {
            int numericId = int.parse(favoriteItem.id);
            updatedFavorites.removeWhere((id) => id == numericId.toString());
          } catch (e) {
            // تجاهل الخطأ إذا لم يكن المعرف رقمًا
          }

          // حذف العنصر بالمعرف hashCode إذا لزم الأمر
          updatedFavorites
              .removeWhere((id) => id == favoriteItem.id.hashCode.toString());

          await prefs.setStringList('favorites', updatedFavorites);
          debugPrint(
              'تم تحديث قائمة المفضلة في SharedPreferences: $updatedFavorites');
        } catch (e) {
          debugPrint('خطأ في تحديث SharedPreferences: $e');
        }
      }

      // تحديث القائمة المحلية بغض النظر عن نتيجة الحذف من قاعدة البيانات
      // هذا يضمن تحديث واجهة المستخدم حتى لو كان هناك مشكلة في قاعدة البيانات
      _items.removeWhere((item) =>
          item.id == favoriteItem.id ||
          item.id == favoriteItem.id.hashCode.toString() ||
          (favoriteItem.item != null && item.item == favoriteItem.item));

      notifyListeners();

      // تأثير اهتزاز للتفاعل
      HapticFeedback.mediumImpact();

      debugPrint('نتيجة حذف العنصر من المفضلة: $success');
      return true; // نعيد true حتى لو كان هناك مشكلة في قاعدة البيانات لتحسين تجربة المستخدم
    } catch (e) {
      debugPrint('خطأ في حذف العنصر من المفضلة: $e');
      return false;
    }
  }

  // استعادة عنصر محذوف من المفضلة
  Future<bool> restoreFavorite(FavoriteItem favoriteItem) async {
    try {
      int itemId;
      try {
        // محاولة تحويل معرف العنصر من String إلى int
        itemId = int.parse(favoriteItem.id);
      } catch (e) {
        // إذا لم يكن المعرف رقمًا، استخدم hashCode كبديل
        itemId = favoriteItem.id.hashCode;
        debugPrint('استخدام hashCode للمعرف في الاستعادة: $itemId');
      }

      // إضافة العنصر إلى المفضلة مرة أخرى
      await FavoritesService.addToFavorites(itemId, favoriteItem.type);

      // إذا كان العنصر من نوع الأذكار، قم بتحديث SharedPreferences أيضًا
      if (favoriteItem.type == 'azkar') {
        try {
          final prefs = await SharedPreferences.getInstance();
          final favorites = prefs.getStringList('favorites') ?? [];
          if (!favorites.contains(favoriteItem.id)) {
            favorites.add(favoriteItem.id);
            await prefs.setStringList('favorites', favorites);
          }
        } catch (e) {
          debugPrint('خطأ في تحديث SharedPreferences: $e');
        }
      }

      // تحديث القائمة
      await loadFavorites();

      // تأثير اهتزاز للتفاعل
      HapticFeedback.mediumImpact();

      return true;
    } catch (e) {
      debugPrint('خطأ في استعادة العنصر إلى المفضلة: $e');
      return false;
    }
  }

  // مشاركة عنصر مفضل
  Future<bool> shareFavorite(FavoriteItem item) async {
    try {
      HapticFeedback.lightImpact(); // تأثير اهتزاز خفيف
      await FavoritesService.shareFavorite(item.item, item.type);
      return true;
    } catch (e) {
      debugPrint('خطأ في مشاركة العنصر: $e');
      return false;
    }
  }

  // تصفية وبحث العناصر
  List<FavoriteItem> _filterAndSearchItems(List<FavoriteItem> items) {
    // تصفية العناصر لعرض الأذكار والأدعية والصلوات على النبي فقط وإخفاء الكتب والقصائد مؤقتاً
    // إذا كان الفلتر "الكل"، نعرض الأذكار والأدعية والصلوات على النبي فقط
    List<FavoriteItem> filteredItems = _currentFilter == 'all'
        ? items
            .where((item) =>
                item.type == 'azkar' ||
                item.type == 'dua' ||
                item.type == 'prophet_prayer')
            .toList()
        : items;

    // التصفية حسب النوع المحدد
    if (_currentFilter != 'all') {
      filteredItems = items
          .where((item) =>
              // عرض الأذكار والأدعية والصلوات على النبي فقط وتجاهل الكتب والقصائد مؤقتاً
              (_currentFilter == 'azkar' && item.type == 'azkar') ||
              (_currentFilter == 'dua' && item.type == 'dua') ||
              (_currentFilter == 'prophet_prayer' &&
                  item.type == 'prophet_prayer'))
          .toList();
    }

    // البحث
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filteredItems = filteredItems
          .where((item) =>
              item.title.toLowerCase().contains(query) ||
              item.subtitle.toLowerCase().contains(query))
          .toList();
    }

    return filteredItems;
  }

  // جلب عناصر المفضلة - تم تحسينها للتوافق مع الذكر
  Future<List<FavoriteItem>> _fetchFavoriteItems() async {
    try {
      debugPrint('بدء تحميل عناصر المفضلة...');

      // استخدام خدمة المفضلة للحصول على العناصر المفضلة
      List<Map<String, dynamic>> favoritesMaps = [];
      try {
        // التحقق من وجود جدول المفضلة أولاً
        final db = await _databaseHelper.database;

        // التحقق من وجود الجدول وإنشائه إذا لم يكن موجوداً
        await db.execute('''
          CREATE TABLE IF NOT EXISTS favorites(
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            item_id TEXT NOT NULL,
            item_type TEXT NOT NULL,
            timestamp INTEGER DEFAULT (strftime('%s','now') * 1000),
            UNIQUE(item_id, item_type)
          )
        ''');

        // التحقق من وجود الأعمدة المطلوبة
        final columns = await db.rawQuery('PRAGMA table_info(favorites)');
        final columnNames = columns.map((c) => c['name'].toString()).toList();

        // إذا كان هناك عمود 'type' ولكن لا يوجد عمود 'item_type'، قم بإضافته
        if (columnNames.contains('type') &&
            !columnNames.contains('item_type')) {
          debugPrint('تحديث جدول المفضلة لإضافة عمود item_type');
          await db.execute('ALTER TABLE favorites ADD COLUMN item_type TEXT');
          await db.execute('UPDATE favorites SET item_type = type');
        }

        favoritesMaps = await FavoritesService.getFavorites();
        debugPrint('تم استرجاع ${favoritesMaps.length} عنصر من قاعدة البيانات');
      } catch (e) {
        debugPrint('خطأ في الحصول على المفضلة من قاعدة البيانات: $e');
        // استمر بقائمة فارغة
      }

      // إذا لم تكن هناك عناصر مفضلة، أرجع قائمة فارغة
      if (favoritesMaps.isEmpty) {
        debugPrint('لا توجد عناصر مفضلة في قاعدة البيانات');
        return [];
      }

      // تحميل المفضلة من SharedPreferences للأذكار
      final prefs = await SharedPreferences.getInstance();
      final sharedPrefsFavorites = prefs.getStringList('favorites') ?? [];

      debugPrint(
          'تم استرجاع ${sharedPrefsFavorites.length} عنصر من SharedPreferences');

      // تحويل البيانات إلى كائنات Favorite
      List<Favorite> favoritesList = [];
      try {
        favoritesList = List.generate(
            favoritesMaps.length, (i) => Favorite.fromMap(favoritesMaps[i]));

        debugPrint('تم تحويل ${favoritesList.length} عنصر إلى كائنات Favorite');
        for (var favorite in favoritesList) {
          debugPrint(
              'عنصر مفضل: id=${favorite.id}, itemId=${favorite.itemId}, type=${favorite.type}, timestamp=${favorite.timestamp}');
        }
      } catch (e) {
        debugPrint('خطأ في تحويل البيانات إلى كائنات Favorite: $e');
        // استمر بقائمة فارغة
      }

      final List<FavoriteItem> items = [];
      debugPrint('بدء معالجة عناصر المفضلة وتحويلها إلى FavoriteItem');

      // تحميل كل عنصر من المفضلة
      for (var favorite in favoritesList) {
        debugPrint(
            'معالجة العنصر المفضل: ${favorite.itemId} من النوع ${favorite.type}');
        dynamic item;
        String title = '';
        String subtitle = '';

        try {
          switch (favorite.type) {
            case 'book':
              try {
                debugPrint('محاولة تحميل الكتاب بالمعرف: ${favorite.itemId}');
                // استخدام getBookById بدلاً من getBook
                final bookData =
                    await _databaseHelper.getBookById(favorite.itemId);
                if (bookData != null && bookData.isNotEmpty) {
                  debugPrint('تم العثور على الكتاب: ${bookData['title']}');
                  // استخراج الوسوم إذا كانت موجودة
                  List<String> tags = [];
                  if (bookData['tags'] != null &&
                      bookData['tags'].toString().isNotEmpty) {
                    tags = bookData['tags'].toString().split(', ');
                  }

                  // محاولة تحويل المعرف إلى رقم
                  int bookId;
                  try {
                    bookId = int.parse(favorite.itemId.toString());
                  } catch (e) {
                    bookId = 0;
                    debugPrint('خطأ في تحويل معرف الكتاب إلى رقم: $e');
                  }

                  final book = Book(
                    id: bookId,
                    title: bookData['title'] ?? '',
                    author: bookData['author'] ?? '',
                    description: bookData['description'] ?? '',
                    coverUrl: bookData['cover_url'] ?? '',
                    localCoverPath: bookData['local_cover_path'],
                    category: bookData['category'] ?? '',
                    pdfUrl: bookData['pdf_url'] ?? '',
                    pdfPath: bookData['pdf_path'],
                    localPdfPath: bookData['local_pdf_path'],
                    pages: bookData['pages'] ?? 0,
                    tags: tags,
                  );

                  item = book;
                  title = book.title;
                  subtitle = book.author;
                  debugPrint('تم تحميل الكتاب المفضل: ${book.title}');
                } else {
                  debugPrint(
                      'لم يتم العثور على الكتاب بالمعرف: ${favorite.itemId}');
                  // محاولة الحصول على الكتب من قاعدة البيانات
                  final allBooks = await _databaseHelper.getBooks();
                  debugPrint(
                      'تم الحصول على ${allBooks.length} كتاب من قاعدة البيانات');

                  // محاولة تحويل المعرف إلى رقم
                  int? bookId;
                  try {
                    bookId = int.parse(favorite.itemId.toString());
                    debugPrint('تم تحويل معرف الكتاب إلى رقم: $bookId');
                  } catch (e) {
                    debugPrint('خطأ في تحويل معرف الكتاب إلى رقم: $e');
                  }

                  // البحث عن الكتاب بالمعرف
                  final matchingBook = allBooks
                      .where((book) =>
                          book.id.toString() == favorite.itemId.toString() ||
                          (bookId != null && book.id == bookId))
                      .firstOrNull;

                  if (matchingBook != null) {
                    debugPrint(
                        'تم العثور على الكتاب في قائمة الكتب: ${matchingBook.title}');
                    item = matchingBook;
                    title = matchingBook.title;
                    subtitle = matchingBook.author;
                  } else {
                    // إنشاء كتاب افتراضي في حالة عدم وجود الكتاب
                    final book = Book(
                      id: bookId ?? 0,
                      title: 'كتاب غير متوفر',
                      author: 'غير معروف',
                      description: '',
                      coverUrl: '',
                      category: '',
                      pdfUrl: '',
                      tags: [],
                    );

                    item = book;
                    title = book.title;
                    subtitle = book.author;
                    debugPrint(
                        'تم إنشاء كتاب افتراضي للمعرف: ${favorite.itemId}');
                  }
                }
              } catch (e) {
                debugPrint('خطأ في تحميل الكتاب المفضل: $e');
                // إنشاء كتاب افتراضي في حالة حدوث خطأ
                int bookId;
                try {
                  bookId = int.parse(favorite.itemId.toString());
                } catch (e) {
                  bookId = 0;
                }

                final book = Book(
                  id: bookId,
                  title: 'خطأ في تحميل الكتاب',
                  author: 'غير معروف',
                  description: '',
                  coverUrl: '',
                  category: '',
                  pdfUrl: '',
                  tags: [],
                );

                item = book;
                title = book.title;
                subtitle = book.author;
                debugPrint('تم إنشاء كتاب افتراضي بسبب خطأ: $e');
              }
              break;
            case 'poem':
              try {
                // Usar el método centralizado para verificar si es un ID problemático
                String poemIdStr = favorite.itemId.toString();
                bool isProblemId = LoadPoemHelper.isProblemId(poemIdStr);

                // Si es un ID problemático, crear un poema especial
                if (isProblemId) {
                  debugPrint('ID problemático detectado: ${favorite.itemId}');
                  // Usar el método centralizado para crear un poema especial
                  final poem = LoadPoemHelper.createSpecialPoem(poemIdStr);
                  item = poem;
                  title = poem.title;
                  subtitle = poem.poet;
                  debugPrint(
                      'تم إنشاء قصيدة خاصة للمعرف المشكل: ${favorite.itemId}');
                } else {
                  // Intentar cargar el poema normalmente
                  final db = await _databaseHelper.database;
                  final List<Map<String, dynamic>> poemMaps = await db.query(
                    'poems',
                    where: 'id = ?',
                    whereArgs: [favorite.itemId],
                  );

                  if (poemMaps.isNotEmpty) {
                    final poem = Poem.fromMap(poemMaps.first);
                    // إضافة حالة المفضلة للقصيدة
                    final poemWithFavorite = poem.copyWith(isFavorite: true);
                    item = poemWithFavorite;
                    title = poemWithFavorite.title;
                    subtitle = poemWithFavorite.poet;
                    debugPrint(
                        'تم تحميل القصيدة المفضلة: ${poemWithFavorite.title}');
                  } else {
                    debugPrint(
                        'لم يتم العثور على القصيدة بالمعرف: ${favorite.itemId}');

                    // Intentar buscar en todos los poemas
                    final allPoems = await _databaseHelper.getPoems();
                    Poem? matchingPoem;

                    // Buscar por diferentes métodos
                    for (var poem in allPoems) {
                      if (poem.id == favorite.itemId.toString() ||
                          poem.id.hashCode.toString() ==
                              favorite.itemId.toString()) {
                        matchingPoem = poem;
                        break;
                      }
                    }

                    if (matchingPoem != null) {
                      final poemWithFavorite =
                          matchingPoem.copyWith(isFavorite: true);
                      item = poemWithFavorite;
                      title = poemWithFavorite.title;
                      subtitle = poemWithFavorite.poet;
                      debugPrint(
                          'تم العثور على القصيدة بطريقة بديلة: ${poemWithFavorite.title}');
                    } else {
                      // إنشاء قصيدة افتراضية في حالة عدم وجود القصيدة
                      final poem = Poem(
                        id: favorite.itemId.toString(),
                        title: 'قصيدة غير متوفرة (معرف: ${favorite.itemId})',
                        poet: 'غير معروف',
                        content:
                            'لم يتم العثور على محتوى القصيدة. يمكنك إزالتها من المفضلة وإضافتها مرة أخرى لاحقاً.',
                        category: 'مفضلة',
                        era: 'غير معروف',
                        isFavorite: true,
                        // Ensure verses are properly initialized
                        verses: [],
                      );
                      item = poem;
                      title = poem.title;
                      subtitle = poem.poet;
                    }
                  }
                }
              } catch (e) {
                debugPrint('خطأ في تحميل القصيدة المفضلة: $e');
                // إنشاء قصيدة افتراضية في حالة حدوث خطأ
                final poem = Poem(
                  id: favorite.itemId.toString(),
                  title: 'خطأ في تحميل القصيدة (معرف: ${favorite.itemId})',
                  poet: 'غير معروف',
                  content:
                      'حدث خطأ أثناء تحميل القصيدة. يمكنك إزالتها من المفضلة وإضافتها مرة أخرى لاحقاً.',
                  category: 'مفضلة',
                  era: 'غير معروف',
                  isFavorite: true,
                  // Ensure verses are properly initialized
                  verses: [],
                );
                item = poem;
                title = poem.title;
                subtitle = poem.poet;
              }
              break;
            case 'dua':
              // تحميل الأدعية المفضلة
              try {
                // استخدام ملف JSON الأصلي للبحث عن الدعاء
                final String response =
                    await rootBundle.loadString('assets/data/duas.json');
                final data = await json.decode(response);

                // معرف العنصر كنص
                String favoriteIdStr = favorite.itemId.toString();

                // طباعة معلومات التشخيص
                debugPrint('البحث عن دعاء بمعرف: $favoriteIdStr');

                // متغير لتتبع العثور على تطابق
                bool foundMatch = false;
                Dua? matchedDua;
                String categoryName = '';

                // البحث في جميع الفئات
                if (data['categories'] != null) {
                  final categories = List<DuaCategory>.from(data['categories']
                      .map((category) => DuaCategory.fromJson(category)));

                  // دالة مساعدة للبحث في فئة وفئاتها الفرعية
                  void searchInCategory(DuaCategory category) {
                    if (foundMatch) return;

                    // البحث في عناصر الفئة
                    for (var dua in category.items) {
                      if (dua.id == favoriteIdStr) {
                        matchedDua = dua;
                        categoryName = category.name;
                        foundMatch = true;
                        debugPrint(
                            'تم العثور على الدعاء في الفئة: ${category.name}');
                        break;
                      }
                    }

                    // البحث في الفئات الفرعية
                    if (!foundMatch && category.subcategories != null) {
                      for (var subcategory in category.subcategories!) {
                        searchInCategory(subcategory);
                        if (foundMatch) break;
                      }
                    }
                  }

                  // البحث في جميع الفئات
                  for (var category in categories) {
                    searchInCategory(category);
                    if (foundMatch) break;
                  }
                }

                if (foundMatch && matchedDua != null) {
                  // إنشاء عنصر مفضل للدعاء
                  item = matchedDua;

                  // استخدام ! للتأكيد على أن matchedDua ليس null
                  final String duaText = matchedDua!.text;
                  title = duaText.length > 50
                      ? '${duaText.substring(0, 50)}...'
                      : duaText;
                  subtitle = categoryName;
                  debugPrint('تم تحميل الدعاء المفضل بنجاح');
                } else {
                  // إنشاء دعاء افتراضي في حالة عدم العثور على الدعاء
                  final dua = Dua(
                    id: favoriteIdStr,
                    text: 'دعاء غير متوفر (معرف: $favoriteIdStr)',
                    isFavorite: true,
                  );
                  item = dua;
                  title = dua.text;
                  subtitle = 'فئة غير معروفة';
                  debugPrint(
                      'تم إنشاء دعاء افتراضي لعدم العثور على الدعاء الأصلي');
                }
              } catch (e) {
                debugPrint('خطأ في تحميل الدعاء المفضل: $e');
                // إنشاء دعاء افتراضي في حالة حدوث خطأ
                final dua = Dua(
                  id: favorite.itemId.toString(),
                  text: 'خطأ في تحميل الدعاء',
                  isFavorite: true,
                );
                item = dua;
                title = dua.text;
                subtitle = 'فئة غير معروفة';
              }
              break;

            case 'prophet_prayer':
              // تحميل الصلوات على النبي المفضلة
              try {
                // استخدام ملف JSON الأصلي للبحث عن الصلاة على النبي
                final String response = await rootBundle
                    .loadString('assets/data/prophet_prayers.json');
                final data = await json.decode(response);

                // معرف العنصر كنص
                String favoriteIdStr = favorite.itemId.toString();

                // طباعة معلومات التشخيص
                debugPrint('البحث عن صلاة على النبي بمعرف: $favoriteIdStr');

                // متغير لتتبع العثور على تطابق
                bool foundMatch = false;
                Dua? matchedPrayer;
                String categoryName = '';

                // البحث في جميع الفئات
                if (data['categories'] != null) {
                  final categories = List<DuaCategory>.from(data['categories']
                      .map((category) => DuaCategory.fromJson(category)));

                  // دالة مساعدة للبحث في فئة وفئاتها الفرعية
                  void searchInCategory(DuaCategory category) {
                    if (foundMatch) return;

                    // البحث في عناصر الفئة
                    for (var prayer in category.items) {
                      if (prayer.id == favoriteIdStr) {
                        matchedPrayer = prayer;
                        categoryName = category.name;
                        foundMatch = true;
                        debugPrint(
                            'تم العثور على الصلاة في الفئة: ${category.name}');
                        break;
                      }
                    }

                    // البحث في الفئات الفرعية
                    if (!foundMatch && category.subcategories != null) {
                      for (var subcategory in category.subcategories!) {
                        searchInCategory(subcategory);
                        if (foundMatch) break;
                      }
                    }
                  }

                  // البحث في جميع الفئات
                  for (var category in categories) {
                    searchInCategory(category);
                    if (foundMatch) break;
                  }
                }

                if (foundMatch && matchedPrayer != null) {
                  // إنشاء عنصر مفضل للصلاة على النبي
                  item = matchedPrayer;

                  // استخدام ! للتأكيد على أن matchedPrayer ليس null
                  final String prayerText = matchedPrayer!.text;
                  title = prayerText.length > 50
                      ? '${prayerText.substring(0, 50)}...'
                      : prayerText;
                  subtitle = categoryName;
                  debugPrint('تم تحميل الصلاة على النبي المفضلة بنجاح');
                } else {
                  // إنشاء صلاة افتراضية في حالة عدم العثور على الصلاة
                  final prayer = Dua(
                    id: favoriteIdStr,
                    text: 'صلاة على النبي غير متوفرة (معرف: $favoriteIdStr)',
                    isFavorite: true,
                  );
                  item = prayer;
                  title = prayer.text;
                  subtitle = 'صيغ الصلاة على النبي';
                  debugPrint(
                      'تم إنشاء صلاة افتراضية لعدم العثور على الصلاة الأصلية');
                }
              } catch (e) {
                debugPrint('خطأ في تحميل الصلاة على النبي المفضلة: $e');
                // إنشاء صلاة افتراضية في حالة حدوث خطأ
                final prayer = Dua(
                  id: favorite.itemId.toString(),
                  text: 'خطأ في تحميل الصلاة على النبي',
                  isFavorite: true,
                );
                item = prayer;
                title = prayer.text;
                subtitle = 'صيغ الصلاة على النبي';
              }
              break;

            case 'azkar':
              // تحسين البحث عن الأذكار
              try {
                // استخدام ملف JSON الأصلي للبحث عن الذكر
                final String response =
                    await rootBundle.loadString('assets/data/azkar.json');
                final data = await json.decode(response);

                // معرف العنصر كنص
                String favoriteIdStr = favorite.itemId.toString();

                // طباعة معلومات التشخيص
                debugPrint('البحث عن ذكر بمعرف: $favoriteIdStr');

                // متغير لتتبع العثور على تطابق
                bool foundMatch = false;
                Zikr? matchedZikr;

                if (data['categories'] != null) {
                  final categories = List<Zikr>.from(data['categories']
                      .map((category) => Zikr.fromJson(category)));

                  // البحث في جميع الفئات
                  for (var category in categories) {
                    // البحث في عناصر الفئة
                    if (category.items != null) {
                      for (var zikrItem in category.items!) {
                        // مقارنة المعرف المباشر أو hashCode
                        if (zikrItem.id == favoriteIdStr ||
                            zikrItem.id.hashCode.toString() == favoriteIdStr) {
                          matchedZikr = Zikr(
                            id: zikrItem.id,
                            name: category.name,
                            description: zikrItem.text,
                            count: zikrItem.count,
                            items: [zikrItem],
                          );
                          foundMatch = true;
                          debugPrint(
                              'تم العثور على الذكر في الفئة: ${category.name}');
                          break;
                        }
                      }
                    }

                    if (foundMatch) break;

                    // البحث في الفئات الفرعية
                    if (!foundMatch && category.subcategories != null) {
                      for (var subcat in category.subcategories!) {
                        if (subcat.items != null) {
                          for (var zikrItem in subcat.items!) {
                            // مقارنة المعرف المباشر أو hashCode
                            if (zikrItem.id == favoriteIdStr ||
                                zikrItem.id.hashCode.toString() ==
                                    favoriteIdStr) {
                              matchedZikr = Zikr(
                                id: zikrItem.id,
                                name: '${category.name} - ${subcat.name}',
                                description: zikrItem.text,
                                count: zikrItem.count,
                                items: [zikrItem],
                              );
                              foundMatch = true;
                              debugPrint(
                                  'تم العثور على الذكر في الفئة الفرعية: ${subcat.name}');
                              break;
                            }
                          }
                        }
                        if (foundMatch) break;
                      }
                    }

                    if (foundMatch) break;
                  }
                }

                // التحقق من SharedPreferences إذا لم يتم العثور على تطابق
                if (!foundMatch) {
                  // البحث في قائمة المفضلة من SharedPreferences
                  for (var prefId in sharedPrefsFavorites) {
                    if (prefId == favoriteIdStr ||
                        prefId.hashCode.toString() == favoriteIdStr) {
                      // البحث مرة أخرى في ملف JSON
                      for (var category in List<Zikr>.from(data['categories']
                          .map((category) => Zikr.fromJson(category)))) {
                        // البحث في عناصر الفئة
                        if (category.items != null) {
                          for (var zikrItem in category.items!) {
                            if (zikrItem.id == prefId) {
                              matchedZikr = Zikr(
                                id: zikrItem.id,
                                name: category.name,
                                description: zikrItem.text,
                                count: zikrItem.count,
                                items: [zikrItem],
                              );
                              foundMatch = true;
                              debugPrint(
                                  'تم العثور على الذكر في SharedPreferences: $prefId');
                              break;
                            }
                          }
                        }

                        if (foundMatch) break;

                        // البحث في الفئات الفرعية
                        if (!foundMatch && category.subcategories != null) {
                          for (var subcat in category.subcategories!) {
                            if (subcat.items != null) {
                              for (var zikrItem in subcat.items!) {
                                if (zikrItem.id == prefId) {
                                  matchedZikr = Zikr(
                                    id: zikrItem.id,
                                    name: '${category.name} - ${subcat.name}',
                                    description: zikrItem.text,
                                    count: zikrItem.count,
                                    items: [zikrItem],
                                  );
                                  foundMatch = true;
                                  debugPrint(
                                      'تم العثور على الذكر في الفئة الفرعية من SharedPreferences');
                                  break;
                                }
                              }
                            }
                            if (foundMatch) break;
                          }
                        }

                        if (foundMatch) break;
                      }
                    }
                    if (foundMatch) break;
                  }
                }

                // إنشاء عنصر AzkarItem إذا تم العثور على تطابق
                if (matchedZikr != null) {
                  item = AzkarItem(
                    id: matchedZikr.id,
                    category: matchedZikr.name,
                    content: matchedZikr.description,
                    count: matchedZikr.count,
                  );
                  title = matchedZikr.description;
                  subtitle = matchedZikr.name;
                  debugPrint('تم إنشاء عنصر AzkarItem: ${matchedZikr.id}');
                } else {
                  // حالة عدم وجود ذكر مطابق
                  debugPrint('لم يتم العثور على ذكر بالمعرف: $favoriteIdStr');
                  item = AzkarItem(
                    id: favoriteIdStr,
                    category: 'أذكار',
                    content: 'هذا الذكر غير متوفر',
                    count: 1,
                  );
                  title = 'ذكر غير متوفر';
                  subtitle = 'أذكار';
                }
              } catch (e) {
                debugPrint('خطأ في البحث عن الذكر: $e');
                // إنشاء عنصر افتراضي في حالة حدوث خطأ
                item = AzkarItem(
                  id: favorite.itemId.toString(),
                  category: 'أذكار',
                  content: 'حدث خطأ أثناء تحميل الذكر',
                  count: 1,
                );
                title = 'خطأ في تحميل الذكر';
                subtitle = 'أذكار';
              }
              break;
          }
        } catch (e) {
          debugPrint('خطأ في معالجة العنصر المفضل: $e');
          // إنشاء عنصر افتراضي بناءً على نوع العنصر
          switch (favorite.type) {
            case 'book':
              item = Book(
                id: 0,
                title: 'خطأ في تحميل الكتاب',
                author: 'غير معروف',
                description: '',
                coverUrl: '',
                category: '',
                pdfUrl: '',
                tags: [],
              );
              title = 'خطأ في تحميل الكتاب';
              subtitle = 'غير معروف';
              break;
            case 'poem':
              item = Poem(
                id: favorite.itemId
                    .toString(), // Usar el ID original en lugar de '0'
                title: 'خطأ في تحميل القصيدة',
                poet: 'غير معروف',
                content: '',
                category: 'مفضلة',
                era: 'غير معروف',
                isFavorite: true,
                // Ensure verses are properly initialized
                verses: [],
              );
              title = 'خطأ في تحميل القصيدة';
              subtitle = 'غير معروف';
              break;
            case 'azkar':
              item = AzkarItem(
                id: favorite.itemId.toString(),
                category: 'أذكار',
                content: 'خطأ في تحميل الذكر',
                count: 1,
              );
              title = 'خطأ في تحميل الذكر';
              subtitle = 'أذكار';
              break;
            default:
              // لا نضيف عنصرًا للأنواع غير المعروفة
              debugPrint('نوع غير معروف: ${favorite.type}');
              break;
          }
        }

        // إضافة العنصر إلى القائمة إذا كان موجودًا
        if (item != null) {
          items.add(FavoriteItem(
            id: favorite.itemId.toString(),
            title: title,
            subtitle: subtitle,
            type: favorite.type,
            item: item,
            addedDate: favorite.addedDate,
          ));
        }
      }

      // إضافة العناصر من SharedPreferences التي لم تكن في قاعدة البيانات
      if (sharedPrefsFavorites.isNotEmpty) {
        debugPrint('إضافة عناصر من SharedPreferences');

        try {
          // استخدام ملف JSON الأصلي للبحث عن الذكر
          final String response =
              await rootBundle.loadString('assets/data/azkar.json');
          final data = await json.decode(response);
          List<Zikr> categories = [];
          try {
            categories = List<Zikr>.from(
                data['categories'].map((category) => Zikr.fromJson(category)));
          } catch (e) {
            debugPrint('خطأ في تحويل بيانات الأذكار: $e');
          }

          for (var prefId in sharedPrefsFavorites) {
            // التحقق مما إذا كان العنصر موجودًا بالفعل في القائمة
            bool alreadyExists = items.any((item) => item.id == prefId);
            if (alreadyExists) {
              debugPrint('العنصر $prefId موجود بالفعل في القائمة');
              continue;
            }

            bool foundMatch = false;
            Zikr? matchedZikr;

            // البحث في جميع الفئات
            for (var category in categories) {
              // البحث في عناصر الفئة
              if (category.items != null) {
                for (var zikrItem in category.items!) {
                  if (zikrItem.id == prefId) {
                    matchedZikr = Zikr(
                      id: zikrItem.id,
                      name: category.name,
                      description: zikrItem.text,
                      count: zikrItem.count,
                      items: [zikrItem],
                    );
                    foundMatch = true;
                    break;
                  }
                }
              }

              if (foundMatch) break;

              // البحث في الفئات الفرعية
              if (!foundMatch && category.subcategories != null) {
                for (var subcat in category.subcategories!) {
                  if (subcat.items != null) {
                    for (var zikrItem in subcat.items!) {
                      if (zikrItem.id == prefId) {
                        matchedZikr = Zikr(
                          id: zikrItem.id,
                          name: '${category.name} - ${subcat.name}',
                          description: zikrItem.text,
                          count: zikrItem.count,
                          items: [zikrItem],
                        );
                        foundMatch = true;
                        break;
                      }
                    }
                  }
                  if (foundMatch) break;
                }
              }

              if (foundMatch) break;
            }

            if (matchedZikr != null) {
              final azkarItem = AzkarItem(
                id: matchedZikr.id,
                category: matchedZikr.name,
                content: matchedZikr.description,
                count: matchedZikr.count,
              );

              items.add(FavoriteItem(
                id: prefId,
                title: matchedZikr.description,
                subtitle: matchedZikr.name,
                type: 'azkar',
                item: azkarItem,
                addedDate:
                    DateTime.now(), // استخدام الوقت الحالي كتاريخ افتراضي
              ));

              debugPrint('تمت إضافة عنصر من SharedPreferences: $prefId');
            }
          }
        } catch (e) {
          debugPrint('خطأ في إضافة عناصر من SharedPreferences: $e');
        }
      }

      debugPrint('تم تحميل ${items.length} عنصر مفضل بنجاح');
      return items;
    } catch (e, stackTrace) {
      debugPrint('خطأ في تحميل المفضلة: $e');
      debugPrint('تفاصيل الخطأ: $stackTrace');
      // إرجاع قائمة فارغة بدلاً من رمي استثناء
      return [];
    } finally {
      // التأكد من أن الدالة تعيد دائمًا قائمة حتى في حالة حدوث خطأ غير متوقع
      debugPrint('تم الانتهاء من تنفيذ _fetchFavoriteItems');
    }
  }

  @override
  void dispose() {
    _disposed = true;
    super.dispose();
  }
}
