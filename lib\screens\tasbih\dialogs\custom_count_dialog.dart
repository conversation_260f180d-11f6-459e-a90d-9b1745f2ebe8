// حوار اختيار العدد المستهدف

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/tasbih_colors.dart';

Future<void> showCountSelectionDialog(
  BuildContext context, {
  required int targetCount,
  required List<int> availableCounts,
  required ValueChanged<int> onCountSelected,
}) async {
  final theme = Theme.of(context);
  final isDarkMode = theme.brightness == Brightness.dark;

  return showModalBottomSheet(
    context: context,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    backgroundColor: isDarkMode ? TasbihColors.darkCardColor : Colors.white,
    builder: (context) {
      return Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: 4,
              width: 40,
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: Colors.grey[400],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Text(
              'اختر عدد التسبيح',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: TasbihColors.primary,
              ),
            ),
            const SizedBox(height: 25),
            Wrap(
              spacing: 15,
              runSpacing: 15,
              alignment: WrapAlignment.center,
              children: availableCounts.map((count) {
                final isSelected = targetCount == count;
                return InkWell(
                  onTap: () {
                    HapticFeedback.selectionClick();
                    onCountSelected(count);
                    Navigator.pop(context);
                  },
                  borderRadius: BorderRadius.circular(15),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 16),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? TasbihColors.primary
                          : TasbihColors.primary.withAlpha(26),
                      borderRadius: BorderRadius.circular(15),
                      boxShadow: isSelected
                          ? [
                              BoxShadow(
                                color: TasbihColors.primary.withAlpha(77),
                                offset: const Offset(0, 3),
                                blurRadius: 8,
                              ),
                            ]
                          : null,
                    ),
                    child: Text(
                      '$count',
                      style: TextStyle(
                        color: isSelected ? Colors.white : TasbihColors.primary,
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 25),
            ElevatedButton(
              onPressed: () async {
                // حفظ مرجع للسياق قبل العملية غير المتزامنة
                final currentContext = context;
                final result = await showCustomCountDialog(currentContext);
                if (result != null && currentContext.mounted) {
                  onCountSelected(result);
                  Navigator.pop(currentContext);
                }
              },
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(double.infinity, 56),
                backgroundColor:
                    isDarkMode ? Colors.grey[800] : Colors.grey[200],
                foregroundColor: theme.textTheme.bodyLarge?.color,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(
                    color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
                  ),
                ),
              ),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children:  [
                  Icon(Icons.add),
                  SizedBox(width: 10),
                  Text(
                    'تعيين عدد مخصص',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    },
  );
}

Future<int?> showCustomCountDialog(BuildContext context) async {
  final TextEditingController controller = TextEditingController();
  final FocusNode focusNode = FocusNode();

  // استخدام showGeneralDialog بدلاً من showDialog لتجنب مشكلة الـ overflow
  return showGeneralDialog<int>(
    context: context,
    barrierDismissible: true,
    barrierLabel: 'تعيين عدد مخصص',
    transitionDuration: const Duration(milliseconds: 250),
    pageBuilder: (context, animation1, animation2) {
      return Container(); // لن يتم استخدامه
    },
    transitionBuilder: (context, animation, secondaryAnimation, child) {
      // تأثير الظهور
      final curvedAnimation = CurvedAnimation(
        parent: animation,
        curve: Curves.easeInOut,
      );

      return ScaleTransition(
        scale: Tween<double>(begin: 0.8, end: 1.0).animate(curvedAnimation),
        child: FadeTransition(
          opacity: curvedAnimation,
          child: SafeArea(
            child: Center(
              child: Material(
                color: Colors.transparent,
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.9,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Theme.of(context).dialogBackgroundColor,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // العنوان
                      const Text(
                        'تعيين عدد مخصص',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: TasbihColors.primary,
                        ),
                      ),
                      const SizedBox(height: 20),

                      // حقل إدخال العدد
                      TextField(
                        controller: controller,
                        focusNode: focusNode,
                        decoration: InputDecoration(
                          hintText: 'أدخل العدد',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                            borderSide: const BorderSide(
                              color: TasbihColors.primary,
                              width: 2,
                            ),
                          ),
                          // إضافة تعبئة أكبر لتسهيل النقر
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 16,
                          ),
                        ),
                        keyboardType: TextInputType.number,
                        textAlign: TextAlign.center,
                        onEditingComplete: () {
                          // إخفاء لوحة المفاتيح عند الانتهاء من التحرير
                          focusNode.unfocus();

                          // محاولة تعيين العدد
                          final customCount = int.tryParse(controller.text);
                          if (customCount != null && customCount > 0) {
                            Navigator.pop(context, customCount);
                          }
                        },
                      ),
                      const SizedBox(height: 20),

                      // أزرار الإجراءات
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          TextButton(
                            style: TextButton.styleFrom(
                              foregroundColor: Colors.grey[600],
                            ),
                            onPressed: () {
                              // إخفاء لوحة المفاتيح
                              focusNode.unfocus();
                              Navigator.pop(context);
                            },
                            child: const Text('إلغاء'),
                          ),
                          const SizedBox(width: 16),
                          TextButton(
                            style: TextButton.styleFrom(
                              foregroundColor: TasbihColors.primary,
                            ),
                            onPressed: () {
                              // إخفاء لوحة المفاتيح
                              focusNode.unfocus();

                              final customCount = int.tryParse(controller.text);
                              if (customCount != null && customCount > 0) {
                                Navigator.pop(context, customCount);
                              }
                            },
                            child: const Text('تعيين'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      );
    },
  );
}
