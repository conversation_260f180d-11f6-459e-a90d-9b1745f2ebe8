// شاشة تفاصيل الصلاة على النبي بتصميم فاخر

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../models/dua.dart'; // افتراض وجود Dua و DuaCategory
import '../utils/app_colors.dart'; // افتراض وجود AppColors و getProphetPrayersColor
import '../providers/prophet_prayers_provider.dart'; // افتراض وجود ProphetPrayersProvider
import '../widgets/prophet_prayer_header.dart'; // استيراد مكون رأس صفحة تفاصيل الصلاة
import '../widgets/prophet_prayer_list.dart'; // افتراض وجود هذا الويدجت مُحسّن
// import 'dart:math' as math; // إذا احتجنا لـ math.pi

class ProphetPrayerDetailsScreen extends StatefulWidget {
  final DuaCategory category;

  const ProphetPrayerDetailsScreen({
    // استخدام const
    Key? key,
    required this.category,
  }) : super(key: key);

  @override
  State<ProphetPrayerDetailsScreen> createState() =>
      _ProphetPrayerDetailsScreenState();
}

class _ProphetPrayerDetailsScreenState extends State<ProphetPrayerDetailsScreen>
    with TickerProviderStateMixin {
  // استخدام TickerProviderStateMixin لعدة AnimationControllers
  late AnimationController _animationController;
  late AnimationController _fabAnimationController;
  late AnimationController _searchAnimationController;
  late ScrollController _scrollController;
  final TextEditingController _searchController = TextEditingController();
  bool _showScrollToTop = false;
  bool _isSearchVisible = false;
  List<Dua> _prayers = []; // قائمة الصلوات الأصلية
  List<Dua> _filteredPrayers = []; // قائمة الصلوات المفلترة للبحث
  // Eliminado campo _searchQuery no utilizado
  // تعريف FocusNode لحقل البحث
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();

    // تهيئة محرك الرسوم المتحركة الرئيسي - تقليل المدة لتحسين السرعة
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800), // تقليل المدة لتحسين السرعة
      vsync: this,
    );

    // تهيئة محرك الرسوم المتحركة لزر التمرير لأعلى
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200), // تقليل المدة لتحسين السرعة
      vsync: this,
    );

    // تهيئة محرك الرسوم المتحركة لحقل البحث
    _searchAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200), // تقليل المدة لتحسين السرعة
      vsync: this,
    );

    // تهيئة متحكم التمرير
    _scrollController = ScrollController();
    _scrollController.addListener(_scrollListener);

    // إضافة مستمع لحقل البحث (سيتم استخدامه لتأخير البحث)
    _searchController.addListener(_onSearchInputChanged);

    // تحميل الصلوات بعد اكتمال بناء الإطار الأول
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // التأكد من أن الواجهة لا تزال موجودة
        _initializePrayers();
      }
    });
  }

  // دالة منفصلة لتهيئة الصلوات - تم تحسينها للأداء
  Future<void> _initializePrayers() async {
    final prophetPrayersProvider =
        Provider.of<ProphetPrayersProvider>(context, listen: false);

    // التحقق من وجود صلوات في الفئة نفسها - أسرع طريقة
    if (widget.category.items.isNotEmpty) {
      if (mounted) {
        setState(() {
          _prayers = widget.category.items;
          _filteredPrayers = widget.category.items;
        });
        // بدء الرسوم المتحركة بعد تعيين البيانات مباشرة
        _animationController.forward();
      }
      return;
    }

    // محاولة الحصول على الصلوات من مزود البيانات
    final prayersFromCategory =
        prophetPrayersProvider.getPrayersByCategory(widget.category.id);

    if (prayersFromCategory.isNotEmpty) {
      if (mounted) {
        setState(() {
          _prayers = prayersFromCategory;
          _filteredPrayers = prayersFromCategory;
        });
        _animationController.forward();
      }
      return;
    }

    // إذا لم يتم العثور على صلوات، محاولة تحميل البيانات مرة أخرى
    try {
      await prophetPrayersProvider.loadProphetPrayers();

      if (mounted) {
        final updatedPrayers =
            prophetPrayersProvider.getPrayersByCategory(widget.category.id);

        setState(() {
          _prayers = updatedPrayers;
          _filteredPrayers = updatedPrayers;
        });
        _animationController.forward();
      }
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات الصلوات: $e');
      // عرض رسالة خطأ للمستخدم إذا لزم الأمر
    }
  }

  // تحديث نتائج البحث عند تغيير النص (مع تأخير)
  void _onSearchInputChanged() {
    final query = _searchController.text.trim();
    // تحديث _searchQuery فورًا إذا كنت تستخدمه في مكان آخر بشكل مباشر
    // setState(() => _searchQuery = query);

    // تأخير عملية الفلترة لتحسين الأداء أثناء الكتابة
    Future.delayed(const Duration(milliseconds: 300), () {
      if (!mounted || _searchController.text.trim() != query) {
        // إذا تغير النص مرة أخرى خلال فترة التأخير، أو تم إلغاء الواجهة، لا تقم بالفلترة
        return;
      }
      _performSearch(query);
    });
  }

  // دالة منفصلة لتنفيذ عملية البحث الفعلية
  void _performSearch(String query) {
    debugPrint('تنفيذ البحث: "$query"');

    if (query.isEmpty) {
      if (mounted) {
        setState(() {
          _filteredPrayers = List.from(_prayers); // عرض جميع الصلوات
        });
        debugPrint('تم إعادة تعيين القائمة إلى ${_prayers.length} صلاة');
      }
    } else if (query == 'reload') {
      // إعادة تحميل البيانات
      _initializePrayers();
      debugPrint('إعادة تحميل البيانات');
    } else {
      final lowerCaseQuery = query.toLowerCase();
      final results = _prayers.where((prayer) {
        final textMatch = prayer.text.toLowerCase().contains(lowerCaseQuery);
        final sourceMatch =
            prayer.source?.toLowerCase().contains(lowerCaseQuery) ?? false;
        final virtueMatch =
            prayer.virtue?.toLowerCase().contains(lowerCaseQuery) ?? false;
        final explanationMatch =
            prayer.explanation?.toLowerCase().contains(lowerCaseQuery) ?? false;
        return textMatch || sourceMatch || virtueMatch || explanationMatch;
      }).toList();

      if (mounted) {
        setState(() {
          _filteredPrayers = results;
        });
        debugPrint('تم العثور على ${results.length} صلاة تطابق البحث');
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _fabAnimationController.dispose();
    _searchAnimationController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _searchController.removeListener(_onSearchInputChanged);
    _searchController.dispose();
    _searchFocusNode.dispose(); // التخلص من FocusNode
    super.dispose();
  }

  void _scrollListener() {
    // تحسين منطق استدعاء setState لزر التمرير لأعلى
    final shouldShow = _scrollController.offset > 150; // تقليل العتبة
    if (shouldShow != _showScrollToTop) {
      if (mounted) {
        // التأكد من أن الواجهة لا تزال موجودة
        setState(() {
          _showScrollToTop = shouldShow;
        });
        if (shouldShow) {
          _fabAnimationController.forward();
          debugPrint('إظهار زر التمرير لأعلى');
        } else {
          _fabAnimationController.reverse();
          debugPrint('إخفاء زر التمرير لأعلى');
        }
      }
    }

    // إخفاء حقل البحث عند التمرير لأسفل
    // تعديل هذا المنطق ليكون أقل حساسية
    if (_isSearchVisible &&
        _scrollController.offset > 100 &&
        _searchController.text.isEmpty) {
      // إخفاء فقط إذا كان فارغًا
      _toggleSearchVisibility(false); // استخدام دالة مساعدة
      debugPrint('إخفاء حقل البحث بسبب التمرير');
    }
  }

  void _scrollToTop() {
    debugPrint('التمرير إلى الأعلى');
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 500), // تقليل المدة
        curve: Curves.easeOutCubic, // منحنى سلس
      );
      debugPrint('تم التمرير إلى الأعلى');
    } else {
      debugPrint('لا يوجد عملاء للتمرير');
    }
    HapticFeedback.mediumImpact();
  }

  // تبديل حالة البحث (فقط إظهار/إخفاء الواجهة)
  void _toggleSearchVisibility(bool visible) {
    if (_isSearchVisible == visible) {
      return; // لا تفعل شيئًا إذا كانت الحالة كما هي
    }

    if (mounted) {
      setState(() {
        _isSearchVisible = visible;
      });
    }

    if (visible) {
      _searchAnimationController.forward();
      // لا حاجة للتمرير التلقائي هنا، المستخدم هو من يقرر
      // التركيز على حقل البحث
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && _isSearchVisible) _searchFocusNode.requestFocus();
      });
    } else {
      _searchAnimationController.reverse();
      _searchFocusNode.unfocus(); // إلغاء التركيز
      // لا تقم بمسح النص هنا تلقائيًا، دع المستخدم يتحكم
      // إذا كنت تريد مسح النص، قم بذلك بشكل صريح عند الضغط على زر الإغلاق مثلاً
    }
    // HapticFeedback.mediumImpact(); // يمكن إضافته عند الضغط على زر البحث
  }

  // Eliminado método _getAnimation no utilizado

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final prophetPrayersColor = AppColors.getProphetPrayersColor(isDarkMode);

    // استخدام Consumer للوصول إلى Provider فقط عند الحاجة (مثل زر المفضلة للفئة إذا كان موجودًا)
    // بما أن هذه الشاشة لا تحتوي على زر مفضلة للفئة، يمكن استخدام Provider.of بشكل مباشر
    // final prophetPrayersProvider = Provider.of<ProphetPrayersProvider>(context, listen: false);

    return Scaffold(
      // استخدام لون خلفية أكثر اتساقًا
      backgroundColor: isDarkMode ? const Color(0xFF1A1A1A) : Colors.grey[50],
      body: SafeArea(
        // استخدام SafeArea لحماية المحتوى من مناطق النظام
        child: Column(
          children: [
            // شريط العنوان المخصص (محسن)
            _buildCustomAppBar(prophetPrayersColor,
                isDarkMode), // استخراج AppBar إلى دالة مساعدة

            // حقل البحث (محسن)
            _buildSearchField(prophetPrayersColor,
                isDarkMode), // استخراج حقل البحث إلى دالة مساعدة

            // محتوى الصفحة (قائمة الصلوات)
            Expanded(
              child: Stack(
                // استخدام Stack للخلفية المتدرجة إذا لزم الأمر
                children: [
                  // خلفية متدرجة خفيفة (اختياري، يمكن إزالتها إذا كانت تسبب بطء)
                  // إذا كانت الخلفية ثابتة، يمكن وضعها مباشرة في Scaffold.backgroundColor
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            isDarkMode
                                ? Colors.grey[900]!.withValues(alpha: 0.5)
                                : Colors.white,
                            isDarkMode
                                ? Colors.black.withValues(alpha: 0.7)
                                : prophetPrayersColor.withValues(alpha: 0.04),
                          ],
                          stops: const [0.5, 1.0], // تعديل التوقفات
                        ),
                      ),
                    ),
                  ),

                  // المحتوى القابل للتمرير مع الرأس
                  CustomScrollView(
                    controller: _scrollController,
                    physics: const BouncingScrollPhysics(),
                    slivers: [
                      // رأس صفحة تفاصيل الصلاة (مكون فاخر)
                      SliverToBoxAdapter(
                        child: ProphetPrayerHeader(
                          category: widget.category,
                          animation: _animationController,
                        ),
                      ),

                      // قائمة الصلوات
                      SliverToBoxAdapter(
                        child: ProphetPrayerList(
                          // استخدام الويدجت مباشرة
                          prayers: _filteredPrayers, // استخدام القائمة المفلترة
                          animation:
                              _animationController, // تمرير AnimationController
                          searchController: _isSearchVisible
                              ? _searchController
                              : null, // تمرير متحكم البحث إذا كان مرئيًا
                          onSearchChanged: (query) {
                            // إعادة تحميل البيانات إذا كان query هو 'reload'
                            if (query == 'reload') {
                              _initializePrayers();
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      // زر التمرير لأعلى (محسن)
      floatingActionButtonLocation:
          FloatingActionButtonLocation.startFloat, // لـ RTL
      floatingActionButton: ScaleTransition(
        // استخدام ScaleTransition مع _fabAnimationController
        scale: _fabAnimationController,
        child: AnimatedOpacity(
          // تأثير ظهور واختفاء سلس
          duration: const Duration(milliseconds: 200), // تقليل المدة
          opacity: _showScrollToTop ? 1.0 : 0.0,
          child: FloatingActionButton(
            onPressed: _scrollToTop,
            backgroundColor: prophetPrayersColor,
            elevation: 3, // تقليل الظل
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16)), // const
            mini: true, // زر أصغر
            tooltip: 'العودة للأعلى', // إضافة tooltip
            child: const Icon(Icons.keyboard_arrow_up,
                color: Colors.white, size: 24), // const
          ),
        ),
      ),
    );
  }

  // --- دوال مساعدة لبناء أجزاء الواجهة ---

  Widget _buildCustomAppBar(Color prophetPrayersColor, bool isDarkMode) {
    // استخدام const حيثما أمكن
    const appBarPadding =
        EdgeInsets.symmetric(horizontal: 12, vertical: 10); // تعديل padding
    // Eliminadas variables no utilizadas iconButtonPadding e iconSize

    return Container(
      // استخدام Container بدلاً من AnimatedContainer إذا لم تكن هناك تغييرات متحركة في الديكور
      padding: appBarPadding,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            prophetPrayersColor,
            prophetPrayersColor.withValues(alpha: 0.8)
          ], // تعديل التدرج
        ),
        boxShadow: [
          // استخدام const للظل
          BoxShadow(
            color: prophetPrayersColor.withValues(alpha: 0.25), // تعديل
            blurRadius: 6, // تعديل
            offset: const Offset(0, 1), // تعديل
          ),
        ],
      ),
      child: Row(
        children: [
          // زر الرجوع
          _buildAppBarButton(
            // استخدام دالة مساعدة لزر AppBar
            icon: Icons.arrow_back_ios_new, // أيقونة أوضح
            tooltip: 'رجوع',
            onPressed: () {
              Navigator.pop(context);
              HapticFeedback.mediumImpact();
            },
            isDarkMode: isDarkMode,
          ),
          // عنوان الصفحة
          Expanded(
            child: Center(
              child: Text(
                widget.category.name,
                style: const TextStyle(
                  // const
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                overflow: TextOverflow.ellipsis, // لمنع تجاوز النص
              ),
            ),
          ),
          // زر البحث
          _buildAppBarButton(
            icon: _isSearchVisible
                ? Icons.close
                : Icons.search_rounded, // أيقونة أوضح
            tooltip: _isSearchVisible ? 'إغلاق البحث' : 'بحث',
            onPressed: () =>
                _toggleSearchVisibility(!_isSearchVisible), // تبديل حالة البحث
            isDarkMode: isDarkMode,
          ),
        ],
      ),
    );
  }

  Widget _buildAppBarButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
    required bool isDarkMode,
  }) {
    return Material(
      // لـ InkWell
      color: Colors.transparent,
      borderRadius: BorderRadius.circular(12), // const
      clipBehavior: Clip.antiAlias, // const
      child: InkWell(
        onTap: onPressed,
        // borderRadius: BorderRadius.circular(12), // تم نقله إلى Material
        splashColor: Colors.white.withValues(alpha: 0.2), // تعديل
        highlightColor: Colors.white.withValues(alpha: 0.1), // تعديل
        child: Tooltip(
          // إضافة Tooltip
          message: tooltip,
          child: Padding(
            padding: const EdgeInsets.all(10.0), // const, padding موحد
            child:
                Icon(icon, color: Colors.white, size: 22.0), // const, حجم موحد
          ),
        ),
      ),
    );
  }

  Widget _buildSearchField(Color prophetPrayersColor, bool isDarkMode) {
    return SizeTransition(
      // استخدام SizeTransition مع _searchAnimationController
      sizeFactor: CurvedAnimation(
          parent: _searchAnimationController, curve: Curves.fastOutSlowIn),
      axisAlignment: -1.0, // للرسوم المتحركة من الأعلى للأسفل
      child: AnimatedOpacity(
        // تأثير تلاشي إضافي
        duration: const Duration(milliseconds: 200), // تزامن مع SizeTransition
        opacity: _isSearchVisible ? 1.0 : 0.0,
        child: Container(
          padding: const EdgeInsets.symmetric(
              horizontal: 16, vertical: 10), // تعديل padding
          decoration: BoxDecoration(
            color: isDarkMode ? Colors.grey[800] : Colors.grey[100],
            boxShadow: _isSearchVisible
                ? const [
                    // const
                    BoxShadow(
                      color: Colors.black12, // تعديل لون الظل
                      blurRadius: 3, // تعديل قوة البلور
                      offset: Offset(0, 1), // تعديل الإزاحة
                    ),
                  ]
                : [],
          ),
          child: TextField(
            controller: _searchController,
            focusNode: _searchFocusNode, // استخدام FocusNode المحدد
            // onChanged لا يُستخدم مباشرة هنا، يتم التعامل معه بواسطة المستمع
            decoration: InputDecoration(
              hintText: 'ابحث عن صيغة صلاة...', // نص أوضح
              hintStyle: TextStyle(
                color:
                    isDarkMode ? Colors.grey[500] : Colors.grey[600], // تعديل
                fontSize: 14,
              ),
              prefixIcon: Icon(Icons.search,
                  color: prophetPrayersColor.withValues(alpha: 0.7),
                  size: 20), // تعديل
              // إضافة زر مسح إذا كان النص غير فارغ
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: Icon(Icons.clear_rounded,
                          color:
                              isDarkMode ? Colors.grey[500] : Colors.grey[600],
                          size: 18), // أيقونة أوضح
                      onPressed: () {
                        _searchController.clear(); // مسح النص
                        _performSearch(''); // تحديث النتائج فورًا
                        HapticFeedback.mediumImpact();
                      },
                      splashRadius: 20, // const
                    )
                  : null,
              filled: true,
              fillColor: isDarkMode
                  ? Colors.grey[700]?.withValues(alpha: 0.5)
                  : Colors.white.withValues(alpha: 0.8), // تعديل
              contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16, vertical: 12), // تعديل
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12), // تعديل
                borderSide: BorderSide.none, // إزالة الحدود
              ),
              // لا حاجة لـ enabledBorder و focusedBorder إذا كان border: BorderSide.none
            ),
            style: TextStyle(
                color: isDarkMode ? Colors.white : Colors.black87,
                fontSize: 14), // تعديل
            textDirection: TextDirection.rtl,
            textAlign: TextAlign.right,
            cursorColor: prophetPrayersColor, // لون مؤشر الكتابة
          ),
        ),
      ),
    );
  }
}
