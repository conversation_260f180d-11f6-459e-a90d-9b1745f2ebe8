// دائرة العداد المركزية
import 'package:flutter/material.dart';
import '../utils/tasbih_colors.dart';

class CounterCircle extends StatefulWidget {
  final int count;
  final VoidCallback onTap;
  final AnimationController animationController;

  const CounterCircle({
    Key? key,
    required this.count,
    required this.onTap,
    required this.animationController,
  }) : super(key: key);

  @override
  State<CounterCircle> createState() => _CounterCircleState();
}

class _CounterCircleState extends State<CounterCircle>
    with SingleTickerProviderStateMixin {
  // تغيير إلى SingleTickerProviderStateMixin لتحسين الأداء
  double _scaleValue = 1.0;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  // متغير لتتبع العدد السابق للكشف عن التغييرات
  int _previousCount = 0;

  // تخزين قيم مسبقة للألوان والتدرجات
  late Color _primaryColor;
  late Color _primaryLightColor;
  late Color _primaryDarkColor;
  late RadialGradient _circleGradient;
  late LinearGradient _innerGradient;

  @override
  void initState() {
    super.initState();

    // تهيئة الألوان مسبقاً
    _primaryColor = TasbihColors.primary;
    _primaryLightColor = Color.lerp(_primaryColor, Colors.white, 0.4)!;
    _primaryDarkColor = Color.lerp(_primaryColor, Colors.black, 0.3)!;

    // تهيئة التدرجات مسبقاً
    _circleGradient = RadialGradient(
      center: const Alignment(-0.2, -0.3),
      radius: 1.0,
      colors: [
        _primaryLightColor,
        _primaryColor,
        _primaryDarkColor,
      ],
      stops: const [0.0, 0.5, 1.0],
    );

    _innerGradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        _primaryColor.withAlpha(200),
        _primaryColor.withAlpha(30),
      ],
    );

    // تهيئة متحكم النبض - استخدام متحكم واحد فقط لتحسين الأداء
    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    );

    _pulseAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.05)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 50,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.05, end: 1.0)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 50,
      ),
    ]).animate(_pulseController);

    // تشغيل الرسوم المتحركة بشكل مستمر
    _pulseController.repeat();

    // تخزين العدد الحالي
    _previousCount = widget.count;
  }

  @override
  void didUpdateWidget(CounterCircle oldWidget) {
    super.didUpdateWidget(oldWidget);

    // إذا تغير العدد، نقوم بإعادة تشغيل الرسوم المتحركة
    if (widget.count != _previousCount) {
      _previousCount = widget.count;

      // إعادة تشغيل الرسوم المتحركة للتأكيد على التغيير
      _pulseController.forward(from: 0.0);
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size.width * 0.35;

    return GestureDetector(
      onTapDown: (_) {
        setState(() {
          _scaleValue = 0.92; // تقليص عند الضغط
        });
      },
      onTapUp: (_) {
        setState(() {
          _scaleValue = 1.0;
        });
        widget.onTap();
      },
      onTapCancel: () {
        setState(() {
          _scaleValue = 1.0;
        });
      },
      child: AnimatedBuilder(
        animation: _pulseController,
        builder: (context, child) {
          return AnimatedScale(
            scale: _scaleValue * _pulseAnimation.value,
            duration: const Duration(milliseconds: 150),
            child: Stack(
              alignment: Alignment.center,
              children: [
                // الدائرة الخارجية المتوهجة
                Container(
                  width: size + 15,
                  height: size + 15,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        _primaryColor.withAlpha(120),
                        _primaryColor.withAlpha(0),
                      ],
                      stops: const [0.6, 1.0],
                    ),
                  ),
                ),

                // الدائرة الرئيسية
                Container(
                  width: size,
                  height: size,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      center: const Alignment(-0.2, -0.3),
                      radius: 1.0,
                      colors: [
                        _primaryLightColor,
                        _primaryColor,
                        _primaryDarkColor,
                      ],
                      stops: const [0.0, 0.5, 1.0],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: _primaryColor.withAlpha(150),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                      // ظل ثانوي للعمق
                      BoxShadow(
                        color: Colors.black.withAlpha(70),
                        blurRadius: 10,
                        spreadRadius: 1,
                        offset: const Offset(0, 3),
                      ),
                    ],
                    border: Border.all(
                      color: Colors.white.withAlpha(40),
                      width: 1.5,
                    ),
                  ),
                ),

                // الحلقة الداخلية
                Container(
                  width: size * 0.85,
                  height: size * 0.85,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        _primaryColor.withAlpha(200),
                        _primaryColor.withAlpha(30),
                      ],
                    ),
                    border: Border.all(
                      color: Colors.white.withAlpha(80),
                      width: 2.0,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: _primaryColor.withAlpha(100),
                        blurRadius: 8,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                ),

                // العداد مع تأثيرات
                AnimatedBuilder(
                  animation: widget.animationController,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: 1.0 + (widget.animationController.value * 0.3),
                      child: Opacity(
                        opacity: 1.0 - (widget.animationController.value * 0.5),
                        child: child,
                      ),
                    );
                  },
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // ظل خلفي للنص
                      Text(
                        '${widget.count}',
                        style:
                            Theme.of(context).textTheme.displayMedium?.copyWith(
                                  color: Colors.black.withAlpha(40),
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      // النص الرئيسي
                      Text(
                        '${widget.count}',
                        style:
                            Theme.of(context).textTheme.displayMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          shadows: [
                            Shadow(
                              color: Colors.black.withAlpha(150),
                              offset: const Offset(0, 2),
                              blurRadius: 6,
                            ),
                            Shadow(
                              color: _primaryColor.withAlpha(180),
                              offset: const Offset(0, 0),
                              blurRadius: 15,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // تأثير اللمعان العلوي
                Positioned(
                  top: size * 0.2,
                  left: size * 0.2,
                  child: Container(
                    width: size * 0.18,
                    height: size * 0.18,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: RadialGradient(
                        colors: [
                          Colors.white.withAlpha(180),
                          Colors.white.withAlpha(0),
                        ],
                      ),
                    ),
                  ),
                ),

                // تأثير لمعان ثانوي أصغر
                Positioned(
                  top: size * 0.3,
                  left: size * 0.6,
                  child: Container(
                    width: size * 0.08,
                    height: size * 0.08,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: RadialGradient(
                        colors: [
                          Colors.white.withAlpha(150),
                          Colors.white.withAlpha(0),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
