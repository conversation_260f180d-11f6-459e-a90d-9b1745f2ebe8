import 'package:flutter/material.dart';
import '../../utils/app_colors.dart';
import '../../utils/responsive_helper.dart';

class BooksAppBar extends StatelessWidget {
  final String title;
  final bool gridViewMode;
  final VoidCallback onToggleViewMode;
  final VoidCallback onRefresh;
  final VoidCallback? onResetDatabase;
  final Animation<double> animation;
  final Animation<double> refreshAnimation;

  const BooksAppBar({
    Key? key,
    required this.title,
    required this.gridViewMode,
    required this.onToggleViewMode,
    required this.onRefresh,
    this.onResetDatabase,
    required this.animation,
    required this.refreshAnimation,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isTablet = ResponsiveHelper.isTablet(context);
    final titleFontSize = isTablet ? 24.0 : 20.0;
    final iconSize = isTablet ? 28.0 : 24.0;
    final padding = isTablet ? 20.0 : 16.0;

    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0, -1),
        end: Offset.zero,
      ).animate(animation),
      child: FadeTransition(
        opacity: animation,
        child: Container(
          padding: EdgeInsets.fromLTRB(padding, padding, padding, padding / 2),
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            boxShadow: [
              BoxShadow(
                color: AppColors.booksColor.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 3),
              ),
            ],
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(20),
              bottomRight: Radius.circular(20),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                    width: 4,
                    height: 24,
                    decoration: BoxDecoration(
                      color: AppColors.booksColor,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    title,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontSize: titleFontSize,
                          fontWeight: FontWeight.bold,
                          color: AppColors.booksColor,
                        ),
                  ),
                ],
              ),
              Row(
                children: [
                  // زر تبديل نمط العرض
                  ScaleTransition(
                    scale: refreshAnimation,
                    child: IconButton(
                      icon: Icon(
                        gridViewMode ? Icons.view_list : Icons.grid_view,
                        color: AppColors.booksColor,
                        size: iconSize,
                      ),
                      onPressed: onToggleViewMode,
                      tooltip: gridViewMode ? 'عرض قائمة' : 'عرض شبكة',
                    ),
                  ),
                  // زر التحديث
                  RotationTransition(
                    turns: Tween<double>(
                      begin: 0.0,
                      end: 1.0,
                    ).animate(refreshAnimation),
                    child: IconButton(
                      icon: Icon(
                        Icons.refresh,
                        color: AppColors.booksColor,
                        size: iconSize,
                      ),
                      onPressed: onRefresh,
                      tooltip: 'تحديث',
                    ),
                  ),
                  // زر إعادة تعيين قاعدة البيانات
                  if (onResetDatabase != null)
                    IconButton(
                      icon: Icon(
                        Icons.settings_backup_restore,
                        color: AppColors.booksColor,
                        size: iconSize,
                      ),
                      onPressed: onResetDatabase,
                      tooltip: 'إعادة تعيين قاعدة البيانات',
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
