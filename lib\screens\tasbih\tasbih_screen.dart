// lib/screens/tasbih/tasbih_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'components/bead.dart';
import 'components/counter_circle.dart';
import 'components/info_card.dart';
import 'components/dhikr_selector.dart';
import 'components/count_selector.dart';
import 'dialogs/custom_count_dialog.dart';
import 'dialogs/settings_dialog.dart';
import 'dialogs/add_dhikr_dialog.dart' show showAddDhikrDialog;
import 'dialogs/dhikr_selection_dialog.dart' as dhikr_dialog;
import 'providers/tasbih_provider.dart';
import 'providers/wird_provider.dart';
import 'utils/tasbih_colors.dart';
import '../../utils/constants.dart';
import 'tutorial/tasbih_tutorial_screen.dart';
//import 'models/dhikr_model.dart';

class TasbihScreen extends StatefulWidget {
  const TasbihScreen({Key? key}) : super(key: key);

  @override
  State<TasbihScreen> createState() => _TasbihScreenState();
}

class _TasbihScreenState extends State<TasbihScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late TasbihProvider _tasbihProvider;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // إنشاء مزود المسبحة مرة واحدة
    _tasbihProvider = TasbihProvider();

    // تأخير الاستدعاء لضمان تهيئة البروفايدر بعد بناء الواجهة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeProvider();
    });
  }

  Future<void> _initializeProvider() async {
    await _tasbihProvider.initialize();

    // عرض الشرح التوضيحي للمسبحة فقط عند تثبيت التطبيق لأول مرة
    if (mounted) {
      await TasbihTutorialScreen.showIfFirstTime(context);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tasbihProvider.dispose();
    super.dispose();
  }

  // بناء زر متحرك
  Widget _buildAnimatedButton({
    required IconData icon,
    required VoidCallback onTap,
    required Color color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: TweenAnimationBuilder<double>(
        tween: Tween(begin: 0.0, end: 1.0),
        duration: const Duration(milliseconds: 200),
        builder: (context, value, _) {
          return Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: color.withAlpha(26),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: color.withAlpha(13),
                  offset: const Offset(0, 1),
                  blurRadius: 3,
                ),
              ],
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          );
        },
      ),
    );
  }

  // فتح حوار الإعدادات
  void _openSettingsMenu(BuildContext context, TasbihProvider provider) {
    showSettingsDialog(
      context,
      vibrationEnabled: provider.vibrationEnabled,
      onVibrationToggled: (value) => provider.toggleVibration(value),
      onResetCounter: () => _showResetConfirmDialog(context, provider),
      // تم تعليق زر إشعارات العودة مؤقتاً حتى لا يظهر للمستخدم
      // onOpenNotificationSettings: () => _openNotificationSettings(context),
    );

    // إضافة زر لإعادة تعيين حالة الشرح التوضيحي (للاختبار فقط)
    // يمكن استخدام هذا الكود في وضع المطور لإعادة تعيين حالة الشرح التوضيحي
    // لاختبار ظهور الشرح التوضيحي عند تثبيت التطبيق لأول مرة
    /*
    import 'package:flutter/foundation.dart';

    if (kDebugMode) {
      // إضافة زر إضافي في حوار الإعدادات لإعادة تعيين حالة الشرح التوضيحي
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('وضع المطور'),
          content: const Text('هل تريد إعادة تعيين حالة الشرح التوضيحي للمسبحة؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () async {
                await TasbihTutorialScreen.resetTutorialState();
                if (context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم إعادة تعيين حالة الشرح التوضيحي بنجاح'),
                    ),
                  );
                }
              },
              child: const Text('نعم'),
            ),
          ],
        ),
      );
    }
    */
  }

  // عرض حوار تأكيد إعادة ضبط العداد الحالي
  void _showResetCurrentCountDialog(
      BuildContext context, TasbihProvider provider) {
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: 'حوار',
      pageBuilder: (context, animation1, animation2) => Container(),
      transitionBuilder: (context, a1, a2, widget) {
        final curvedValue = Curves.easeInOutBack.transform(a1.value) - 0.05;
        return Transform.scale(
          scale: curvedValue.clamp(0.0, 1.0),
          child: Opacity(
            opacity: a1.value,
            child: Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              insetPadding: const EdgeInsets.symmetric(horizontal: 25),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'إعادة ضبط العداد',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'هل تريد إعادة ضبط العداد الحالي فقط؟',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ElevatedButton(
                          onPressed: () => Navigator.pop(context),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey.withAlpha(50),
                            foregroundColor:
                                Theme.of(context).textTheme.bodyLarge?.color,
                          ),
                          child: const Text('إلغاء'),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton(
                          onPressed: () {
                            provider.resetCurrentCount();
                            Navigator.pop(context);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: TasbihColors.primary,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('نعم'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
      transitionDuration: const Duration(milliseconds: 300),
    );
  }

  // عرض حوار تأكيد إعادة ضبط جميع البيانات
  void _showResetConfirmDialog(BuildContext context, TasbihProvider provider) {
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: 'حوار',
      pageBuilder: (context, animation1, animation2) => Container(),
      transitionBuilder: (context, a1, a2, widget) {
        final curvedValue = Curves.easeInOutBack.transform(a1.value) - 0.05;
        return Transform.scale(
          scale: curvedValue.clamp(0.0, 1.0),
          child: Opacity(
            opacity: a1.value,
            child: Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              insetPadding: const EdgeInsets.symmetric(horizontal: 25),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'إعادة ضبط البيانات',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.red[700],
                          ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'هل تريد إعادة ضبط جميع بيانات المسبحة؟\nسيتم حذف جميع الإحصائيات والعدادات.',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ElevatedButton(
                          onPressed: () => Navigator.pop(context),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey.withAlpha(50),
                            foregroundColor:
                                Theme.of(context).textTheme.bodyLarge?.color,
                          ),
                          child: const Text('إلغاء'),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton(
                          onPressed: () {
                            provider.resetCounter();
                            Navigator.pop(context);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red[700],
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('نعم'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
      transitionDuration: const Duration(milliseconds: 300),
    );
  }

  // فتح قائمة اختيار الذكر
  void _openDhikrSelection(
      BuildContext context, TasbihProvider provider) async {
    await dhikr_dialog.showDhikrSelectionDialog(
      context,
      dhikrs: provider.availableDhikrs,
      selectedDhikr: provider.selectedDhikr,
      onDhikrSelected: (dhikr) => provider.updateSelectedDhikr(dhikr),
      onUpdateDhikrTarget: (id, count) => provider.updateDhikrTarget(id, count),
      onUpdateDhikrName: (id, name) => provider.updateDhikrName(id, name),
      onAddNew: () async {
        Navigator.pop(context); // إغلاق حوار الاختيار أولاً

        // فتح حوار إضافة ذكر جديد
        final name = await showAddDhikrDialog(context);
        if (name != null && name.isNotEmpty) {
          // إضافة ذكر جديد
          final success =
              await provider.addNewDhikr(name, provider.targetCount);

          if (success && context.mounted) {
            // عرض رسالة نجاح
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم إضافة ذكر "$name" بنجاح'),
                backgroundColor: TasbihColors.primary,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10)),
              ),
            );

            // إعادة فتح حوار اختيار الذكر لعرض الذكر الجديد
            await Future.delayed(const Duration(milliseconds: 500));
            if (context.mounted) {
              _openDhikrSelection(context, provider);
            }
          }
        }
      },
    );
  }

  // فتح قائمة اختيار العدد المستهدف
  void _openCountSelection(BuildContext context, TasbihProvider provider) {
    showCountSelectionDialog(
      context,
      targetCount: provider.targetCount,
      availableCounts: provider.availableCounts,
      onCountSelected: (count) => provider.updateTargetCount(count),
    );
  }

  // إعادة تعيين الأوراد المكتملة بناءً على الوقت والعودة إلى الشاشة السابقة
  void _resetCompletedWirdsAndNavigateBack() {
    // الحصول على مزود الأوراد
    final wirdProvider = Provider.of<WirdProvider>(context, listen: false);

    // حفظ مرجع للسياق قبل العملية غير المتزامنة
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // العودة إلى الشاشة السابقة أولاً
    Navigator.pop(context);

    // ثم إعادة تعيين الأوراد المكتملة بعد العودة
    wirdProvider.resetAllCompletedWirds().then((resetWirdNames) {
      // عرض تنبيه أنيق إذا تم إعادة تعيين أي ورد
      if (resetWirdNames.isNotEmpty) {
        // تأخير قليل لضمان أن الشاشة الرئيسية قد تم تحميلها
        Future.delayed(const Duration(milliseconds: 300), () {
          // استخدام scaffoldMessenger المحفوظ بدلاً من context
          _showResetWirdsNotificationWithMessenger(
              scaffoldMessenger, resetWirdNames);
        });
      }
    });
  }

  // عرض تنبيه أنيق باستخدام ScaffoldMessenger المحفوظ
  static void _showResetWirdsNotificationWithMessenger(
      ScaffoldMessengerState scaffoldMessenger, List<String> resetWirdNames) {
    // إنشاء نص التنبيه
    String message;
    if (resetWirdNames.length == 1) {
      message = 'تم إعادة تعيين الورد "${resetWirdNames[0]}" لبدء يوم جديد';
    } else if (resetWirdNames.length == 2) {
      message =
          'تم إعادة تعيين الوردين "${resetWirdNames[0]}" و "${resetWirdNames[1]}" لبدء يوم جديد';
    } else {
      message = 'تم إعادة تعيين ${resetWirdNames.length} أوراد لبدء يوم جديد';
    }

    // عرض التنبيه
    scaffoldMessenger.showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.refresh_rounded,
              color: Colors.white,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(message),
            ),
          ],
        ),
        backgroundColor: Colors.teal.shade700,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        margin: const EdgeInsets.all(8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        elevation: 4,
        action: SnackBarAction(
          label: 'حسناً',
          textColor: Colors.white,
          onPressed: () {
            scaffoldMessenger.hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _tasbihProvider,
      builder: (context, _) {
        return Consumer<TasbihProvider>(
          builder: (context, provider, _) {
            final theme = Theme.of(context);
            final isDarkMode = theme.brightness == Brightness.dark;
            final size = MediaQuery.of(context).size;

            return Scaffold(
              body: Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  gradient: TasbihColors.getBackgroundGradient(isDarkMode),
                ),
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 16),
                    child: Column(
                      children: [
                        // شريط العنوان
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            _buildAnimatedButton(
                              icon: Icons.arrow_back,
                              onTap: () {
                                // استدعاء وظيفة إعادة تعيين الأوراد المكتملة قبل العودة
                                _resetCompletedWirdsAndNavigateBack();
                              },
                              color: TasbihColors.primary,
                            ),
                            Flexible(
                              child: Text(
                                'المسبحة الإلكترونية',
                                style: theme.textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: TasbihColors.primary,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Wrap(
                              spacing: 8,
                              children: [
                                _buildAnimatedButton(
                                  icon: Icons.bar_chart,
                                  onTap: () => Navigator.pushNamed(
                                      context, AppConstants.tasbihStatsRoute),
                                  color: TasbihColors.secondary,
                                ),
                                _buildAnimatedButton(
                                  icon: Icons.menu_book_outlined,
                                  onTap: () => Navigator.pushNamed(
                                      context, AppConstants.wirdListRoute),
                                  color: TasbihColors.tertiary,
                                ),
                                _buildAnimatedButton(
                                  icon: Icons.settings,
                                  onTap: () =>
                                      _openSettingsMenu(context, provider),
                                  color: TasbihColors.primary,
                                ),
                              ],
                            ),
                          ],
                        ),

                        const SizedBox(height: 20),

                        // صف بطاقات المعلومات
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              Expanded(
                                child: InfoCard(
                                  title: 'الحالي',
                                  value: '${provider.dhikrCount}',
                                  icon: Icons.format_list_numbered,
                                  color: TasbihColors.primary,
                                  isDarkMode: isDarkMode,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: InfoCard(
                                  title: 'الدورات',
                                  value: '${provider.sessionCount}',
                                  icon: Icons.loop,
                                  color: TasbihColors.secondary,
                                  isDarkMode: isDarkMode,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: InfoCard(
                                  title: 'المجموع',
                                  value: '${provider.totalCount}',
                                  icon: Icons.all_inclusive,
                                  color: TasbihColors.tertiary,
                                  isDarkMode: isDarkMode,
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 20),

                        // بطاقات الذكر والعدد المستهدف
                        LayoutBuilder(
                          builder: (context, constraints) {
                            // إذا كانت الشاشة ضيقة جداً، نعرض العناصر في عمود
                            if (constraints.maxWidth < 300) {
                              return Column(
                                children: [
                                  GestureDetector(
                                    onTap: () =>
                                        _openDhikrSelection(context, provider),
                                    child: DhikrSelector(
                                      selectedDhikr: provider.selectedDhikr,
                                      onTap: () => _openDhikrSelection(
                                          context, provider),
                                    ),
                                  ),
                                  const SizedBox(height: 12),
                                  GestureDetector(
                                    onTap: () =>
                                        _openCountSelection(context, provider),
                                    child: CountSelector(
                                      targetCount: provider.targetCount,
                                      onTap: () => _openCountSelection(
                                          context, provider),
                                    ),
                                  ),
                                ],
                              );
                            }

                            // وإلا نعرضها في صف كالمعتاد
                            return Row(
                              children: [
                                Expanded(
                                  child: GestureDetector(
                                    onTap: () =>
                                        _openDhikrSelection(context, provider),
                                    child: DhikrSelector(
                                      selectedDhikr: provider.selectedDhikr,
                                      onTap: () => _openDhikrSelection(
                                          context, provider),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                GestureDetector(
                                  onTap: () =>
                                      _openCountSelection(context, provider),
                                  child: CountSelector(
                                    targetCount: provider.targetCount,
                                    onTap: () =>
                                        _openCountSelection(context, provider),
                                  ),
                                ),
                              ],
                            );
                          },
                        ),

                        const Spacer(),

                        // دائرة خرز المسبحة
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 800),
                          curve: Curves.easeOutBack,
                          height: size.width * 0.8,
                          width: size.width * 0.8,
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              // إضافة تأثير دوران خفيف للدائرة بأكملها عند تغيير العدد المستهدف
                              // تحسين: استخدام AnimatedRotation بدلاً من TweenAnimationBuilder
                              AnimatedRotation(
                                turns: 0,
                                duration: const Duration(milliseconds: 800),
                                curve: Curves.easeOutBack,
                                key: ValueKey<int>(provider.targetCount),
                                child: RepaintBoundary(
                                  child: Stack(
                                    alignment: Alignment.center,
                                    children: [
                                      // تحسين: استخدام SizedBox لتحديد حجم ثابت
                                      SizedBox(
                                        width: size.width * 0.8,
                                        height: size.width * 0.8,
                                        child: Stack(
                                          alignment: Alignment.center,
                                          children: [
                                            // تحسين: استخدام IndexedStack بدلاً من for loop
                                            for (int i = 0;
                                                i < provider.targetCount;
                                                i++)
                                              BeadComponent(
                                                key: ValueKey<String>(
                                                    "bead_${provider.targetCount}_$i"),
                                                index: i,
                                                count: provider.targetCount,
                                                position:
                                                    provider.beadPositions[i],
                                                size: size.width * 0.8,
                                                isDarkMode: isDarkMode,
                                              ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),

                              // تأثير التموج عند الضغط - تحسين الأداء
                              if (provider.isRippleActive)
                                RepaintBoundary(
                                  child: TweenAnimationBuilder<double>(
                                    tween: Tween(begin: 0.0, end: 1.0),
                                    duration: const Duration(
                                        milliseconds: 500), // تقليل مدة التأثير
                                    curve: Curves.easeOut, // تبسيط منحنى الحركة
                                    builder: (context, value, _) {
                                      return Container(
                                        width: size.width *
                                            0.6 *
                                            value, // تقليل حجم التأثير
                                        height: size.width * 0.6 * value,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          // تبسيط التدرج
                                          border: Border.all(
                                            color: TasbihColors.primary
                                                .withAlpha((100 * (1 - value))
                                                    .toInt()),
                                            width: 2,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),

                              // الدائرة الداخلية مع عدد الذكر - تحسين الأداء
                              RepaintBoundary(
                                child: CounterCircle(
                                  count: provider.dhikrCount,
                                  onTap: () {
                                    // تحسين: تبسيط تسلسل الرسوم المتحركة
                                    _animationController.forward(from: 0.0);
                                    provider.incrementCount();
                                  },
                                  animationController: _animationController,
                                ),
                              ),
                            ],
                          ),
                        ),

                        const Spacer(),

                        // زر إعادة ضبط العداد الحالي
                        TextButton.icon(
                          onPressed: () =>
                              _showResetCurrentCountDialog(context, provider),
                          icon: const Icon(
                            Icons.refresh,
                            color: TasbihColors.primary,
                          ),
                          label: Text(
                            'تصفير العداد',
                            style: theme.textTheme.bodyLarge?.copyWith(
                              color: TasbihColors.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 24, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
