import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:share_plus/share_plus.dart';
import '../database/database_helper.dart';
import '../utils/app_colors.dart';

class PdfViewerScreen extends StatefulWidget {
  final String pdfPath;
  final String bookTitle;
  final int? bookId; // إضافة معرف الكتاب لحفظ الإشارات المرجعية

  const PdfViewerScreen({
    Key? key,
    required this.pdfPath,
    required this.bookTitle,
    this.bookId,
  }) : super(key: key);

  @override
  State<PdfViewerScreen> createState() => _PdfViewerScreenState();
}

class _PdfViewerScreenState extends State<PdfViewerScreen> {
  int _totalPages = 0;
  int _currentPage = 0;
  bool _isLoading = true;
  bool _isNightMode = false;
  PDFViewController? _pdfViewController;
  List<Map<String, dynamic>> _bookmarks = [];
  bool _hasLastReadPosition = false;

  @override
  void initState() {
    super.initState();
    _loadBookmarks();
    _checkLastReadPosition();

    // استعادة إعدادات العرض
    _loadViewSettings();
  }

  Future<void> _loadViewSettings() async {
    // يمكن استرجاع الإعدادات من SharedPreferences
  }

  Future<void> _loadBookmarks() async {
    if (widget.bookId != null) {
      try {
        final dbHelper = DatabaseHelper();
        final bookmarks = await dbHelper.getBookmarks(widget.bookId!);
        setState(() {
          _bookmarks = bookmarks;
        });
      } catch (e) {
        debugPrint('Error loading bookmarks: $e');
      }
    }
  }

  Future<void> _checkLastReadPosition() async {
    if (widget.bookId != null) {
      try {
        final dbHelper = DatabaseHelper();
        final lastPosition = await dbHelper.getLastReadPosition(widget.bookId!);
        if (lastPosition != null) {
          setState(() {
            _hasLastReadPosition = true;
          });
        }
      } catch (e) {
        debugPrint('Error checking last read position: $e');
      }
    }
  }

  Future<void> _saveBookmark(String? note) async {
    if (widget.bookId == null) return;

    try {
      final dbHelper = DatabaseHelper();
      await dbHelper.saveBookmark(
        widget.bookId!,
        _currentPage,
        note,
      );

      // تحديث قائمة الإشارات المرجعية
      await _loadBookmarks();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حفظ إشارة مرجعية للصفحة ${_currentPage + 1}'),
            behavior: SnackBarBehavior.floating,
            width: MediaQuery.of(context).size.width * 0.9,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء حفظ الإشارة المرجعية: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _saveLastReadPosition() async {
    if (widget.bookId == null) return;

    try {
      final dbHelper = DatabaseHelper();
      await dbHelper.saveLastReadPosition(widget.bookId!, _currentPage);
    } catch (e) {
      debugPrint('Error saving last read position: $e');
    }
  }

  void _showBookmarkDialog() {
    final TextEditingController noteController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة إشارة مرجعية'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('الصفحة: ${_currentPage + 1}'),
            const SizedBox(height: 16),
            TextField(
              controller: noteController,
              decoration: const InputDecoration(
                labelText: 'ملاحظة (اختياري)',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _saveBookmark(
                  noteController.text.isNotEmpty ? noteController.text : null);
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _showBookmarksDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.3,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) {
          return Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Center(
                  child: Padding(
                    padding: EdgeInsets.only(bottom: 16),
                    child: Text(
                      'الإشارات المرجعية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                if (_bookmarks.isEmpty)
                  const Center(
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 32),
                      child: Text('لا توجد إشارات مرجعية محفوظة'),
                    ),
                  )
                else
                  Expanded(
                    child: ListView.builder(
                      controller: scrollController,
                      itemCount: _bookmarks.length,
                      itemBuilder: (context, index) {
                        final bookmark = _bookmarks[index];
                        final pageNumber = bookmark['page'] as int;
                        final note = bookmark['note'] as String?;
                        final timestamp = DateTime.fromMillisecondsSinceEpoch(
                          bookmark['timestamp'] as int,
                        );

                        return ListTile(
                          leading: CircleAvatar(
                            child: Text('${pageNumber + 1}'),
                          ),
                          title: Text('الصفحة ${pageNumber + 1}'),
                          subtitle: note != null && note.isNotEmpty
                              ? Text(note,
                                  maxLines: 2, overflow: TextOverflow.ellipsis)
                              : Text(
                                  '${timestamp.day}/${timestamp.month}/${timestamp.year} - ${timestamp.hour}:${timestamp.minute}',
                                  style: const TextStyle(fontSize: 12),
                                ),
                          trailing: IconButton(
                            icon: const Icon(Icons.delete_outline),
                            onPressed: () async {
                              Navigator.pop(context);
                              final dbHelper = DatabaseHelper();
                              await dbHelper
                                  .deleteBookmark(bookmark['id'] as int);
                              await _loadBookmarks();
                            },
                          ),
                          onTap: () {
                            Navigator.pop(context);
                            _pdfViewController?.setPage(pageNumber);
                          },
                        );
                      },
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _shareFile() {
    final file = File(widget.pdfPath);
    if (file.existsSync()) {
      Share.shareFiles(
        [widget.pdfPath],
        subject: 'مشاركة كتاب ${widget.bookTitle}',
        text: 'أشارك معك كتاب ${widget.bookTitle} من تطبيق وهج السالك',
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('لا يمكن العثور على ملف PDF')),
      );
    }
  }

  void _toggleNightMode() {
    setState(() {
      _isNightMode = !_isNightMode;
    });

    // تأثير ملموس بسيط
    HapticFeedback.lightImpact();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return WillPopScope(
      onWillPop: () async {
        // حفظ آخر موضع قراءة عند الخروج
        await _saveLastReadPosition();
        return true;
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            widget.bookTitle,
            style: TextStyle(
              color: isDark ? Colors.white : AppColors.booksColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          centerTitle: true,
          backgroundColor: _isNightMode
              ? Colors.black
              : isDark
                  ? theme.appBarTheme.backgroundColor
                  : Colors.white,
          iconTheme: IconThemeData(
            color: _isNightMode
                ? Colors.white
                : isDark
                    ? Colors.white
                    : AppColors.booksColor,
          ),
          elevation: 0,
          actions: [
            IconButton(
              icon:
                  Icon(_isNightMode ? Icons.wb_sunny : Icons.nightlight_round),
              tooltip: _isNightMode ? 'وضع النهار' : 'وضع الليل',
              onPressed: _toggleNightMode,
            ),
            IconButton(
              icon: const Icon(Icons.bookmark_border),
              tooltip: 'الإشارات المرجعية',
              onPressed: _showBookmarksDialog,
            ),
            IconButton(
              icon: const Icon(Icons.share),
              tooltip: 'مشاركة',
              onPressed: _shareFile,
            ),
            IconButton(
              icon: const Icon(Icons.info_outline),
              tooltip: 'تفاصيل الكتاب',
              onPressed: () {
                Navigator.pop(context, {
                  'lastPage': _currentPage,
                  'totalPages': _totalPages,
                  'bookmarks': _bookmarks.length,
                });
              },
            ),
          ],
        ),
        body: Stack(
          children: [
            Container(
              color:
                  _isNightMode ? Colors.black : theme.scaffoldBackgroundColor,
              child: PDFView(
                filePath: widget.pdfPath,
                enableSwipe: true,
                swipeHorizontal: true,
                autoSpacing: false,
                pageFling: true,
                pageSnap: true,
                defaultPage: _currentPage,
                fitPolicy: FitPolicy.BOTH,
                preventLinkNavigation: false,
                nightMode: _isNightMode,
                onRender: (pages) {
                  setState(() {
                    _totalPages = pages!;
                    _isLoading = false;
                  });

                  // اذا كان هناك موضع قراءة سابق، انتقل إليه
                  if (_hasLastReadPosition) {
                    _checkLastReadPosition().then((_) {
                      if (_hasLastReadPosition && widget.bookId != null) {
                        Future.delayed(const Duration(milliseconds: 300), () {
                          DatabaseHelper()
                              .getLastReadPosition(widget.bookId!)
                              .then((position) {
                            if (position != null) {
                              _pdfViewController?.setPage(position);
                            }
                          });
                        });
                      }
                    });
                  }
                },
                onError: (error) {
                  setState(() {
                    _isLoading = false;
                  });
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('خطأ: $error')),
                  );
                },
                onPageChanged: (page, total) {
                  setState(() {
                    _currentPage = page!;
                  });
                },
                onViewCreated: (PDFViewController pdfViewController) {
                  _pdfViewController = pdfViewController;
                },
              ),
            ),
            _isLoading
                ? Center(
                    child: const CircularProgressIndicator(
                      color: AppColors.booksColor,
                    ),
                  )
                : const SizedBox.shrink(),
          ],
        ),
        floatingActionButton: _isLoading
            ? null
            : FloatingActionButton(
                onPressed: _showBookmarkDialog,
                tooltip: 'إضافة إشارة مرجعية',
                backgroundColor: AppColors.booksColor,
                child: const Icon(Icons.bookmark_add),
              ),
        bottomNavigationBar: _isLoading
            ? null
            : BottomAppBar(
                color: _isNightMode
                    ? Colors.black
                    : isDark
                        ? theme.bottomAppBarTheme.color
                        : Colors.white,
                elevation: 8,
                shape: const CircularNotchedRectangle(),
                child: Container(
                  height: 64,
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'صفحة ${_currentPage + 1} من $_totalPages',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _isNightMode
                              ? Colors.white
                              : theme.textTheme.bodyLarge?.color,
                        ),
                      ),
                      Row(
                        children: [
                          IconButton(
                            icon: const Icon(Icons.arrow_back_ios),
                            tooltip: 'الصفحة السابقة',
                            onPressed: _currentPage <= 0
                                ? null
                                : () {
                                    _pdfViewController
                                        ?.setPage(_currentPage - 1);
                                  },
                          ),
                          IconButton(
                            icon: const Icon(Icons.arrow_forward_ios),
                            tooltip: 'الصفحة التالية',
                            onPressed: _currentPage >= _totalPages - 1
                                ? null
                                : () {
                                    _pdfViewController
                                        ?.setPage(_currentPage + 1);
                                  },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
      ),
    );
  }
}
