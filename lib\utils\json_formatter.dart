import 'dart:convert';
import 'package:flutter/material.dart';
import '../models/zikr.dart';

/// أداة لتنسيق وتحسين ملفات JSON
class JsonFormatter {
  /// تنسيق نص JSON ليكون أكثر قابلية للقراءة
  static String formatJson(String jsonString) {
    try {
      final dynamic jsonData = json.decode(jsonString);
      return const JsonEncoder.withIndent('  ').convert(jsonData);
    } catch (e) {
      debugPrint('خطأ في تنسيق JSON: $e');
      return jsonString;
    }
  }
  
  /// تحويل كائنات Dart إلى نص JSON منسق
  static String formatZikrList(List<Zikr> categories) {
    try {
      final Map<String, dynamic> jsonData = {
        'categories': categories.map((category) => category.toJson()).toList(),
      };
      return const JsonEncoder.withIndent('  ').convert(jsonData);
    } catch (e) {
      debugPrint('خطأ في تحويل كائنات Dart إلى JSON: $e');
      return '{}';
    }
  }
  
  /// إضافة فئة جديدة إلى ملف JSON
  static String addCategory(String jsonString, Zikr newCategory) {
    try {
      final dynamic jsonData = json.decode(jsonString);
      if (jsonData['categories'] is List) {
        final List<dynamic> categories = List.from(jsonData['categories']);
        categories.add(newCategory.toJson());
        jsonData['categories'] = categories;
      }
      return const JsonEncoder.withIndent('  ').convert(jsonData);
    } catch (e) {
      debugPrint('خطأ في إضافة فئة جديدة: $e');
      return jsonString;
    }
  }
  
  /// إضافة ذكر جديد إلى فئة محددة
  static String addZikrItem(String jsonString, String categoryId, ZikrItem newItem) {
    try {
      final dynamic jsonData = json.decode(jsonString);
      if (jsonData['categories'] is List) {
        final List<dynamic> categories = List.from(jsonData['categories']);
        
        // البحث عن الفئة المطلوبة
        for (int i = 0; i < categories.length; i++) {
          final category = categories[i];
          if (category['id'] == categoryId) {
            // إضافة العنصر إلى الفئة
            if (category['items'] is List) {
              final List<dynamic> items = List.from(category['items']);
              items.add(newItem.toJson());
              category['items'] = items;
              category['count'] = items.length;
            } else {
              category['items'] = [newItem.toJson()];
              category['count'] = 1;
            }
            categories[i] = category;
            break;
          }
          
          // البحث في الفئات الفرعية
          if (category['subcategories'] is List) {
            final List<dynamic> subcategories = List.from(category['subcategories']);
            for (int j = 0; j < subcategories.length; j++) {
              final subcategory = subcategories[j];
              if (subcategory['id'] == categoryId) {
                // إضافة العنصر إلى الفئة الفرعية
                if (subcategory['items'] is List) {
                  final List<dynamic> items = List.from(subcategory['items']);
                  items.add(newItem.toJson());
                  subcategory['items'] = items;
                  subcategory['count'] = items.length;
                } else {
                  subcategory['items'] = [newItem.toJson()];
                  subcategory['count'] = 1;
                }
                subcategories[j] = subcategory;
                category['subcategories'] = subcategories;
                categories[i] = category;
                break;
              }
            }
          }
        }
        
        jsonData['categories'] = categories;
      }
      return const JsonEncoder.withIndent('  ').convert(jsonData);
    } catch (e) {
      debugPrint('خطأ في إضافة ذكر جديد: $e');
      return jsonString;
    }
  }
  
  /// تحديث عدد العناصر في جميع الفئات
  static String updateCounts(String jsonString) {
    try {
      final dynamic jsonData = json.decode(jsonString);
      if (jsonData['categories'] is List) {
        final List<dynamic> categories = List.from(jsonData['categories']);
        
        for (int i = 0; i < categories.length; i++) {
          final category = categories[i];
          
          // تحديث عدد العناصر في الفئة
          if (category['items'] is List) {
            category['count'] = (category['items'] as List).length;
          }
          
          // تحديث عدد العناصر في الفئات الفرعية
          if (category['subcategories'] is List) {
            final List<dynamic> subcategories = List.from(category['subcategories']);
            int totalSubcategoryItems = 0;
            
            for (int j = 0; j < subcategories.length; j++) {
              final subcategory = subcategories[j];
              if (subcategory['items'] is List) {
                subcategory['count'] = (subcategory['items'] as List).length;
                totalSubcategoryItems += subcategory['count'] as int;
              }
              subcategories[j] = subcategory;
            }
            
            category['subcategories'] = subcategories;
            category['count'] = totalSubcategoryItems;
          }
          
          categories[i] = category;
        }
        
        jsonData['categories'] = categories;
      }
      return const JsonEncoder.withIndent('  ').convert(jsonData);
    } catch (e) {
      debugPrint('خطأ في تحديث العدادات: $e');
      return jsonString;
    }
  }
}
