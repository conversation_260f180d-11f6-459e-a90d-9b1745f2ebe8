//
import 'package:flutter/material.dart';

class ActivityItem {
  final String title;
  final String section;
  final IconData icon;
  final Color color;
  final String time;
  final String route;

  ActivityItem({
    required this.title,
    required this.section,
    required this.icon,
    required this.color,
    required this.time,
    required this.route,
  });
}

class CategoryItem {
  final String id;
  final String title;
  final IconData icon;
  final Color color;
  final String route;
  final bool isAvailable;

  CategoryItem({
    required this.id,
    required this.title,
    required this.icon,
    required this.color,
    required this.route,
    this.isAvailable = true,
  });
}