import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../models/poem.dart';
// import '../utils/constants.dart'; // Ya no es necesario
import '../utils/app_colors.dart';
import '../services/database_helper.dart';

class PoemDetailsScreen extends StatefulWidget {
  final Poem? poem;

  const PoemDetailsScreen({super.key, this.poem});

  @override
  State<PoemDetailsScreen> createState() => _PoemDetailsScreenState();
}

class _PoemDetailsScreenState extends State<PoemDetailsScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // تتبع حالة المفضلة
  bool _isFavorite = false;
  bool _isLoading = true;
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // للنص
  double _fontSize = 18.0;
  bool _showMenuOptions = false;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    // إعداد الحركات
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 700),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut,
      ),
    );

    // بدء الحركة
    Future.delayed(const Duration(milliseconds: 100), () {
      _animationController.forward();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // استلام البيانات من الصفحة السابقة أو من المعلمة
    Poem? poem;

    // التحقق من وجود القصيدة في المعلمة
    if (widget.poem != null) {
      poem = widget.poem;
      debugPrint('تم استلام القصيدة من المعلمة: ${poem!.title}');
    } else {
      // محاولة الحصول على القصيدة من المسار
      try {
        poem = ModalRoute.of(context)?.settings.arguments as Poem?;
        debugPrint('تم استلام القصيدة من المسار: ${poem?.title}');
      } catch (e) {
        debugPrint('خطأ في استلام القصيدة: $e');
      }
    }

    if (poem != null) {
      // التحقق إذا كانت القصيدة في المفضلة
      _checkFavoriteStatus(poem.id.toString());
    } else {
      debugPrint('لم يتم العثور على القصيدة');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('لم يتم العثور على القصيدة')),
        );
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            Navigator.pop(context);
          }
        });
      }
    }
  }

  // التحقق من حالة المفضلة
  Future<void> _checkFavoriteStatus(String poemId) async {
    try {
      final isFavorite =
          await _databaseHelper.isFavorite(int.parse(poemId), 'poem');
      setState(() {
        _isFavorite = isFavorite;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في التحقق من المفضلة: $e')),
        );
      }
    }
  }

  // تبديل حالة المفضلة
  Future<void> _toggleFavorite(Poem poem) async {
    try {
      setState(() {
        _isLoading = true;
      });

      if (_isFavorite) {
        await _databaseHelper.removeFavorite(int.parse(poem.id), 'poem');
      } else {
        await _databaseHelper.addFavorite(int.parse(poem.id), 'poem');
      }

      setState(() {
        _isFavorite = !_isFavorite;
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isFavorite
                ? 'تمت الإضافة إلى المفضلة'
                : 'تمت الإزالة من المفضلة'),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    }
  }

  // مشاركة القصيدة
  void _sharePoem(Poem poem) {
    Share.share(
      'قصيدة: ${poem.title}\n\nللشاعر: ${poem.poet}\n\n${poem.content}\n\nمن ديوان المنهل الراوي',
      subject: poem.title,
    );
  }

  // نسخ نص القصيدة
  void _copyPoemText(Poem poem) {
    Clipboard.setData(ClipboardData(text: poem.content)).then((_) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم نسخ نص القصيدة'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    });
  }

  // تكبير حجم الخط
  void _increaseFontSize() {
    setState(() {
      _fontSize = _fontSize + 2 <= 30 ? _fontSize + 2 : 30;
    });
  }

  // تصغير حجم الخط
  void _decreaseFontSize() {
    setState(() {
      _fontSize = _fontSize - 2 >= 14 ? _fontSize - 2 : 14;
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // الحصول على القصيدة من المعلمة أو من المسار
    Poem? poem = widget.poem;

    if (poem == null) {
      try {
        poem = ModalRoute.of(context)?.settings.arguments as Poem?;
      } catch (e) {
        debugPrint('خطأ في الحصول على القصيدة: $e');
      }
    }

    // إذا لم يتم العثور على القصيدة، عرض رسالة خطأ
    if (poem == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('خطأ'),
        ),
        body: const Center(
          child: Text('لم يتم العثور على القصيدة'),
        ),
      );
    }

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor.withAlpha(200),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.arrow_back_ios,
              color: AppColors.poemsColor,
              size: 20,
            ),
          ),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          // زر المفضلة
          IconButton(
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor.withAlpha(200),
                shape: BoxShape.circle,
              ),
              child: Icon(
                _isFavorite ? Icons.favorite : Icons.favorite_border,
                color: _isFavorite ? Colors.red : AppColors.poemsColor,
                size: 20,
              ),
            ),
            onPressed: _isLoading ? null : () => _toggleFavorite(poem!),
          ),
          // زر المشاركة
          IconButton(
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor.withAlpha(200),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.share,
                color: AppColors.poemsColor,
                size: 20,
              ),
            ),
            onPressed: () => _sharePoem(poem!),
          ),
          // زر النسخ
          IconButton(
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor.withAlpha(200),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.copy,
                color: AppColors.poemsColor,
                size: 20,
              ),
            ),
            onPressed: () => _copyPoemText(poem!),
          ),
        ],
      ),
      body: Stack(
        children: [
          // الخلفية العلوية
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: 250,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppColors.poemsColor.withAlpha(50),
                    AppColors.poemsColor.withAlpha(20),
                    Theme.of(context).scaffoldBackgroundColor,
                  ],
                ),
              ),
              child: Stack(
                fit: StackFit.expand,
                children: [
                  // زخرفة إسلامية
                  Positioned(
                    top: -20,
                    right: -20,
                    child: Opacity(
                      opacity: 0.08,
                      child: SvgPicture.asset(
                        'assets/images/p1.svg',
                        width: 250,
                        height: 250,
                        colorFilter: const ColorFilter.mode(
                          AppColors.poemsColor,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // محتوى القصيدة
          Padding(
            padding: const EdgeInsets.only(top: 80.0),
            child: SingleChildScrollView(
              controller: _scrollController,
              physics: const BouncingScrollPhysics(),
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // ديكور بسملة
                      Center(
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white,
                            border: Border.all(
                              color: AppColors.poemsColor.withAlpha(80),
                              width: 2,
                            ),
                          ),
                          child: Center(
                            child: Image.asset(
                              'assets/images/allah.png',
                              width: 60,
                              height: 60,
                              color: AppColors.poemsColor.withAlpha(200),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),

                      // عنوان القصيدة
                      Center(
                        child: Text(
                          poem.title,
                          style: const TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: AppColors.poemsColor,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 12),

                      // اسم الشاعر
                      Center(
                        child: Text(
                          poem.poet,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: AppColors.poemsColor.withAlpha(180),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 24),

                      // محتوى القصيدة
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: AppColors.poemsColor.withAlpha(20),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: AppColors.poemsColor.withAlpha(50),
                            width: 1,
                          ),
                        ),
                        child: SelectableText.rich(
                          TextSpan(
                            children: _formatPoemContent(poem.content),
                          ),
                          style: TextStyle(
                            fontSize: _fontSize,
                            height: 1.8,
                            color: Theme.of(context).textTheme.bodyLarge?.color,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 100), // مساحة إضافية في النهاية
                    ],
                  ),
                ),
              ),
            ),
          ),

          // شريط أدوات القراءة
          Positioned(
            bottom: 20,
            left: 20,
            right: 20,
            child: AnimatedOpacity(
              opacity: _showMenuOptions ? 1.0 : 0.0,
              duration: const Duration(milliseconds: 200),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(26), // 0.1 * 255 = ~26
                      spreadRadius: 1,
                      blurRadius: 10,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.text_decrease),
                      onPressed: _decreaseFontSize,
                      tooltip: 'تصغير الخط',
                      color: AppColors.poemsColor,
                    ),
                    Text(
                      '${_fontSize.toInt()}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColors.poemsColor,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.text_increase),
                      onPressed: _increaseFontSize,
                      tooltip: 'تكبير الخط',
                      color: AppColors.poemsColor,
                    ),
                  ],
                ),
              ),
            ),
          ),

          // زر عرض أدوات القراءة
          Positioned(
            bottom: 20,
            right: 20,
            child: AnimatedOpacity(
              opacity: _showMenuOptions ? 0.0 : 1.0,
              duration: const Duration(milliseconds: 200),
              child: FloatingActionButton(
                backgroundColor: AppColors.poemsColor,
                onPressed: () {
                  setState(() {
                    _showMenuOptions = true;
                    Future.delayed(const Duration(seconds: 5), () {
                      if (mounted) {
                        setState(() {
                          _showMenuOptions = false;
                        });
                      }
                    });
                  });
                },
                mini: true,
                child: const Icon(
                  Icons.text_fields,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // دالة تنسيق القصيدة
  List<InlineSpan> _formatPoemContent(String content) {
    final lines = content.split('\n');
    final spans = <InlineSpan>[];

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];

      // تفصيل شطري البيت إذا وجد فاصل (...)
      if (line.contains('...')) {
        final parts = line.split('...');
        if (parts.length == 2) {
          spans.add(TextSpan(text: parts[0].trim()));
          spans.add(const TextSpan(
              text: '   ...   ', style: TextStyle(color: Colors.grey)));
          spans.add(TextSpan(text: parts[1].trim()));
        } else {
          spans.add(TextSpan(text: line));
        }
      } else {
        spans.add(TextSpan(text: line));
      }

      // إضافة سطر جديد إلا في آخر سطر
      if (i < lines.length - 1) {
        spans.add(const TextSpan(text: '\n\n'));
      }
    }

    return spans;
  }
}
