// lib/screens/home/<USER>/header_section.dart
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:math' as math;
import 'dart:ui';
//import '../../../utils/constants.dart';
import '../../../utils/search_delegate.dart';
import '../controllers/home_controller.dart';
import 'package:provider/provider.dart';

class HeaderSection extends StatefulWidget {
  const HeaderSection({super.key});

  @override
  State<HeaderSection> createState() => _HeaderSectionState();
}

class _HeaderSectionState extends State<HeaderSection>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2500),
    )..forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final controller = Provider.of<HomeController>(context);

    return SliverAppBar(
      expandedHeight: screenSize.height * 0.27,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: Theme.of(context).colorScheme.primary,
      flexibleSpace: FlexibleSpaceBar(
        titlePadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'وهج السالك',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 22,
                shadows: [
                  Shadow(
                    color: Colors.black.withAlpha(77), // 0.3 * 255 = 77
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
            ),
            Row(
              children: [
                IconButton(
                  icon: const Icon(
                    Icons.search,
                    color: Colors.white,
                  ),
                  onPressed: () {
                    showSearch(
                      context: context,
                      delegate: AppSearchDelegate(),
                    );
                  },
                ),
                IconButton(
                  icon: const Icon(
                    Icons.settings,
                    color: Colors.white,
                  ),
                  onPressed: () {
                    Navigator.pushNamed(context, '/settings');
                  },
                ),
              ],
            ),
          ],
        ),
        background: Stack(
          fit: StackFit.expand,
          children: [
            // خلفية متدرجة بألوان متناسقة
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topRight,
                  end: Alignment.bottomLeft,
                  colors: [
                    Theme.of(context).colorScheme.primary,
                    Theme.of(context).colorScheme.primary,
                    Theme.of(context).colorScheme.primary.withBlue(
                          (Theme.of(context).colorScheme.primary.blue + 40)
                              .clamp(0, 255),
                        ),
                  ],
                  stops: const [0.2, 0.6, 1.0],
                ),
              ),
            ),

            // طبقة خلفية لضبط الإضاءة
            Container(
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  center: Alignment.topRight,
                  radius: 1.0,
                  colors: [
                    Colors.white.withAlpha(38), // 0.15 * 255 = 38
                    Colors.transparent,
                  ],
                  stops: const [0.1, 0.6],
                ),
              ),
            ),

            // تأثير وهج متحرك متقدم
            AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return CustomPaint(
                  painter: LightSourcePainter(
                    progress: _animationController.value,
                    baseColor: Colors.white,
                  ),
                  child: const SizedBox.expand(),
                );
              },
            ),

            // زخرفة إسلامية رئيسية - متحركة ومتطورة
            AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                final value = _animationController.value;
                return Stack(
                  children: [
                    // الزخرفة الأولى - أكبر حجماً وشفافية أقل
                    Positioned(
                      right: -40 + (value * 10),
                      top: -30 + (value * 10),
                      child: Opacity(
                        opacity: 0.25 * value,
                        child: Transform.rotate(
                          angle: 0.05 * math.pi * (1 - value),
                          child: SvgPicture.asset(
                            'assets/images/p2.svg',
                            width: screenSize.width * 0.7,
                            height: screenSize.width * 0.7,
                            colorFilter: ColorFilter.mode(
                              Colors.white.withAlpha(230), // 0.9 * 255 = 230
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ),
                    ),

                    // الزخرفة الثانية - أصغر حجماً وشفافية أكبر - في موضع مختلف
                    Positioned(
                      left: -60 + (value * 20),
                      bottom: -screenSize.height * 0.1,
                      child: Opacity(
                        opacity: 0.18 * value,
                        child: Transform.rotate(
                          angle: -0.1 * math.pi * (1 - value),
                          child: Transform.scale(
                            scale: 0.7 + (0.3 * value),
                            child: SvgPicture.asset(
                              'assets/images/p2.svg',
                              width: screenSize.width * 0.6,
                              height: screenSize.width * 0.6,
                              colorFilter: ColorFilter.mode(
                                Colors.white.withAlpha(230), // 0.9 * 255 = 230
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),

            // معلومات التطبيق المحسنة والمتناسقة
            AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                final value = _animationController.value;
                return Positioned(
                  left: 20,
                  bottom: screenSize.height * 0.03,
                  child: Opacity(
                    opacity: value,
                    child: Transform.translate(
                      offset: Offset(0, 20 * (1 - value)),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // أيقونة التحية المحسنة
                          Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color:
                                  Colors.white.withAlpha(51), // 0.2 * 255 = 51
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black
                                      .withAlpha(26), // 0.1 * 255 = 26
                                  blurRadius: 8,
                                  spreadRadius: 1,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                              border: Border.all(
                                color: Colors.white
                                    .withAlpha(77), // 0.3 * 255 = 77
                                width: 1,
                              ),
                            ),
                            child: const Icon(
                              Icons.wb_sunny_rounded,
                              color: Colors.white,
                              size: 30,
                            ),
                          ),
                          const SizedBox(height: 10),

                          // كبسولة التحية
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 14, vertical: 7),
                            decoration: BoxDecoration(
                              color:
                                  Colors.white.withAlpha(38), // 0.15 * 255 = 38
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black
                                      .withAlpha(20), // 0.08 * 255 = 20
                                  blurRadius: 6,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                              border: Border.all(
                                color: Colors.white
                                    .withAlpha(77), // 0.3 * 255 = 77
                                width: 0.5,
                              ),
                            ),
                            child: Text(
                              controller.greeting,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(
                                    color: Colors.black
                                        .withAlpha(77), // 0.3 * 255 = 77
                                    blurRadius: 2,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

// إضافة فئة رسم متقدمة لتأثير مصدر الضوء
class LightSourcePainter extends CustomPainter {
  final double progress;
  final Color baseColor;

  LightSourcePainter({
    required this.progress,
    required this.baseColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);

    // الوهج الأول من الزاوية العلوية
    final topGlow = RadialGradient(
      center: Alignment(0.8 + (0.2 * math.sin(progress * math.pi)),
          -0.8 + (0.2 * math.cos(progress * math.pi))),
      radius: 1.0 + (0.1 * math.sin(progress * math.pi * 2)),
      colors: [
        baseColor.withAlpha(
            (0.15 * progress * 255).toInt()), // 0.15 * progress * 255
        baseColor.withAlpha(
            (0.05 * progress * 255).toInt()), // 0.05 * progress * 255
        Colors.transparent,
      ],
      stops: const [0.0, 0.3, 0.6],
    ).createShader(rect);

    canvas.drawRect(rect, Paint()..shader = topGlow);

    // الوهج الثاني من الأسفل - يتحرك بشكل مختلف
    final bottomGlow = RadialGradient(
      center: Alignment(-0.7 + (0.15 * math.cos(progress * math.pi * 1.5)),
          0.7 + (0.15 * math.sin(progress * math.pi * 1.5))),
      radius: 0.8 + (0.15 * math.sin(progress * math.pi * 1.3)),
      colors: [
        baseColor.withAlpha(
            (0.08 * progress * 255).toInt()), // 0.08 * progress * 255
        baseColor.withAlpha(
            (0.03 * progress * 255).toInt()), // 0.03 * progress * 255
        Colors.transparent,
      ],
      stops: const [0.0, 0.3, 0.6],
    ).createShader(rect);

    canvas.drawRect(rect, Paint()..shader = bottomGlow);
  }

  @override
  bool shouldRepaint(LightSourcePainter oldDelegate) =>
      oldDelegate.progress != progress;
}
