import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/book.dart';
import '../utils/app_colors.dart';

class BookCover extends StatelessWidget {
  final Book book;
  final double borderRadius;
  final bool isHero;
  final double? height;
  final double? width;

  const BookCover({
    Key? key,
    required this.book,
    this.borderRadius = 12,
    this.isHero = true,
    this.height,
    this.width,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget coverWidget = _buildCoverImage();

    if (isHero) {
      coverWidget = Hero(
        tag: 'book-cover-${book.id}',
        child: coverWidget,
      );
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(borderRadius),
      child: coverWidget,
    );
  }

  Widget _buildCoverImage() {
    // التحقق من وجود مسار صورة محلي
    if (book.localCoverPath != null && book.localCoverPath!.isNotEmpty) {
      return Image.asset(
        book.localCoverPath!,
        fit: BoxFit.cover,
        height: height,
        width: width,
        errorBuilder: (_, __, ___) => _buildNetworkOrFallbackCover(),
      );
    }

    return _buildNetworkOrFallbackCover();
  }

  Widget _buildNetworkOrFallbackCover() {
    if (book.coverUrl.isNotEmpty) {
      return CachedNetworkImage(
        imageUrl: book.coverUrl,
        fit: BoxFit.cover,
        height: height,
        width: width,
        placeholder: (_, __) => Container(
          color: AppColors.getCategoryColor(book.category)
              .withValues(alpha: 77), // 0.3 * 255 = ~77
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
        errorWidget: (_, __, ___) => _buildFallbackCover(),
      );
    }

    return _buildFallbackCover();
  }

  Widget _buildFallbackCover() {
    final color = AppColors.getCategoryColor(book.category);
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withValues(alpha: 204), // 0.8 * 255 = ~204
            color,
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              book.title.isNotEmpty ? book.title.substring(0, 1) : '؟',
              style: const TextStyle(
                fontSize: 40,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Text(
                book.title,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
