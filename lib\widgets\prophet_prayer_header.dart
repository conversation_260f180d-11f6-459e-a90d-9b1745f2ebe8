// مكون رأس صفحة تفاصيل الصلاة على النبي

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:math' as math;
import '../models/dua.dart';
import '../utils/app_colors.dart';
import '../utils/icon_helper.dart'; // إضافة استيراد IconHelper

class ProphetPrayerHeader extends StatefulWidget {
  final DuaCategory category;
  final Animation<double> animation;

  const ProphetPrayerHeader({
    Key? key,
    required this.category,
    required this.animation,
  }) : super(key: key);

  @override
  State<ProphetPrayerHeader> createState() => _ProphetPrayerHeaderState();
}

class _ProphetPrayerHeaderState extends State<ProphetPrayerHeader>
    with TickerProviderStateMixin {
  // تم إزالة _pulseAnimationController و _pulseAnimation لأنها لم تعد مستخدمة
  late AnimationController _decorationAnimationController;
  late AnimationController _shimmerAnimationController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _opacityAnimation;
  late Animation<double> _shimmerAnimation;

  @override
  void initState() {
    super.initState();

    // تهيئة محرك الرسوم المتحركة للزخرفة - تم تقليل المدة بشكل كبير لتحسين الأداء
    _decorationAnimationController = AnimationController(
      duration:
          const Duration(seconds: 20), // زيادة المدة لتقليل استهلاك المعالج
      vsync: this,
    )..repeat(reverse: false);

    _rotationAnimation = Tween<double>(begin: 0, end: 2 * math.pi).animate(
      CurvedAnimation(
        parent: _decorationAnimationController,
        curve: Curves.linear,
      ),
    );

    // تقليل مدى التغير في الشفافية لتحسين الأداء
    _opacityAnimation = Tween<double>(begin: 0.06, end: 0.08).animate(
      CurvedAnimation(
        parent: _decorationAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    // تم إزالة تهيئة محرك الرسوم المتحركة للنبض لأنه لم يعد مستخدمًا

    // تهيئة محرك الرسوم المتحركة للتوهج - تم زيادة المدة لتحسين الأداء
    _shimmerAnimationController = AnimationController(
      duration:
          const Duration(seconds: 3), // زيادة المدة لتقليل استهلاك المعالج
      vsync: this,
    )..repeat(reverse: false);

    // تقليل مدى التوهج لتحسين الأداء
    _shimmerAnimation = Tween<double>(begin: -0.3, end: 1.3).animate(
      CurvedAnimation(
        parent: _shimmerAnimationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _decorationAnimationController.dispose();
    // تم إزالة _pulseAnimationController.dispose() لأنه لم يعد مستخدمًا
    _shimmerAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final prophetPrayersColor = AppColors.getProphetPrayersColor(isDarkMode);

    // تحويل اسم الأيقونة إلى أيقونة باستخدام IconHelper
    IconData iconData;
    try {
      // استخدام IconHelper للحصول على الأيقونة المناسبة
      iconData = IconHelper.getIconForCategory(widget.category.name,
          iconName: widget.category.iconName);
    } catch (e) {
      // استخدام أيقونة افتراضية إذا لم يتم العثور على الأيقونة
      iconData = Icons.auto_awesome;
      debugPrint(
          'خطأ في تحويل اسم الأيقونة: ${widget.category.iconName}، الخطأ: $e');
    }

    // استخدام RepaintBoundary للقسم بأكمله لتحسين أداء الرسم
    return RepaintBoundary(
      child: AnimatedBuilder(
        animation: widget.animation,
        builder: (context, child) {
          // تم إيقاف تحرك الهيدر الزائد مع الحفاظ على التصميم الفاخر
          return Opacity(
            opacity: widget.animation.value,
            // تم إزالة Transform.translate و Transform.scale لإيقاف تحرك الهيدر الزائد
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.fromLTRB(20, 35, 20, 35),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    prophetPrayersColor,
                    prophetPrayersColor.withValues(alpha: 0.9),
                    prophetPrayersColor.withValues(alpha: 0.78),
                  ],
                  stops: const [0.0, 0.6, 1.0],
                ),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(35),
                  bottomRight: Radius.circular(35),
                ),
                // تقليل قوة الظلال لتحسين الأداء
                boxShadow: [
                  BoxShadow(
                    color: prophetPrayersColor.withValues(alpha: 0.2),
                    blurRadius: 15, // تقليل قوة البلور
                    spreadRadius: 1, // تقليل انتشار الظل
                    offset: const Offset(0, 6), // تقليل الإزاحة
                  ),
                ],
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.12),
                  width: 1,
                ),
              ),
              child: Stack(
                children: [
                  // تأثير توهج في الزاوية - ثابت بدون رسوم متحركة
                  Positioned(
                    left: -20,
                    top: -20,
                    child: Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            prophetPrayersColor.withValues(alpha: 0.2),
                            Colors.transparent,
                          ],
                          stops: const [0.1, 1.0],
                        ),
                      ),
                    ),
                  ),

                  // زخرفة خلفية متحركة - تم تحسينها للأداء
                  Positioned.fill(
                    child: RepaintBoundary(
                      child: AnimatedBuilder(
                        animation: _decorationAnimationController,
                        builder: (context, child) {
                          return Opacity(
                            opacity: _opacityAnimation.value,
                            child: Transform.rotate(
                              angle: _rotationAnimation.value,
                              child: SvgPicture.asset(
                                'assets/images/p2.svg',
                                fit: BoxFit.cover,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),

                  // تم إزالة الزخرفة الخلفية الثابتة لتحسين الأداء

                  // تأثير توهج متحرك - تم تحسينه للأداء وتجنب مشكلة ParentDataWidget
                  Positioned(
                    left: -150 +
                        (MediaQuery.of(context).size.width *
                            _shimmerAnimation.value),
                    top: 0,
                    bottom: 0,
                    width: 100, // تقليل عرض التوهج
                    child: RepaintBoundary(
                      child: AnimatedBuilder(
                        animation: _shimmerAnimation,
                        builder: (context, child) {
                          return Transform.rotate(
                            angle: math.pi / 4,
                            child: Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.white.withValues(alpha: 0),
                                    Colors.white.withValues(
                                        alpha: 0.04), // تقليل قوة التوهج
                                    Colors.white.withValues(alpha: 0),
                                  ],
                                  stops: const [0.0, 0.5, 1.0],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),

                  // محتوى الرأس
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // أيقونة الفئة مع تأثير توهج
                      Row(
                        children: [
                          // أيقونة فاخرة متحركة
                          _buildLuxuryIcon(iconData, prophetPrayersColor),
                          const SizedBox(width: 16),

                          // عنوان الفئة - تم تبسيط التأثيرات
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // عنوان - تم إزالة ShaderMask لتحسين الأداء
                                Text(
                                  widget.category.name,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 26,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 0.5,
                                    height: 1.2,
                                    // تقليل قوة الظل
                                    shadows: [
                                      Shadow(
                                        color: Colors.black26,
                                        offset: Offset(0, 1),
                                        blurRadius: 2,
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 10),

                                // عدد الصلوات - تم تبسيط التأثيرات
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 14,
                                    vertical: 8,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.12),
                                    borderRadius: BorderRadius.circular(20),
                                    // إزالة الظل لتحسين الأداء
                                    border: Border.all(
                                      color:
                                          Colors.white.withValues(alpha: 0.2),
                                      width: 1,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(4),
                                        decoration: BoxDecoration(
                                          color: Colors.white
                                              .withValues(alpha: 0.16),
                                          shape: BoxShape.circle,
                                        ),
                                        child: const Icon(
                                          Icons.format_list_numbered,
                                          color: Colors.white,
                                          size: 14,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        '${widget.category.items.length} صيغة',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 14,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 25),

                      // وصف الفئة - تم تبسيط الرسوم المتحركة
                      if (widget.category.description.isNotEmpty)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 18,
                            vertical: 14,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.16),
                              width: 1,
                            ),
                            // إزالة الظل لتحسين الأداء
                          ),
                          child: Text(
                            widget.category.description,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              height: 1.6,
                              // إزالة الظل لتحسين الأداء
                            ),
                            textAlign: TextAlign.right,
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // بناء أيقونة فاخرة متحركة - تم تحسينها للأداء
  Widget _buildLuxuryIcon(IconData iconData, Color prophetPrayersColor) {
    // استخدام RepaintBoundary لتحسين أداء الرسم
    return RepaintBoundary(
      child: Stack(
        alignment: Alignment.center,
        children: [
          // طبقة توهج خارجية - تم تقليل مدة الرسوم المتحركة
          TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: 0.0, end: 1.0),
            // تقليل المدة من 3 ثوانٍ إلى 1.5 ثانية
            duration: const Duration(milliseconds: 1500),
            curve: Curves.easeInOut,
            builder: (context, value, _) {
              // تبسيط حساب الألفا لتحسين الأداء
              return Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      prophetPrayersColor.withValues(
                          alpha: 0.12), // تبسيط الحساب بإزالة الدالة المثلثية
                      Colors.transparent,
                    ],
                    stops: const [0.1, 1.0],
                  ),
                ),
              );
            },
          ),

          // حلقة خارجية متحركة - تم تقليل مدة الرسوم المتحركة
          TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: 0.0, end: 1.0),
            // تقليل المدة من 6 ثوانٍ إلى 3 ثوانٍ
            duration: const Duration(seconds: 3),
            curve: Curves.easeInOut,
            builder: (context, value, _) {
              return Transform.rotate(
                angle: value * math.pi * 2,
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: prophetPrayersColor.withValues(alpha: 0.12),
                      width: 1.5, // تقليل سمك الحدود
                    ),
                  ),
                ),
              );
            },
          ),

          // تم إزالة الحلقة الداخلية المتحركة لتحسين الأداء

          // الأيقونة المركزية - تم تبسيطها
          Container(
            width: 46,
            height: 46,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  prophetPrayersColor.withValues(alpha: 0.27),
                  prophetPrayersColor.withValues(alpha: 0.47),
                ],
              ),
              shape: BoxShape.circle,
              // تقليل تأثير الظل
              boxShadow: [
                BoxShadow(
                  color: prophetPrayersColor.withValues(alpha: 0.12),
                  blurRadius: 6, // تقليل قوة البلور
                  spreadRadius: 0,
                  offset: const Offset(0, 2), // تقليل الإزاحة
                ),
              ],
            ),
            // تم تبسيط الرسوم المتحركة للأيقونة
            child: IconHelper.getIconWidget(
              categoryName: widget.category.name,
              iconPath: widget.category.iconPath,
              iconName: widget.category.iconName,
              size: 24,
              color: Colors.white,
              section: 'prophet_prayers',
            ),
          ),
        ],
      ),
    );
  }
}
