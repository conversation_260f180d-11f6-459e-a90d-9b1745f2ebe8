import 'package:flutter/material.dart';
import '../../../utils/responsive_helper.dart';

class SortOptionsDialog extends StatelessWidget {
  final Function(SortOption) onSortSelected;

  const SortOptionsDialog({
    Key? key,
    required this.onSortSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      title: const Text(
        'ترتيب حسب',
        textAlign: TextAlign.center,
        style: TextStyle(
          fontWeight: FontWeight.bold,
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildSortOption(
            context,
            'الأحدث',
            Icons.arrow_downward,
            () => onSortSelected(SortOption.newest),
          ),
          const Divider(),
          _buildSortOption(
            context,
            'الأقدم',
            Icons.arrow_upward,
            () => onSortSelected(SortOption.oldest),
          ),
          const Divider(),
          _buildSortOption(
            context,
            'أبجدياً (أ-ي)',
            Icons.sort_by_alpha,
            () => onSortSelected(SortOption.alphabetical),
          ),
        ],
      ),
    );
  }

  Widget _buildSortOption(
      BuildContext context, String title, IconData icon, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(10),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
        child: Row(
          children: [
            Icon(
              icon,
              size: 20,
              color: Colors.amber,
            ),
            const SizedBox(width: 16),
            Text(
              title,
              style: TextStyle(
                fontSize: ResponsiveHelper.getFontSize(context, 16),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

enum SortOption {
  newest,
  oldest,
  alphabetical,
}
