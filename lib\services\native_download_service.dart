import 'dart:async';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import '../models/book.dart';
import '../database/database_helper.dart';

class NativeDownloadService {
  static const MethodChannel _channel = MethodChannel('com.example.wahaj_alsaalik/download');
  static final Map<String, StreamController<int>> _progressControllers = {};
  static final DatabaseHelper _databaseHelper = DatabaseHelper();

  // تنزيل ملف PDF للكتاب
  static Future<String?> downloadBook(Book book) async {
    if (book.pdfUrl.isEmpty) {
      throw Exception('رابط تنزيل الكتاب غير متوفر');
    }

    try {
      // إنشاء اسم ملف آمن
      final fileName = '${book.title.replaceAll(RegExp(r'[^\w\s]+'), '_')}_${book.id}.pdf';
      
      // بدء التنزيل باستخدام الواجهة البرمجية الأصلية
      final downloadId = await _channel.invokeMethod<String>('downloadFile', {
        'url': book.pdfUrl,
        'fileName': fileName,
        'title': 'تنزيل ${book.title}',
        'description': 'جاري تنزيل كتاب ${book.title} للمؤلف ${book.author}',
      });

      if (downloadId == null) {
        throw Exception('فشل بدء التنزيل');
      }

      // إنشاء مراقب تقدم التنزيل
      final progressController = StreamController<int>.broadcast();
      _progressControllers[downloadId] = progressController;

      // بدء مراقبة حالة التنزيل
      _monitorDownloadProgress(downloadId, book.id, fileName, progressController);

      return downloadId;
    } catch (e) {
      rethrow;
    }
  }

  // الحصول على تدفق تقدم التنزيل
  static Stream<int>? getDownloadProgress(String downloadId) {
    return _progressControllers[downloadId]?.stream;
  }

  // إلغاء التنزيل
  static Future<bool> cancelDownload(String downloadId) async {
    try {
      final result = await _channel.invokeMethod<bool>('cancelDownload', {
        'downloadId': downloadId,
      });
      
      // إغلاق وإزالة مراقب التقدم
      _progressControllers[downloadId]?.close();
      _progressControllers.remove(downloadId);
      
      return result ?? false;
    } catch (e) {
      return false;
    }
  }

  // مراقبة تقدم التنزيل
  static Future<void> _monitorDownloadProgress(
    String downloadId,
    int bookId,
    String fileName,
    StreamController<int> progressController,
  ) async {
    bool isCompleted = false;
    
    while (!isCompleted && !progressController.isClosed) {
      try {
        final status = await _channel.invokeMethod<String>('getDownloadStatus', {
          'downloadId': downloadId,
        });

        if (status == null) {
          await Future.delayed(const Duration(seconds: 1));
          continue;
        }

        if (status.startsWith('RUNNING:')) {
          // تحديث التقدم
          final progress = int.tryParse(status.split(':')[1]) ?? 0;
          progressController.add(progress);
          
          // تحديث حالة التنزيل في قاعدة البيانات
          await _databaseHelper.updateBookDownloadProgress(bookId, progress);
        } else if (status.startsWith('PENDING:') || status.startsWith('PAUSED:')) {
          // تحديث التقدم
          final progress = int.tryParse(status.split(':')[1]) ?? 0;
          progressController.add(progress);
        } else if (status == 'SUCCESSFUL') {
          // التنزيل اكتمل بنجاح
          progressController.add(100);
          
          // الحصول على مسار الملف المنزل
          final downloadsDir = await _getDownloadsDirectory();
          final filePath = '${downloadsDir.path}/$fileName';
          
          // التحقق من وجود الملف
          if (File(filePath).existsSync()) {
            // تحديث مسار الملف في قاعدة البيانات
            await _databaseHelper.updateBookPdfPath(bookId, filePath);
            await _databaseHelper.updateBookDownloadProgress(bookId, 100);
          }
          
          isCompleted = true;
        } else if (status.startsWith('FAILED:')) {
          // التنزيل فشل
          progressController.addError('فشل التنزيل: ${status.split(':')[1]}');
          await _databaseHelper.updateBookDownloadProgress(bookId, -1);
          isCompleted = true;
        } else {
          // حالة غير معروفة
          await Future.delayed(const Duration(seconds: 1));
        }
      } catch (e) {
        progressController.addError('خطأ في مراقبة التنزيل: $e');
        await Future.delayed(const Duration(seconds: 1));
      }
      
      if (!isCompleted) {
        await Future.delayed(const Duration(seconds: 1));
      }
    }
    
    // إغلاق المراقب عند الانتهاء
    if (!progressController.isClosed) {
      progressController.close();
      _progressControllers.remove(downloadId);
    }
  }

  // الحصول على مجلد التنزيلات
  static Future<Directory> _getDownloadsDirectory() async {
    if (Platform.isAndroid) {
      return Directory('/storage/emulated/0/Download');
    } else {
      final directory = await getApplicationDocumentsDirectory();
      return directory;
    }
  }
}
