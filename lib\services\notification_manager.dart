// مدير الإشعارات - يقوم بتوحيد واجهة التعامل مع الإشعارات في التطبيق
// يستخدم LocalNotificationService الجديد بدلاً من AwesomeNotifications

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'local_notification_service.dart';

/// مدير الإشعارات - واجهة موحدة للتعامل مع الإشعارات في التطبيق
class NotificationManager {
  // تطبيق نمط Singleton للتأكد من وجود نسخة واحدة فقط من المدير
  static final NotificationManager _instance = NotificationManager._internal();
  factory NotificationManager() => _instance;
  NotificationManager._internal();

  // خدمة الإشعارات المحلية
  final LocalNotificationService _notificationService =
      LocalNotificationService();

  // حالة التهيئة
  bool _initialized = false;

  /// تهيئة مدير الإشعارات
  /// يجب استدعاء هذه الدالة عند بدء التطبيق
  Future<void> init() async {
    if (_initialized) {
      debugPrint('مدير الإشعارات مهيأ بالفعل');
      return;
    }

    try {
      // تهيئة خدمة الإشعارات المحلية
      await _notificationService.init();

      // تهيئة الإشعارات المجدولة
      await _notificationService.initializeNotifications();

      _initialized = true;
      debugPrint('تم تهيئة مدير الإشعارات بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة مدير الإشعارات: $e');
      _initialized = false;
    }
  }

  /// طلب أذونات الإشعارات
  Future<bool> requestNotificationPermissions() async {
    return await _notificationService.requestNotificationPermissions();
  }

  /// التحقق من أذونات الإشعارات
  Future<bool> checkNotificationPermissions() async {
    return await _notificationService.checkNotificationPermissions();
  }

  /// تحديث إعدادات الإشعارات
  Future<void> updateNotificationSettings({
    bool? morningEnabled,
    TimeOfDay? morningTime,
    bool? eveningEnabled,
    TimeOfDay? eveningTime,
  }) async {
    await _notificationService.updateNotificationSettings(
      morningEnabled: morningEnabled,
      morningTime: morningTime,
      eveningEnabled: eveningEnabled,
      eveningTime: eveningTime,
    );
  }

  /// التحقق من تفعيل إشعارات أذكار الصباح
  Future<bool> isMorningAzkarEnabled() async {
    return await _notificationService.isMorningAzkarEnabled();
  }

  /// الحصول على وقت إشعارات أذكار الصباح
  Future<TimeOfDay> getMorningAzkarTime() async {
    return await _notificationService.getMorningAzkarTime();
  }

  /// التحقق من تفعيل إشعارات أذكار المساء
  Future<bool> isEveningAzkarEnabled() async {
    return await _notificationService.isEveningAzkarEnabled();
  }

  /// الحصول على وقت إشعارات أذكار المساء
  Future<TimeOfDay> getEveningAzkarTime() async {
    return await _notificationService.getEveningAzkarTime();
  }

  /// جدولة إشعار أذكار الصباح - تم تعطيلها مؤقتاً
  Future<bool> scheduleMorningAzkarNotification(TimeOfDay time) async {
    // تم تعطيل هذه الميزة مؤقتاً
    debugPrint('تم تعطيل جدولة إشعارات أذكار الصباح مؤقتاً');
    return false;
    // return await _notificationService.scheduleMorningAzkarNotification(time);
  }

  /// إلغاء إشعار أذكار الصباح
  Future<void> cancelMorningAzkarNotification() async {
    await _notificationService.cancelMorningAzkarNotification();
  }

  /// جدولة إشعار أذكار المساء - تم تعطيلها مؤقتاً
  Future<bool> scheduleEveningAzkarNotification(TimeOfDay time) async {
    // تم تعطيل هذه الميزة مؤقتاً
    debugPrint('تم تعطيل جدولة إشعارات أذكار المساء مؤقتاً');
    return false;
    // return await _notificationService.scheduleEveningAzkarNotification(time);
  }

  /// إلغاء إشعار أذكار المساء
  Future<void> cancelEveningAzkarNotification() async {
    await _notificationService.cancelEveningAzkarNotification();
  }

  /// جدولة إشعار تذكير بالورد
  Future<bool> scheduleWirdReminderNotification(
    int wirdId,
    String wirdName,
    TimeOfDay time,
  ) async {
    return await _notificationService.scheduleWirdReminderNotification(
      wirdId,
      wirdName,
      time,
    );
  }

  /// إلغاء إشعار تذكير بالورد
  Future<void> cancelWirdReminderNotification(int wirdId) async {
    await _notificationService.cancelWirdReminderNotification(wirdId);
  }

  /// إرسال إشعار اختباري فوري للورد
  Future<bool> sendTestWirdReminderNotification(
    int wirdId,
    String wirdName,
  ) async {
    return await _notificationService.sendTestWirdReminderNotification(
      wirdId,
      wirdName,
    );
  }

  /// التحقق من جدولة إشعار الورد وإعادة جدولته إذا لزم الأمر
  Future<bool> verifyWirdReminderNotification(
    int wirdId,
    String wirdName,
    TimeOfDay time,
  ) async {
    try {
      // التحقق من وجود الإشعار في قائمة الإشعارات المعلقة
      final pendingNotifications =
          await _notificationService.getPendingNotifications();
      final notificationExists = pendingNotifications
          .any((notification) => notification.id == 13000 + wirdId);

      debugPrint(
          'التحقق من وجود إشعار الورد $wirdId: ${notificationExists ? "موجود" : "غير موجود"}');

      // إذا لم يكن الإشعار موجوداً، نقوم بإعادة جدولته
      if (!notificationExists) {
        debugPrint('إعادة جدولة إشعار الورد: $wirdName');
        return await scheduleWirdReminderNotification(
          wirdId,
          wirdName,
          time,
        );
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في التحقق من إشعار الورد: $e');
      return false;
    }
  }

  /// جدولة إشعار تذكير بالعودة
  Future<bool> scheduleReturnReminderNotification() async {
    return await _notificationService.scheduleReturnReminderNotification();
  }

  /// التحقق من جدولة إشعار أذكار الصباح وإعادة جدولته إذا لزم الأمر
  Future<bool> verifyMorningAzkarNotification() async {
    try {
      // التحقق من تفعيل إشعارات أذكار الصباح
      final bool morningEnabled = await isMorningAzkarEnabled();
      if (!morningEnabled) {
        debugPrint('إشعارات أذكار الصباح غير مفعلة');
        return true;
      }

      // التحقق من وجود الإشعار في قائمة الإشعارات المعلقة
      final pendingNotifications =
          await _notificationService.getPendingNotifications();
      final notificationExists =
          pendingNotifications.any((notification) => notification.id == 10001);

      debugPrint(
          'التحقق من وجود إشعار أذكار الصباح: ${notificationExists ? "موجود" : "غير موجود"}');

      // إذا لم يكن الإشعار موجوداً، نقوم بإعادة جدولته
      if (!notificationExists) {
        debugPrint('إعادة جدولة إشعار أذكار الصباح');
        final TimeOfDay morningTime = await getMorningAzkarTime();
        return await scheduleMorningAzkarNotification(morningTime);
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في التحقق من إشعار أذكار الصباح: $e');
      return false;
    }
  }

  /// التحقق من جدولة إشعار أذكار المساء وإعادة جدولته إذا لزم الأمر
  Future<bool> verifyEveningAzkarNotification() async {
    try {
      // التحقق من تفعيل إشعارات أذكار المساء
      final bool eveningEnabled = await isEveningAzkarEnabled();
      if (!eveningEnabled) {
        debugPrint('إشعارات أذكار المساء غير مفعلة');
        return true;
      }

      // التحقق من وجود الإشعار في قائمة الإشعارات المعلقة
      final pendingNotifications =
          await _notificationService.getPendingNotifications();
      final notificationExists =
          pendingNotifications.any((notification) => notification.id == 10002);

      debugPrint(
          'التحقق من وجود إشعار أذكار المساء: ${notificationExists ? "موجود" : "غير موجود"}');

      // إذا لم يكن الإشعار موجوداً، نقوم بإعادة جدولته
      if (!notificationExists) {
        debugPrint('إعادة جدولة إشعار أذكار المساء');
        final TimeOfDay eveningTime = await getEveningAzkarTime();
        return await scheduleEveningAzkarNotification(eveningTime);
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في التحقق من إشعار أذكار المساء: $e');
      return false;
    }
  }

  /// تنظيف موارد المدير
  void dispose() {
    _notificationService.dispose();
    _initialized = false;
  }

  /// إرسال إشعار اختباري فوري لأذكار الصباح
  Future<bool> sendTestMorningAzkarNotification() async {
    return await _notificationService.sendTestMorningAzkarNotification();
  }

  /// إرسال إشعار اختباري فوري لأذكار المساء
  Future<bool> sendTestEveningAzkarNotification() async {
    return await _notificationService.sendTestEveningAzkarNotification();
  }

  /// تأجيل إشعار لوقت لاحق
  Future<bool> snoozeNotification(int originalId, int snoozeMinutes) async {
    return await _notificationService.snoozeNotification(
        originalId, snoozeMinutes);
  }

  /// تحديث وقت تأجيل الإشعارات
  Future<void> updateSnoozeTime(int minutes) async {
    // حفظ وقت التأجيل في الإعدادات المشتركة
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setInt('notification_snooze_minutes', minutes);
    debugPrint('تم تحديث وقت تأجيل الإشعارات إلى $minutes دقيقة');
  }

  /// الحصول على وقت تأجيل الإشعارات
  Future<int> getSnoozeTime() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getInt('notification_snooze_minutes') ??
        10; // الافتراضي: 10 دقائق
  }
}
