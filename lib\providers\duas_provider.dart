// مزود الأدعية

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/dua.dart';
import '../database/database_helper.dart';

class DuasProvider extends ChangeNotifier {
  List<DuaCategory> _categories = [];
  List<Dua> _featuredDuas = [];
  bool _isLoading = true;
  String _searchQuery = '';
  Set<String> _favoriteDuaIds = {}; // مجموعة معرفات الأدعية المفضلة
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // الحصول على قائمة الفئات
  List<DuaCategory> get categories => _categories;

  // الحصول على قائمة الأدعية المميزة
  List<Dua> get featuredDuas => _featuredDuas;

  // الحصول على حالة التحميل
  bool get isLoading => _isLoading;

  // الحصول على نص البحث
  String get searchQuery => _searchQuery;

  // الحصول على قائمة الأدعية المفضلة
  List<Dua> get favoriteDuas {
    List<Dua> favorites = [];

    // دالة مساعدة لجمع الأدعية المفضلة من فئة وفئاتها الفرعية
    void collectFromCategory(DuaCategory category) {
      // جمع الأدعية المفضلة من الفئة الحالية
      for (var dua in category.items) {
        if (dua.isFavorite) {
          favorites.add(dua);
        }
      }

      // جمع الأدعية المفضلة من الفئات الفرعية
      if (category.subcategories != null) {
        for (var subcategory in category.subcategories!) {
          collectFromCategory(subcategory);
        }
      }
    }

    // جمع الأدعية المفضلة من جميع الفئات
    for (var category in _categories) {
      collectFromCategory(category);
    }

    return favorites;
  }

  // تهيئة المزود - محسنة للأداء
  Future<void> initialize() async {
    try {
      _isLoading = true;
      notifyListeners();

      // تنفيذ عمليات التحميل بالتوازي لتحسين الأداء
      await Future.wait([
        loadDuas(),
        loadFavorites(),
      ]);

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تهيئة مزود الأدعية: $e');
      _isLoading = false;
      notifyListeners();
    }
  }

  // تحميل الأدعية من ملف JSON - محسنة للأداء
  Future<void> loadDuas() async {
    try {
      // لا نقوم بتعيين _isLoading هنا لأنه تم تعيينه في initialize()

      // قراءة ملف الأدعية
      final String jsonString =
          await rootBundle.loadString('assets/data/duas.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);

      // تحويل البيانات إلى نماذج
      _categories = (jsonData['categories'] as List<dynamic>)
          .map((category) => DuaCategory.fromJson(category))
          .toList();

      // استخراج الأدعية المميزة
      _extractFeaturedDuas();

      // طباعة معلومات تشخيصية
      debugPrint('تم تحميل ${_categories.length} فئة من الأدعية');
      debugPrint('تم استخراج ${_featuredDuas.length} دعاء مميز');

      // لا نقوم بتعيين _isLoading هنا لأنه سيتم تعيينه في initialize()
    } catch (e) {
      debugPrint('خطأ في تحميل الأدعية: $e');
      rethrow; // إعادة رمي الاستثناء ليتم التقاطه في initialize()
    }
  }

  // استخراج الأدعية المميزة من جميع الفئات
  void _extractFeaturedDuas() {
    _featuredDuas = [];

    // دالة مساعدة لاستخراج الأدعية المميزة من فئة وفئاتها الفرعية
    void extractFromCategory(DuaCategory category) {
      // استخراج الأدعية المميزة من الفئة الحالية
      for (var dua in category.items) {
        if (dua.isFeatured) {
          _featuredDuas.add(dua);
        }
      }

      // استخراج الأدعية المميزة من الفئات الفرعية
      if (category.subcategories != null) {
        for (var subcategory in category.subcategories!) {
          extractFromCategory(subcategory);
        }
      }
    }

    // استخراج الأدعية المميزة من جميع الفئات
    for (var category in _categories) {
      extractFromCategory(category);
    }

    // ترتيب الأدعية المميزة عشوائياً (اختياري)
    // _featuredDuas.shuffle();

    // عرض جميع الأدعية المميزة بدلاً من تحديدها بـ 5 فقط
    // if (_featuredDuas.length > 5) {
    //   _featuredDuas = _featuredDuas.sublist(0, 5);
    // }
  }

  // تحديث نص البحث
  void updateSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  // البحث عن الأدعية
  List<DuaCategory> searchDuas(String query) {
    if (query.isEmpty) {
      return _categories;
    }

    // دالة مساعدة للبحث في فئة وفئاتها الفرعية
    DuaCategory? searchInCategory(DuaCategory category) {
      // البحث في الفئة الحالية
      final matchingItems = category.items.where((dua) {
        return dua.text.contains(query) ||
            (dua.translation != null && dua.translation!.contains(query)) ||
            (dua.source != null && dua.source!.contains(query)) ||
            (dua.explanation != null && dua.explanation!.contains(query)) ||
            (dua.occasion != null && dua.occasion!.contains(query));
      }).toList();

      // البحث في الفئات الفرعية
      List<DuaCategory> matchingSubcategories = [];
      if (category.subcategories != null) {
        for (var subcategory in category.subcategories!) {
          final matchingSubcategory = searchInCategory(subcategory);
          if (matchingSubcategory != null) {
            matchingSubcategories.add(matchingSubcategory);
          }
        }
      }

      // إذا لم يتم العثور على أي نتائج، أرجع null
      if (matchingItems.isEmpty && matchingSubcategories.isEmpty) {
        return null;
      }

      // إنشاء فئة جديدة تحتوي على العناصر المطابقة فقط
      return DuaCategory(
        id: category.id,
        name: category.name,
        description: category.description,
        count: matchingItems.length,
        iconName: category.iconName,
        items: matchingItems,
        subcategories:
            matchingSubcategories.isEmpty ? null : matchingSubcategories,
      );
    }

    // البحث في جميع الفئات
    List<DuaCategory> results = [];
    for (var category in _categories) {
      final matchingCategory = searchInCategory(category);
      if (matchingCategory != null) {
        results.add(matchingCategory);
      }
    }

    return results;
  }

  // تحميل الأدعية المفضلة - محسنة للأداء
  Future<void> loadFavorites() async {
    try {
      debugPrint('جاري تحميل الأدعية المفضلة...');

      // تحميل المفضلة من قاعدة البيانات
      final favorites = await _databaseHelper.getFavorites();

      // تحديث مجموعة معرفات الأدعية المفضلة بطريقة أكثر كفاءة
      _favoriteDuaIds = favorites
          .where((favorite) => favorite['item_type'] == 'dua')
          .map((favorite) => favorite['item_id'] as String)
          .toSet();

      // تحديث حالة المفضلة في نماذج الأدعية
      _updateFavoriteStatus();

      debugPrint('تم تحميل ${_favoriteDuaIds.length} دعاء مفضل');
    } catch (e) {
      debugPrint('خطأ في تحميل الأدعية المفضلة: $e');
      // لا نقوم بإعادة رمي الاستثناء هنا لأن هذه ليست وظيفة حرجة
      // ويمكن للتطبيق أن يستمر في العمل حتى لو فشل تحميل المفضلة
    }
  }

  // تحديث حالة المفضلة في نماذج الأدعية - محسنة للأداء
  void _updateFavoriteStatus() {
    // تحسين الأداء: إنشاء مجموعة من معرفات الأدعية المفضلة للبحث السريع
    final favoriteIds = _favoriteDuaIds;
    bool hasChanges = false;

    // دالة مساعدة لتحديث حالة المفضلة في فئة وفئاتها الفرعية
    void updateInCategory(DuaCategory category) {
      // تحديث الأدعية في الفئة الحالية
      for (int i = 0; i < category.items.length; i++) {
        final dua = category.items[i];
        final isFavorite = favoriteIds.contains(dua.id);

        // إذا كانت حالة المفضلة مختلفة، قم بتحديث النموذج
        if (dua.isFavorite != isFavorite) {
          category.items[i] = dua.copyWith(isFavorite: isFavorite);
          hasChanges = true;
        }
      }

      // تحديث الأدعية في الفئات الفرعية
      if (category.subcategories != null) {
        for (var subcategory in category.subcategories!) {
          updateInCategory(subcategory);
        }
      }
    }

    // تحديث الأدعية في جميع الفئات
    for (var category in _categories) {
      updateInCategory(category);
    }

    // تحديث الأدعية المميزة
    for (int i = 0; i < _featuredDuas.length; i++) {
      final dua = _featuredDuas[i];
      final isFavorite = favoriteIds.contains(dua.id);

      if (dua.isFavorite != isFavorite) {
        _featuredDuas[i] = dua.copyWith(isFavorite: isFavorite);
        hasChanges = true;
      }
    }

    // إخطار المستمعين بالتغييرات فقط إذا كانت هناك تغييرات
    if (hasChanges) {
      notifyListeners();
    }
  }

  // إضافة دعاء إلى المفضلة
  Future<bool> addToFavorites(Dua dua) async {
    try {
      // إضافة إلى قاعدة البيانات
      await _databaseHelper.addToFavorites(dua.id, itemType: 'dua');

      // تحديث مجموعة المعرفات
      _favoriteDuaIds.add(dua.id);

      // تحديث حالة المفضلة في النماذج
      _updateFavoriteStatus();

      debugPrint('تمت إضافة الدعاء إلى المفضلة: ${dua.id}');
      return true;
    } catch (e) {
      debugPrint('خطأ في إضافة الدعاء إلى المفضلة: $e');
      return false;
    }
  }

  // إزالة دعاء من المفضلة
  Future<bool> removeFromFavorites(Dua dua) async {
    try {
      // إزالة من قاعدة البيانات
      await _databaseHelper.removeFromFavorites(dua.id, itemType: 'dua');

      // تحديث مجموعة المعرفات
      _favoriteDuaIds.remove(dua.id);

      // تحديث حالة المفضلة في النماذج
      _updateFavoriteStatus();

      debugPrint('تمت إزالة الدعاء من المفضلة: ${dua.id}');
      return true;
    } catch (e) {
      debugPrint('خطأ في إزالة الدعاء من المفضلة: $e');
      return false;
    }
  }

  // تبديل حالة المفضلة
  Future<bool> toggleFavorite(Dua dua) async {
    if (dua.isFavorite) {
      return await removeFromFavorites(dua);
    } else {
      return await addToFavorites(dua);
    }
  }

  // التحقق مما إذا كان الدعاء مفضلاً
  bool isFavorite(String duaId) {
    return _favoriteDuaIds.contains(duaId);
  }

  // طريقة بديلة للتحقق مما إذا كان الدعاء مفضلاً (للتوافق مع الكود الحالي)
  bool isDuaFavorite(String duaId) {
    return isFavorite(duaId);
  }

  // الحصول على دعاء عشوائي
  Dua? getRandomDua() {
    if (_categories.isEmpty) {
      return null;
    }

    // جمع جميع الأدعية من جميع الفئات
    List<Dua> allDuas = [];

    void collectDuasFromCategory(DuaCategory category) {
      allDuas.addAll(category.items);

      if (category.subcategories != null) {
        for (var subcategory in category.subcategories!) {
          collectDuasFromCategory(subcategory);
        }
      }
    }

    for (var category in _categories) {
      collectDuasFromCategory(category);
    }

    if (allDuas.isEmpty) {
      return null;
    }

    // اختيار دعاء عشوائي
    allDuas.shuffle();
    return allDuas.first;
  }
}
