import 'package:flutter/material.dart';
import '../utils/app_colors.dart';

class FilterButton extends StatelessWidget {
  final bool hasActiveFilters;
  final VoidCallback onPressed;
  final String label;
  final IconData icon;

  const FilterButton({
    super.key,
    required this.hasActiveFilters,
    required this.onPressed,
    this.label = 'تصفية',
    this.icon = Icons.filter_list,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius:
            BorderRadius.circular(MediaQuery.of(context).size.width * 0.03),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: MediaQuery.of(context).size.width * 0.03,
            vertical: MediaQuery.of(context).size.height * 0.01,
          ),
          decoration: BoxDecoration(
            color: hasActiveFilters
                ? AppColors.getAzkarColor(
                        Theme.of(context).brightness == Brightness.dark)
                    .withAlpha(38) // 0.15 * 255 = 38
                : Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFF252A34) // TasbihColors.darkCardColor
                    : Colors.grey[200],
            borderRadius:
                BorderRadius.circular(MediaQuery.of(context).size.width * 0.03),
            border: Border.all(
              color: hasActiveFilters
                  ? AppColors.getAzkarColor(
                          Theme.of(context).brightness == Brightness.dark)
                      .withAlpha(77) // 0.3 * 255 = 77
                  : Colors.transparent,
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: MediaQuery.of(context).size.width * 0.045,
                color: hasActiveFilters
                    ? AppColors.getAzkarColor(
                        Theme.of(context).brightness == Brightness.dark)
                    : Theme.of(context).brightness == Brightness.dark
                        ? const Color(
                            0xFFAAAAAA) // TasbihColors.darkTextSecondary
                        : Colors.grey[700],
              ),
              SizedBox(width: MediaQuery.of(context).size.width * 0.01),
              Text(
                label,
                style: TextStyle(
                  fontSize: MediaQuery.of(context).size.width < 360 ? 12 : 14,
                  color: hasActiveFilters
                      ? AppColors.getAzkarColor(
                          Theme.of(context).brightness == Brightness.dark)
                      : Theme.of(context).brightness == Brightness.dark
                          ? const Color(
                              0xFFAAAAAA) // TasbihColors.darkTextSecondary
                          : Colors.grey[700],
                  fontWeight:
                      hasActiveFilters ? FontWeight.bold : FontWeight.normal,
                ),
              ),

              // مؤشر الفلاتر النشطة
              if (hasActiveFilters) ...[
                SizedBox(width: MediaQuery.of(context).size.width * 0.01),
                Container(
                  width: MediaQuery.of(context).size.width * 0.02,
                  height: MediaQuery.of(context).size.width * 0.02,
                  decoration: BoxDecoration(
                    color: AppColors.getAzkarColor(
                        Theme.of(context).brightness == Brightness.dark),
                    shape: BoxShape.circle,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
