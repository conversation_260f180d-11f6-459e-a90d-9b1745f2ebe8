import 'package:flutter/material.dart';
import '../../database/database_helper.dart';
import '../../models/poem.dart';
import '../../screens/poem_detail_screen.dart';

/// Clase auxiliar para cargar y navegar a los detalles de un poema desde favoritos
class LoadPoemHelper {
  /// Carga un poema por su ID y navega a la pantalla de detalles
  /// تحديد ما إذا كان معرف القصيدة مشكلة أو hashCode
  static bool isProblemId(dynamic poemId) {
    // تحويل المعرف إلى نص للتعامل معه
    String poemIdStr = poemId.toString();

    // قائمة المعرفات المعروفة بأنها تسبب مشاكل
    const List<String> knownProblemIds = [
      '966232435',
      '810514149',
      '962365697',
    ];

    // التحقق من المعرفات المعروفة
    if (knownProblemIds.contains(poemIdStr)) {
      debugPrint('معرف معروف بأنه يسبب مشاكل: $poemIdStr');
      return true;
    }

    // التحقق من المعرفات الرقمية الكبيرة (ربما hashCode)
    try {
      int numericId = int.parse(poemIdStr);
      // المعرفات الأكبر من 100 مليون هي على الأرجح hashCodes
      if (numericId > 100000000) {
        debugPrint('معرف كبير يعتبر hashCode: $poemIdStr');
        return true;
      }
    } catch (e) {
      // ليس رقمًا
      debugPrint('المعرف ليس رقمًا: $poemIdStr');
    }

    // التحقق من المعرفات الغريبة الأخرى
    if (poemIdStr.contains(':') ||
        poemIdStr.contains('/') ||
        poemIdStr.contains('\\')) {
      debugPrint('معرف يحتوي على رموز غير صالحة: $poemIdStr');
      return true;
    }

    return false;
  }

  /// إنشاء قصيدة خاصة للمعرفات المشكلة
  static Poem createSpecialPoem(dynamic poemId) {
    // تحويل المعرف إلى نص للتعامل معه
    String poemIdStr = poemId.toString();

    bool isLargeNumericId = false;
    bool hasSpecialChars = poemIdStr.contains(':') ||
        poemIdStr.contains('/') ||
        poemIdStr.contains('\\');

    try {
      int numericId = int.parse(poemIdStr);
      isLargeNumericId = numericId > 100000000;
    } catch (e) {
      // ليس رقمًا
      debugPrint('المعرف ليس رقمًا عند إنشاء قصيدة خاصة: $poemIdStr');
    }

    // إنشاء محتوى مناسب للقصيدة الخاصة
    const content =
        'هذه القصيدة من المفضلة ولكن لا يمكن عرض محتواها حالياً. يمكنك إزالتها من المفضلة وإضافتها مرة أخرى لاحقاً.';

    // إنشاء عنوان مناسب بناءً على نوع المشكلة
    String title = 'قصيدة من المفضلة (معرف: $poemIdStr';
    if (isLargeNumericId) {
      title += ' - معرف كبير';
    } else if (hasSpecialChars) {
      title += ' - معرف غير صالح';
    }
    title += ')';

    // إنشاء الأبيات من المحتوى
    final verses =
        content.split('\n').where((line) => line.trim().isNotEmpty).toList();
    debugPrint(
        'تم إنشاء قصيدة خاصة للمعرف: $poemIdStr مع ${verses.length} بيت');

    return Poem(
      id: poemIdStr,
      title: title,
      poet: 'شاعر معروف',
      content: content,
      category: 'مفضلة',
      era: 'معاصر',
      isFavorite: true,
      verses: verses,
    );
  }

  static Future<void> loadAndNavigateToPoem(
    BuildContext context,
    dynamic poemId,
    Function(bool) setLoading,
    Function(String) showErrorMessage,
  ) async {
    try {
      setLoading(true);
      debugPrint('جاري تحميل القصيدة بالمعرف: $poemId');

      // تحويل المعرف إلى نص للتعامل معه في السجلات
      String poemIdStr = poemId.toString();
      debugPrint('تم تحويل المعرف إلى نص: $poemIdStr');

      // التحقق من المعرفات المشكلة للمعلومات فقط
      bool isKnownProblemId = isProblemId(poemIdStr);
      if (isKnownProblemId) {
        debugPrint('تم التعرف على معرف مشكلة: $poemIdStr');
        // لا نقوم بإنشاء قصيدة خاصة مباشرة، بل نحاول البحث عن القصيدة الحقيقية أولاً
        debugPrint('محاولة البحث عن القصيدة الحقيقية قبل إنشاء قصيدة خاصة');
      }

      // محاولة تحميل القصيدة باستخدام طرق مختلفة
      final dbHelper = DatabaseHelper();
      Poem? poem;

      try {
        // 1. محاولة الحصول على القصيدة مباشرة بواسطة المعرف
        debugPrint('محاولة الحصول على القصيدة باستخدام المعرف: $poemIdStr');
        final poemData = await dbHelper.getPoemById(poemIdStr);

        if (poemData != null) {
          debugPrint(
              'تم العثور على القصيدة باستخدام getPoemById: ${poemData['title']}');
          poem = Poem.fromMap(poemData).copyWith(isFavorite: true);

          // التأكد من تهيئة الأبيات بشكل صحيح
          if (poem.verses.isEmpty && poem.content.isNotEmpty) {
            final verses = poem.content
                .split('\n')
                .where((line) => line.trim().isNotEmpty)
                .toList();
            poem = poem.copyWith(verses: verses);
            debugPrint('تم تهيئة الأبيات من المحتوى: ${verses.length} بيت');
          }
        } else {
          debugPrint(
              'لم يتم العثور على القصيدة باستخدام getPoemById، جاري البحث بطرق أخرى');

          // 2. محاولة البحث في جميع القصائد
          final allPoems = await dbHelper.getPoems();
          debugPrint(
              'تم الحصول على ${allPoems.length} قصيدة من قاعدة البيانات');

          // البحث باستخدام طرق مختلفة
          Poem? matchingPoem;

          // أ. البحث بواسطة المعرف المطابق
          matchingPoem =
              allPoems.where((p) => p.id.toString() == poemIdStr).firstOrNull;
          if (matchingPoem != null) {
            debugPrint(
                'تم العثور على القصيدة بواسطة المعرف المطابق: ${matchingPoem.title}');
          }

          // ب. البحث بواسطة hashCode
          if (matchingPoem == null) {
            final hashCodeMatches = allPoems.where((p) =>
                p.id.hashCode.toString() == poemIdStr ||
                p.id == poemIdStr.hashCode.toString());

            if (hashCodeMatches.isNotEmpty) {
              matchingPoem = hashCodeMatches.first;
              debugPrint(
                  'تم العثور على القصيدة بواسطة hashCode: ${matchingPoem.title}');
            }
          }

          // ج. البحث بواسطة المقارنة الرقمية
          if (matchingPoem == null) {
            try {
              int numericId = int.parse(poemIdStr);
              final numericMatches = allPoems.where((p) {
                try {
                  return int.parse(p.id) == numericId;
                } catch (_) {
                  return false;
                }
              });

              if (numericMatches.isNotEmpty) {
                matchingPoem = numericMatches.first;
                debugPrint(
                    'تم العثور على القصيدة بواسطة المقارنة الرقمية: ${matchingPoem.title}');
              }
            } catch (_) {
              debugPrint('لا يمكن تحويل المعرف إلى رقم: $poemIdStr');
            }
          }

          // د. البحث بواسطة التطابق الجزئي
          if (matchingPoem == null) {
            final partialMatches = allPoems.where(
                (p) => p.id.contains(poemIdStr) || poemIdStr.contains(p.id));

            if (partialMatches.isNotEmpty) {
              matchingPoem = partialMatches.first;
              debugPrint(
                  'تم العثور على القصيدة بواسطة التطابق الجزئي: ${matchingPoem.title}');
            }
          }

          if (matchingPoem != null) {
            poem = matchingPoem.copyWith(isFavorite: true);

            // التأكد من تهيئة الأبيات بشكل صحيح
            if (poem.verses.isEmpty && poem.content.isNotEmpty) {
              final verses = poem.content
                  .split('\n')
                  .where((line) => line.trim().isNotEmpty)
                  .toList();
              poem = poem.copyWith(verses: verses);
              debugPrint('تم تهيئة الأبيات من المحتوى: ${verses.length} بيت');
            }
          } else {
            debugPrint('لم يتم العثور على القصيدة بأي طريقة');
          }
        }
      } catch (e) {
        debugPrint('خطأ أثناء البحث عن القصيدة: $e');
      }

      setLoading(false);

      // إذا تم العثور على القصيدة، الانتقال إلى صفحة التفاصيل
      if (poem != null) {
        final foundPoem = poem; // إنشاء متغير غير قابل للإلغاء
        debugPrint('تم العثور على القصيدة الحقيقية: ${foundPoem.title}');
        if (context.mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => PoemDetailScreen(poem: foundPoem),
            ),
          );
        }
        return;
      }

      // إذا كان المعرف مشكلة معروفة، إنشاء قصيدة خاصة
      if (isKnownProblemId) {
        debugPrint(
            'لم يتم العثور على القصيدة الحقيقية، إنشاء قصيدة خاصة للمعرف المشكل: $poemIdStr');
        final specialPoem = createSpecialPoem(poemIdStr);

        if (context.mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => PoemDetailScreen(poem: specialPoem),
            ),
          );
        }
        return;
      }

      // إذا لم يتم العثور على القصيدة، إنشاء قصيدة افتراضية
      debugPrint(
          'لم يتم العثور على القصيدة, إنشاء قصيدة افتراضية للمعرف: $poemIdStr');

      // إنشاء محتوى مناسب للقصيدة الافتراضية
      const content =
          'لم يتم العثور على محتوى القصيدة. يمكنك إزالتها من المفضلة وإضافتها مرة أخرى لاحقاً.';

      // إنشاء الأبيات من المحتوى
      final verses =
          content.split('\n').where((line) => line.trim().isNotEmpty).toList();
      debugPrint('تم إنشاء ${verses.length} بيت للقصيدة الافتراضية');

      final defaultPoem = Poem(
        id: poemIdStr,
        title: 'قصيدة غير متوفرة (معرف: $poemIdStr)',
        poet: 'غير معروف',
        content: content,
        category: 'مفضلة',
        era: 'غير معروف',
        isFavorite: true,
        verses: verses,
      );

      if (context.mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => PoemDetailScreen(poem: defaultPoem),
          ),
        );
      }
    } catch (e) {
      setLoading(false);
      debugPrint('خطأ في تحميل القصيدة: $e');
      showErrorMessage('حدث خطأ أثناء تحميل القصيدة');
    }
  }
}
