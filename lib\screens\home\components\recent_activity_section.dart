//قسم النشاط الأخير
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
//import 'package:provider/provider.dart';
import 'dart:math' as math;
import '../../../utils/constants.dart';
import '../../../utils/app_colors.dart';
//import '../controllers/home_controller.dart';
import 'package:intl/intl.dart';

class RecentActivitySection extends StatefulWidget {
  const RecentActivitySection({super.key});

  @override
  State<RecentActivitySection> createState() => _RecentActivitySectionState();
}

class _RecentActivitySectionState extends State<RecentActivitySection>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    )..forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    final isDarkMode = brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم
        _buildSectionTitle(context),

        const SizedBox(height: 16),

        // بطاقة النشاطات
        AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(
                  0,
                  15 * (1 - _animationController.value),
                ),
                child: Opacity(
                  opacity: _animationController.value,
                  child: _buildActivityCard(context, isDarkMode),
                ),
              );
            }),
      ],
    );
  }

  Widget _buildSectionTitle(BuildContext context) {
    return Row(
      children: [
        _buildDecorativeLine(context),
        const SizedBox(width: 8),
        Text(
          'نشاطك الأخير',
          style: Theme.of(context).textTheme.titleLarge!.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const Spacer(),

        // زر عرض الكل
        TextButton.icon(
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text('سيتم توفير سجل النشاط الكامل قريباً'),
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                duration: const Duration(seconds: 2),
                action: SnackBarAction(
                  label: 'موافق',
                  onPressed: () {},
                ),
              ),
            );
          },
          icon: const Icon(Icons.arrow_forward, size: 16),
          label: const Text('عرض الكل'),
          style: TextButton.styleFrom(
            foregroundColor: Theme.of(context).colorScheme.tertiary,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(
                color: Theme.of(context).colorScheme.tertiary.withOpacity(0.3),
                width: 1.0,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDecorativeLine(BuildContext context) {
    return Container(
      width: 4,
      height: 24,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.tertiary,
        borderRadius: BorderRadius.circular(2),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.tertiary.withOpacity(0.3),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityCard(BuildContext context, bool isDarkMode) {
    final cardColor = isDarkMode
        ? Theme.of(context).colorScheme.surface.withOpacity(0.8)
        : Theme.of(context).colorScheme.surface;

    return Card(
      elevation: 8,
      shadowColor:
          Theme.of(context).shadowColor.withOpacity(isDarkMode ? 0.4 : 0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: BorderSide(
          color: Theme.of(context).dividerColor.withOpacity(0.1),
          width: 0.5,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            colors: [
              cardColor,
              cardColor.withOpacity(0.95),
            ],
          ),
        ),
        child: Stack(
          children: [
            // زخرفة إسلامية في الخلفية
            Positioned(
              top: -80,
              right: -80,
              child: Opacity(
                opacity: isDarkMode ? 0.05 : 0.03,
                child: SvgPicture.asset(
                  'assets/images/p2.svg',
                  width: 200,
                  height: 200,
                  colorFilter: ColorFilter.mode(
                    Theme.of(context).colorScheme.primary,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),

            // تأثير الوهج المتحرك
            AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return CustomPaint(
                  painter: ActivityGlowPainter(
                    progress: _animationController.value,
                    baseColor: Theme.of(context).colorScheme.primary,
                    isDarkMode: isDarkMode,
                  ),
                  child: const SizedBox.expand(),
                );
              },
            ),

            // عناصر النشاط
            Column(
              children: [
                _buildActivityItem(
                  context,
                  isDarkMode,
                  title: 'ورد الصباح',
                  section: 'الأذكار',
                  icon: Icons.wb_sunny_rounded,
                  color: AppColors.azkarColor,
                  time: _getFormattedTime(
                      DateTime.now().subtract(const Duration(hours: 1))),
                  progress: 0.8,
                  onTap: () => Navigator.pushNamed(context, '/azkar'),
                ),
                _buildDivider(context),
                _buildActivityItem(
                  context,
                  isDarkMode,
                  title: 'المسبحة',
                  section: 'تسبيح',
                  icon: Icons.panorama_fish_eye,
                  color: Theme.of(context).colorScheme.primary,
                  time: _getFormattedTime(
                      DateTime.now().subtract(const Duration(hours: 3))),
                  count: '33/100',
                  onTap: () =>
                      Navigator.pushNamed(context, AppConstants.tasbihRoute),
                ),
                _buildDivider(context),
                _buildActivityItem(
                  context,
                  isDarkMode,
                  title: 'ورد المساء',
                  section: 'الأذكار',
                  icon: Icons.nights_stay_rounded,
                  color: AppColors.azkarColor,
                  time: 'أمس',
                  progress: 1.0,
                  completed: true,
                  onTap: () => Navigator.pushNamed(context, '/azkar'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(
    BuildContext context,
    bool isDarkMode, {
    required String title,
    required String section,
    required IconData icon,
    required Color color,
    required String time,
    required VoidCallback onTap,
    double? progress,
    String? count,
    bool completed = false,
  }) {
    final textColor = isDarkMode ? Colors.white : Colors.black87;
    final subtitleColor = isDarkMode
        ? Theme.of(context).colorScheme.onSurface.withOpacity(0.6)
        : Theme.of(context).colorScheme.onSurface.withOpacity(0.6);

    return InkWell(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      splashColor: color.withOpacity(0.1),
      highlightColor: color.withOpacity(0.05),
      borderRadius: BorderRadius.circular(16),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            // أيقونة النشاط
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color
                    .withAlpha(isDarkMode ? 51 : 38), // 0.2*255=51, 0.15*255=38
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: color.withOpacity(isDarkMode ? 0.3 : 0.2),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),

            const SizedBox(width: 16),

            // تفاصيل النشاط
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: textColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Text(
                        section,
                        style: TextStyle(
                          color: subtitleColor,
                          fontSize: 13,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        width: 4,
                        height: 4,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: subtitleColor,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        time,
                        style: TextStyle(
                          color: subtitleColor,
                          fontSize: 13,
                        ),
                      ),
                    ],
                  ),

                  // شريط التقدم أو العدد (إذا وجد)
                  if (progress != null) ...[
                    const SizedBox(height: 8),
                    ProgressIndicator(
                      progress: progress,
                      color: color,
                      isDarkMode: isDarkMode,
                      completed: completed,
                    ),
                  ],

                  if (count != null) ...[
                    const SizedBox(height: 6),
                    Text(
                      count,
                      style: TextStyle(
                        color: color,
                        fontWeight: FontWeight.bold,
                        fontSize: 13,
                      ),
                    ),
                  ],
                ],
              ),
            ),

            // سهم النقر
            Icon(
              Icons.arrow_forward_ios,
              size: 14,
              color: textColor.withOpacity(0.5),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    final color = Theme.of(context).dividerColor.withOpacity(0.1);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      height: 1,
      color: color,
    );
  }

  String _getFormattedTime(DateTime time) {
    try {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final yesterday = today.subtract(const Duration(days: 1));
      final activityDate = DateTime(time.year, time.month, time.day);

      if (activityDate == today) {
        return DateFormat('hh:mm a', 'ar').format(time);
      } else if (activityDate == yesterday) {
        return 'أمس';
      } else {
        return DateFormat('dd/MM', 'ar').format(time);
      }
    } catch (e) {
      debugPrint('خطأ في تنسيق الوقت: $e');
      // إرجاع قيمة افتراضية في حالة حدوث خطأ
      return 'مؤخراً';
    }
  }
}

// مؤشر التقدم المخصص
class ProgressIndicator extends StatelessWidget {
  final double progress;
  final Color color;
  final bool isDarkMode;
  final bool completed;

  const ProgressIndicator({
    super.key,
    required this.progress,
    required this.color,
    required this.isDarkMode,
    this.completed = false,
  });

  @override
  Widget build(BuildContext context) {
    final baseColor = isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Stack(
          children: [
            // خلفية شريط التقدم
            Container(
              height: 6,
              width: double.infinity,
              decoration: BoxDecoration(
                color: baseColor,
                borderRadius: BorderRadius.circular(3),
              ),
            ),

            // شريط التقدم
            Container(
              height: 6,
              width: double.infinity * progress,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: [
                    color.withOpacity(0.7),
                    color,
                  ],
                ),
                borderRadius: BorderRadius.circular(3),
                boxShadow: [
                  BoxShadow(
                    color: color.withOpacity(0.3),
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
            ),
          ],
        ),

        // النسبة المئوية أو حالة الاكتمال
        const SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (completed)
              Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: color,
                    size: 14,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'مكتمل',
                    style: TextStyle(
                      color: color,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              )
            else
              Text(
                '${(progress * 100).toInt()}%',
                style: TextStyle(
                  color: color,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),

            // وقت المتبقي (إذا لم يكتمل)
            if (!completed)
              Text(
                'المتبقي: ${5 - (progress * 5).toInt()} دقائق',
                style: TextStyle(
                  color:
                      isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
                  fontSize: 12,
                ),
              ),
          ],
        ),
      ],
    );
  }
}

// رسام مخصص لتأثير الوهج المتحرك
class ActivityGlowPainter extends CustomPainter {
  final double progress;
  final Color baseColor;
  final bool isDarkMode;

  ActivityGlowPainter({
    required this.progress,
    required this.baseColor,
    required this.isDarkMode,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);

    // الوهج الأول
    final topGlow = RadialGradient(
      center: Alignment(
        0.7 + (0.2 * math.sin(progress * math.pi * 1.2)),
        -0.6 + (0.1 * math.cos(progress * math.pi * 1.2)),
      ),
      radius: 0.7 + (0.1 * math.sin(progress * math.pi * 1.5)),
      colors: [
        baseColor.withOpacity(isDarkMode ? 0.08 : 0.04),
        baseColor.withOpacity(isDarkMode ? 0.03 : 0.01),
        Colors.transparent,
      ],
      stops: const [0.0, 0.3, 0.6],
    ).createShader(rect);

    canvas.drawRect(rect, Paint()..shader = topGlow);

    // الوهج الثاني
    final bottomGlow = RadialGradient(
      center: Alignment(
        -0.5 + (0.1 * math.cos(progress * math.pi * 2)),
        0.6 + (0.1 * math.sin(progress * math.pi * 2)),
      ),
      radius: 0.6 + (0.1 * math.sin(progress * math.pi * 1.7)),
      colors: [
        baseColor.withOpacity(isDarkMode ? 0.06 : 0.03),
        baseColor.withOpacity(isDarkMode ? 0.02 : 0.01),
        Colors.transparent,
      ],
      stops: const [0.0, 0.4, 0.7],
    ).createShader(rect);

    canvas.drawRect(rect, Paint()..shader = bottomGlow);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
