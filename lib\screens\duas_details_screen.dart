// شاشة تفاصيل الأدعية

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui'; // لـ ImageFilter
import 'package:provider/provider.dart';
import '../models/dua.dart'; // افتراض وجود هذا الملف (Dua, DuaCategory)
import '../utils/app_colors.dart'; // افتراض وجود هذا الملف (AppColors, getDuasColor, getSourceColor, getFadlColor, favoritesColor)
import '../providers/duas_provider.dart'; // افتراض وجود هذا الملف (DuasProvider, isDuaFavorite, toggleFavorite)
import 'package:share_plus/share_plus.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:math' as math; // لإستخدام math.pi
import '../utils/icon_helper.dart'; // إضافة استيراد IconHelper

class DuasDetailsScreen extends StatefulWidget {
  final DuaCategory category;

  const DuasDetailsScreen({
    Key? key,
    required this.category,
  }) : super(key: key);

  @override
  State<DuasDetailsScreen> createState() => _DuasDetailsScreenState();
}

class _DuasDetailsScreenState extends State<DuasDetailsScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late ScrollController _scrollController;
  bool _showScrollToTop = false;
  bool _showAppBarTitle = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _scrollController = ScrollController();
    _scrollController.addListener(_scrollListener);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    final currentOffset = _scrollController.offset;

    final newShowScrollToTop = currentOffset > 300;
    if (newShowScrollToTop != _showScrollToTop) {
      if (mounted) {
        setState(() {
          _showScrollToTop = newShowScrollToTop;
        });
      }
    }

    final newShowAppBarTitle = currentOffset > 150;
    if (newShowAppBarTitle != _showAppBarTitle) {
      if (mounted) {
        setState(() {
          _showAppBarTitle = newShowAppBarTitle;
        });
      }
    }
  }

  void _scrollToTop() {
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 400),
      curve: Curves.easeOutCubic,
    );
  }

  Animation<double> _getAnimation(int index) {
    final double normalizedIndex = index /
        (widget.category.items.isNotEmpty
            ? widget.category.items.length
            : 10.0);
    final double startDelay = (normalizedIndex * 0.3).clamp(0.0, 0.4);
    final double endDelay = (startDelay + 0.6).clamp(startDelay, 1.0);

    return Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Interval(
          startDelay,
          endDelay,
          curve: Curves.easeOutCubic,
        ),
      ),
    );
  }

  void _showDuaDialog(Dua dua) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final duasColor = AppColors.getDuasColor(isDarkMode);

    const dialogBorderRadiusValue = 28.0;
    const dialogBorderRadius =
        BorderRadius.all(Radius.circular(dialogBorderRadiusValue));
    const dialogBorderWidth = 1.5;
    const dialogAlphaValue = 230;

    showDialog(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.black.withAlpha(120),
      builder: (context) => Dialog(
        shape: const RoundedRectangleBorder(borderRadius: dialogBorderRadius),
        elevation: 16,
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
        child: ClipRRect(
          borderRadius: dialogBorderRadius,
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    (isDarkMode ? Colors.grey[850]! : Colors.white)
                        .withAlpha(dialogAlphaValue),
                    (isDarkMode ? Colors.grey[800]! : Colors.grey[100]!)
                        .withAlpha(dialogAlphaValue),
                  ],
                ),
                borderRadius: dialogBorderRadius,
                border: Border.all(
                    color: duasColor.withAlpha(76), width: dialogBorderWidth),
              ),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.85),
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildDialogSectionTitle(dua, duasColor, isDarkMode),
                      const SizedBox(height: 16),
                      _buildAnimatedDialogDivider(duasColor),
                      const SizedBox(height: 16),
                      _buildDialogDuaTextContent(dua, duasColor, isDarkMode),
                      const SizedBox(height: 16),
                      if (dua.translation != null &&
                          dua.translation!.isNotEmpty) ...[
                        _buildDialogDuaTranslation(dua, isDarkMode),
                        const SizedBox(height: 16),
                      ],
                      if (dua.virtue != null && dua.virtue!.isNotEmpty) ...[
                        _buildDialogDuaVirtue(dua, duasColor, isDarkMode),
                        const SizedBox(height: 16),
                      ],
                      _buildDialogActionButtons(dua, duasColor, isDarkMode),
                      const SizedBox(height: 8),
                      _buildDialogCloseButton(isDarkMode),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDialogSectionTitle(Dua dua, Color duasColor, bool isDarkMode) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeOutCubic,
      child: Row(
        textDirection: TextDirection.rtl,
        children: [
          TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: 0.0, end: 1.0),
            duration: const Duration(milliseconds: 800),
            curve: Curves.elasticOut,
            child: Icon(Icons.format_quote, color: duasColor, size: 20),
            builder: (context, animValue, iconChild) {
              return Transform.rotate(
                angle: animValue * 2 * math.pi * 0.1,
                child: Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: duasColor.withAlpha(51),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: duasColor.withAlpha(30),
                        blurRadius: 8 * animValue,
                        spreadRadius: 2 * animValue,
                      ),
                    ],
                  ),
                  child: iconChild,
                ),
              );
            },
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ShaderMask(
              shaderCallback: (bounds) {
                return LinearGradient(
                  colors: [
                    isDarkMode ? Colors.white : Colors.black87,
                    duasColor
                  ],
                  stops: const [0.7, 1.0],
                  begin: Alignment.centerRight,
                  end: Alignment.centerLeft,
                ).createShader(bounds);
              },
              child: Text(
                dua.source ?? 'دعاء',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black87,
                ),
                textDirection: TextDirection.rtl,
                textAlign: TextAlign.right,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ],
      ),
      builder: (context, value, rowAsChild) {
        return Opacity(
          opacity: value,
          child: Transform.translate(
            offset: Offset(0, 20 * (1 - value)),
            child: rowAsChild,
          ),
        );
      },
    );
  }

  Widget _buildAnimatedDialogDivider(Color duasColor) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 600),
      curve: Curves.easeOutCubic,
      child: Container(
        height: 1.5,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.transparent,
              duasColor.withAlpha(100),
              Colors.transparent
            ],
            stops: const [0.1, 0.5, 0.9],
          ),
        ),
      ),
      builder: (context, value, dividerChild) =>
          Opacity(opacity: value, child: dividerChild),
    );
  }

  Widget _buildDialogDuaTextContent(Dua dua, Color duasColor, bool isDarkMode) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 700),
      curve: Curves.easeOutCubic,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: duasColor.withAlpha(20),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: duasColor.withAlpha(60), width: 1),
          boxShadow: [
            BoxShadow(
              color: duasColor.withAlpha(10),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            Positioned(
              right: -20,
              bottom: -20,
              child: Opacity(
                opacity: 0.04,
                child: Icon(Icons.format_quote, size: 100, color: duasColor),
              ),
            ),
            ConstrainedBox(
              constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.3),
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SelectableText(
                      dua.text,
                      style: TextStyle(
                        fontSize: 17,
                        height: 1.7,
                        color: isDarkMode ? Colors.white : Colors.black87,
                        shadows: [
                          Shadow(
                              color: duasColor.withAlpha(20),
                              offset: const Offset(0, 1),
                              blurRadius: 1),
                        ],
                      ),
                      textAlign: TextAlign.right,
                      textDirection: TextDirection.rtl,
                      key: ValueKey('dialog_dua_text_${dua.id}'),
                    ),
                    if (dua.text.length > 150) ...[
                      const SizedBox(height: 8),
                      Icon(Icons.keyboard_double_arrow_down,
                          size: 16, color: duasColor.withAlpha(128)),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      builder: (context, value, textContentChild) {
        return Opacity(
          opacity: value,
          child: Transform.translate(
            offset: Offset(0, 25 * (1 - value)),
            child: textContentChild,
          ),
        );
      },
    );
  }

  Widget _buildDialogDuaTranslation(Dua dua, bool isDarkMode) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 800),
      curve: Curves.easeOutCubic,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: (isDarkMode ? Colors.grey[800]! : Colors.grey[100]!)
              .withAlpha(120),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
              color: (isDarkMode ? Colors.grey[700]! : Colors.grey[200]!)
                  .withAlpha(150),
              width: 1),
        ),
        constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.15),
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SelectableText(
                dua.translation!,
                style: TextStyle(
                  fontSize: 13,
                  fontStyle: FontStyle.italic,
                  height: 1.4,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                ),
                textAlign: TextAlign.right,
                textDirection: TextDirection.rtl,
                key: ValueKey('dialog_dua_translation_${dua.id}'),
              ),
              if (dua.translation!.length > 100) ...[
                const SizedBox(height: 8),
                Icon(Icons.keyboard_double_arrow_down,
                    size: 14, color: Colors.grey.withAlpha(128)),
              ],
            ],
          ),
        ),
      ),
      builder: (context, value, translationChild) =>
          Opacity(opacity: value, child: translationChild),
    );
  }

  Widget _buildDialogDuaVirtue(Dua dua, Color duasColor, bool isDarkMode) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 900),
      curve: Curves.easeOutCubic,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: duasColor.withAlpha(10),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: duasColor.withAlpha(40), width: 1),
          boxShadow: [
            BoxShadow(
              color: duasColor.withAlpha(8),
              blurRadius: 5,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          textDirection: TextDirection.rtl,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: duasColor.withAlpha(25),
                shape: BoxShape.circle,
              ),
              child: Icon(Icons.star, color: duasColor, size: 16),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.15),
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      SelectableText(
                        dua.virtue!,
                        style: TextStyle(
                          fontSize: 13,
                          height: 1.4,
                          color:
                              isDarkMode ? Colors.grey[300] : Colors.grey[700],
                        ),
                        textAlign: TextAlign.right,
                        textDirection: TextDirection.rtl,
                        key: ValueKey('dialog_dua_virtue_${dua.id}'),
                      ),
                      if (dua.virtue!.length > 100) ...[
                        const SizedBox(height: 8),
                        Center(
                            child: Icon(Icons.keyboard_double_arrow_down,
                                size: 14, color: duasColor.withAlpha(128))),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      builder: (context, value, virtueChild) {
        return Opacity(
          opacity: value,
          child: Transform.translate(
            offset: Offset(0, 15 * (1 - value)),
            child: virtueChild,
          ),
        );
      },
    );
  }

  Widget _buildDialogActionButtons(Dua dua, Color duasColor, bool isDarkMode) {
    return Consumer<DuasProvider>(
      builder: (context, duasProvider, _) {
        final isFavorite = duasProvider.isDuaFavorite(dua.id);

        return TweenAnimationBuilder<double>(
          tween: Tween<double>(begin: 0.0, end: 1.0),
          duration: const Duration(milliseconds: 1000),
          curve: Curves.easeOutCubic,
          child: LayoutBuilder(
            builder: (context, constraints) {
              final bool isSmallScreen = constraints.maxWidth < 300;

              if (isSmallScreen) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildButton(
                      icon: isFavorite ? Icons.favorite : Icons.favorite_border,
                      text: isFavorite ? 'إزالة من المفضلة' : 'إضافة للمفضلة',
                      color: isFavorite ? AppColors.favoritesColor : duasColor,
                      onTap: () async =>
                          await _handleFavoriteToggle(dua, duasProvider),
                    ),
                    const SizedBox(height: 10),
                    _buildButton(
                      icon: Icons.share,
                      text: 'مشاركة',
                      color: duasColor,
                      onTap: () => _handleShareDua(dua),
                    ),
                    const SizedBox(height: 10),
                    _buildButton(
                      icon: Icons.copy,
                      text: 'نسخ',
                      color: duasColor,
                      onTap: () => _handleCopyDua(dua, duasColor),
                    ),
                  ],
                );
              } else {
                return Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      textDirection: TextDirection.rtl,
                      children: [
                        Expanded(
                          child: _buildButton(
                            icon: isFavorite
                                ? Icons.favorite
                                : Icons.favorite_border,
                            text: isFavorite ? 'إزالة' : 'إضافة للمفضلة',
                            color: isFavorite
                                ? AppColors.favoritesColor
                                : duasColor,
                            onTap: () async =>
                                await _handleFavoriteToggle(dua, duasProvider),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildButton(
                            icon: Icons.share,
                            text: 'مشاركة',
                            color: duasColor,
                            onTap: () => _handleShareDua(dua),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _buildButton(
                          icon: Icons.copy,
                          text: 'نسخ',
                          color: duasColor,
                          onTap: () => _handleCopyDua(dua, duasColor),
                        ),
                      ],
                    )
                  ],
                );
              }
            },
          ),
          builder: (context, value, buttonsChild) =>
              Opacity(opacity: value, child: buttonsChild),
        );
      },
    );
  }

  Future<void> _handleFavoriteToggle(Dua dua, DuasProvider provider) async {
    HapticFeedback.mediumImpact();
    final bool wasInFavorites = provider.isDuaFavorite(dua.id);
    final success = await provider.toggleFavorite(dua);
    if (!success || !mounted) return;
    _showFavoriteSnackBar(context, wasInFavorites, provider, dua);
  }

  void _handleShareDua(Dua dua) {
    HapticFeedback.mediumImpact();
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    }
    _shareDua(dua);
  }

  void _handleCopyDua(Dua dua, Color duasColor) {
    HapticFeedback.mediumImpact();
    Clipboard.setData(ClipboardData(text: dua.text));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Row(
            textDirection: TextDirection.rtl,
            children: [
              Icon(Icons.check_circle, color: Colors.white, size: 16),
              SizedBox(width: 8),
              Text('تم نسخ الدعاء'),
            ],
          ),
          backgroundColor: duasColor,
          duration: const Duration(seconds: 1),
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        ),
      );
    }
  }

  Widget _buildDialogCloseButton(bool isDarkMode) {
    return Align(
      alignment: Alignment.centerLeft,
      child: IconButton(
        onPressed: () {
          HapticFeedback.mediumImpact();
          if (mounted) Navigator.pop(context);
        },
        icon: Icon(Icons.close,
            color: (isDarkMode ? Colors.grey[400] : Colors.grey[600])
                ?.withAlpha(204),
            size: 22),
        tooltip: 'إغلاق',
        padding: const EdgeInsets.all(4),
      ),
    );
  }

  Widget _buildButton({
    required IconData icon,
    required String text,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      borderRadius: BorderRadius.circular(30),
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: onTap,
        splashColor: color.withAlpha(50),
        highlightColor: color.withAlpha(30),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 10),
          decoration: BoxDecoration(
            color: color.withAlpha(20),
            borderRadius: BorderRadius.circular(30),
            border: Border.all(color: color.withAlpha(40), width: 1),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            textDirection: TextDirection.rtl,
            children: [
              Icon(icon, color: color, size: 18),
              const SizedBox(width: 8),
              Flexible(
                child: Text(
                  text,
                  style: TextStyle(
                      color: color, fontWeight: FontWeight.bold, fontSize: 13),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showFavoriteSnackBar(BuildContext context, bool wasInFavorites,
      DuasProvider provider, Dua dua) {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    scaffoldMessenger.clearSnackBars();

    scaffoldMessenger.showSnackBar(
      SnackBar(
        content: Row(
          textDirection: TextDirection.rtl,
          children: [
            TweenAnimationBuilder<double>(
              tween: Tween<double>(begin: 0.5, end: 1.0),
              duration: const Duration(milliseconds: 400),
              curve: Curves.elasticOut,
              builder: (context, value, _) {
                return Transform.scale(
                  scale: value,
                  child: Icon(
                    !wasInFavorites ? Icons.favorite : Icons.favorite_border,
                    color: Colors.white,
                    size: 18,
                  ),
                );
              },
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                !wasInFavorites
                    ? 'تمت الإضافة إلى المفضلة'
                    : 'تمت الإزالة من المفضلة',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        backgroundColor:
            !wasInFavorites ? AppColors.favoritesColor : Colors.grey.shade700,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        elevation: 6,
        margin: const EdgeInsets.all(16),
        action: SnackBarAction(
          label: 'تراجع',
          textColor: Colors.white,
          onPressed: () {
            provider.toggleFavorite(dua);
          },
        ),
      ),
    );
  }

  void _shareDua(Dua dua) {
    HapticFeedback.lightImpact();
    final StringBuffer shareTextBuffer = StringBuffer();

    shareTextBuffer.writeln('❁ ❁ ❁ ❁ ❁');
    shareTextBuffer.writeln(dua.text);
    shareTextBuffer.writeln('❁ ❁ ❁ ❁ ❁');

    if (dua.translation != null && dua.translation!.isNotEmpty) {
      shareTextBuffer.writeln('\nالترجمة: ${dua.translation}');
    }
    if (dua.source != null && dua.source!.isNotEmpty) {
      shareTextBuffer.writeln('\n📚 المصدر: ${dua.source}');
    }
    if (dua.reference != null && dua.reference!.isNotEmpty) {
      shareTextBuffer.writeln('📖 المرجع: ${dua.reference}');
    }
    if (dua.virtue != null && dua.virtue!.isNotEmpty) {
      shareTextBuffer.writeln('\n✨ الفضل:\n${dua.virtue}');
    }
    shareTextBuffer.writeln('\n🌟 مشاركة من تطبيق وهج السالك 🌟');
    Share.share(shareTextBuffer.toString());

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Row(
            textDirection: TextDirection.rtl,
            children: [
              Icon(Icons.check_circle, color: Colors.white, size: 16),
              SizedBox(width: 8),
              Text('تم تحضير المشاركة'),
            ],
          ),
          backgroundColor: AppColors.getDuasColor(
              Theme.of(context).brightness == Brightness.dark),
          duration: const Duration(seconds: 1),
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        ),
      );
    }
  }

  Widget _buildEnhancedDuaItem(
      Dua dua, Color duasColor, bool isDarkMode, int index) {
    const cardBorderRadiusValue = 24.0;
    const cardBorderRadius =
        BorderRadius.all(Radius.circular(cardBorderRadiusValue));
    final sourceColor = AppColors.getSourceColor(isDarkMode);
    final fadlColor = AppColors.getFadlColor(isDarkMode);

    return Padding(
      key: ValueKey('dua_item_${dua.id}'),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: AnimatedBuilder(
        animation: _getAnimation(index),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: cardBorderRadius,
            color: isDarkMode ? Colors.grey[850] : Colors.white,
            boxShadow: [
              BoxShadow(
                color: duasColor.withAlpha(10),
                blurRadius: 8,
                spreadRadius: 0,
                offset: const Offset(0, 2),
              ),
            ],
            border: Border.all(
              color: duasColor.withAlpha(20),
              width: 1,
            ),
          ),
          child: ClipRRect(
            borderRadius: cardBorderRadius,
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  HapticFeedback.mediumImpact();
                  _showDuaDialog(dua);
                },
                splashColor: duasColor.withAlpha(30),
                highlightColor: duasColor.withAlpha(20),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          if (dua.source != null && dua.source!.isNotEmpty)
                            _buildMetaChip(
                                icon: Icons.bookmark_border,
                                text: dua.source!,
                                color: sourceColor,
                                isDarkMode: isDarkMode),
                          if (dua.source != null &&
                              dua.source!.isNotEmpty &&
                              dua.reference != null &&
                              dua.reference!.isNotEmpty)
                            const SizedBox(width: 8),
                          if (dua.reference != null &&
                              dua.reference!.isNotEmpty)
                            _buildMetaChip(
                                icon: Icons.menu_book_outlined,
                                text: dua.reference!,
                                color: duasColor,
                                isDarkMode: isDarkMode),
                          const Spacer(),
                          if (dua.isFeatured) _buildFeaturedStarIcon(),
                        ],
                      ),
                      if ((dua.source != null && dua.source!.isNotEmpty) ||
                          (dua.reference != null && dua.reference!.isNotEmpty))
                        const SizedBox(height: 12),
                      _buildDuaTextSection(dua, duasColor, isDarkMode),
                      if (dua.translation != null &&
                          dua.translation!.isNotEmpty) ...[
                        const SizedBox(height: 12),
                        _buildTranslationSection(dua, isDarkMode),
                      ],
                      if (dua.virtue != null && dua.virtue!.isNotEmpty) ...[
                        const SizedBox(height: 12),
                        _buildVirtueSection(dua, fadlColor, isDarkMode),
                      ],
                      const SizedBox(height: 12),
                      _buildDuaItemActions(dua, duasColor, index, context),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
        builder: (context, child) {
          final animationValue = _getAnimation(index).value;
          return Opacity(
            opacity: animationValue,
            child: Transform.translate(
              offset: Offset(0, 30 * (1 - animationValue)),
              child: child,
            ),
          );
        },
      ),
    );
  }

  Widget _buildMetaChip(
      {required IconData icon,
      required String text,
      required Color color,
      required bool isDarkMode}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      decoration: BoxDecoration(
        color: color.withAlpha(isDarkMode ? 30 : 20),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        textDirection: TextDirection.rtl,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 6),
          Flexible(
            child: Text(
              text,
              style: TextStyle(
                  fontSize: 11, fontWeight: FontWeight.w600, color: color),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturedStarIcon() {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.8, end: 1.0),
      duration: const Duration(milliseconds: 1200),
      curve: Curves.easeInOut,
      builder: (context, value, _) {
        return Transform.scale(
          scale: value,
          child: Container(
            padding: const EdgeInsets.all(5),
            decoration: BoxDecoration(
              color: AppColors.favoritesColor.withAlpha(20),
              shape: BoxShape.circle,
            ),
            child: const Icon(Icons.star,
                color: AppColors.favoritesColor, size: 16),
          ),
        );
      },
    );
  }

  Widget _buildDuaTextSection(Dua dua, Color duasColor, bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: duasColor.withAlpha(isDarkMode ? 20 : 10),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
            color: duasColor.withAlpha(isDarkMode ? 30 : 15), width: 1),
      ),
      child: Stack(
        children: [
          Positioned(
            left: -15,
            top: -15,
            child: Opacity(
              opacity: 0.04,
              child: Icon(Icons.format_quote, size: 60, color: duasColor),
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ConstrainedBox(
                constraints: BoxConstraints(
                    maxHeight: dua.text.length > 180 ? 120 : double.infinity),
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: SelectableText(
                    dua.text,
                    style: TextStyle(
                      fontSize: 17,
                      height: 1.7,
                      color: isDarkMode
                          ? Colors.white.withAlpha(230)
                          : Colors.black87,
                    ),
                    key: ValueKey('list_dua_text_${dua.id}'),
                    textAlign: TextAlign.right,
                    textDirection: TextDirection.rtl,
                  ),
                ),
              ),
              if (dua.text.length > 180) ...[
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  textDirection: TextDirection.rtl,
                  children: [
                    Icon(Icons.keyboard_double_arrow_down,
                        size: 14, color: duasColor.withAlpha(100)),
                    const SizedBox(width: 4),
                    Text(
                      'اضغط للمزيد في الحوار',
                      style: TextStyle(
                          fontSize: 11,
                          color: duasColor.withAlpha(150),
                          fontStyle: FontStyle.italic),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTranslationSection(Dua dua, bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color:
            (isDarkMode ? Colors.grey[800] : Colors.grey[100])?.withAlpha(100),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
            color: (isDarkMode ? Colors.grey[700] : Colors.grey[200])!
                .withAlpha(120),
            width: 0.8),
      ),
      child: ConstrainedBox(
        constraints: BoxConstraints(
            maxHeight: dua.translation!.length > 120 ? 80 : double.infinity),
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SelectableText(
                dua.translation!,
                style: TextStyle(
                  fontSize: 13,
                  fontStyle: FontStyle.italic,
                  height: 1.4,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                ),
                key: ValueKey('list_dua_translation_${dua.id}'),
                textAlign: TextAlign.right,
                textDirection: TextDirection.rtl,
              ),
              if (dua.translation!.length > 120) ...[
                const SizedBox(height: 4),
                Icon(Icons.keyboard_double_arrow_down,
                    size: 12, color: Colors.grey.withAlpha(100)),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVirtueSection(Dua dua, Color fadlColor, bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: fadlColor.withAlpha(isDarkMode ? 20 : 10),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
            color: fadlColor.withAlpha(isDarkMode ? 30 : 15), width: 0.8),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        textDirection: TextDirection.rtl,
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: fadlColor.withAlpha(isDarkMode ? 30 : 20),
              shape: BoxShape.circle,
            ),
            child: Icon(Icons.star_border, color: fadlColor, size: 16),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                  maxHeight: dua.virtue!.length > 120 ? 80 : double.infinity),
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    SelectableText(
                      dua.virtue!,
                      style: TextStyle(
                        fontSize: 13,
                        height: 1.4,
                        color: isDarkMode ? Colors.grey[350] : Colors.grey[700],
                      ),
                      key: ValueKey('list_dua_virtue_${dua.id}'),
                      textAlign: TextAlign.right,
                      textDirection: TextDirection.rtl,
                    ),
                    if (dua.virtue!.length > 120) ...[
                      const SizedBox(height: 4),
                      Center(
                          child: Icon(Icons.keyboard_double_arrow_down,
                              size: 12, color: fadlColor.withAlpha(100))),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDuaItemActions(
      Dua dua, Color duasColor, int index, BuildContext itemContext) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      textDirection: TextDirection.rtl,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: duasColor.withAlpha(15),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Text(
            '${index + 1}',
            style: TextStyle(
                fontSize: 11, fontWeight: FontWeight.bold, color: duasColor),
          ),
        ),
        Consumer<DuasProvider>(builder: (context, duasProvider, _) {
          final isFavorite = duasProvider.isDuaFavorite(dua.id);
          return Row(
            textDirection: TextDirection.rtl,
            children: [
              _buildMiniActionButton(
                icon: isFavorite ? Icons.favorite : Icons.favorite_border,
                color: isFavorite ? AppColors.favoritesColor : duasColor,
                onTap: () async =>
                    await _handleFavoriteToggle(dua, duasProvider),
                tooltip: isFavorite ? 'إزالة من المفضلة' : 'إضافة للمفضلة',
              ),
              const SizedBox(width: 8),
              _buildMiniActionButton(
                icon: Icons.share_outlined,
                color: duasColor,
                onTap: () => _handleShareDua(dua),
                tooltip: 'مشاركة الدعاء',
              ),
            ],
          );
        }),
      ],
    );
  }

  Widget _buildMiniActionButton(
      {required IconData icon,
      required Color color,
      required VoidCallback onTap,
      String? tooltip}) {
    return Material(
      color: Colors.transparent,
      shape: const CircleBorder(),
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: onTap,
        splashColor: color.withAlpha(40),
        highlightColor: color.withAlpha(25),
        child: Tooltip(
          message: tooltip ?? '',
          child: Container(
            padding: const EdgeInsets.all(7),
            decoration: BoxDecoration(
              color: color.withAlpha(15),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 18),
          ),
        ),
      ),
    );
  }

  bool _isCategoryFavorite(DuasProvider provider, DuaCategory category) {
    if (category.items.isEmpty) return false;
    for (var dua in category.items) {
      if (!provider.isDuaFavorite(dua.id)) {
        return false;
      }
    }
    return true;
  }

  Future<void> _toggleCategoryFavorite(
      DuasProvider provider, DuaCategory category) async {
    final bool isCurrentlyFavorite = _isCategoryFavorite(provider, category);
    final bool shouldAdd = !isCurrentlyFavorite;

    HapticFeedback.mediumImpact();
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    scaffoldMessenger.clearSnackBars();

    List<Future<bool>> toggleFutures = [];
    for (var dua in category.items) {
      if ((shouldAdd && !provider.isDuaFavorite(dua.id)) ||
          (!shouldAdd && provider.isDuaFavorite(dua.id))) {
        toggleFutures.add(provider.toggleFavorite(dua));
      }
    }
    await Future.wait(toggleFutures);

    if (!mounted) return;

    scaffoldMessenger.showSnackBar(
      SnackBar(
        content: Row(
          textDirection: TextDirection.rtl,
          children: [
            Icon(shouldAdd ? Icons.favorite : Icons.favorite_border,
                color: Colors.white, size: 18),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                shouldAdd
                    ? 'تمت إضافة ${category.name} للمفضلة'
                    : 'تمت إزالة ${category.name} من المفضلة',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        backgroundColor:
            shouldAdd ? AppColors.favoritesColor : Colors.grey.shade700,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        elevation: 6,
        margin: const EdgeInsets.all(16),
        action: SnackBarAction(
          label: 'تراجع',
          textColor: Colors.white,
          onPressed: () => _toggleCategoryFavorite(provider, category),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final duasColor = AppColors.getDuasColor(isDarkMode);

    // تحويل اسم الأيقونة إلى أيقونة باستخدام IconHelper
    IconData iconData;
    try {
      // استخدام IconHelper للحصول على الأيقونة المناسبة
      iconData = IconHelper.getIconForCategory(widget.category.name,
          iconName: widget.category.iconName);
    } catch (e) {
      // استخدام أيقونة افتراضية إذا لم يتم العثور على الأيقونة
      iconData = Icons.category_outlined;
      debugPrint(
          'خطأ في تحويل اسم الأيقونة: ${widget.category.iconName}، الخطأ: $e');
    }

    return Consumer<DuasProvider>(
      builder: (context, duasProvider, _) {
        final bool isCurrentCategoryFavorite =
            _isCategoryFavorite(duasProvider, widget.category);

        return Scaffold(
          backgroundColor:
              isDarkMode ? const Color(0xFF121212) : Colors.grey[100],
          body: CustomScrollView(
            controller: _scrollController,
            physics: const BouncingScrollPhysics(),
            slivers: [
              SliverAppBar(
                expandedHeight: 220,
                floating: false,
                pinned: true,
                elevation: _showAppBarTitle ? 2 : 0,
                backgroundColor: duasColor,
                stretch: true,
                title: AnimatedOpacity(
                  opacity: _showAppBarTitle ? 1.0 : 0.0,
                  duration: const Duration(milliseconds: 250),
                  child: Text(
                    widget.category.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 20,
                      color: Colors.white,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                centerTitle: true,
                leading: IconButton(
                  icon:
                      const Icon(Icons.arrow_back_ios_new, color: Colors.white),
                  onPressed: () => Navigator.of(context).pop(),
                  tooltip: 'رجوع',
                ),
                actions: [
                  Tooltip(
                    message: isCurrentCategoryFavorite
                        ? 'إزالة القسم من المفضلة'
                        : 'إضافة القسم للمفضلة',
                    child: IconButton(
                      icon: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        transitionBuilder:
                            (Widget child, Animation<double> animation) {
                          return ScaleTransition(
                              scale: animation, child: child);
                        },
                        child: Icon(
                          isCurrentCategoryFavorite
                              ? Icons.favorite
                              : Icons.favorite_border,
                          color: Colors.white,
                          key: ValueKey<bool>(isCurrentCategoryFavorite),
                        ),
                      ),
                      onPressed: () async => await _toggleCategoryFavorite(
                          duasProvider, widget.category),
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                flexibleSpace: FlexibleSpaceBar(
                  stretchModes: const [
                    StretchMode.zoomBackground,
                    StretchMode.blurBackground,
                    StretchMode.fadeTitle,
                  ],
                  background: Stack(
                    fit: StackFit.expand,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [duasColor, duasColor.withAlpha(200)],
                          ),
                        ),
                      ),
                      RepaintBoundary(
                        child: TweenAnimationBuilder<double>(
                          tween: Tween<double>(begin: 0.0, end: 1.0),
                          duration: const Duration(seconds: 45),
                          curve: Curves.linear,
                          child: Opacity(
                            opacity: 0.04,
                            child: SvgPicture.asset('assets/images/p2.svg',
                                fit: BoxFit.cover),
                          ),
                          builder: (context, value, svgChild) {
                            return Transform.rotate(
                                angle: value * 2 * math.pi, child: svgChild);
                          },
                        ),
                      ),
                      Opacity(
                        opacity: 0.06,
                        child: SvgPicture.asset('assets/images/p2.svg',
                            fit: BoxFit.cover),
                      ),
                      const DecoratedBox(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              Color.fromRGBO(0, 0, 0, 0.4),
                              Color.fromRGBO(0, 0, 0, 0.7)
                            ],
                            stops: [0.5, 0.8, 1.0],
                          ),
                        ),
                      ),
                      Positioned(
                        left: 0,
                        right: 0,
                        bottom: 20,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.white.withAlpha(38),
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                      color: Colors.white.withAlpha(77),
                                      width: 1.5),
                                  boxShadow: const [
                                    BoxShadow(
                                        color: Colors.black26,
                                        blurRadius: 8,
                                        offset: Offset(0, 2)),
                                  ],
                                ),
                                child: Icon(iconData,
                                    color: Colors.white, size: 36),
                              ),
                              const SizedBox(height: 12),
                              Text(
                                widget.category.name,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 22,
                                  fontWeight: FontWeight.bold,
                                  shadows: [
                                    Shadow(
                                        color: Colors.black54,
                                        offset: Offset(0, 1),
                                        blurRadius: 2),
                                  ],
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 6),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 10, vertical: 5),
                                decoration: BoxDecoration(
                                  color: Colors.white.withAlpha(51),
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: Text(
                                  '${widget.category.items.length} ${widget.category.items.length == 1 ? "دعاء" : "أدعية"}',
                                  style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 13,
                                      fontWeight: FontWeight.w500),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              if (widget.category.description.isNotEmpty)
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16, 20, 16, 12),
                    child: TweenAnimationBuilder<double>(
                      tween: Tween<double>(begin: 0.0, end: 1.0),
                      duration: const Duration(milliseconds: 600),
                      curve: Curves.easeOutCubic,
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: isDarkMode ? Colors.grey[800] : Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                              color: (isDarkMode
                                  ? Colors.grey[700]
                                  : Colors.grey[300])!,
                              width: 0.8),
                          boxShadow: [
                            BoxShadow(
                              color: (isDarkMode
                                      ? Colors.black26
                                      : Colors.grey.shade300)
                                  .withAlpha(128),
                              blurRadius: 6,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              textDirection: TextDirection.rtl,
                              children: [
                                Icon(Icons.info_outline_rounded,
                                    size: 20, color: duasColor),
                                const SizedBox(width: 10),
                                Text(
                                  'عن هذه الأدعية',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: isDarkMode
                                        ? Colors.white.withAlpha(230)
                                        : Colors.black87,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 10),
                            SelectableText(
                              widget.category.description,
                              style: TextStyle(
                                fontSize: 14,
                                height: 1.5,
                                color: isDarkMode
                                    ? Colors.grey[400]
                                    : Colors.grey[700],
                              ),
                              textAlign: TextAlign.right,
                              textDirection: TextDirection.rtl,
                            ),
                          ],
                        ),
                      ),
                      builder: (context, value, descriptionChild) {
                        return Opacity(
                          opacity: value,
                          child: Transform.translate(
                            offset: Offset(0, 20 * (1 - value)),
                            child: descriptionChild,
                          ),
                        );
                      },
                    ),
                  ),
                ),
              SliverPadding(
                padding: const EdgeInsets.only(bottom: 16, top: 8),
                sliver: SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      if (index >= widget.category.items.length) return null;
                      final dua = widget.category.items[index];
                      return _buildEnhancedDuaItem(
                          dua, duasColor, isDarkMode, index);
                    },
                    childCount: widget.category.items.length,
                  ),
                ),
              ),
              const SliverToBoxAdapter(child: SizedBox(height: 70)),
            ],
          ),
          floatingActionButtonLocation: FloatingActionButtonLocation.startFloat,
          floatingActionButton: _showScrollToTop
              ? TweenAnimationBuilder<double>(
                  tween: Tween<double>(begin: 0.0, end: 1.0),
                  duration: const Duration(milliseconds: 250),
                  curve: Curves.easeOut,
                  child: FloatingActionButton(
                    onPressed: _scrollToTop,
                    backgroundColor: duasColor,
                    mini: true,
                    elevation: 3,
                    child: const Icon(Icons.keyboard_arrow_up,
                        color: Colors.white, size: 24),
                  ),
                  builder: (context, value, fabChild) {
                    return Transform.scale(
                        scale: value,
                        child: Opacity(opacity: value, child: fabChild));
                  },
                )
              : null,
        );
      },
    );
  }
}
