import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter/services.dart';
import 'providers/theme_provider.dart';
import 'screens/splash_screen.dart'; // Import SplashScreen
import 'screens/welcome_screen.dart'; // Import WelcomeScreen
import 'screens/about/about_screen.dart'; // Import AboutScreen
import 'screens/books_screen.dart';
import 'screens/book_details_screen.dart';
import 'screens/poems_screen_new.dart' as new_poems;
import 'screens/poem_details_screen.dart';
import 'screens/azkar_screen.dart';
import 'screens/favorites_screen.dart';
import 'screens/favorites/providers/favorites_provider.dart';
import 'screens/settings/settings_screen.dart';
import 'screens/home_screen.dart';
import 'services/permission_manager.dart';
import 'screens/tasbih/tasbih_screen.dart';
import 'screens/tasbih/tasbih_stats_screen.dart';
import 'screens/tasbih/providers/tasbih_stats_provider.dart';
import 'screens/tasbih/providers/tasbih_provider.dart';
import 'screens/tasbih/providers/wird_provider.dart';
import 'screens/tasbih/screens/wird_list_screen.dart';
import 'screens/tasbih/screens/wird_detail_screen.dart';
import 'screens/tasbih/screens/wird_player_screen.dart';
// import 'screens/tasbih/services/awesome_notification_service.dart'; // تم تعطيل النظام القديم
import 'screens/duas_screen.dart';
import 'screens/duas_details_screen.dart';
import 'screens/prophet_prayers_screen.dart';
import 'screens/prophet_prayer_details_screen.dart';
import 'providers/duas_provider.dart';
import 'providers/prophet_prayers_provider.dart';
import 'models/dua.dart';
// تم إزالة استيراد شاشة المطور
import 'models/book.dart';
import 'models/poem.dart';
import 'models/daily_wisdom.dart';
import 'utils/constants.dart';
// import 'services/notification_service.dart'; // تم استبداله بـ notification_manager.dart
import 'services/notification_manager.dart'; // استيراد مدير الإشعارات الجديد
// import 'package:awesome_notifications/awesome_notifications.dart'; // تم تعطيل النظام القديم
import 'database/database_helper.dart';
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'dart:convert';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

// تهيئة ملفات JSON مسبقاً
Future<void> _preloadJsonFiles() async {
  try {
    // تهيئة ملف الحكم
    await DailyWisdom.getAllQuotes();
    debugPrint('تم تهيئة ملف الحكم بنجاح');

    // تهيئة ملف الكتب
    await rootBundle.loadString('assets/data/books.json');
    debugPrint('تم تهيئة ملف الكتب بنجاح');

    // تهيئة ملف القصائد
    await rootBundle.loadString('assets/data/poems.json');
    debugPrint('تم تهيئة ملف القصائد بنجاح');

    // تهيئة ملف الأذكار
    await rootBundle.loadString('assets/data/azkar.json');
    debugPrint('تم تهيئة ملف الأذكار بنجاح');

    // تهيئة ملف الأدعية
    await rootBundle.loadString('assets/data/duas.json');
    debugPrint('تم تهيئة ملف الأدعية بنجاح');
  } catch (e) {
    debugPrint('خطأ في تهيئة ملفات JSON: $e');
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة بيانات التاريخ للغة العربية
  await initializeDateFormatting('ar', null);
  Intl.defaultLocale = 'ar';

  // تهيئة قاعدة البيانات
  final DatabaseHelper databaseHelper = DatabaseHelper();
  await databaseHelper.database;

  // تهيئة ملفات JSON مسبقاً
  await _preloadJsonFiles();

  // تهيئة مدير الإشعارات الجديد
  final notificationManager = NotificationManager();
  await notificationManager.init(); // يتضمن تهيئة الإشعارات المجدولة

  // تم تعطيل تسجيل المستمعين للنظام القديم
  // await AwesomeNotifications().setListeners(
  //   onActionReceivedMethod: onActionReceivedMethod,
  //   onNotificationCreatedMethod: onNotificationCreatedMethod,
  //   onNotificationDisplayedMethod: onNotificationDisplayedMethod,
  //   onDismissActionReceivedMethod: onDismissActionReceivedMethod,
  // );

  // تم نقل التحقق من أذونات الإشعارات إلى كلاس _MyAppState

  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  runApp(const MyApp());
}

// تم تعطيل معالجات الإشعارات القديمة
// /// استدعاء عند إنشاء إشعار جديد
// @pragma('vm:entry-point')
// Future<void> onNotificationCreatedMethod(
//     ReceivedNotification receivedNotification) async {
//   debugPrint('تم إنشاء الإشعار: ${receivedNotification.id}');
// }
//
// /// استدعاء عند عرض الإشعار للمستخدم
// @pragma('vm:entry-point')
// Future<void> onNotificationDisplayedMethod(
//     ReceivedNotification receivedNotification) async {
//   debugPrint('تم عرض الإشعار: ${receivedNotification.id}');
// }
//
// /// استدعاء عند النقر على الإشعار أو أحد أزراره
// @pragma('vm:entry-point')
// Future<void> onActionReceivedMethod(ReceivedAction receivedAction) async {
//   debugPrint(
//       'استلام إجراء: ${receivedAction.id} | ${receivedAction.buttonKeyPressed}');
//
//   try {
//     if (receivedAction.buttonKeyPressed == 'OPEN_AZKAR') {
//       // انتظار لحظة لضمان استقرار التطبيق
//       await Future.delayed(const Duration(milliseconds: 300));
//
//       if (MyApp.navigatorKey.currentState != null) {
//         // طباعة نوع الإشعار للتشخيص فقط
//         if (receivedAction.payload != null &&
//             receivedAction.payload!.containsKey('type')) {
//           final notificationType = receivedAction.payload!['type'];
//           debugPrint('نوع الإشعار: $notificationType');
//         }
//
//         debugPrint('تم طلب فتح صفحة الأذكار الرئيسية');
//
//         // توجيه المستخدم إلى صفحة الأذكار الرئيسية بدلاً من صفحة التفاصيل
//         // لتجنب مشكلة الشاشة الحمراء
//         MyApp.navigatorKey.currentState!.pushNamedAndRemoveUntil(
//           AppConstants.azkarRoute, // استخدام مسار صفحة الأذكار الرئيسية
//           (route) => route.isFirst, // الإبقاء على الصفحة الرئيسية فقط في المكدس
//         );
//       }
//     } else if (receivedAction.buttonKeyPressed == 'SNOOZE') {
//       // تنفيذ تأجيل الإشعار
//       int id = receivedAction.id ?? 0;
//       int originalId = id;
//       int snoozeMinutes = 10; // قيمة افتراضية
//
//       // إرجاع المعرّف الأصلي إذا كان مؤجلاً
//       if (id == 199) {
//         originalId = 1;
//       } else if (id == 299) {
//         originalId = 2;
//       } else if (receivedAction.payload != null &&
//           receivedAction.payload!.containsKey('original_id')) {
//         originalId = int.parse(receivedAction.payload!['original_id'] ?? '0');
//         // استخدام مدة التأجيل المخزنة في الإشعار نفسه
//         if (receivedAction.payload!.containsKey('snooze_minutes')) {
//           snoozeMinutes =
//               int.parse(receivedAction.payload!['snooze_minutes'] ?? '10');
//         }
//       }
//
//       await NotificationManager().snoozeNotification(originalId, snoozeMinutes);
//       debugPrint('تم تأجيل الإشعار $originalId لمدة $snoozeMinutes دقيقة');
//     }
//   } catch (e) {
//     debugPrint('خطأ: $e');
//     // محاولة بديلة في حالة حدوث خطأ
//     try {
//       if (receivedAction.buttonKeyPressed == 'OPEN_AZKAR' &&
//           MyApp.navigatorKey.currentState != null) {
//         // الانتقال للصفحة الرئيسية ثم الأذكار
//         MyApp.navigatorKey.currentState!
//             .pushNamedAndRemoveUntil('/home', (route) => false);
//         await Future.delayed(const Duration(milliseconds: 500));
//
//         // الانتقال إلى صفحة الأذكار الرئيسية
//         MyApp.navigatorKey.currentState!.pushNamed(AppConstants.azkarRoute);
//       }
//     } catch (fallbackError) {
//       debugPrint('خطأ في المحاولة البديلة: $fallbackError');
//     }
//   }
// }
//
// /// استدعاء عند إغلاق الإشعار بدون تفاعل
// @pragma('vm:entry-point')
// Future<void> onDismissActionReceivedMethod(
//     ReceivedAction receivedAction) async {
//   debugPrint('تم تجاهل الإشعار: ${receivedAction.id}');
// }

/// معالج الإشعارات الجديد
@pragma('vm:entry-point')
void notificationTapBackground(NotificationResponse notificationResponse) {
  // معالجة النقر على الإشعار في الخلفية
  debugPrint('معالجة النقر على الإشعار في الخلفية: ${notificationResponse.id}');

  // استخراج البيانات من الإشعار
  final String? payload = notificationResponse.payload;
  if (payload == null) return;

  try {
    final Map<String, dynamic> data = jsonDecode(payload);
    final String? notificationType = data['type'];

    debugPrint('نوع الإشعار: $notificationType');

    // معالجة الإشعارات حسب نوعها
    switch (notificationType) {
      case 'morning_azkar':
      case 'morning_azkar_test':
        MyApp.navigatorKey.currentState?.pushNamed(AppConstants.azkarRoute,
            arguments: {'categoryId': 'morning'});
        break;

      case 'evening_azkar':
      case 'evening_azkar_test':
        MyApp.navigatorKey.currentState?.pushNamed(AppConstants.azkarRoute,
            arguments: {'categoryId': 'evening'});
        break;

      case 'wird_reminder':
      case 'wird_reminder_test':
        final String? wirdId = data['wird_id'];
        if (wirdId != null) {
          MyApp.navigatorKey.currentState?.pushNamed(
              data['is_test'] == 'true'
                  ? AppConstants.wirdDetailRoute
                  : AppConstants.wirdPlayerRoute,
              arguments: {'wirdId': wirdId});
        }
        break;

      case 'return_reminder':
        MyApp.navigatorKey.currentState?.pushNamed(AppConstants.homeRoute);
        break;

      case 'snoozed_notification':
        final String? originalId = data['original_id'];
        if (originalId != null) {
          MyApp.navigatorKey.currentState?.pushNamed(AppConstants.homeRoute);
        }
        break;

      default:
        MyApp.navigatorKey.currentState?.pushNamed(AppConstants.homeRoute);
        break;
    }
  } catch (e) {
    debugPrint('خطأ في معالجة بيانات الإشعار: $e');
    MyApp.navigatorKey.currentState?.pushNamed(AppConstants.homeRoute);
  }
}

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final PermissionManager _permissionManager = PermissionManager();

  @override
  void initState() {
    super.initState();
    // تم تعليق التحقق من أذونات الإشعارات عند بدء التطبيق
    /*
    // تأخير التحقق من أذونات الإشعارات حتى يتم تهيئة التطبيق بالكامل
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        _checkNotificationPermissions();
      }
    });
    */
  }

  // التحقق من أذونات الإشعارات - تم تعليقه مؤقتاً
  Future<void> _checkNotificationPermissions() async {
    /*
    try {
      // التأكد من أن السياق لا يزال صالحاً
      if (!mounted) return;

      // التحقق من أذونات الإشعارات باستخدام النظام القديم
      await _permissionManager.checkNotificationPermissionsOnStartup(context);

      // التحقق من أذونات الإشعارات باستخدام النظام الجديد
      final notificationManager = NotificationManager();
      final hasPermission =
          await notificationManager.checkNotificationPermissions();

      if (!hasPermission) {
        // طلب الأذونات إذا لم تكن ممنوحة
        await notificationManager.requestNotificationPermissions();
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من أذونات الإشعارات: $e');
    }
    */
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => ThemeProvider()),
        ChangeNotifierProvider(create: (context) => TasbihStatsProvider()),
        ChangeNotifierProvider(create: (context) => FavoritesProvider()),
        ChangeNotifierProvider(create: (context) => TasbihProvider()),
        ChangeNotifierProvider(create: (context) => DuasProvider()),
        ChangeNotifierProvider(create: (context) => ProphetPrayersProvider()),
        ChangeNotifierProxyProvider<TasbihProvider, WirdProvider>(
          create: (context) =>
              WirdProvider(Provider.of<TasbihProvider>(context, listen: false)),
          update: (context, tasbihProvider, previous) =>
              previous ?? WirdProvider(tasbihProvider),
        ),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            navigatorKey: MyApp.navigatorKey,
            title: 'وهج السالك',
            themeMode: themeProvider.themeMode,
            theme: themeProvider.lightTheme,
            darkTheme: themeProvider.darkTheme,
            debugShowCheckedModeBanner: false,
            initialRoute: '/',
            routes: {
              '/': (context) => const SplashScreen(),
              '/home': (context) => const HomeScreen(),
              '/welcome': (context) => const WelcomeScreen(),
              '/about': (context) => const AboutScreen(),
              AppConstants.booksRoute: (context) => const BooksScreen(),
              AppConstants.poemsRoute: (context) =>
                  const new_poems.PoemsScreen(),
              AppConstants.azkarRoute: (context) => const AzkarScreen(),
              AppConstants.favoritesRoute: (context) =>
                  const FavoritesScreenWrapper(),
              AppConstants.settingsRoute: (context) => const SettingsScreen(),
              AppConstants.tasbihRoute: (context) => const TasbihScreen(),
              AppConstants.tasbihStatsRoute: (context) =>
                  const TasbihStatsScreen(),
              AppConstants.wirdListRoute: (context) => const WirdListScreen(),
              AppConstants.duasRoute: (context) => const DuasScreen(),
              AppConstants.prophetPrayersRoute: (context) =>
                  const ProphetPrayersScreen(),
              // لا يمكن استخدام شاشة تشغيل الورد مباشرة لأنها تتطلب معرف الورد
            },
            onGenerateRoute: (settings) {
              debugPrint('طلب تنقل إلى: ${settings.name}');

              if (settings.name == AppConstants.azkarDetailsRoute) {
                final category =
                    settings.arguments as String? ?? 'أذكار الصباح';
                debugPrint('فتح تفاصيل الأذكار لفئة: $category');

                // بدلاً من فتح صفحة التفاصيل مباشرة، نقوم بفتح صفحة الأذكار الرئيسية
                // لتجنب مشكلة الشاشة الحمراء
                return MaterialPageRoute(
                  builder: (context) => const AzkarScreen(),
                );
              } else if (settings.name == AppConstants.bookDetailsRoute) {
                final book = settings.arguments as Book;
                return MaterialPageRoute(
                  builder: (context) => BookDetailsScreen(book: book),
                );
              } else if (settings.name == AppConstants.poemDetailsRoute) {
                final poem = settings.arguments as Poem;
                return MaterialPageRoute(
                  builder: (context) => PoemDetailsScreen(poem: poem),
                );
              } else if (settings.name == AppConstants.wirdDetailRoute) {
                final wirdId = settings.arguments as int;
                return MaterialPageRoute(
                  builder: (context) => WirdDetailScreen(wirdId: wirdId),
                );
              } else if (settings.name == AppConstants.wirdPlayerRoute) {
                final wirdId = settings.arguments as int;
                return MaterialPageRoute(
                  builder: (context) => WirdPlayerScreen(wirdId: wirdId),
                );
              } else if (settings.name == AppConstants.duasDetailsRoute) {
                final category = settings.arguments as DuaCategory;
                return MaterialPageRoute(
                  builder: (context) => DuasDetailsScreen(category: category),
                );
              } else if (settings.name ==
                  AppConstants.prophetPrayersDetailsRoute) {
                final category = settings.arguments as DuaCategory;
                return MaterialPageRoute(
                  builder: (context) =>
                      ProphetPrayerDetailsScreen(category: category),
                );
              }
              return null;
            },
          );
        },
      ),
    );
  }
}
