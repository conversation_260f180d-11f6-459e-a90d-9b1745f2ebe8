import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sqflite/sqflite.dart'; // لاستخدام ConflictAlgorithm

import '../models/book.dart';
import 'book_details_screen.dart'; //تفاصيل الكتب
import '../database/database_helper.dart';
import '../utils/app_colors.dart'; //الالوان
import '../utils/responsive_helper.dart'; // مساعد التوافق مع الأجهزة المختلفة
import '../widgets/custom_search_bar.dart'; //البحث
import '../widgets/category_selector.dart'; //الفئات
import '../widgets/loading_widget.dart'; //التحميل
import '../widgets/error_widget.dart'; //الخطأ
import 'components/books_app_bar.dart'; // شريط التطبيق المخصص
import 'components/book_card.dart'; // بطاقة الكتاب المحسنة

class BooksScreen extends StatefulWidget {
  const BooksScreen({Key? key}) : super(key: key);

  @override
  State<BooksScreen> createState() => _BooksScreenState();
}

class _BooksScreenState extends State<BooksScreen>
    with TickerProviderStateMixin {
  // حالة البيانات
  List<Book> _books = [];
  List<Book> _filteredBooks = [];
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  String _searchQuery = '';

  // أنيميشن كونترولر
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late Animation<double> _headerAnimation;
  late Animation<double> _searchBarAnimation;
  late Animation<double> _categoriesAnimation;
  late Animation<double> _refreshAnimation;

  // إعدادات واجهة المستخدم
  bool _gridViewMode = true;
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final ScrollController _scrollController = ScrollController();

  // قائمة الفئات
  final List<String> _categories = [
    'الكل',
    'الفقه',
    'التفسير',
    'الحديث',
    'العقيدة',
    'السيرة',
    'اللغة العربية',
    'أدب',
    'تاريخ',
  ];

  // الفئة المحددة حاليا - null تعني "الكل"
  String? _selectedCategory;

  @override
  void initState() {
    super.initState();

    // إعداد مراقبات الرسوم المتحركة
    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _slideController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _scaleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );

    _headerAnimation = CurvedAnimation(
      parent: _slideController,
      curve: const Interval(0.0, 0.4, curve: Curves.easeOut),
    );

    _searchBarAnimation = CurvedAnimation(
      parent: _slideController,
      curve: const Interval(0.2, 0.6, curve: Curves.easeOut),
    );

    _categoriesAnimation = CurvedAnimation(
      parent: _slideController,
      curve: const Interval(0.4, 0.8, curve: Curves.easeOut),
    );

    _refreshAnimation = CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    );

    // تحميل الكتب من قاعدة البيانات
    _loadBooksFromDatabase();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  // تحميل الكتب من قاعدة البيانات
  Future<void> _loadBooksFromDatabase() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // التحقق من وجود جدول books
      final db = await _databaseHelper.database;
      var tableCheck = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='books'");

      if (tableCheck.isEmpty) {
        // إنشاء الجدول إذا لم يكن موجوداً
        await db.execute('''
          CREATE TABLE IF NOT EXISTS books(
            id TEXT PRIMARY KEY,
            title TEXT,
            author TEXT,
            description TEXT,
            cover_url TEXT,
            local_cover_path TEXT,
            category TEXT,
            pdf_url TEXT,
            pdf_path TEXT,
            local_pdf_path TEXT,
            pages INTEGER DEFAULT 0,
            date_added INTEGER,
            tags TEXT,
            is_favorite INTEGER DEFAULT 0,
            rating REAL DEFAULT 0.0,
            download_progress INTEGER DEFAULT -1,
            last_read_page INTEGER DEFAULT 0,
            last_read_date INTEGER
          )
        ''');

        // استيراد البيانات الأولية من الأصول
        await _importBooksFromAssets();
        return; // _importBooksFromAssets سيستدعي _loadBooksFromDatabase مرة أخرى بعد الانتهاء
      }

      // التحقق من وجود بيانات
      final bookCount = Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM books'));

      if (bookCount == 0) {
        // لا توجد بيانات، قم باستيراد البيانات الأولية
        await _importBooksFromAssets();
        return; // _importBooksFromAssets سيستدعي _loadBooksFromDatabase مرة أخرى بعد الانتهاء
      }

      // استعلام عن جميع الكتب
      final List<Map<String, dynamic>> bookMaps = await db.query('books');

      // تحويل نتائج قاعدة البيانات إلى كائنات Book
      setState(() {
        _books = bookMaps.map((bookMap) {
          // تحويل سلسلة الوسوم إلى قائمة
          List<String> tags = [];
          if (bookMap['tags'] != null &&
              bookMap['tags'].toString().isNotEmpty) {
            tags = bookMap['tags'].toString().split(', ');
          }

          // إنشاء كائن Book متوافق من بيانات قاعدة البيانات
          return Book(
            id: int.tryParse(bookMap['id'].toString().replaceAll('b', '')) ?? 0,
            title: bookMap['title'] ?? '',
            author: bookMap['author'] ?? '',
            description: bookMap['description'] ?? '',
            coverUrl: bookMap['cover_url'] ?? '',
            localCoverPath: bookMap['local_cover_path'],
            category: bookMap['category'] ?? '',
            pdfUrl: bookMap['pdf_url'] ?? '',
            pdfPath: bookMap['pdf_path'],
            localPdfPath: bookMap['local_pdf_path'],
            pages: bookMap['pages'] ?? 0,
            tags: tags,
          );
        }).toList();

        _applyFilters();
        _isLoading = false;
      });

      // تشغيل الرسوم المتحركة بعد تحميل البيانات
      _fadeController.forward();
      _slideController.forward();
      _scaleController.forward();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'حدث خطأ في تحميل الكتب: $e';
      });
    }
  }

  // استيراد الكتب من ملف الأصول JSON
  Future<void> _importBooksFromAssets() async {
    try {
      // إعادة تعيين قاعدة البيانات لحل مشكلة الأعمدة المفقودة
      try {
        await _databaseHelper.resetDatabase();
        debugPrint('تم إعادة تعيين قاعدة البيانات بنجاح');
      } catch (e) {
        debugPrint('خطأ في إعادة تعيين قاعدة البيانات: $e');
      }

      // قراءة ملف البيانات
      final String jsonData =
          await rootBundle.loadString('assets/data/books.json');
      final Map<String, dynamic> jsonMap = json.decode(jsonData);

      if (!jsonMap.containsKey('books')) {
        throw Exception(
            'تنسيق ملف JSON غير صالح - يجب أن يحتوي على مفتاح "books"');
      }

      final List<dynamic> booksData = jsonMap['books'];
      debugPrint('تم العثور على ${booksData.length} كتاب في ملف JSON');

      // حفظ البيانات في قاعدة البيانات
      final db = await _databaseHelper.database;

      for (var bookData in booksData) {
        try {
          // معالجة معرف الكتاب بصيغة متوافقة
          final bookId = bookData['id'].toString();

          // تخزين الوسوم كنص مفصول بفواصل إذا كانت موجودة
          final List<dynamic> tags = bookData['tags'] ?? [];
          final String tagsString = tags.join(', ');

          // إعداد بيانات الكتاب للإدخال مع مطابقة أسماء الحقول بدقة
          Map<String, dynamic> bookMap = {
            'id': bookId,
            'title': bookData['title'] ?? '',
            'author': bookData['author'] ?? '',
            'description': bookData['description'] ?? '',
            'cover_url': bookData['coverUrl'] ?? '',
            'local_cover_path': bookData['localCoverPath'] ?? '',
            'category': bookData['category'] ?? '',
            'pdf_url': bookData['pdfUrl'] ?? '',
            'pdf_path': bookData['pdfPath'] ?? '',
            'local_pdf_path': bookData['localPdfPath'] ?? '',
            'pages': bookData['pages'] ?? 0,
            'date_added': DateTime.now().millisecondsSinceEpoch,
            'tags': tagsString,
            'is_favorite': 0,
            'rating': 0.0,
            'download_progress': -1,
            'last_read_page': 0,
            'last_read_date': 0,
          };

          await db.insert(
            'books',
            bookMap,
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        } catch (e) {
          debugPrint('خطأ في إدخال كتاب: $e');
        }
      }

      debugPrint('تم استيراد ${booksData.length} كتاب بنجاح');

      // بعد الاستيراد، تحميل الكتب
      await _loadBooksFromDatabase();
    } catch (e) {
      debugPrint('خطأ في استيراد الكتب: $e');
      setState(() {
        _hasError = true;
        _errorMessage = 'خطأ في استيراد الكتب: $e';
        _isLoading = false;
      });
    }
  }

  // تطبيق الفلاتر على الكتب
  void _applyFilters() {
    // تطبيق فلتر البحث أولاً
    var filtered = _books;
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((book) {
        return book.title.contains(_searchQuery) ||
            book.author.contains(_searchQuery) ||
            book.description.contains(_searchQuery);
      }).toList();
    }

    // ثم تطبيق فلتر الفئة
    if (_selectedCategory != null) {
      filtered =
          filtered.where((book) => book.category == _selectedCategory).toList();
    }

    setState(() {
      _filteredBooks = filtered;
    });
  }

  // البحث في الكتب
  void _onSearch(String query) {
    setState(() {
      _searchQuery = query;
    });
    _applyFilters();
  }

  // مسح البحث
  void _onClearSearch() {
    setState(() {
      _searchQuery = '';
    });
    _applyFilters();
  }

  // فتح صفحة تفاصيل الكتاب
  void _openBookDetails(Book book) {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            BookDetailsScreen(book: book),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          var begin = const Offset(1.0, 0.0);
          var end = Offset.zero;
          var curve = Curves.easeInOutCubic;
          var tween =
              Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
      ),
    ).then((_) {
      // إعادة تحميل البيانات عند العودة (اختياري)
      //_loadBooksFromDatabase();
    });
  }

  // تحديث الكتب
  Future<void> _refreshBooks() async {
    _scaleController.reset();
    _scaleController.forward();
    await _importBooksFromAssets();
  }

  // إعادة تعيين قاعدة البيانات وإعادة تحميل الكتب
  Future<void> _resetDatabaseAndReloadBooks() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // إعادة تعيين قاعدة البيانات
      await _databaseHelper.resetDatabase();
      debugPrint('تم إعادة تعيين قاعدة البيانات بنجاح');

      // إعادة تحميل الكتب
      await _importBooksFromAssets();

      // عرض رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إعادة تعيين قاعدة البيانات وتحميل الكتب بنجاح'),
            behavior: SnackBarBehavior.floating,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      debugPrint('خطأ في إعادة تعيين قاعدة البيانات: $e');
      setState(() {
        _hasError = true;
        _errorMessage = 'خطأ في إعادة تعيين قاعدة البيانات: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      backgroundColor: isDarkMode
          ? Theme.of(context).scaffoldBackgroundColor
          : AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // شريط العنوان المحسن
            BooksAppBar(
              title: 'المكتبة الإسلامية',
              gridViewMode: _gridViewMode,
              onToggleViewMode: () {
                setState(() {
                  _gridViewMode = !_gridViewMode;
                });
              },
              onRefresh: _refreshBooks,
              onResetDatabase: _resetDatabaseAndReloadBooks,
              animation: _headerAnimation,
              refreshAnimation: _refreshAnimation,
            ),

            // شريط البحث
            SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(-1, 0),
                end: Offset.zero,
              ).animate(_searchBarAnimation),
              child: FadeTransition(
                opacity: _searchBarAnimation,
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                  child: CustomSearchBar(
                    onChanged: _onSearch,
                    onClear: _onClearSearch,
                    hintText: 'ابحث عن كتاب أو مؤلف...',
                  ),
                ),
              ),
            ),

            // شريط الفئات
            SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(1, 0),
                end: Offset.zero,
              ).animate(_categoriesAnimation),
              child: FadeTransition(
                opacity: _categoriesAnimation,
                child: Padding(
                  padding: const EdgeInsets.only(top: 8, bottom: 8),
                  child: CategorySelector(
                    categories: _categories,
                    selectedCategory: _selectedCategory,
                    onCategorySelected: (category) {
                      setState(() {
                        _selectedCategory = category.isEmpty ? null : category;
                        _applyFilters();
                      });
                    },
                    allCategoriesLabel: 'الكل',
                  ),
                ),
              ),
            ),

            // عرض عدد الكتب
            if (!_isLoading && !_hasError)
              SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0, 1),
                  end: Offset.zero,
                ).animate(_categoriesAnimation),
                child: FadeTransition(
                  opacity: _categoriesAnimation,
                  child: Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: AppColors.booksColor
                                .withAlpha(26), // 0.1 * 255 = 26
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            'عدد الكتب: ${_filteredBooks.length}',
                            style: const TextStyle(
                              color: AppColors.booksColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

            // حالات العرض المختلفة
            Expanded(
              child: _buildContentSection(),
            ),
          ],
        ),
      ),
    );
  }

  // بناء محتوى رئيسي حسب الحالة
  Widget _buildContentSection() {
    // حالة التحميل
    if (_isLoading) {
      return const LoadingWidget();
    }

    // حالة الخطأ
    if (_hasError) {
      return ErrorMessageWidget(
        message: _errorMessage,
        onRetry: _loadBooksFromDatabase,
      );
    }

    // لا توجد كتب
    if (_filteredBooks.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.menu_book,
              size: 80,
              color: Colors.grey.withAlpha(128), // 0.5 * 255 = 128
            ),
            const SizedBox(height: 16),
            const Text(
              'لا توجد كتب متطابقة مع البحث',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _searchQuery = '';
                  _selectedCategory = null;
                });
                _applyFilters();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.booksColor,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              child: const Text('عرض كل الكتب'),
            ),
          ],
        ),
      );
    }

    // عرض الكتب بناءً على نمط العرض المحدد
    return FadeTransition(
      opacity: _fadeController,
      child: _gridViewMode ? _buildBooksGrid() : _buildBooksList(),
    );
  }

  // بناء عرض الشبكة - تم تحسينه للتوافق مع الشاشات المختلفة
  Widget _buildBooksGrid() {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = ResponsiveHelper.isTablet(context);
    // Eliminamos la variable isDarkMode ya que no se utiliza

    // تحسين التوافق مع الشاشات المختلفة
    int columnCount;
    double childAspectRatio;

    if (screenSize.width > 1200) {
      columnCount = 5; // للشاشات الكبيرة جداً
      childAspectRatio = 0.7;
    } else if (screenSize.width > 900) {
      columnCount = 4; // للشاشات الكبيرة
      childAspectRatio = 0.65;
    } else if (screenSize.width > 600) {
      columnCount = 3; // للأجهزة اللوحية
      childAspectRatio = 0.6;
    } else if (screenSize.width > 400) {
      columnCount = 2; // للهواتف الكبيرة
      childAspectRatio = 0.6;
    } else {
      columnCount = 2; // للهواتف الصغيرة
      childAspectRatio = 0.55;
    }

    final padding = isTablet ? 20.0 : 16.0;
    final spacing = isTablet ? 20.0 : 16.0;

    return Padding(
      padding: EdgeInsets.all(padding),
      child: GridView.builder(
        controller: _scrollController,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: columnCount,
          childAspectRatio: childAspectRatio,
          crossAxisSpacing: spacing,
          mainAxisSpacing: spacing,
        ),
        itemCount: _filteredBooks.length,
        itemBuilder: (context, index) {
          final book = _filteredBooks[index];
          final delay = (index % 10) * 0.1;

          return AnimatedBuilder(
            animation: _fadeController,
            builder: (context, child) {
              final start = delay;
              final end = min(start + 0.5, 1.0);

              final Animation<double> itemAnimation = CurvedAnimation(
                parent: _fadeController,
                curve: Interval(start, end, curve: Curves.easeOut),
              );

              return BookCard(
                book: book,
                onTap: () => _openBookDetails(book),
                animation: itemAnimation,
              );
            },
          );
        },
      ),
    );
  }

  // بناء عرض القائمة - تم تحسينه للتوافق مع الشاشات المختلفة
  Widget _buildBooksList() {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = ResponsiveHelper.isTablet(context);
    // Eliminamos la variable isDarkMode ya que no se utiliza

    // تحسين التوافق مع الشاشات المختلفة
    final padding = isTablet ? 20.0 : 16.0;
    final itemHeight = screenSize.width > 600 ? 160.0 : 140.0;
    final bottomPadding = screenSize.width > 600 ? 20.0 : 16.0;

    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.all(padding),
      itemCount: _filteredBooks.length,
      itemBuilder: (context, index) {
        final book = _filteredBooks[index];
        final delay = index * 0.05;

        return AnimatedBuilder(
          animation: _fadeController,
          builder: (context, child) {
            final start = delay;
            final end = min(start + 0.5, 1.0);

            final Animation<double> itemAnimation = CurvedAnimation(
              parent: _fadeController,
              curve: Interval(start, end, curve: Curves.easeOut),
            );

            return Padding(
              padding: EdgeInsets.only(bottom: bottomPadding),
              child: SizedBox(
                height: itemHeight,
                child: BookCard(
                  book: book,
                  onTap: () => _openBookDetails(book),
                  animation: itemAnimation,
                ),
              ),
            );
          },
        );
      },
    );
  }
}
