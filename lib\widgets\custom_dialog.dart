import 'package:flutter/material.dart';

// ويدجت لعرض حوار مخصص
void showCustomDialog({
  required BuildContext context,
  required String title,
  required String content,
  required List<Widget> actions,
}) {
  showDialog(
    context: context,
    builder: (context) {
      return AlertDialog(
        title: Text(
          title,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        content: Text(
          content,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        actions: actions,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        actionsAlignment: MainAxisAlignment.center,
      );
    },
  );
}
