//  إنشاء ملف لشريط العنوان

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:math' as math;

class SettingsHeader extends StatelessWidget {
  final Color settingsColor;

  const SettingsHeader({
    super.key,
    required this.settingsColor,
  });

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return SliverAppBar(
      pinned: true,
      expandedHeight: screenSize.height * 0.25,
      elevation: 0,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      flexibleSpace: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          // حساب نسبة التمدد (1.0 = ممدد بالكامل، 0.0 = مطوي بالكامل)
          final FlexibleSpaceBarSettings settings = context
              .dependOnInheritedWidgetOfExactType<FlexibleSpaceBarSettings>()!;
          final double deltaExtent = settings.maxExtent - settings.minExtent;
          final double scrollRatio = (1.0 -
                  (settings.currentExtent - settings.minExtent) / deltaExtent)
              .clamp(0.0, 1.0);

          return FlexibleSpaceBar(
            titlePadding:
                const EdgeInsets.only(bottom: 16, right: 16, left: 16),
            title: Text(
              'الإعدادات',
              style: TextStyle(
                color: settingsColor,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: Colors.black.withAlpha(51), // 0.2 * 255 = 51
                    offset: const Offset(0, 1),
                    blurRadius: 2,
                  ),
                ],
              ),
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
              textAlign: TextAlign.right, // محاذاة النص إلى اليمين
            ),
            background: Stack(
              fit: StackFit.expand,
              children: [
                // خلفية متدرجة متقدمة مع تدرجات متعددة
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topRight,
                      end: Alignment.bottomLeft,
                      colors: isDarkMode
                          ? [
                              settingsColor.withAlpha(77), // 0.3 * 255 = 77
                              settingsColor.withAlpha(46), // 0.18 * 255 = 46
                              settingsColor.withAlpha(26), // 0.1 * 255 = 26
                            ]
                          : [
                              settingsColor.withAlpha(51), // 0.2 * 255 = 51
                              settingsColor.withAlpha(31), // 0.12 * 255 = 31
                              settingsColor.withAlpha(13), // 0.05 * 255 = 13
                            ],
                      stops: const [0.1, 0.5, 0.9],
                    ),
                  ),
                ),

                // تأثيرات إضاءة ديناميكية
                CustomPaint(
                  painter: SettingsLightingEffect(
                    color: settingsColor,
                    progress: 1.0 - scrollRatio,
                    isDarkMode: isDarkMode,
                  ),
                  child: const SizedBox.expand(),
                ),

                // زخرفة إسلامية رئيسية - متحركة حسب التمرير
                TweenAnimationBuilder<double>(
                  tween: Tween<double>(begin: 0.0, end: 1.0),
                  duration: const Duration(milliseconds: 1500),
                  curve: Curves.easeOutCubic,
                  builder: (context, value, child) {
                    return Stack(
                      children: [
                        // زخرفة أولى
                        Positioned(
                          right: -20 +
                              (10 * scrollRatio), // تعديل الموضع من -40 إلى -20
                          top: -10 +
                              (15 * scrollRatio), // تعديل الموضع من -30 إلى -10
                          child: Opacity(
                            opacity: 0.3 -
                                (0.05 *
                                    scrollRatio), // زيادة قيمة الشفافية من 0.12 إلى 0.3
                            child: Transform.rotate(
                              angle: -0.05 * math.pi +
                                  (0.02 * math.pi * scrollRatio),
                              child: Transform.scale(
                                scale: 1.0 - (0.2 * scrollRatio),
                                child: SvgPicture.asset(
                                  'assets/images/p2.svg',
                                  width: screenSize.width *
                                      0.6, // زيادة العرض من 0.4 إلى 0.6
                                  height: screenSize.width *
                                      0.6, // زيادة الارتفاع من 0.4 إلى 0.6
                                  colorFilter: ColorFilter.mode(
                                    settingsColor
                                        .withAlpha(230), // 0.9 * 255 = 230
                                    BlendMode.srcIn,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),

                        // زخرفة ثانية للعمق
                        Positioned(
                          left: -30 +
                              (15 * scrollRatio), // تعديل الموضع من -50 إلى -30
                          bottom: -screenSize.height *
                              0.05, // تعديل الموضع من -0.1 إلى -0.05
                          child: Opacity(
                            opacity: 0.25 -
                                (0.04 *
                                    scrollRatio), // زيادة قيمة الشفافية من 0.08 إلى 0.25
                            child: Transform.rotate(
                              angle: 0.07 * math.pi * (1 - scrollRatio * 0.5),
                              child: Transform.scale(
                                scale: 0.7 - (0.2 * scrollRatio),
                                child: SvgPicture.asset(
                                  'assets/images/p2.svg',
                                  width: screenSize.width *
                                      0.5, // زيادة العرض من 0.35 إلى 0.5
                                  height: screenSize.width *
                                      0.5, // زيادة الارتفاع من 0.35 إلى 0.5
                                  colorFilter: ColorFilter.mode(
                                    settingsColor
                                        .withAlpha(204), // 0.8 * 255 = 204
                                    BlendMode.srcIn,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),

                // تأثير ضبابي في الأسفل
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    height: 40,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          isDarkMode
                              ? Colors.black.withAlpha(26) // 0.1 * 255 = 26
                              : Colors.black.withAlpha(8), // 0.03 * 255 = 8
                          isDarkMode
                              ? Colors.black.withAlpha(38) // 0.15 * 255 = 38
                              : Colors.black.withAlpha(13), // 0.05 * 255 = 13
                        ],
                        stops: const [0.0, 0.5, 1.0],
                      ),
                    ),
                  ),
                ),

                // أيقونة مع معلومات - متوافقة مع اتجاه اللغة العربية
                Positioned(
                  bottom: 16,
                  right: 16, // تغيير الموضع من اليسار إلى اليمين
                  child: Opacity(
                    opacity: 1.0 - scrollRatio,
                    child: Row(
                      textDirection: TextDirection
                          .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                      children: [
                        Container(
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color:
                                settingsColor.withAlpha(51), // 0.2 * 255 = 51
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black
                                    .withAlpha(13), // 0.05 * 255 = 13
                                blurRadius: 5,
                                offset: const Offset(0, 2),
                              ),
                            ],
                            border: Border.all(
                              color:
                                  settingsColor.withAlpha(77), // 0.3 * 255 = 77
                              width: 0.5,
                            ),
                          ),
                          child: Icon(
                            Icons.settings,
                            color: settingsColor,
                            size: 24,
                            shadows: [
                              Shadow(
                                color: Colors.black
                                    .withAlpha(51), // 0.2 * 255 = 51
                                blurRadius: 2,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

// تأثير الإضاءة المتقدم
class SettingsLightingEffect extends CustomPainter {
  final Color color;
  final double progress;
  final bool isDarkMode;

  SettingsLightingEffect({
    required this.color,
    required this.progress,
    required this.isDarkMode,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);

    // تأثير وهج في الزاوية العلوية اليمنى
    final double intensity = isDarkMode ? 0.1 : 0.15;
    final topRightGlow = RadialGradient(
      center: const Alignment(0.7, -0.6),
      radius: 0.9 + (0.1 * math.sin(progress * math.pi * 2)),
      colors: [
        color.withAlpha(
            (intensity * math.max(0, math.sin(progress * math.pi)) * 255)
                .toInt()), // intensity * sin(progress * pi) * 255
        color.withAlpha(
            (intensity * 0.4 * math.max(0, math.sin(progress * math.pi)) * 255)
                .toInt()), // intensity * 0.4 * sin(progress * pi) * 255
        Colors.transparent,
      ],
      stops: const [0.0, 0.4, 0.8],
    ).createShader(rect);

    canvas.drawRect(rect, Paint()..shader = topRightGlow);

    // تأثير وهج في الجزء السفلي
    final bottomGlow = RadialGradient(
      center: const Alignment(-0.5, 0.8),
      radius: 0.8 + (0.15 * math.sin(progress * math.pi * 1.3)),
      colors: [
        color.withAlpha((intensity *
                0.7 *
                math.max(0, math.sin((progress + 0.5) * math.pi)) *
                255)
            .toInt()), // intensity * 0.7 * sin((progress + 0.5) * pi) * 255
        color.withAlpha((intensity *
                0.3 *
                math.max(0, math.sin((progress + 0.5) * math.pi)) *
                255)
            .toInt()), // intensity * 0.3 * sin((progress + 0.5) * pi) * 255
        Colors.transparent,
      ],
      stops: const [0.0, 0.3, 0.7],
    ).createShader(rect);

    canvas.drawRect(rect, Paint()..shader = bottomGlow);

    // نقاط ضوء متوهجة صغيرة
    _drawLightSpot(canvas, size, 0.6, -0.3,
        0.15 * math.max(0, math.sin(progress * math.pi)), color);
    _drawLightSpot(canvas, size, -0.4, 0.5,
        0.12 * math.max(0, math.sin((progress + 0.3) * math.pi)), color);
  }

  void _drawLightSpot(Canvas canvas, Size size, double dx, double dy,
      double intensity, Color color) {
    final center = Offset(
      size.width * 0.5 + (dx * size.width * 0.5),
      size.height * 0.5 + (dy * size.height * 0.5),
    );

    final radius = size.width * 0.12;

    final spotGradient = RadialGradient(
      colors: [
        color.withAlpha((intensity * 255).toInt()), // intensity * 255
        color.withAlpha(
            (intensity * 0.3 * 255).toInt()), // intensity * 0.3 * 255
        Colors.transparent,
      ],
      stops: const [0.0, 0.3, 1.0],
    ).createShader(Rect.fromCircle(center: center, radius: radius));

    canvas.drawCircle(
      center,
      radius,
      Paint()..shader = spotGradient,
    );
  }

  @override
  bool shouldRepaint(SettingsLightingEffect oldDelegate) =>
      oldDelegate.progress != progress ||
      oldDelegate.isDarkMode != isDarkMode ||
      oldDelegate.color != color;
}
