// شاشة تشغيل الورد

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/wird_provider.dart';
import '../providers/tasbih_provider.dart';
import '../models/wird_model.dart';
import '../utils/tasbih_colors.dart';
import '../components/counter_circle.dart';
import '../components/bead.dart';
import 'package:vibration/vibration.dart';
import '../../../services/notification_manager.dart';

class WirdPlayerScreen extends StatefulWidget {
  final int wirdId;

  const WirdPlayerScreen({Key? key, required this.wirdId}) : super(key: key);

  @override
  State<WirdPlayerScreen> createState() => _WirdPlayerScreenState();
}

class _WirdPlayerScreenState extends State<WirdPlayerScreen>
    with SingleTickerProviderStateMixin {
  // متغير للتحكم في الاهتزاز
  bool _vibrationEnabled = true;
  late AnimationController _animationController;
  int _count = 0;
  bool _isCompleted = false;
  List<double> _beadPositions = [];
  bool _isDarkMode = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // تحميل إعدادات الاهتزاز من التخزين المحلي
    _loadVibrationSettings();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // إعادة تعيين جميع الأوراد المكتملة عند فتح الشاشة
        _resetAllCompletedWirds();

        final wirdProvider = Provider.of<WirdProvider>(context, listen: false);

        // تنشيط الورد المحدد أولاً
        wirdProvider.activateWird(widget.wirdId).then((_) {
          // ثم بدء تشغيل الورد
          wirdProvider.startWird();
        });

        // تهيئة مواضع الخرز (إذا كان الورد قد تم تنشيطه بالفعل)
        final currentItem = wirdProvider.currentItem;
        if (currentItem != null) {
          setState(() {
            // تهيئة قيمة العداد
            _count = currentItem.currentCount;

            _beadPositions =
                List.generate(currentItem.targetCount, (index) => 0.0);
            // تحديث مواضع الخرز المكتملة
            for (int i = 0;
                i < currentItem.currentCount && i < _beadPositions.length;
                i++) {
              _beadPositions[i] = 1.0;
            }

            // تحديد وضع الثيم
            _isDarkMode = Theme.of(context).brightness == Brightness.dark;
          });
        }

        // إضافة مستمع للتغييرات في WirdProvider
        wirdProvider.addListener(_updateFromProvider);
      }
    });
  }

  // إعادة تعيين الأوراد المكتملة بناءً على الوقت وعرض تنبيه أنيق
  Future<void> _resetAllCompletedWirds() async {
    final wirdProvider = Provider.of<WirdProvider>(context, listen: false);

    // إعادة تعيين الأوراد المكتملة وجلب قائمة الأوراد التي تم إعادة تعيينها
    final resetWirdNames = await wirdProvider.resetAllCompletedWirds();

    // عرض تنبيه أنيق إذا تم إعادة تعيين أي ورد
    if (resetWirdNames.isNotEmpty && mounted) {
      _showResetWirdsNotification(resetWirdNames);
    }
  }

  // عرض تنبيه أنيق عند إعادة تعيين الأوراد المكتملة
  void _showResetWirdsNotification(List<String> resetWirdNames) {
    // تأخير قليل لضمان أن الشاشة قد تم تحميلها بالكامل
    Future.delayed(const Duration(milliseconds: 500), () {
      if (!mounted) return;

      // إنشاء نص التنبيه
      String message;
      if (resetWirdNames.length == 1) {
        message = 'تم إعادة تعيين الورد "${resetWirdNames[0]}" لبدء يوم جديد';
      } else if (resetWirdNames.length == 2) {
        message =
            'تم إعادة تعيين الوردين "${resetWirdNames[0]}" و "${resetWirdNames[1]}" لبدء يوم جديد';
      } else {
        message = 'تم إعادة تعيين ${resetWirdNames.length} أوراد لبدء يوم جديد';
      }

      // عرض التنبيه
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(
                Icons.refresh_rounded,
                color: Colors.white,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(message),
              ),
            ],
          ),
          backgroundColor: Colors.teal.shade700,
          duration: const Duration(seconds: 4),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          margin: const EdgeInsets.all(8),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          elevation: 4,
          action: SnackBarAction(
            label: 'حسناً',
            textColor: Colors.white,
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    });
  }

  // دالة لتحديث الواجهة عند تغيير العنصر الحالي
  void _updateFromProvider() {
    // لا يوجد تحقق mounted في الكود الأصلي هنا
    // if (!mounted) return;

    final wirdProvider = Provider.of<WirdProvider>(context, listen: false);
    final currentItem = wirdProvider.currentItem;
    final activeWird = wirdProvider.activeWird;

    if (currentItem != null) {
      // التحقق من إكمال الورد قبل تحديث الحالة
      final isWirdCompleted = activeWird?.isCompleted ?? false;
      final wasCompletedBefore = _isCompleted;

      setState(() {
        // تحديث قيمة العداد
        _count = currentItem.currentCount;

        // إعادة تهيئة مواضع الخرز
        _beadPositions = List.generate(currentItem.targetCount, (index) => 0.0);

        // تحديث مواضع الخرز المكتملة
        for (int i = 0;
            i < currentItem.currentCount && i < _beadPositions.length;
            i++) {
          _beadPositions[i] = 1.0;
        }

        // تحديث وضع الثيم
        _isDarkMode = Theme.of(context).brightness == Brightness.dark;

        // تحديث حالة الإكمال
        _isCompleted = isWirdCompleted;
      });

      // إذا تم إكمال الورد ولم يكن مكتملاً من قبل، عرض حوار الإكمال
      if (isWirdCompleted && !wasCompletedBefore) {
        // تأخير بسيط لضمان عرض الحوار بعد اكتمال التحديثات
        Future.delayed(const Duration(milliseconds: 300), () {
          // لا يوجد تحقق mounted في الكود الأصلي هنا
          // if (mounted) {
          _showCompletionDialog();
          // }
        });
      }
    }
    // لا يوجد else لمعالجة currentItem == null في الكود الأصلي
  }

  @override
  void dispose() {
    // إزالة المستمع عند التخلص من الشاشة
    final wirdProvider = Provider.of<WirdProvider>(context, listen: false);
    wirdProvider
        .removeListener(_updateFromProvider); // لا يوجد try-catch في الأصلي

    _animationController.dispose();
    super.dispose();
  }

  // تحميل إعدادات الاهتزاز من التخزين المحلي
  Future<void> _loadVibrationSettings() async {
    // لا يوجد تحقق mounted في الكود الأصلي هنا
    try {
      final tasbihProvider =
          Provider.of<TasbihProvider>(context, listen: false);
      // لا يوجد تحقق mounted إضافي في الكود الأصلي هنا
      setState(() {
        _vibrationEnabled = tasbihProvider.vibrationEnabled;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل إعدادات الاهتزاز: $e');
      // لا يوجد تعيين قيمة افتراضية في الأصلي
    }
  }

  // دالة للاهتزاز الخفيف بين الأذكار
  Future<void> _performLightVibration() async {
    if (!_vibrationEnabled) return;

    try {
      // التحقق من دعم الجهاز للاهتزاز
      bool? hasVibrator = await Vibration.hasVibrator();
      bool? hasAmplitudeControl = await Vibration.hasAmplitudeControl();

      if (hasVibrator == true) {
        if (hasAmplitudeControl == true) {
          // استخدام نمط اهتزاز خفيف جداً مع التحكم في الشدة
          Vibration.vibrate(
            duration: 35, // اهتزاز خفيف لمدة 35 مللي ثانية فقط
            amplitude: 50, // شدة منخفضة جداً
          );
        } else {
          // استخدام اهتزاز قصير جداً للأجهزة التي لا تدعم التحكم في الشدة
          Vibration.vibrate(duration: 25);
        }
      } else {
        // استخدام HapticFeedback كبديل خفيف
        HapticFeedback.selectionClick();
      }
    } catch (e) {
      // لا يوجد debugPrint في الأصلي
      // في حالة حدوث خطأ، استخدم HapticFeedback كبديل
      HapticFeedback.selectionClick();
    }
  }

  // دالة للاهتزاز عند إكمال الذكر (مشابهة للمسبحة الرئيسية)
  Future<void> _performCompletionVibration() async {
    if (!_vibrationEnabled) return;

    try {
      // التحقق من دعم الجهاز للاهتزاز
      bool? hasVibrator = await Vibration.hasVibrator();
      bool? hasAmplitudeControl = await Vibration.hasAmplitudeControl();

      if (hasVibrator == true) {
        if (hasAmplitudeControl == true) {
          // استخدام نمط اهتزاز خفيف جداً مع التحكم في الشدة
          Vibration.vibrate(
            duration: 40, // اهتزاز خفيف لمدة 40 مللي ثانية فقط
            amplitude: 60, // شدة منخفضة جداً
          );
        } else {
          // استخدام اهتزاز قصير جداً للأجهزة التي لا تدعم التحكم في الشدة
          Vibration.vibrate(duration: 30);
        }

        // اهتزاز ثاني بعد فترة قصيرة لتحسين التجربة
        await Future.delayed(const Duration(milliseconds: 100));
        // لا يوجد تحقق mounted في الكود الأصلي هنا
        if (hasAmplitudeControl == true) {
          Vibration.vibrate(
            duration: 30,
            amplitude: 40, // شدة أقل للاهتزاز الثاني
          );
        } else {
          Vibration.vibrate(duration: 20);
        }
      } else {
        // استخدام HapticFeedback كبديل خفيف
        HapticFeedback.lightImpact();
        await Future.delayed(const Duration(milliseconds: 100));
        // لا يوجد تحقق mounted في الكود الأصلي هنا
        HapticFeedback.selectionClick();
      }
    } catch (e) {
      // لا يوجد debugPrint في الأصلي
      // في حالة حدوث خطأ، استخدم HapticFeedback كبديل
      HapticFeedback.lightImpact();
    }
  }

  Future<void> _incrementCounter() async {
    // لا يوجد تحقق mounted في الكود الأصلي هنا
    final wirdProvider = Provider.of<WirdProvider>(context, listen: false);
    final currentItem = wirdProvider.currentItem;

    if (currentItem != null) {
      // التحقق من أن العداد لم يصل إلى الهدف بعد
      // الكود الأصلي يتحقق فقط من currentCount من المزود
      if (currentItem.currentCount >= currentItem.targetCount || _isCompleted) {
        // لا يوجد debugPrint في الأصلي
        return; // لا تقم بالزيادة إذا كان العداد قد وصل إلى الهدف أو تم إكمال الورد
      }

      // تشغيل الرسوم المتحركة
      _animationController.forward(from: 0.0);

      // زيادة العداد محلياً قبل التحديث في المزود
      setState(() {
        // العداد المحلي يتحدث مباشرة
        _count = currentItem.currentCount + 1;

        // تحديث مواضع الخرز
        if (_count > 0 && _count <= _beadPositions.length) {
          _beadPositions[_count - 1] = 1.0;
        }
      });

      // التحقق مما إذا كان هذا هو العدد الأخير للذكر الحالي
      // باستخدام العداد المحلي _count بعد زيادته
      bool isLastCount = _count == currentItem.targetCount;

      // زيادة العداد في المزود
      await wirdProvider.incrementCurrentItem();

      // لا يوجد تحقق mounted في الكود الأصلي هنا
      // if (!mounted) return;

      // التحقق من العنصر الحالي بعد التحديث
      final updatedCurrentItem = wirdProvider.currentItem;
      final isWirdCompleted = wirdProvider.activeWird?.isCompleted ?? false;
      /*final wasCompletedBefore = _isCompleted; // حفظ الحالة السابقة*/

      // إذا كان هذا هو العدد الأخير للذكر الحالي، قم بالاهتزاز مثل المسبحة الرئيسية
      if (isLastCount) {
        _performCompletionVibration();
      }

      // إذا تغير العنصر الحالي (انتقلنا إلى ذكر جديد) أو تم إكمال الورد
      if (updatedCurrentItem != currentItem || isWirdCompleted) {
        // اهتزاز خفيف عند الانتقال بين الأذكار
        if (updatedCurrentItem != currentItem && !isLastCount) {
          // نقوم بالاهتزاز الخفيف فقط إذا لم يكن هناك اهتزاز إكمال الذكر
          _performLightVibration();
        }

        // تحديث الواجهة بالكامل
        _updateFromProvider(); // ستقوم هذه الدالة بتحديث _isCompleted
      }

      // عرض حوار الإكمال إذا تم إكمال الورد ولم يكن مكتملاً من قبل
      // تم نقل هذا المنطق إلى _updateFromProvider في الكود الأصلي
      // if (isWirdCompleted && !wasCompletedBefore) {
      //   setState(() {
      //     _isCompleted = true;
      //   });
      //   Future.delayed(const Duration(milliseconds: 300), () {
      //     if (mounted) { // الكود الأصلي قد لا يحتوي على هذا التحقق
      //       _showCompletionDialog();
      //     }
      //   });
      // }
    }
  }

  void _showCompletionDialog() {
    // التحقق من أن الشاشة ما زالت موجودة (لم يكن هذا في الأصلي)
    // if (!mounted) return;

    final wirdProvider = Provider.of<WirdProvider>(context, listen: false);
    final activeWird = wirdProvider.activeWird;

    if (activeWird != null) {
      // التأكد من أن الورد مكتمل بالفعل (لم يكن هذا في الأصلي)
      // if (!activeWird.isCompleted) return;

      // اهتزاز قوي عند إكمال الورد
      HapticFeedback.heavyImpact();
      Future.delayed(const Duration(milliseconds: 300), () {
        // لا يوجد تحقق mounted في الأصلي هنا
        HapticFeedback.mediumImpact();
      });

      // التأكد من عدم وجود حوار مفتوح بالفعل (لا يوجد تحقق في الأصلي)
      showDialog(
        context: context,
        barrierDismissible: false, // الأصلي
        builder: (dialogContext) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(24),
            ),
            backgroundColor:
                _isDarkMode ? TasbihColors.darkCardColor : Colors.white,
            elevation: 10,
            title: Container(
              padding: const EdgeInsets.symmetric(
                vertical: 8,
                horizontal: 16,
              ),
              decoration: BoxDecoration(
                color: TasbihColors.primary.withAlpha(20),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: TasbihColors.primary.withAlpha(50),
                  width: 1,
                ),
              ),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.celebration,
                    color: TasbihColors.primary,
                    size: 30,
                  ),
                  SizedBox(width: 12),
                  Text(
                    'تهانينا!',
                    style: TextStyle(
                      color: TasbihColors.primary,
                      fontWeight: FontWeight.bold,
                      fontSize: 20,
                    ),
                  ),
                ],
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: RadialGradient(
                      colors: [
                        TasbihColors.primary.withAlpha(50),
                        TasbihColors.primary.withAlpha(30),
                      ],
                      radius: 0.8,
                    ),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: TasbihColors.primary.withAlpha(40),
                        blurRadius: 15,
                        spreadRadius: 2,
                      ),
                    ],
                    border: Border.all(
                      color: TasbihColors.primary.withAlpha(70),
                      width: 2,
                    ),
                  ),
                  child: const Icon(
                    Icons.check_circle,
                    color: TasbihColors.primary,
                    size: 70,
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  'لقد أكملت ورد "${activeWird.name}"',
                  textAlign: TextAlign.center,
                  style: Theme.of(dialogContext).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        // لا يوجد تحديد لون صريح هنا في الأصلي
                      ),
                ),
                const SizedBox(height: 12),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  decoration: BoxDecoration(
                    color: _isDarkMode
                        ? TasbihColors.primary.withAlpha(30)
                        : TasbihColors.primary.withAlpha(25),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: TasbihColors.primary.withAlpha(20),
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                        spreadRadius: 1,
                      ),
                    ],
                    border: Border.all(
                      color: TasbihColors.primary.withAlpha(50),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: TasbihColors.primary.withAlpha(30),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.format_list_numbered,
                          size: 18,
                          color: TasbihColors.primary,
                        ),
                      ),
                      const SizedBox(width: 10),
                      Text(
                        'عدد التسبيحات: ${activeWird.totalDhikrCount}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: TasbihColors.primary,
                          fontSize: 15,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    color: _isDarkMode
                        ? Colors.grey[800]!.withAlpha(50)
                        : Colors.grey[100],
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color:
                          _isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
                      width: 1,
                    ),
                  ),
                  child: const Text(
                    'بارك الله في جهودك وتقبل منك صالح الأعمال',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontStyle: FontStyle.italic,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey,
                      fontSize: 14,
                      height: 1.5,
                    ),
                  ),
                ),
              ],
            ),
            actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            actions: [
              OutlinedButton.icon(
                onPressed: () {
                  // الكود الأصلي يستدعي completeWird هنا
                  wirdProvider.completeWird(activeWird.id);
                  Navigator.pop(dialogContext); // إغلاق الحوار
                  Navigator.pop(context); // العودة إلى الشاشة السابقة
                },
                icon: const Icon(Icons.arrow_back, size: 18),
                label: const Text(
                  'العودة',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.grey[700],
                  side: BorderSide(
                    color: Colors.grey[400]!,
                    width: 1.5,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: const EdgeInsets.symmetric(
                    vertical: 12,
                    horizontal: 16,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                onPressed: () {
                  // إغلاق الحوار أولاً
                  Navigator.pop(dialogContext);

                  // ثم إكمال الورد وإعادة ضبطه
                  _completeAndResetWird(activeWird.id);
                },
                icon: const Icon(Icons.refresh, size: 18),
                label: const Text(
                  'إعادة البدء',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: TasbihColors.primary,
                  foregroundColor: Colors.white,
                  elevation: 2,
                  shadowColor: TasbihColors.primary.withAlpha(100),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: const EdgeInsets.symmetric(
                    vertical: 12,
                    horizontal: 16,
                  ),
                ),
              ),
            ],
          );
        },
      );
    }
    // لا يوجد else أو debugPrint في الأصلي
  }

  @override
  Widget build(BuildContext context) {
    // لم يتم تحديد _isDarkMode هنا في الأصلي
    return Consumer<WirdProvider>(
      builder: (context, wirdProvider, child) {
        final activeWird = wirdProvider.activeWird;
        final currentItem = wirdProvider.currentItem;

        if (activeWird == null || currentItem == null) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('تشغيل الورد'),
              centerTitle: true,
              backgroundColor: TasbihColors.primary,
              foregroundColor: Colors.white,
            ),
            body: const Center(
              child: Text(
                  'لا يوجد ورد نشط أو لا توجد أذكار في الورد'), // الرسالة الأصلية
            ),
          );
        }

        // الآن نحن متأكدون أن activeWird و currentItem ليسا null
        // تحديد _isDarkMode داخل الـ builder بناءً على السياق الحالي
        _isDarkMode = Theme.of(context).brightness == Brightness.dark;

        return Scaffold(
          appBar: AppBar(
            title: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  activeWird.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                  // لا يوجد overflow في الأصلي
                ),
                const SizedBox(height: 2),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(51), // اللون الأصلي
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'الذكر ${currentItem.order} من ${activeWird.items.length}',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500, // الخط الأصلي
                    ),
                  ),
                ),
              ],
            ),
            centerTitle: true,
            backgroundColor: TasbihColors.primary,
            foregroundColor: Colors.white,
            elevation: 0, // الأصلي
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(
                bottom: Radius.circular(16), // الانحناء الأصلي
              ),
            ),
            actions: [
              // زر اختبار الإشعارات
              IconButton(
                icon:
                    const Icon(Icons.notifications_active), // الأيقونة الأصلية
                tooltip: 'اختبار الإشعارات',
                onPressed: () {
                  _testWirdNotification(); // استدعاء الدالة مباشرة
                },
              ),
              // زر إعادة ضبط الورد
              IconButton(
                icon: const Icon(Icons.refresh),
                tooltip: 'إعادة ضبط الورد',
                onPressed: () {
                  // استدعاء دالة عرض الحوار
                  _showResetConfirmationDialog(context, activeWird);
                },
              ),
            ],
          ),
          // لا يوجد WillPopScope في الأصلي
          body: SafeArea(
            child: LayoutBuilder(
              // الأصلي استخدم LayoutBuilder
              builder: (context, constraints) {
                return Column(
                  children: [
                    // الجزء العلوي مع معلومات الذكر
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: _isDarkMode // استخدام _isDarkMode المحدثة
                              ? [
                                  TasbihColors.primary
                                      .withAlpha(40), // الألوان الأصلية
                                  Colors.transparent,
                                ]
                              : [
                                  TasbihColors.primary
                                      .withAlpha(25), // الألوان الأصلية
                                  Colors.transparent,
                                ],
                          stops: const [0.0, 0.3], // التوقف الأصلي
                        ),
                      ),
                      padding: const EdgeInsets.fromLTRB(
                          16, 16, 16, 4), // الحشو الأصلي
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: _isDarkMode
                                      ? TasbihColors.primary
                                          .withAlpha(40) // اللون الأصلي
                                      : TasbihColors.primary
                                          .withAlpha(25), // اللون الأصلي
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: _isDarkMode
                                        ? TasbihColors.primary
                                            .withAlpha(76) // اللون الأصلي
                                        : TasbihColors.primary
                                            .withAlpha(76), // اللون الأصلي
                                    width: 1,
                                  ),
                                ),
                                child: const Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.auto_awesome, // الأيقونة الأصلية
                                      size: 16,
                                      color: TasbihColors.primary,
                                    ),
                                    SizedBox(width: 6),
                                    Text(
                                      'الذكر الحالي',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: TasbihColors.primary,
                                        fontSize: 14, // الخط الأصلي
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const Spacer(),
                              // عداد التقدم
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: _isDarkMode
                                      ? Colors.grey[800]!
                                          .withAlpha(100) // اللون الأصلي
                                      : Colors.grey
                                          .withAlpha(25), // اللون الأصلي
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Text(
                                  // استخدام العداد من العنصر مباشرة
                                  '${currentItem.currentCount}/${currentItem.targetCount}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: TasbihColors.primary,
                                    fontSize: 14, // الخط الأصلي
                                    // لا يوجد fontFeatures في الأصلي
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16), // المسافة الأصلية
                          Card(
                            elevation: 6,
                            shadowColor: TasbihColors.primary.withAlpha(100),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(24),
                              side: BorderSide(
                                color: _isDarkMode
                                    ? Colors.grey[700]!
                                    : Colors.grey[200]!,
                                width: 1.5,
                              ),
                            ),
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? TasbihColors.darkCardColor
                                    : TasbihColors.lightCardColor,
                            child: Padding(
                              padding: const EdgeInsets.all(24),
                              child: Column(
                                children: [
                                  // عرض النص العربي إذا كان موجوداً، وإلا عرض اسم الذكر
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 10,
                                    ),
                                    decoration: BoxDecoration(
                                      color: _isDarkMode
                                          ? TasbihColors.primary.withAlpha(15)
                                          : TasbihColors.primary.withAlpha(10),
                                      borderRadius: BorderRadius.circular(16),
                                      border: Border.all(
                                        color:
                                            TasbihColors.primary.withAlpha(30),
                                        width: 1,
                                      ),
                                    ),
                                    child: Text(
                                      currentItem.dhikr.name, // اسم الذكر أولاً
                                      style: Theme.of(context)
                                          .textTheme
                                          .headlineSmall
                                          ?.copyWith(
                                            fontWeight: FontWeight.bold,
                                            color: TasbihColors.primary,
                                            letterSpacing: 0.5,
                                          ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                  if (currentItem.dhikr.arabicText.isNotEmpty &&
                                      currentItem.dhikr.arabicText !=
                                          currentItem.dhikr.name) ...[
                                    const SizedBox(height: 16),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 12,
                                      ),
                                      decoration: BoxDecoration(
                                        color: _isDarkMode
                                            ? Colors.grey[800]!.withAlpha(100)
                                            : Colors.grey[100],
                                        borderRadius: BorderRadius.circular(16),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withAlpha(10),
                                            blurRadius: 4,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: Text(
                                        currentItem.dhikr.arabicText,
                                        style: Theme.of(context)
                                            .textTheme
                                            .headlineSmall
                                            ?.copyWith(
                                              height: 1.5,
                                            ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ],
                                  const SizedBox(height: 12), // المسافة الأصلية
                                  // عرض العدد الحالي والعدد المستهدف
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 20, vertical: 12),
                                    decoration: BoxDecoration(
                                      color: _isDarkMode
                                          ? TasbihColors.primary.withAlpha(40)
                                          : TasbihColors.primary.withAlpha(30),
                                      borderRadius: BorderRadius.circular(20),
                                      boxShadow: [
                                        BoxShadow(
                                          color: TasbihColors.primary
                                              .withAlpha(20),
                                          blurRadius: 6,
                                          offset: const Offset(0, 2),
                                          spreadRadius: 1,
                                        ),
                                      ],
                                      border: Border.all(
                                        color:
                                            TasbihColors.primary.withAlpha(50),
                                        width: 1,
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.all(6),
                                          decoration: BoxDecoration(
                                            color: TasbihColors.primary
                                                .withAlpha(30),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          child: Icon(
                                            Icons.repeat,
                                            size: 16,
                                            color: _isDarkMode
                                                ? Colors.white
                                                : Colors.black87,
                                          ),
                                        ),
                                        const SizedBox(width: 10),
                                        Text(
                                          'العدد: ',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 15,
                                            color:
                                                Theme.of(context).brightness ==
                                                        Brightness.dark
                                                    ? Colors.white
                                                    : Colors.black87,
                                          ),
                                        ),
                                        Text(
                                          '${currentItem.currentCount}/${currentItem.targetCount}',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                            color: TasbihColors.primary,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(height: 12), // المسافة الأصلية
                                  // لا يوجد فاصل أو معلومات إضافية في هذا الجزء بالكود الأصلي الذي حللته سابقاً
                                  // ولكنها موجودة في الكود الكامل الذي قدمته أول مرة، لذا سأبقيها
                                  Divider(
                                      color: _isDarkMode // اللون الأصلي للفاصل
                                          ? Colors.grey[700]
                                          : Colors.grey.withAlpha(76)),
                                  const SizedBox(height: 8),
                                  // تم إزالة قسم النطق والترجمة
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // جزء المسبحة
                    Expanded(
                      child: SingleChildScrollView(
                        // الأصلي استخدم SingleChildScrollView
                        physics: const BouncingScrollPhysics(), // الأصلي
                        child: Center(
                          child: Padding(
                            // لا يوجد Padding في الأصلي
                            padding: const EdgeInsets.symmetric(
                                vertical: 0), // إزالة الحشو
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const SizedBox(height: 4), // المسافة الأصلية
                                // المسبحة الدائرية مع الخرز
                                SizedBox(
                                  // الحجم الأصلي
                                  height:
                                      MediaQuery.of(context).size.width * 0.65,
                                  width:
                                      MediaQuery.of(context).size.width * 0.65,
                                  child: Stack(
                                    alignment: Alignment.center,
                                    children: [
                                      // رسم خرز المسبحة (الكود الأصلي لعرض الخرز)
                                      for (int i = 0;
                                          i < _beadPositions.length;
                                          i++)
                                        // المنطق الأصلي لعرض الخرز
                                        if (_beadPositions.length <= 99 ||
                                            _beadPositions[i] > 0 ||
                                            (currentItem.currentCount > 0 &&
                                                i >=
                                                    currentItem.currentCount -
                                                        5 &&
                                                i <=
                                                    currentItem.currentCount +
                                                        5))
                                          BeadComponent(
                                            index: i,
                                            count: _beadPositions.length,
                                            position: _beadPositions[i],
                                            // الحجم الأصلي
                                            size: MediaQuery.of(context)
                                                    .size
                                                    .width *
                                                0.65,
                                            isDarkMode: _isDarkMode,
                                          ),

                                      // الدائرة الداخلية مع عدد الذكر
                                      GestureDetector(
                                        onTap: _isCompleted
                                            ? null
                                            : _incrementCounter, // الأصلي
                                        child: CounterCircle(
                                          count:
                                              _count, // استخدام العداد المحلي _count
                                          onTap: _isCompleted
                                              ? () {}
                                              : _incrementCounter, // الأصلي
                                          animationController:
                                              _animationController,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    // الجزء السفلي مع معلومات التقدم
                    Container(
                      padding: const EdgeInsets.all(16), // الحشو الأصلي
                      decoration: BoxDecoration(
                        color: _isDarkMode // اللون الأصلي
                            ? TasbihColors.darkBackground.withAlpha(50)
                            : Colors.grey.withAlpha(13),
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(24), // الانحناء الأصلي
                        ),
                        boxShadow: [
                          // الظل الأصلي
                          BoxShadow(
                            color: Colors.black.withAlpha(8),
                            blurRadius: 10,
                            offset: const Offset(0, -3),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 6), // الحشو الأصلي
                                decoration: BoxDecoration(
                                  color: _isDarkMode // اللون الأصلي
                                      ? TasbihColors.primary.withAlpha(40)
                                      : TasbihColors.primary.withAlpha(25),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: const Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.insights, // الأيقونة الأصلية
                                      size: 16,
                                      color: TasbihColors.primary,
                                    ),
                                    SizedBox(width: 6),
                                    Text(
                                      'تقدم الورد',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: TasbihColors.primary,
                                        fontSize: 14, // الخط الأصلي
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const Spacer(),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 6), // الحشو الأصلي
                                decoration: BoxDecoration(
                                  color: _isDarkMode // اللون الأصلي
                                      ? TasbihColors.primary.withAlpha(40)
                                      : TasbihColors.primary.withAlpha(25),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Text(
                                  // العداد الكلي الأصلي
                                  '${activeWird.completedDhikrCount}/${activeWird.totalDhikrCount}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: TasbihColors.primary,
                                    fontSize: 14, // الخط الأصلي
                                    // لا يوجد fontFeatures في الأصلي
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16), // المسافة الأصلية
                          Stack(
                            children: [
                              // شريط التقدم الرئيسي
                              ClipRRect(
                                borderRadius:
                                    BorderRadius.circular(8), // الانحناء الأصلي
                                child: LinearProgressIndicator(
                                  value: activeWird
                                      .completionPercentage, // القيمة الأصلية
                                  backgroundColor: _isDarkMode // اللون الأصلي
                                      ? TasbihColors.darkCardColor
                                          .withAlpha(150)
                                      : Colors.grey[200],
                                  valueColor:
                                      const AlwaysStoppedAnimation<Color>(
                                    TasbihColors.primary,
                                  ),
                                  minHeight: 12, // الارتفاع الأصلي
                                ),
                              ),
                              // نسبة الإكمال فوق شريط التقدم
                              Positioned.fill(
                                child: Center(
                                  child: Text(
                                    // النص الأصلي
                                    '${(activeWird.completionPercentage * 100).toInt()}%',
                                    style: const TextStyle(
                                      fontSize: 10, // الخط الأصلي
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                      shadows: [
                                        // الظل الأصلي
                                        Shadow(
                                          color: Colors.black54,
                                          blurRadius: 2,
                                          offset: Offset(0, 1),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 20), // المسافة الأصلية
                          Row(
                            children: [
                              Expanded(
                                child: OutlinedButton.icon(
                                  onPressed: () {
                                    // لا يوجد إيقاف للورد هنا في الأصلي
                                    Navigator.pop(context);
                                  },
                                  icon: const Icon(
                                      Icons.close), // الأيقونة الأصلية
                                  label: const Text('إغلاق'),
                                  style: OutlinedButton.styleFrom(
                                    foregroundColor: _isDarkMode // اللون الأصلي
                                        ? TasbihColors.darkTextSecondary
                                        : Colors.grey[700],
                                    side: BorderSide(
                                        // الحد الأصلي
                                        color: _isDarkMode
                                            ? Colors.grey[700]!
                                            : Colors.grey[400]!),
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 12), // الحشو الأصلي
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(
                                          12), // الانحناء الأصلي
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16), // المسافة الأصلية
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed:
                                      _isCompleted // المنطق الأصلي للتعطيل
                                          ? null
                                          : () {
                                              wirdProvider.stopWird(); // الأصلي
                                              Navigator.pop(context); // الأصلي
                                            },
                                  icon: const Icon(
                                      Icons.pause), // الأيقونة الأصلية
                                  label: const Text('إيقاف مؤقت'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor:
                                        TasbihColors.primary, // اللون الأصلي
                                    foregroundColor:
                                        Colors.white, // اللون الأصلي
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 12), // الحشو الأصلي
                                    disabledBackgroundColor:
                                        _isDarkMode // اللون الأصلي للمعطل
                                            ? Colors.grey[700]
                                            : Colors.grey[300],
                                    // لا يوجد disabledForegroundColor في الأصلي
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(
                                          12), // الانحناء الأصلي
                                    ),
                                    elevation: 2, // الظل الأصلي
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        );
      },
    );
  }

  // دالة لإكمال الورد وإعادة ضبطه (الكود الأصلي)
  Future<void> _completeAndResetWird(int wirdId) async {
    // لا يوجد تحقق mounted في الأصلي هنا
    final wirdProvider = Provider.of<WirdProvider>(context, listen: false);

    // إكمال الورد
    wirdProvider.completeWird(wirdId);

    // إعادة ضبط الورد
    await wirdProvider.resetWird(wirdId);

    // لا يوجد تحقق mounted أو setState أو startWird في الأصلي هنا
    // التحديث كان يعتمد على المستمع _updateFromProvider فقط
    // if (mounted) {
    //   setState(() {
    //     _isCompleted = false;
    //   });
    //   wirdProvider.startWird();
    // }
  }

  // دالة لاختبار إشعارات الورد (محسنة)
  Future<void> _testWirdNotification() async {
    if (!mounted) return;

    try {
      final wirdProvider = Provider.of<WirdProvider>(context, listen: false);
      final activeWird = wirdProvider.activeWird;

      if (activeWird != null) {
        // اهتزاز خفيف للتأكيد
        HapticFeedback.lightImpact();

        // عرض مؤشر التحميل
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (dialogContext) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            backgroundColor:
                _isDarkMode ? TasbihColors.darkCardColor : Colors.white,
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(
                  valueColor:
                      AlwaysStoppedAnimation<Color>(TasbihColors.primary),
                ),
                const SizedBox(height: 16),
                Text(
                  'جاري إرسال إشعار تجريبي للورد: ${activeWird.name}',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: _isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        );

        // تأخير قصير لتحسين تجربة المستخدم
        await Future.delayed(const Duration(milliseconds: 500));

        // استدعاء خدمة الإشعارات لإرسال إشعار تجريبي
        final success = await wirdProvider.testWirdNotification(activeWird.id);

        // إغلاق مؤشر التحميل
        if (mounted && Navigator.canPop(context)) {
          Navigator.of(context).pop();
        }

        if (!mounted) return;

        // عرض رسالة نجاح أو فشل
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم إرسال إشعار تجريبي للورد: ${activeWird.name}'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              duration: const Duration(seconds: 2),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        } else {
          // عرض حوار خطأ مع خيارات
          showDialog(
            context: context,
            builder: (dialogContext) => AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              backgroundColor:
                  _isDarkMode ? TasbihColors.darkCardColor : Colors.white,
              title: Row(
                children: [
                  const Icon(
                    Icons.warning_amber_rounded,
                    color: Colors.orange,
                    size: 28,
                  ),
                  const SizedBox(width: 10),
                  Text(
                    'تنبيه',
                    style: TextStyle(
                      color: _isDarkMode ? Colors.white : Colors.black87,
                    ),
                  ),
                ],
              ),
              content: Text(
                'فشل إرسال إشعار تجريبي للورد. قد يكون ذلك بسبب عدم وجود أذونات للإشعارات أو مشكلة في النظام.',
                style: TextStyle(
                  color: _isDarkMode ? Colors.white70 : Colors.black54,
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(dialogContext);
                  },
                  child: const Text('إغلاق'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    Navigator.pop(dialogContext);
                    // طلب أذونات الإشعارات
                    final notificationManager = NotificationManager();
                    await notificationManager.requestNotificationPermissions();
                    // محاولة إرسال الإشعار مرة أخرى
                    _testWirdNotification();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: TasbihColors.primary,
                  ),
                  child: const Text('طلب الأذونات وإعادة المحاولة'),
                ),
              ],
            ),
          );
        }
      } else {
        // عرض رسالة خطأ إذا لم يكن هناك ورد نشط
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('لا يوجد ورد نشط لاختبار الإشعارات'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحاً
      if (mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }

      if (!mounted) return;

      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء اختبار الإشعارات: $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );

      debugPrint('Error testing notification: $e');
    }
  }

  // دالة عرض حوار تأكيد إعادة الضبط (الكود الأصلي مع الفاصلة المنقوطة الصحيحة)
  void _showResetConfirmationDialog(
      BuildContext dialogContext, WirdModel wird) {
    // لم يتم تحديد isDialogDark في الأصلي هنا
    final bool isDialogDarkOriginal =
        Theme.of(dialogContext).brightness == Brightness.dark;

    // استدعاء showDialog - هذه عبارة تحتاج فاصلة منقوطة في نهايتها
    showDialog(
      context: dialogContext, // استخدام السياق الممرر
      builder: (context) {
        // سياق الـ builder هو سياق الحوار نفسه
        // إرجاع ويدجت AlertDialog
        return AlertDialog(
          // لا يوجد shape في الأصلي
          title: Text(
            'إعادة ضبط الورد',
            // استخدام isDialogDarkOriginal لتحديد لون النص
            style: TextStyle(
                color: isDialogDarkOriginal ? Colors.white : null), // الأصلي
          ),
          content: Text(
            'هل أنت متأكد من إعادة ضبط الورد؟ سيتم فقدان التقدم الحالي.',
            // استخدام isDialogDarkOriginal لتحديد لون النص
            style: TextStyle(
                color: isDialogDarkOriginal
                    ? TasbihColors.darkTextSecondary
                    : null), // الأصلي
          ),
          actions: [
            // بداية قائمة الأزرار actions
            TextButton(
              onPressed: () =>
                  Navigator.pop(context), // استخدام context الحوار للإغلاق
              style: TextButton.styleFrom(
                // استخدام isDialogDarkOriginal لتحديد لون النص
                foregroundColor: isDialogDarkOriginal
                    ? TasbihColors.darkTextSecondary
                    : Colors.grey[700], // الأصلي
              ), // نهاية styleFrom
              child: const Text('إلغاء'),
            ), // نهاية TextButton

            ElevatedButton(
              onPressed: () {
                // بداية دالة onPressed
                // إغلاق الحوار أولاً باستخدام context الحوار
                Navigator.pop(context);

                // ثم إعادة ضبط الورد
                // لا يوجد تحقق mounted في الأصلي هنا
                _completeAndResetWird(wird.id);
              }, // نهاية دالة onPressed
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red, // اللون الأصلي
                foregroundColor: Colors.white, // اللون الأصلي
                // لا يوجد shape في الأصلي
              ), // نهاية styleFrom
              child: const Text('إعادة ضبط'),
            ), // نهاية ElevatedButton
          ], // نهاية قائمة الأزرار actions
        ); // نهاية AlertDialog
      }, // نهاية دالة الـ builder
    ); // <<<--- !!! تم التأكد من وجود الفاصلة المنقوطة الواحدة فقط هنا !!!
  } // نهاية دالة _showResetConfirmationDialog
} // نهاية كلاس _WirdPlayerScreenState
