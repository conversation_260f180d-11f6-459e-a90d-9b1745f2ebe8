// شاشة إحصائيات المسبحة

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'utils/tasbih_colors.dart';
import 'providers/tasbih_stats_provider.dart';
import 'models/tasbih_stats_model.dart';
import 'components/stats_card.dart';
import 'components/stats_section_title.dart';
import 'components/stats_progress_indicator.dart';
import 'components/activity_chart.dart';
import 'components/dhikr_pie_chart.dart';
import 'components/achievement_card.dart';
import 'components/loading_arc_painter.dart';

class TasbihStatsScreen extends StatefulWidget {
  const TasbihStatsScreen({Key? key}) : super(key: key);

  @override
  State<TasbihStatsScreen> createState() => _TasbihStatsScreenState();
}

class _TasbihStatsScreenState extends State<TasbihStatsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late TasbihStatsProvider _statsProvider;

  // متغيرات للتحقق من بناء الشاشة
  bool _isScreenValidated = false; // متغير للتحقق من اكتمال بناء الشاشة
  bool _isValidating = true;
  bool _hasError = false;
  String _errorMessage = '';

  // متغير للتحكم في تأثيرات الظهور
  final List<bool> _sectionsVisible = [false, false, false, false, false];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // تهيئة مزود الإحصائيات
    _statsProvider = Provider.of<TasbihStatsProvider>(context, listen: false);

    // استخدام WidgetsBinding لتأخير التهيئة حتى اكتمال البناء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // تأخير قصير قبل تهيئة الإحصائيات لضمان اكتمال بناء الواجهة
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          _statsProvider.initialize().then((_) {
            _validateScreen();
          }).catchError((error) {
            if (mounted) {
              setState(() {
                _hasError = true;
                _errorMessage = 'حدث خطأ أثناء تحميل الإحصائيات: $error';
                _isValidating = false;
              });
            }
          });
        }
      });
    });

    // تأثيرات ظهور الأقسام بشكل متتالي
    _animateSectionsSequentially();
  }

  // دالة للتحقق من بناء الشاشة بشكل صحيح
  void _validateScreen() {
    if (!mounted) return;

    setState(() {
      _isValidating = true;
    });

    // محاكاة عملية التحقق من بناء الشاشة
    Future.delayed(const Duration(milliseconds: 300), () {
      if (!mounted) return;

      try {
        // التحقق من وجود البيانات الأساسية
        final stats = _statsProvider.stats;
        final hasBasicData = stats.totalCount >= 0 && stats.sessionCount >= 0;

        // التحقق من وجود بيانات الأذكار
        final hasDhikrData = stats.dhikrStats.isNotEmpty;

        // التحقق من وجود بيانات الأيام
        final hasDailyData = stats.dailyStats.isNotEmpty;

        if (mounted) {
          setState(() {
            _isScreenValidated = hasBasicData;
            _hasError = !(hasBasicData);
            _errorMessage = !hasBasicData
                ? 'لم يتم تحميل البيانات الأساسية بشكل صحيح'
                : (!hasDhikrData || !hasDailyData)
                    ? 'تم تحميل البيانات الأساسية، لكن بعض البيانات التفصيلية غير متوفرة'
                    : '';
            _isValidating = false;
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _hasError = true;
            _errorMessage = 'حدث خطأ أثناء التحقق من بناء الشاشة: $e';
            _isValidating = false;
          });
        }
      }
    });
  }

  // دالة لتحريك الأقسام بشكل متتالي
  void _animateSectionsSequentially() {
    // تأخير ظهور كل قسم بشكل متتالي
    for (int i = 0; i < _sectionsVisible.length; i++) {
      Future.delayed(Duration(milliseconds: 200 + (i * 150)), () {
        if (mounted) {
          setState(() {
            _sectionsVisible[i] = true;
          });
        }
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: TasbihColors.getBackgroundGradient(isDarkMode),
        ),
        child: SafeArea(
          child: Consumer<TasbihStatsProvider>(
            builder: (context, provider, child) {
              // عرض شاشة التحميل الفاخرة
              if (provider.isLoading || _isValidating) {
                return _buildLuxuryLoadingScreen(isDarkMode, size);
              }

              // عرض شاشة الخطأ إذا كان هناك خطأ
              if (_hasError) {
                return _buildErrorScreen(isDarkMode, size);
              }

              final stats = provider.stats;

              return Column(
                children: [
                  // شريط العنوان
                  AnimatedOpacity(
                    opacity: _sectionsVisible[0] ? 1.0 : 0.0,
                    duration: const Duration(milliseconds: 500),
                    curve: Curves.easeOutQuart,
                    child: AnimatedSlide(
                      offset: _sectionsVisible[0]
                          ? Offset.zero
                          : const Offset(0, -0.2),
                      duration: const Duration(milliseconds: 500),
                      curve: Curves.easeOutQuart,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            _buildAnimatedButton(
                              icon: Icons.arrow_back,
                              onTap: () => Navigator.pop(context),
                              color: TasbihColors.primary,
                            ),
                            Text(
                              'إحصائيات المسبحة',
                              style: theme.textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: TasbihColors.primary,
                              ),
                            ),
                            _buildAnimatedButton(
                              icon: Icons.share,
                              onTap: () => provider.shareStats(),
                              color: TasbihColors.primary,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // شريط التبويب
                  AnimatedOpacity(
                    opacity: _sectionsVisible[1] ? 1.0 : 0.0,
                    duration: const Duration(milliseconds: 500),
                    curve: Curves.easeOutQuart,
                    child: AnimatedSlide(
                      offset: _sectionsVisible[1]
                          ? Offset.zero
                          : const Offset(0, 0.2),
                      duration: const Duration(milliseconds: 500),
                      curve: Curves.easeOutQuart,
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 20),
                        decoration: BoxDecoration(
                          color: isDarkMode
                              ? TasbihColors.darkCardColor.withAlpha(150)
                              : Colors.white.withAlpha(200),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: TasbihColors.primary.withAlpha(20),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: TabBar(
                          controller: _tabController,
                          indicator: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            color: TasbihColors.primary,
                            boxShadow: [
                              BoxShadow(
                                color: TasbihColors.primary.withAlpha(50),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          labelColor: Colors.white,
                          unselectedLabelColor:
                              isDarkMode ? Colors.white70 : Colors.black54,
                          tabs: const [
                            Tab(text: 'الملخص'),
                            Tab(text: 'التفاصيل'),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // محتوى التبويب
                  Expanded(
                    child: AnimatedOpacity(
                      opacity: _sectionsVisible[2] ? 1.0 : 0.0,
                      duration: const Duration(milliseconds: 700),
                      curve: Curves.easeOutQuart,
                      child: TabBarView(
                        controller: _tabController,
                        children: [
                          // صفحة الملخص
                          _buildSummaryTab(
                              context, stats, isDarkMode, size, provider),

                          // صفحة التفاصيل
                          _buildDetailsTab(context, stats, isDarkMode),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  // بناء شاشة التحميل الفاخرة
  Widget _buildLuxuryLoadingScreen(bool isDarkMode, Size size) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // أيقونة متحركة
          TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: 0.0, end: 1.0),
            duration: const Duration(milliseconds: 1500),
            curve: Curves.elasticOut,
            builder: (context, value, child) {
              return Transform.scale(
                scale: value,
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    color: TasbihColors.primary.withAlpha(30),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: TasbihColors.primary.withAlpha(50),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: Center(
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        // دائرة متحركة
                        TweenAnimationBuilder<double>(
                          tween: Tween<double>(begin: 0.0, end: 2 * 3.14),
                          duration: const Duration(seconds: 2),
                          curve: Curves.linear,
                          builder: (context, value, child) {
                            return CustomPaint(
                              size: const Size(80, 80),
                              painter: LoadingArcPainter(
                                progress: value,
                                color: TasbihColors.primary,
                              ),
                            );
                          },
                        ),

                        // أيقونة الإحصائيات
                        const Icon(
                          Icons.bar_chart,
                          size: 40,
                          color: TasbihColors.primary,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),

          const SizedBox(height: 30),

          // نص التحميل
          TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: 0.0, end: 1.0),
            duration: const Duration(milliseconds: 800),
            curve: Curves.easeOutQuad,
            builder: (context, value, child) {
              return Opacity(
                opacity: value,
                child: Text(
                  'جاري التحقق من بناء الإحصائيات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
              );
            },
          ),

          const SizedBox(height: 10),

          // نص فرعي
          TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: 0.0, end: 1.0),
            duration: const Duration(milliseconds: 1000),
            curve: Curves.easeOutQuad,
            builder: (context, value, child) {
              return Opacity(
                opacity: value,
                child: Text(
                  'نعمل على تجهيز إحصائياتك بشكل مثالي',
                  style: TextStyle(
                    fontSize: 14,
                    color: isDarkMode ? Colors.white70 : Colors.black54,
                  ),
                  textAlign: TextAlign.center,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  // بناء شاشة الخطأ
  Widget _buildErrorScreen(bool isDarkMode, Size size) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة الخطأ
            TweenAnimationBuilder<double>(
              tween: Tween<double>(begin: 0.0, end: 1.0),
              duration: const Duration(milliseconds: 800),
              curve: Curves.elasticOut,
              builder: (context, value, child) {
                return Transform.scale(
                  scale: value,
                  child: Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      color: Colors.red.withAlpha(30),
                      shape: BoxShape.circle,
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.error_outline,
                        size: 50,
                        color: Colors.red,
                      ),
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 30),

            // عنوان الخطأ
            Text(
              'حدث خطأ أثناء تحميل الإحصائيات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 10),

            // رسالة الخطأ
            Text(
              _errorMessage,
              style: TextStyle(
                fontSize: 14,
                color: isDarkMode ? Colors.white70 : Colors.black54,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 30),

            // زر إعادة المحاولة
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _isValidating = true;
                  _hasError = false;
                });
                _statsProvider.initialize().then((_) {
                  _validateScreen();
                });
              },
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: TasbihColors.primary,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// بناء صفحة الملخص
Widget _buildSummaryTab(BuildContext context, TasbihStatsModel stats,
    bool isDarkMode, Size size, TasbihStatsProvider statsProvider) {
  // حساب معدل النمو للعرض في البطاقات
  final growthRate = stats.growthRate;

  // تحديد أحجام الشاشات المختلفة بشكل أكثر دقة
  final isVerySmallScreen = size.width < 320; // للشاشات الصغيرة جداً
  final isSmallScreen =
      size.width >= 320 && size.width < 360; // للشاشات الصغيرة
  final isMediumScreen =
      size.width >= 360 && size.width < 600; // للهواتف العادية
  final isLargeScreen = size.width >= 600; // للأجهزة اللوحية والشاشات الكبيرة

  // تحديد المسافات والأحجام بناءً على حجم الشاشة بشكل أكثر دقة
  final horizontalPadding = isVerySmallScreen
      ? size.width * 0.03
      : (isSmallScreen
          ? size.width * 0.035
          : (isMediumScreen ? size.width * 0.04 : size.width * 0.05));

  final cardSpacing = isVerySmallScreen
      ? size.width * 0.02
      : (isSmallScreen
          ? size.width * 0.025
          : (isMediumScreen ? size.width * 0.03 : size.width * 0.04));

  final sectionSpacing = isVerySmallScreen
      ? size.height * 0.015
      : (isSmallScreen
          ? size.height * 0.02
          : (isMediumScreen ? size.height * 0.025 : size.height * 0.03));

  // تحديد ارتفاع الرسوم البيانية بناءً على حجم الشاشة بشكل أكثر دقة
  final chartHeight = isVerySmallScreen
      ? size.height * 0.2
      : (isSmallScreen
          ? size.height * 0.22
          : (isMediumScreen ? size.height * 0.25 : size.height * 0.22));

  final pieChartHeight = isVerySmallScreen
      ? size.height * 0.25
      : (isSmallScreen
          ? size.height * 0.28
          : (isMediumScreen ? size.height * 0.3 : size.height * 0.25));

  return SingleChildScrollView(
    physics: const BouncingScrollPhysics(),
    padding: EdgeInsets.symmetric(
      horizontal: horizontalPadding,
      vertical: size.height * 0.02,
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // بطاقات الإحصائيات الرئيسية - تخطيط متجاوب
        if (isLargeScreen)
          // تخطيط للشاشات الكبيرة: 3 بطاقات في صف واحد
          Row(
            children: [
              Expanded(
                child: StatsCard(
                  title: 'المجموع',
                  value: '${stats.totalCount}',
                  icon: Icons.all_inclusive,
                  color: TasbihColors.primary,
                  showTrend: stats.totalCount > 0 && growthRate != 0,
                  trendValue: growthRate,
                  useCircularIcon: true,
                ),
              ),
              SizedBox(width: cardSpacing),
              Expanded(
                child: StatsCard(
                  title: 'الدورات',
                  value: '${stats.sessionCount}',
                  icon: Icons.loop,
                  color: TasbihColors.secondary,
                  subtitle: 'إجمالي الدورات المكتملة',
                  useCircularIcon: true,
                ),
              ),
              SizedBox(width: cardSpacing),
              Expanded(
                child: StatsCard(
                  title: 'الذكر الأكثر استخداماً',
                  value: stats.mostUsedDhikr.name,
                  icon: Icons.favorite,
                  color: TasbihColors.tertiary,
                  subtitle: 'تم تكراره ${stats.mostUsedDhikr.count} مرة',
                  useCircularIcon: true,
                ),
              ),
            ],
          )
        else
          // تخطيط للشاشات الصغيرة: 2 بطاقات في صف ثم بطاقة منفصلة
          Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: StatsCard(
                      title: 'المجموع',
                      value: '${stats.totalCount}',
                      icon: Icons.all_inclusive,
                      color: TasbihColors.primary,
                      showTrend: stats.totalCount > 0 && growthRate != 0,
                      trendValue: growthRate,
                      useCircularIcon: true,
                    ),
                  ),
                  SizedBox(width: cardSpacing),
                  Expanded(
                    child: StatsCard(
                      title: 'الدورات',
                      value: '${stats.sessionCount}',
                      icon: Icons.loop,
                      color: TasbihColors.secondary,
                      subtitle: 'إجمالي الدورات المكتملة',
                      useCircularIcon: true,
                    ),
                  ),
                ],
              ),
              SizedBox(height: size.height * 0.015),
              StatsCard(
                title: 'الذكر الأكثر استخداماً',
                value: stats.mostUsedDhikr.name,
                icon: Icons.favorite,
                color: TasbihColors.tertiary,
                subtitle: 'تم تكراره ${stats.mostUsedDhikr.count} مرة',
                useCircularIcon: true,
              ),
            ],
          ),

        SizedBox(height: sectionSpacing),

        // عنوان الرسم البياني
        const StatsSectionTitle(
          title: 'نشاط الأسبوع الماضي',
          icon: Icons.insert_chart_outlined,
        ),

        SizedBox(height: size.height * 0.01),

        // الرسم البياني - متجاوب مع حجم الشاشة
        Container(
          height: chartHeight,
          padding: EdgeInsets.all(size.width * 0.03),
          decoration: BoxDecoration(
            color: isDarkMode ? TasbihColors.darkCardColor : Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(isDarkMode ? 60 : 20),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ActivityChart(
            dailyStats: stats.dailyStats,
            height: chartHeight -
                (size.width * 0.06), // تعديل الارتفاع مع مراعاة الـ padding
            daysToShow: 7,
          ),
        ),

        SizedBox(height: sectionSpacing),

        // عنوان توزيع الأذكار
        const StatsSectionTitle(
          title: 'توزيع الأذكار',
          icon: Icons.pie_chart,
        ),

        SizedBox(height: size.height * 0.01),

        // رسم بياني دائري لتوزيع الأذكار - متجاوب مع حجم الشاشة
        Container(
          height: pieChartHeight,
          padding: EdgeInsets.all(size.width * 0.03),
          decoration: BoxDecoration(
            color: isDarkMode ? TasbihColors.darkCardColor : Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(isDarkMode ? 60 : 20),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: DhikrPieChart(
            dhikrStats: stats.dhikrStats,
            size: pieChartHeight -
                (size.width * 0.06), // تعديل الحجم مع مراعاة الـ padding
          ),
        ),

        SizedBox(height: sectionSpacing),

        // بطاقات إضافية - تخطيط متجاوب
        if (isLargeScreen)
          // تخطيط للشاشات الكبيرة: 4 بطاقات في صف واحد
          Row(
            children: [
              Expanded(
                child: StatsCard(
                  title: 'السلسلة الحالية',
                  value: '${stats.currentStreak} يوم',
                  icon: Icons.local_fire_department,
                  color: Colors.orange,
                  subtitle: 'استمر للحصول على المزيد!',
                ),
              ),
              SizedBox(width: cardSpacing),
              Expanded(
                child: StatsCard(
                  title: 'أطول سلسلة',
                  value: '${stats.longestStreak} يوم',
                  icon: Icons.emoji_events,
                  color: Colors.amber,
                  subtitle: 'أفضل إنجاز لك',
                ),
              ),
              SizedBox(width: cardSpacing),
              Expanded(
                child: StatsCard(
                  title: 'المتوسط اليومي',
                  value: '${stats.averageDailyCount}',
                  icon: Icons.trending_up,
                  color: Colors.teal,
                  subtitle: 'تسبيحة في اليوم',
                ),
              ),
              SizedBox(width: cardSpacing),
              Expanded(
                child: StatsCard(
                  title: 'معدل الساعة',
                  value: stats.hourlyRate.toStringAsFixed(1),
                  icon: Icons.speed,
                  color: Colors.indigo,
                  subtitle: 'تسبيحة في الساعة',
                ),
              ),
            ],
          )
        else
          // تخطيط للشاشات الصغيرة: صفين من البطاقات
          Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: StatsCard(
                      title: 'السلسلة الحالية',
                      value: '${stats.currentStreak} يوم',
                      icon: Icons.local_fire_department,
                      color: Colors.orange,
                      subtitle: 'استمر للحصول على المزيد!',
                    ),
                  ),
                  SizedBox(width: cardSpacing),
                  Expanded(
                    child: StatsCard(
                      title: 'أطول سلسلة',
                      value: '${stats.longestStreak} يوم',
                      icon: Icons.emoji_events,
                      color: Colors.amber,
                      subtitle: 'أفضل إنجاز لك',
                    ),
                  ),
                ],
              ),
              SizedBox(height: size.height * 0.015),
              Row(
                children: [
                  Expanded(
                    child: StatsCard(
                      title: 'المتوسط اليومي',
                      value: '${stats.averageDailyCount}',
                      icon: Icons.trending_up,
                      color: Colors.teal,
                      subtitle: 'تسبيحة في اليوم',
                    ),
                  ),
                  SizedBox(width: cardSpacing),
                  Expanded(
                    child: StatsCard(
                      title: 'معدل الساعة',
                      value: stats.hourlyRate.toStringAsFixed(1),
                      icon: Icons.speed,
                      color: Colors.indigo,
                      subtitle: 'تسبيحة في الساعة',
                    ),
                  ),
                ],
              ),
            ],
          ),

        SizedBox(height: sectionSpacing),

        // عنوان الإنجازات
        const StatsSectionTitle(
          title: 'الإنجازات',
          icon: Icons.emoji_events_outlined,
        ),

        SizedBox(height: size.height * 0.01),

        // قائمة الإنجازات - تخطيط متجاوب
        if (isLargeScreen && stats.achievements.isNotEmpty)
          // تخطيط للشاشات الكبيرة: عرض الإنجازات في صفوف متعددة
          Wrap(
            spacing: cardSpacing,
            runSpacing: size.height * 0.015,
            children: stats.achievements.map((achievement) {
              return SizedBox(
                width:
                    (size.width - (horizontalPadding * 2) - (cardSpacing * 2)) /
                        3,
                child: AchievementCard(
                  achievement: achievement,
                  onTap: () {
                    // يمكن إضافة تفاصيل الإنجاز هنا
                  },
                ),
              );
            }).toList(),
          )
        else if (stats.achievements.isNotEmpty)
          // تخطيط للشاشات الصغيرة: عرض الإنجازات في عمود
          Column(
            children: stats.achievements
                .map((achievement) => Padding(
                      padding: EdgeInsets.only(bottom: size.height * 0.012),
                      child: AchievementCard(
                        achievement: achievement,
                        onTap: () {
                          // يمكن إضافة تفاصيل الإنجاز هنا
                        },
                      ),
                    ))
                .toList(),
          )
        // إذا لم تكن هناك إنجازات، عرض رسالة
        else
          Container(
            padding: EdgeInsets.all(size.width * 0.04),
            decoration: BoxDecoration(
              color: isDarkMode ? TasbihColors.darkCardColor : Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(isDarkMode ? 60 : 20),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Center(
              child: Column(
                children: [
                  Icon(
                    Icons.emoji_events_outlined,
                    size: size.width * 0.12,
                    color: Colors.grey[400],
                  ),
                  SizedBox(height: size.height * 0.02),
                  Text(
                    'استمر في استخدام المسبحة للحصول على إنجازات!',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: size.width * 0.04,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),

        SizedBox(height: sectionSpacing),

        // زر مشاركة الإحصائيات - متجاوب مع حجم الشاشة
        SizedBox(
          width: double.infinity,
          height: size.height * 0.06,
          child: ElevatedButton.icon(
            onPressed: () => _shareStats(statsProvider),
            icon: Icon(Icons.share, size: size.width * 0.05),
            label: Text(
              'مشاركة الإحصائيات',
              style: TextStyle(
                fontSize: size.width * 0.04,
                fontWeight: FontWeight.bold,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: TasbihColors.primary,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(
                horizontal: size.width * 0.04,
                vertical: size.height * 0.012,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    ),
  );
}

// بناء زر متحرك
Widget _buildAnimatedButton({
  required IconData icon,
  required VoidCallback onTap,
  required Color color,
}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: color.withAlpha(26),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: color.withAlpha(13),
            offset: const Offset(0, 1),
            blurRadius: 3,
          ),
        ],
      ),
      child: Icon(
        icon,
        color: color,
        size: 24,
      ),
    ),
  );
}

// تنسيق التاريخ
String _formatDate(DateTime date) {
  final now = DateTime.now();
  final difference = now.difference(date).inDays;

  if (difference == 0) {
    return 'اليوم';
  } else if (difference == 1) {
    return 'الأمس';
  } else if (difference < 7) {
    return 'منذ $difference أيام';
  } else {
    return '${date.day}/${date.month}/${date.year}';
  }
}

// بناء صفحة التفاصيل
Widget _buildDetailsTab(
    BuildContext context, TasbihStatsModel stats, bool isDarkMode) {
  final size = MediaQuery.of(context).size;

  // تحديد أحجام الشاشات المختلفة بشكل أكثر دقة
  final isVerySmallScreen = size.width < 320; // للشاشات الصغيرة جداً
  final isSmallScreen =
      size.width >= 320 && size.width < 360; // للشاشات الصغيرة
  final isMediumScreen =
      size.width >= 360 && size.width < 600; // للهواتف العادية
  final isLargeScreen = size.width >= 600; // للأجهزة اللوحية والشاشات الكبيرة

  // تحديد المسافات والأحجام بناءً على حجم الشاشة بشكل أكثر دقة
  final horizontalPadding = isVerySmallScreen
      ? size.width * 0.03
      : (isSmallScreen
          ? size.width * 0.035
          : (isMediumScreen ? size.width * 0.04 : size.width * 0.05));

  final cardSpacing = isVerySmallScreen
      ? size.width * 0.02
      : (isSmallScreen
          ? size.width * 0.025
          : (isMediumScreen ? size.width * 0.03 : size.width * 0.04));

  final sectionSpacing = isVerySmallScreen
      ? size.height * 0.015
      : (isSmallScreen
          ? size.height * 0.02
          : (isMediumScreen ? size.height * 0.025 : size.height * 0.03));

  final itemPadding = isVerySmallScreen
      ? size.width * 0.02
      : (isSmallScreen
          ? size.width * 0.025
          : (isMediumScreen ? size.width * 0.03 : size.width * 0.04));

  return SingleChildScrollView(
    physics: const BouncingScrollPhysics(),
    padding: EdgeInsets.symmetric(
      horizontal: horizontalPadding,
      vertical: size.height * 0.02,
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان قسم الأذكار
        const StatsSectionTitle(
          title: 'إحصائيات الأذكار',
          icon: Icons.format_list_bulleted,
        ),

        SizedBox(height: size.height * 0.01),

        // قائمة الأذكار مع نسبها - متجاوبة مع حجم الشاشة
        Container(
          padding: EdgeInsets.all(itemPadding),
          decoration: BoxDecoration(
            color: isDarkMode ? TasbihColors.darkCardColor : Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(isDarkMode ? 60 : 20),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: isLargeScreen && stats.dhikrStats.length > 1
              // تخطيط للشاشات الكبيرة: عرض الأذكار في عمودين
              ? Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        children: stats.dhikrStats
                            .sublist(0, (stats.dhikrStats.length / 2).ceil())
                            .map((dhikr) => Padding(
                                  padding: EdgeInsets.only(
                                      bottom: size.height * 0.012),
                                  child: StatsProgressIndicator(
                                    title: dhikr.name,
                                    value: '${dhikr.count}',
                                    progress: dhikr.percentage / 100,
                                    color: TasbihColors.primary,
                                  ),
                                ))
                            .toList(),
                      ),
                    ),
                    SizedBox(width: cardSpacing),
                    Expanded(
                      child: Column(
                        children: stats.dhikrStats
                            .sublist((stats.dhikrStats.length / 2).ceil())
                            .map((dhikr) => Padding(
                                  padding: EdgeInsets.only(
                                      bottom: size.height * 0.012),
                                  child: StatsProgressIndicator(
                                    title: dhikr.name,
                                    value: '${dhikr.count}',
                                    progress: dhikr.percentage / 100,
                                    color: TasbihColors.primary,
                                  ),
                                ))
                            .toList(),
                      ),
                    ),
                  ],
                )
              // تخطيط للشاشات الصغيرة أو عندما يكون هناك ذكر واحد فقط
              : Column(
                  children: [
                    ...stats.dhikrStats.map((dhikr) => Padding(
                          padding: EdgeInsets.only(bottom: size.height * 0.012),
                          child: StatsProgressIndicator(
                            title: dhikr.name,
                            value: '${dhikr.count}',
                            progress: dhikr.percentage / 100,
                            color: TasbihColors.primary,
                          ),
                        )),
                    if (stats.dhikrStats.isEmpty)
                      Padding(
                        padding:
                            EdgeInsets.symmetric(vertical: size.height * 0.02),
                        child: Center(
                          child: Text(
                            'لم يتم تسجيل أي أذكار بعد',
                            style: TextStyle(
                              fontSize: size.width * 0.04,
                              color: Colors.grey[600],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
        ),

        SizedBox(height: sectionSpacing),

        // عنوان قسم الإحصائيات العامة
        const StatsSectionTitle(
          title: 'إحصائيات عامة',
          icon: Icons.insights,
        ),

        SizedBox(height: size.height * 0.01),

        // بطاقات الإحصائيات العامة - متجاوبة مع حجم الشاشة
        Container(
          padding: EdgeInsets.all(itemPadding),
          decoration: BoxDecoration(
            color: isDarkMode ? TasbihColors.darkCardColor : Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(isDarkMode ? 60 : 20),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: isLargeScreen
              // تخطيط للشاشات الكبيرة: عرض الإحصائيات في صفوف متعددة
              ? Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: _buildDetailItem(
                            context,
                            icon: Icons.trending_up,
                            title: 'المتوسط اليومي',
                            value: '${stats.averageDailyCount} تسبيحة',
                            color: Colors.teal,
                          ),
                        ),
                        SizedBox(width: cardSpacing),
                        Expanded(
                          child: _buildDetailItem(
                            context,
                            icon: Icons.speed,
                            title: 'معدل الساعة',
                            value:
                                '${stats.hourlyRate.toStringAsFixed(1)} تسبيحة',
                            color: Colors.indigo,
                          ),
                        ),
                      ],
                    ),
                    Divider(height: size.height * 0.03),
                    Row(
                      children: [
                        Expanded(
                          child: _buildDetailItem(
                            context,
                            icon: Icons.timer,
                            title: 'إجمالي وقت الاستخدام',
                            value: _formatTime(stats.totalUsageTime),
                            color: Colors.purple,
                          ),
                        ),
                        SizedBox(width: cardSpacing),
                        Expanded(
                          child: _buildDetailItem(
                            context,
                            icon: Icons.calendar_today,
                            title: 'الأيام النشطة',
                            value: '${stats.activeDaysCount} يوم',
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ),
                    if (stats.bestDay != null) ...[
                      Divider(height: size.height * 0.03),
                      Row(
                        children: [
                          Expanded(
                            child: _buildDetailItem(
                              context,
                              icon: Icons.emoji_events,
                              title: 'أفضل يوم',
                              value:
                                  '${stats.bestDay!.dayName} (${stats.bestDay!.count} تسبيحة)',
                              color: Colors.amber,
                            ),
                          ),
                          SizedBox(width: cardSpacing),
                          Expanded(
                            child: _buildDetailItem(
                              context,
                              icon: Icons.access_time,
                              title: 'آخر استخدام',
                              value: _formatDate(stats.lastUsedDate),
                              color: Colors.green,
                            ),
                          ),
                        ],
                      ),
                    ] else ...[
                      Divider(height: size.height * 0.03),
                      _buildDetailItem(
                        context,
                        icon: Icons.access_time,
                        title: 'آخر استخدام',
                        value: _formatDate(stats.lastUsedDate),
                        color: Colors.green,
                      ),
                    ],
                  ],
                )
              // تخطيط للشاشات الصغيرة: عرض الإحصائيات في عمود
              : Column(
                  children: [
                    // بطاقة متوسط التسبيحات اليومية
                    _buildDetailItem(
                      context,
                      icon: Icons.trending_up,
                      title: 'المتوسط اليومي',
                      value: '${stats.averageDailyCount} تسبيحة',
                      color: Colors.teal,
                    ),

                    Divider(height: size.height * 0.03),

                    // بطاقة معدل الساعة
                    _buildDetailItem(
                      context,
                      icon: Icons.speed,
                      title: 'معدل الساعة',
                      value: '${stats.hourlyRate.toStringAsFixed(1)} تسبيحة',
                      color: Colors.indigo,
                    ),

                    Divider(height: size.height * 0.03),

                    // بطاقة إجمالي وقت الاستخدام
                    _buildDetailItem(
                      context,
                      icon: Icons.timer,
                      title: 'إجمالي وقت الاستخدام',
                      value: _formatTime(stats.totalUsageTime),
                      color: Colors.purple,
                    ),

                    Divider(height: size.height * 0.03),

                    // بطاقة عدد الأيام النشطة
                    _buildDetailItem(
                      context,
                      icon: Icons.calendar_today,
                      title: 'الأيام النشطة',
                      value: '${stats.activeDaysCount} يوم',
                      color: Colors.blue,
                    ),

                    // بطاقة أفضل يوم
                    if (stats.bestDay != null) ...[
                      Divider(height: size.height * 0.03),
                      _buildDetailItem(
                        context,
                        icon: Icons.emoji_events,
                        title: 'أفضل يوم',
                        value:
                            '${stats.bestDay!.dayName} (${stats.bestDay!.count} تسبيحة)',
                        color: Colors.amber,
                      ),
                    ],

                    Divider(height: size.height * 0.03),

                    // بطاقة تاريخ آخر استخدام
                    _buildDetailItem(
                      context,
                      icon: Icons.access_time,
                      title: 'آخر استخدام',
                      value: _formatDate(stats.lastUsedDate),
                      color: Colors.green,
                    ),
                  ],
                ),
        ),

        SizedBox(height: sectionSpacing),

        // عنوان قسم السلاسل
        const StatsSectionTitle(
          title: 'سلاسل الاستخدام',
          icon: Icons.local_fire_department,
        ),

        SizedBox(height: size.height * 0.01),

        // بطاقات السلاسل - متجاوبة مع حجم الشاشة
        Row(
          children: [
            Expanded(
              child: StatsCard(
                title: 'السلسلة الحالية',
                value: '${stats.currentStreak}',
                icon: Icons.local_fire_department,
                color: Colors.orange,
                subtitle: 'يوم متتالي',
                useCircularIcon: true,
              ),
            ),
            SizedBox(width: cardSpacing),
            Expanded(
              child: StatsCard(
                title: 'أطول سلسلة',
                value: '${stats.longestStreak}',
                icon: Icons.emoji_events,
                color: Colors.amber,
                subtitle: 'يوم متتالي',
                useCircularIcon: true,
              ),
            ),
          ],
        ),

        SizedBox(height: sectionSpacing),

        // زر إعادة ضبط الإحصائيات - متجاوب مع حجم الشاشة
        SizedBox(
          width: double.infinity,
          height: size.height * 0.06,
          child: ElevatedButton.icon(
            onPressed: () {
              final provider =
                  Provider.of<TasbihStatsProvider>(context, listen: false);
              _showResetConfirmationDialog(context, provider);
            },
            icon: Icon(Icons.refresh, size: size.width * 0.05),
            label: Text(
              'إعادة ضبط الإحصائيات',
              style: TextStyle(
                fontSize: size.width * 0.04,
                fontWeight: FontWeight.bold,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.redAccent,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(
                vertical: size.height * 0.012,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    ),
  );
}

// بناء عنصر تفاصيل - متجاوب مع حجم الشاشة
Widget _buildDetailItem(
  BuildContext context, {
  required IconData icon,
  required String title,
  required String value,
  required Color color,
}) {
  final theme = Theme.of(context);
  final isDarkMode = theme.brightness == Brightness.dark;
  final textColor = isDarkMode ? Colors.white : Colors.black87;
  final size = MediaQuery.of(context).size;

  // تحديد أحجام الشاشات المختلفة بشكل أكثر دقة
  final isVerySmallScreen = size.width < 320; // للشاشات الصغيرة جداً
  final isSmallScreen =
      size.width >= 320 && size.width < 360; // للشاشات الصغيرة
  final isMediumScreen =
      size.width >= 360 && size.width < 600; // للهواتف العادية
  final isLargeScreen = size.width >= 600; // للأجهزة اللوحية والشاشات الكبيرة

  // تحديد أحجام العناصر بناءً على حجم الشاشة بشكل أكثر دقة
  final iconSize = isVerySmallScreen
      ? size.width * 0.04
      : (isSmallScreen
          ? size.width * 0.045
          : (isMediumScreen ? size.width * 0.05 : size.width * 0.055));

  final iconPadding = isVerySmallScreen
      ? size.width * 0.02
      : (isSmallScreen
          ? size.width * 0.022
          : (isMediumScreen ? size.width * 0.025 : size.width * 0.03));

  final spacingWidth = isVerySmallScreen
      ? size.width * 0.02
      : (isSmallScreen
          ? size.width * 0.025
          : (isMediumScreen ? size.width * 0.03 : size.width * 0.035));

  final titleFontSize = isVerySmallScreen
      ? size.width * 0.03
      : (isSmallScreen
          ? size.width * 0.032
          : (isMediumScreen ? size.width * 0.035 : size.width * 0.04));

  // استخدام isLargeScreen لتحديد حجم الخط بشكل أكثر دقة
  final valueFontSize = isVerySmallScreen
      ? size.width * 0.035
      : (isSmallScreen
          ? size.width * 0.038
          : (isMediumScreen
              ? size.width * 0.04
              : (isLargeScreen ? size.width * 0.045 : size.width * 0.042)));

  final verticalSpacing = isVerySmallScreen
      ? size.height * 0.003
      : (isSmallScreen
          ? size.height * 0.004
          : (isMediumScreen ? size.height * 0.005 : size.height * 0.006));

  return Row(
    children: [
      // أيقونة العنصر - متجاوبة مع حجم الشاشة
      Container(
        padding: EdgeInsets.all(iconPadding),
        decoration: BoxDecoration(
          color: color.withAlpha(25),
          shape: BoxShape.circle,
        ),
        child: Icon(
          icon,
          color: color,
          size: iconSize,
        ),
      ),
      SizedBox(width: spacingWidth),

      // المحتوى - متجاوب مع حجم الشاشة
      Expanded(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: titleFontSize,
                color: textColor.withAlpha(178),
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: verticalSpacing),
            Text(
              value,
              style: TextStyle(
                fontSize: valueFontSize,
                fontWeight: FontWeight.bold,
                color: textColor,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    ],
  );
}

// تنسيق الوقت (بالدقائق) إلى ساعات ودقائق
String _formatTime(int minutes) {
  final hours = minutes ~/ 60;
  final remainingMinutes = minutes % 60;

  if (hours > 0) {
    return '$hours ساعة و $remainingMinutes دقيقة';
  } else {
    return '$remainingMinutes دقيقة';
  }
}

// عرض حوار تأكيد إعادة الضبط
Future<void> _showResetConfirmationDialog(
    BuildContext context, TasbihStatsProvider statsProvider) async {
  // تخزين مرجع ScaffoldMessenger قبل العمليات غير المتزامنة
  final scaffoldMessenger = ScaffoldMessenger.of(context);

  final confirmed = await showDialog<bool>(
    context: context,
    builder: (dialogContext) => AlertDialog(
      title: const Text('إعادة ضبط الإحصائيات'),
      content: const Text(
          'هل أنت متأكد من رغبتك في إعادة ضبط جميع إحصائيات المسبحة؟ لا يمكن التراجع عن هذا الإجراء.'),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(dialogContext, false),
          child: const Text('إلغاء'),
        ),
        TextButton(
          onPressed: () => Navigator.pop(dialogContext, true),
          style: TextButton.styleFrom(
            foregroundColor: Colors.red,
          ),
          child: const Text('إعادة الضبط'),
        ),
      ],
    ),
  );

  if (confirmed == true) {
    // إعادة ضبط الإحصائيات
    await statsProvider.resetStats();

    // استخدام ScaffoldMessenger المخزن مسبقًا بدلاً من استخدام BuildContext بعد العمليات غير المتزامنة
    scaffoldMessenger.showSnackBar(
      const SnackBar(
        content: Text('تمت إعادة ضبط الإحصائيات'),
        backgroundColor: Colors.red,
      ),
    );
  }
}

// مشاركة الإحصائيات
void _shareStats(TasbihStatsProvider statsProvider) {
  HapticFeedback.lightImpact();
  statsProvider.shareStats();
}
