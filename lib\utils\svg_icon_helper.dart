import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// مساعد لمعالجة أيقونات SVG في التطبيق
/// يدعم تحميل الأيقونات من مسارات SVG مع الحفاظ على التوافق مع النظام القديم
class SvgIconHelper {
  /// الحصول على ويدجت أيقونة مناسبة للفئة
  /// يدعم كل من أيقونات SVG والأيقونات التقليدية مع تحجيم احترافي
  static Widget getIconWidget({
    required String categoryName,
    String? iconPath,
    String? iconName,
    required double size,
    required Color color,
    String section = 'general',
    BoxFit fit = BoxFit.contain,
    bool enableShadow = false,
    double? shadowBlurRadius,
    Color? shadowColor,
  }) {
    Widget iconWidget;

    // إذا كان هناك مسار SVG، استخدمه
    if (iconPath != null && iconPath.isNotEmpty && isValidSvgPath(iconPath)) {
      iconWidget = _buildSvgIcon(
        iconPath: iconPath,
        size: size,
        color: color,
      );
    } else {
      // إذا لم يكن هناك مسار SVG صحيح، جرب الحصول على مسار افتراضي
      final defaultPath = getDefaultSvgPath(categoryName, section);
      if (isValidSvgPath(defaultPath)) {
        iconWidget = _buildSvgIcon(
          iconPath: defaultPath,
          size: size,
          color: color,
        );
      } else {
        // استخدم النظام القديم كحل أخير
        iconWidget = _buildMaterialIcon(
          categoryName: categoryName,
          iconName: iconName,
          size: size,
          color: color,
        );
      }
    }

    // إضافة ظل إذا كان مطلوباً
    if (enableShadow) {
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: shadowColor ?? color.withValues(alpha: 0.3),
              blurRadius: shadowBlurRadius ?? size * 0.1,
              offset: Offset(0, size * 0.05),
            ),
          ],
        ),
        child: iconWidget,
      );
    }

    return iconWidget;
  }

  /// بناء أيقونة SVG مع تحجيم احترافي
  static Widget _buildSvgIcon({
    required String iconPath,
    required double size,
    required Color color,
  }) {
    return SizedBox(
      width: size,
      height: size,
      child: SvgPicture.asset(
        iconPath,
        width: size,
        height: size,
        fit: BoxFit.contain, // ضمان التحجيم الصحيح
        colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
        placeholderBuilder: (BuildContext context) => Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(size * 0.1),
          ),
          child: Icon(
            Icons.image_outlined,
            size: size * 0.6,
            color: color.withValues(alpha: 0.5),
          ),
        ),
        // معالجة الأخطاء
        errorBuilder: (context, error, stackTrace) => Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(size * 0.1),
          ),
          child: Icon(
            Icons.broken_image_outlined,
            size: size * 0.6,
            color: color.withValues(alpha: 0.5),
          ),
        ),
      ),
    );
  }

  /// بناء أيقونة Material (النظام القديم)
  static Widget _buildMaterialIcon({
    required String categoryName,
    String? iconName,
    required double size,
    required Color color,
  }) {
    IconData iconData;

    if (iconName != null) {
      iconData = _mapStringToIcon(iconName);
    } else {
      iconData = _getIconForCategoryName(categoryName);
    }

    return Icon(
      iconData,
      size: size,
      color: color,
    );
  }

  /// تحويل اسم الأيقونة إلى IconData (من النظام القديم)
  static IconData _mapStringToIcon(String iconName) {
    // خريطة الأيقونات المعروفة - جميع الأيقونات ثابتة
    switch (iconName) {
      // أيقونات الأذكار
      case 'wb_sunny_outlined':
        return Icons.wb_sunny_outlined;
      case 'nights_stay_outlined':
        return Icons.nights_stay_outlined;
      case 'hotel_outlined':
        return Icons.hotel_outlined;
      case 'account_balance_outlined':
        return Icons.account_balance_outlined;
      case 'mosque_outlined':
        return Icons.mosque_outlined;
      case 'restaurant_outlined':
        return Icons.restaurant_outlined;
      case 'brightness_2_outlined':
        return Icons.brightness_2_outlined;
      case 'wb_sunny':
        return Icons.wb_sunny;
      case 'wb_twilight':
        return Icons.wb_twilight;
      case 'spa':
        return Icons.spa;
      case 'nightlight_round':
        return Icons.nightlight_round;

      // أيقونات الدعاء
      case 'menu_book':
        return Icons.menu_book;
      case 'auto_awesome':
        return Icons.auto_awesome;
      case 'category_outlined':
        return Icons.category_outlined;
      case 'event':
        return Icons.event;

      // أيقونات صيغ الصلاة على النبي
      case 'school':
        return Icons.school;
      case 'star':
        return Icons.star;
      case 'favorite':
        return Icons.favorite;

      // أيقونات عامة
      case 'format_quote':
        return Icons.format_quote;
      case 'emoji_events':
        return Icons.emoji_events;
      case 'edit':
        return Icons.edit;

      // أيقونات افتراضية
      default:
        return Icons.format_quote;
    }
  }

  /// الحصول على أيقونة بناءً على اسم الفئة (من النظام القديم)
  static IconData _getIconForCategoryName(String categoryName) {
    if (categoryName.contains('الصباح')) {
      return Icons.wb_sunny_outlined;
    } else if (categoryName.contains('المساء')) {
      return Icons.nights_stay_outlined;
    } else if (categoryName.contains('النوم')) {
      return Icons.hotel_outlined;
    } else if (categoryName.contains('المسجد')) {
      return Icons.account_balance_outlined;
    } else if (categoryName.contains('الصلاة')) {
      return Icons.mosque_outlined;
    } else if (categoryName.contains('الطعام')) {
      return Icons.restaurant_outlined;
    } else if (categoryName.contains('الفجر')) {
      return Icons.brightness_2_outlined;
    } else if (categoryName.contains('الظهر')) {
      return Icons.wb_sunny;
    } else if (categoryName.contains('العصر')) {
      return Icons.wb_twilight;
    } else if (categoryName.contains('المغرب')) {
      return Icons.nights_stay_outlined;
    } else if (categoryName.contains('العشاء')) {
      return Icons.nights_stay_outlined;
    } else if (categoryName.contains('القرآن')) {
      return Icons.menu_book;
    } else if (categoryName.contains('الدعاء')) {
      return Icons.auto_awesome;
    } else if (categoryName.contains('الصلاة على النبي')) {
      return Icons.star;
    } else {
      return Icons.format_quote;
    }
  }

  /// التحقق من صحة مسار SVG
  static bool isValidSvgPath(String? path) {
    return path != null &&
        path.isNotEmpty &&
        path.endsWith('.svg') &&
        path.startsWith('assets/icons/');
  }

  /// الحصول على مسار SVG افتراضي للفئة
  static String getDefaultSvgPath(String categoryName, String section) {
    String fileName = 'general'; // افتراضي

    if (section == 'azkar') {
      if (categoryName.contains('الصباح')) {
        fileName = 'morning';
      } else if (categoryName.contains('المساء')) {
        fileName = 'evening';
      } else if (categoryName.contains('النوم')) {
        fileName = 'sleep';
      } else if (categoryName.contains('الصلاة')) {
        fileName = 'prayer';
      } else if (categoryName.contains('المسجد')) {
        fileName = 'mosque';
      } else if (categoryName.contains('الطعام')) {
        fileName = 'food';
      } else if (categoryName.contains('الوضوء')) {
        fileName = 'wudu';
      } else if (categoryName.contains('السفر')) {
        fileName = 'travel';
      } else if (categoryName.contains('الذكر') ||
          categoryName.contains('التسبيح')) {
        fileName = 'dhikr';
      } else {
        fileName = 'general';
      }
    } else if (section == 'duas') {
      if (categoryName.contains('القرآن')) {
        fileName = 'quran';
      } else if (categoryName.contains('الحماية') ||
          categoryName.contains('الحفظ')) {
        fileName = 'protection';
      } else if (categoryName.contains('الاستغفار') ||
          categoryName.contains('التوبة')) {
        fileName = 'forgiveness';
      } else {
        fileName = 'general';
      }
    } else if (section == 'prophet_prayers') {
      if (categoryName.contains('المأثورة') ||
          categoryName.contains('التقليدية')) {
        fileName = 'traditional';
      } else if (categoryName.contains('العلماء')) {
        fileName = 'scholars';
      } else {
        fileName = 'general';
      }
    }

    return 'assets/icons/$section/$fileName.svg';
  }
}
