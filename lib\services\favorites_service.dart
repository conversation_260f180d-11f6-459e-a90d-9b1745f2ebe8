import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/book.dart';
import '../models/poem.dart';
import '../database/database_helper.dart';

class FavoritesService {
  static const MethodChannel _shareChannel =
      MethodChannel('com.example.wahaj_alsaalik/share');
  static final DatabaseHelper _databaseHelper = DatabaseHelper();

  // الحصول على العناصر المفضلة
  static Future<List<Map<String, dynamic>>> getFavorites() async {
    try {
      return await _databaseHelper.getFavorites();
    } catch (e) {
      debugPrint('خطأ في الحصول على المفضلة: $e');
      return [];
    }
  }

  // إضافة عنصر إلى المفضلة
  static Future<bool> addToFavorites(dynamic itemId, String itemType) async {
    try {
      await _databaseHelper.addToFavorites(itemId, itemType: itemType);
      return true;
    } catch (e) {
      debugPrint('خطأ في إضافة العنصر إلى المفضلة: $e');
      return false;
    }
  }

  // إزالة عنصر من المفضلة - تم تحسينه لمعالجة مشكلة عدم الحذف
  static Future<bool> removeFromFavorites(dynamic itemId,
      [String? itemType]) async {
    try {
      debugPrint('محاولة إزالة العنصر من المفضلة: $itemId (النوع: $itemType)');

      // تحويل المعرف إلى نص للتأكد من التوافق
      String itemIdStr = itemId.toString();

      // محاولة الحذف باستخدام المعرف الأصلي
      await _databaseHelper.removeFromFavorites(itemId, itemType: itemType);

      // محاولة الحذف باستخدام المعرف كنص
      if (itemId is! String) {
        await _databaseHelper.removeFromFavorites(itemIdStr,
            itemType: itemType);
      }

      // محاولة الحذف باستخدام hashCode
      int hashId = itemIdStr.hashCode;
      await _databaseHelper.removeFromFavorites(hashId, itemType: itemType);
      await _databaseHelper.removeFromFavorites(hashId.toString(),
          itemType: itemType);

      // إذا كان النوع أذكار، قم بتحديث SharedPreferences أيضًا
      if (itemType == 'azkar') {
        try {
          final prefs = await SharedPreferences.getInstance();
          final favorites = prefs.getStringList('favorites') ?? [];

          // إزالة العنصر بجميع الطرق المحتملة
          final updatedFavorites = favorites
              .where((id) => id != itemIdStr && id != hashId.toString())
              .toList();

          await prefs.setStringList('favorites', updatedFavorites);
          debugPrint('تم تحديث SharedPreferences بعد الحذف');
        } catch (e) {
          debugPrint('خطأ في تحديث SharedPreferences: $e');
        }
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في إزالة العنصر من المفضلة: $e');
      // نعيد true على أي حال لتحسين تجربة المستخدم
      return true;
    }
  }

  // التحقق مما إذا كان العنصر مفضلاً
  static Future<bool> isFavorite(dynamic itemId, String itemType) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود الأعمدة المطلوبة
      final columns = await db.rawQuery('PRAGMA table_info(favorites)');
      final columnNames = columns.map((c) => c['name'].toString()).toList();

      // تحديد أي عمود يجب استخدامه
      final typeColumnName =
          columnNames.contains('item_type') ? 'item_type' : 'type';

      final result = await db.query(
        'favorites',
        where: 'item_id = ? AND $typeColumnName = ?',
        whereArgs: [itemId.toString(), itemType],
      );
      return result.isNotEmpty;
    } catch (e) {
      debugPrint('خطأ في التحقق من المفضلة: $e');
      return false;
    }
  }

  // مشاركة عنصر مفضل
  static Future<void> shareFavorite(dynamic item, String itemType) async {
    try {
      debugPrint('مشاركة عنصر من النوع: $itemType');

      switch (itemType) {
        case 'book':
          if (item is Book) {
            await _shareBook(item);
          }
          break;
        case 'poem':
          if (item is Poem) {
            await _sharePoem(item);
          }
          break;
        case 'azkar':
          await _shareAzkar(item);
          break;
        case 'dua':
          await _shareAzkar(item); // استخدام نفس طريقة مشاركة الأذكار للأدعية
          break;
        default:
          // محاولة مشاركة أي نوع غير معروف باستخدام طريقة الأذكار
          debugPrint('نوع غير معروف: $itemType، محاولة مشاركته كذكر');
          await _shareAzkar(item);
      }
    } catch (e) {
      debugPrint('خطأ في مشاركة العنصر: $e');
      rethrow;
    }
  }

  // مشاركة كتاب
  static Future<void> _shareBook(Book book) async {
    try {
      // محاولة استخدام الواجهة البرمجية الأصلية
      await _shareChannel.invokeMethod('shareBook', {
        'title': book.title,
        'author': book.author,
        'description': book.description,
      });
    } catch (e) {
      // استخدام مكتبة share_plus كبديل
      final String shareText = '''
${book.title}
المؤلف: ${book.author}

${book.description}

مشاركة من تطبيق وهج السالك
''';

      await Share.share(shareText);
    }
  }

  // مشاركة قصيدة
  static Future<void> _sharePoem(Poem poem) async {
    try {
      // محاولة استخدام الواجهة البرمجية الأصلية
      await _shareChannel.invokeMethod('sharePoem', {
        'title': poem.title,
        'poet': poem.poet,
        'content': poem.content,
      });
    } catch (e) {
      // استخدام مكتبة share_plus كبديل
      final String shareText = '''
${poem.title}
للشاعر: ${poem.poet}

${poem.content}

مشاركة من تطبيق وهج السالك
''';

      await Share.share(shareText);
    }
  }

  // مشاركة ذكر
  static Future<void> _shareAzkar(dynamic azkar) async {
    String title = '';
    String content = '';
    String category = '';

    // طباعة معلومات تشخيصية
    debugPrint('نوع كائن الذكر: ${azkar.runtimeType}');

    try {
      // استخراج البيانات من كائن الذكر
      if (azkar is Map<String, dynamic>) {
        title = azkar['title'] ?? azkar['name'] ?? '';
        content = azkar['content'] ?? azkar['text'] ?? '';
        category = azkar['category'] ?? '';
        debugPrint('تم استخراج البيانات من Map: $title, $category');
      } else if (azkar.runtimeType.toString().contains('AzkarItem')) {
        // التعامل مع AzkarItem
        title = 'ذكر';
        content = azkar.content ?? '';
        category = azkar.category ?? '';
        debugPrint('تم استخراج البيانات من AzkarItem: $category, $content');
      } else if (azkar.runtimeType.toString().contains('Dua')) {
        // التعامل مع Dua
        title = 'دعاء';
        content = azkar.text ?? '';

        // إضافة الترجمة إذا كانت متوفرة
        if (azkar.translation != null && azkar.translation.isNotEmpty) {
          content += '\n\n${azkar.translation}';
        }

        // إضافة المصدر إذا كان متوفراً
        if (azkar.source != null && azkar.source.isNotEmpty) {
          content += '\n\nالمصدر: ${azkar.source}';
        }

        // إضافة الفضل إذا كان متوفراً
        if (azkar.virtue != null && azkar.virtue.isNotEmpty) {
          content += '\n\nالفضل: ${azkar.virtue}';
        }

        debugPrint('تم استخراج البيانات من Dua: $title, $content');
      } else if (azkar.runtimeType.toString().contains('ZikrItem')) {
        // التعامل مع ZikrItem
        title = 'ذكر';
        content = azkar.text ?? '';

        // إضافة المصدر إذا كان متوفراً
        if (azkar.source != null && azkar.source.isNotEmpty) {
          content += '\n\nالمصدر: ${azkar.source}';
        }

        // إضافة الفضل إذا كان متوفراً
        if (azkar.fadl != null && azkar.fadl.isNotEmpty) {
          content += '\n\nالفضل: ${azkar.fadl}';
        }

        debugPrint('تم استخراج البيانات من ZikrItem: $title, $content');
      } else {
        // محاولة استخراج البيانات من أي كائن آخر
        try {
          if (azkar.title != null) {
            title = azkar.title;
          } else if (azkar.name != null) {
            title = azkar.name;
          }
        } catch (e) {
          title = 'ذكر';
        }

        try {
          if (azkar.content != null) {
            content = azkar.content;
          } else if (azkar.text != null) {
            content = azkar.text;
          }
        } catch (e) {
          content = '';
        }

        try {
          if (azkar.category != null) {
            category = azkar.category;
          }
        } catch (e) {
          category = '';
        }

        debugPrint('تم استخراج البيانات من كائن غير معروف: $title, $category');
      }

      // التأكد من أن المحتوى غير فارغ
      if (content.isEmpty) {
        content = 'محتوى غير متوفر';
      }

      // محاولة استخدام الواجهة البرمجية الأصلية
      await _shareChannel.invokeMethod('shareAzkar', {
        'title': title,
        'category': category,
        'content': content,
      });
    } catch (e) {
      debugPrint('خطأ في استخدام الواجهة البرمجية الأصلية: $e');

      // استخدام مكتبة share_plus كبديل
      String shareText = '';

      if (title.isNotEmpty) {
        shareText += '$title\n';
      }

      if (category.isNotEmpty) {
        shareText += '$category\n';
      }

      if (shareText.isNotEmpty) {
        shareText += '\n';
      }

      shareText += '$content\n\n';
      shareText += 'مشاركة من تطبيق وهج السالك';

      await Share.share(shareText);
    }
  }
}
