// بطاقة الصلاة على النبي بتصميم فاخر

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;
import 'dart:ui';
import '../models/dua.dart';
import '../utils/app_colors.dart';
import '../utils/icon_helper.dart'; // إضافة استيراد IconHelper

// تعريف نوع العرض
enum ProphetPrayerCardDisplayMode {
  grid, // عرض شبكي
  list, // عرض قائمة
}

class ProphetPrayerCard extends StatefulWidget {
  final DuaCategory category;
  final Function onTap;
  final Animation<double> animation;
  final ProphetPrayerCardDisplayMode displayMode; // نوع العرض

  const ProphetPrayerCard({
    Key? key,
    required this.category,
    required this.onTap,
    required this.animation,
    this.displayMode =
        ProphetPrayerCardDisplayMode.list, // القيمة الافتراضية هي العرض الشبكي
  }) : super(key: key);

  @override
  State<ProphetPrayerCard> createState() => _ProphetPrayerCardState();
}

class _ProphetPrayerCardState extends State<ProphetPrayerCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _hoverController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  late Animation<double> _rotateAnimation;
  late Animation<double> _glowAnimation;
  late Animation<Color?> _colorAnimation;
  late Animation<double> _shimmerAnimation;

  bool _isHovered = false;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
            begin: 1.0,
            end: widget.displayMode == ProphetPrayerCardDisplayMode.list
                ? 1.02
                : 1.05)
        .animate(
      CurvedAnimation(
        parent: _hoverController,
        curve: Curves.easeOutCubic,
      ),
    );

    _elevationAnimation = Tween<double>(
            begin: widget.displayMode == ProphetPrayerCardDisplayMode.list
                ? 2.0
                : 4.0,
            end: widget.displayMode == ProphetPrayerCardDisplayMode.list
                ? 10.0
                : 20.0)
        .animate(
      CurvedAnimation(
        parent: _hoverController,
        curve: Curves.easeOutCubic,
      ),
    );

    _rotateAnimation = Tween<double>(
            begin: 0.0,
            end: widget.displayMode == ProphetPrayerCardDisplayMode.list
                ? 0.0
                : 0.015)
        .animate(
      CurvedAnimation(
        parent: _hoverController,
        curve: Curves.easeInOutBack,
      ),
    );

    _glowAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _hoverController,
        curve: const Interval(0.0, 0.75, curve: Curves.easeOut),
      ),
    );

    _shimmerAnimation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(
        parent: _hoverController,
        curve: const Interval(0.0, 1.0, curve: Curves.easeInOut),
      ),
    );

    // سيتم تحديد اللون في build
    _colorAnimation = ColorTween(
      begin: Colors.transparent,
      end: Colors.transparent, // سيتم تحديثه في build
    ).animate(_hoverController);
  }

  @override
  void dispose() {
    _hoverController.dispose();
    super.dispose();
  }

  // دالة لحساب حجم الخط المتكيف مع حجم الشاشة
  double _getAdaptiveFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;

    // تحديد معامل التكيف بناءً على حجم الشاشة
    double scaleFactor;
    if (screenWidth > 1200) {
      // شاشات كبيرة جداً
      scaleFactor = 1.2;
    } else if (screenWidth > 900) {
      // شاشات كبيرة
      scaleFactor = 1.1;
    } else if (screenWidth > 600) {
      // شاشات متوسطة
      scaleFactor = 1.0;
    } else if (screenWidth > 400) {
      // شاشات صغيرة
      scaleFactor = 0.9;
    } else {
      // شاشات صغيرة جداً
      scaleFactor = 0.8;
    }

    return baseFontSize * scaleFactor;
  }

  // دالة لحساب الأحجام المتكيفة للعناصر المرئية
  double _getAdaptiveSize(BuildContext context, double baseSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // استخدام متوسط العرض والارتفاع لضمان تناسق أفضل
    final screenAverage = (screenWidth + screenHeight) / 2;

    // تحديد معامل التكيف بناءً على متوسط حجم الشاشة
    double scaleFactor;
    if (screenAverage > 1000) {
      // شاشات كبيرة جداً
      scaleFactor = 1.2;
    } else if (screenAverage > 800) {
      // شاشات كبيرة
      scaleFactor = 1.1;
    } else if (screenAverage > 600) {
      // شاشات متوسطة
      scaleFactor = 1.0;
    } else if (screenAverage > 400) {
      // شاشات صغيرة
      scaleFactor = 0.9;
    } else {
      // شاشات صغيرة جداً
      scaleFactor = 0.8;
    }

    // تطبيق معامل التكيف مع مراعاة نسبة العرض إلى الارتفاع
    final aspectRatio = screenWidth / screenHeight;
    final aspectAdjustment =
        aspectRatio > 1.5 ? 0.9 : (aspectRatio < 0.7 ? 1.1 : 1.0);

    return baseSize * scaleFactor * aspectAdjustment;
  }

  void _onHover(bool isHovered) {
    if (isHovered) {
      _hoverController.forward();
      HapticFeedback.lightImpact();
    } else {
      _hoverController.reverse();
    }
    setState(() {
      _isHovered = isHovered;
    });
  }

  void _onPressed(bool isPressed) {
    setState(() {
      _isPressed = isPressed;
    });
  }

  // بناء تخطيط عرض القائمة بتصميم فاخر ومحسن
  Widget _buildListViewLayout(BuildContext context, bool isDarkMode,
      Size screenSize, Color prophetPrayersColor, IconData iconData) {
    // تحديد ارتفاع البطاقة بناءً على حجم الشاشة
    final cardHeight = _getAdaptiveSize(context, 100);

    return Container(
      height: cardHeight,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        // إضافة تأثير زجاجي للخلفية
        color: isDarkMode
            ? Colors.grey[850]!.withValues(alpha: 0.8)
            : Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color:
                prophetPrayersColor.withValues(alpha: _isHovered ? 0.3 : 0.1),
            blurRadius: _isHovered ? 15 : 8,
            spreadRadius: _isHovered ? 2 : 0,
            offset: Offset(0, _isHovered ? 5 : 3),
          ),
        ],
        // إضافة حدود متوهجة عند التحويم
        border: Border.all(
          color: _isHovered
              ? prophetPrayersColor.withValues(alpha: 0.6)
              : prophetPrayersColor.withValues(alpha: 0.2),
          width: _isHovered ? 1.5 : 1.0,
        ),
      ),
      child: Row(
        textDirection: TextDirection.rtl, // اتجاه من اليمين إلى اليسار
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // أيقونة الفئة مع تأثير توهج وحركة
          TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: 0, end: _isHovered ? 1.0 : 0.0),
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeOutCubic,
            builder: (context, value, child) {
              return Transform.scale(
                scale: 1.0 + (value * 0.1), // تكبير بسيط عند التحويم
                child: Container(
                  width: _getAdaptiveSize(context, 56),
                  height: _getAdaptiveSize(context, 56),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        prophetPrayersColor.withValues(
                            alpha: 0.7 + (value * 0.2)),
                        prophetPrayersColor.withValues(alpha: 0.9),
                      ],
                      begin: Alignment.topRight,
                      end: Alignment.bottomLeft,
                    ),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: prophetPrayersColor.withValues(
                            alpha: 0.4 + (value * 0.3)),
                        blurRadius: 8 + (value * 4),
                        spreadRadius: value * 2,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: Icon(
                    iconData,
                    color: Colors.white,
                    size: _getAdaptiveSize(context, 24),
                  ),
                ),
              );
            },
          ),
          const SizedBox(width: 16),

          // المحتوى الرئيسي (العنوان والوصف)
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              textDirection: TextDirection.rtl,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // عنوان مع تأثير تدرج لوني متحرك
                TweenAnimationBuilder<double>(
                  tween: Tween<double>(begin: 0, end: _isHovered ? 1.0 : 0.0),
                  duration: const Duration(milliseconds: 400),
                  curve: Curves.easeOut,
                  builder: (context, value, child) {
                    return ShaderMask(
                      shaderCallback: (bounds) {
                        return LinearGradient(
                          colors: [
                            isDarkMode ? Colors.white : Colors.black87,
                            prophetPrayersColor,
                            prophetPrayersColor.withValues(alpha: 0.8),
                          ],
                          stops: [0.3, 0.6 + (value * 0.2), 1.0],
                          begin: Alignment.centerRight,
                          end: Alignment.centerLeft,
                          tileMode: TileMode.clamp,
                        ).createShader(bounds);
                      },
                      child: Text(
                        widget.category.name,
                        style: TextStyle(
                          fontSize: _getAdaptiveFontSize(context, 17),
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          height: 1.3,
                          letterSpacing: 0.2,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textDirection: TextDirection.rtl,
                        textAlign: TextAlign.right,
                      ),
                    );
                  },
                ),
                const SizedBox(height: 8),

                // وصف الفئة مع تأثير ظهور تدريجي
                AnimatedOpacity(
                  opacity: _isHovered ? 1.0 : 0.9,
                  duration: const Duration(milliseconds: 300),
                  child: Text(
                    widget.category.description,
                    style: TextStyle(
                      fontSize: _getAdaptiveFontSize(context, 13),
                      height: 1.4,
                      color: isDarkMode ? Colors.grey[300] : Colors.grey[800],
                      fontWeight:
                          _isHovered ? FontWeight.w500 : FontWeight.normal,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textDirection: TextDirection.rtl,
                    textAlign: TextAlign.right,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: 12),

          // عدد الصلوات وزر التصفح
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // عدد الصلوات مع تأثير متحرك
              TweenAnimationBuilder<double>(
                tween: Tween<double>(begin: 0, end: _isHovered ? 1.0 : 0.0),
                duration: const Duration(milliseconds: 400),
                curve: Curves.easeOut,
                builder: (context, value, child) {
                  return Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: prophetPrayersColor.withValues(
                          alpha: 0.15 + (value * 0.1)),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: prophetPrayersColor.withValues(
                            alpha: 0.3 + (value * 0.3)),
                        width: 1,
                      ),
                      boxShadow: _isHovered
                          ? [
                              BoxShadow(
                                color:
                                    prophetPrayersColor.withValues(alpha: 0.2),
                                blurRadius: 4,
                                spreadRadius: 0,
                                offset: const Offset(0, 2),
                              ),
                            ]
                          : [],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      textDirection: TextDirection.rtl,
                      children: [
                        Icon(
                          Icons.format_list_numbered,
                          size: 14,
                          color: prophetPrayersColor,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          '${widget.category.items.length} صيغة',
                          style: TextStyle(
                            fontSize: _getAdaptiveFontSize(context, 12),
                            fontWeight: FontWeight.w600,
                            color: prophetPrayersColor,
                          ),
                          textDirection: TextDirection.rtl,
                          textAlign: TextAlign.right,
                        ),
                      ],
                    ),
                  );
                },
              ),

              const SizedBox(height: 10),

              // زر التصفح مع تأثير نبض
              TweenAnimationBuilder<double>(
                tween: Tween<double>(begin: 0, end: _isHovered ? 1.0 : 0.0),
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeOutCubic,
                builder: (context, value, child) {
                  return Transform.scale(
                    scale: 1.0 + (value * 0.15),
                    child: Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topRight,
                          end: Alignment.bottomLeft,
                          colors: [
                            prophetPrayersColor.withValues(
                                alpha: 0.7 + (value * 0.2)),
                            prophetPrayersColor.withValues(alpha: 0.9),
                          ],
                        ),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: prophetPrayersColor.withValues(
                                alpha: 0.3 + (value * 0.3)),
                            blurRadius: 8,
                            spreadRadius: value * 1,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.arrow_back_ios,
                        size: 14,
                        color: Colors.white,
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final screenSize = MediaQuery.of(context).size;
    final prophetPrayersColor = AppColors.getProphetPrayersColor(isDarkMode);

    // تحديث لون الأنيميشن
    _colorAnimation = ColorTween(
      begin: Colors.transparent,
      end: prophetPrayersColor.withValues(alpha: 0.6),
    ).animate(_hoverController);

    // تحويل اسم الأيقونة إلى أيقونة باستخدام IconHelper
    IconData iconData;
    try {
      // استخدام IconHelper للحصول على الأيقونة المناسبة
      iconData = IconHelper.getIconForCategory(widget.category.name,
          iconName: widget.category.iconName);
    } catch (e) {
      // استخدام أيقونة افتراضية إذا لم يتم العثور على الأيقونة
      iconData = Icons.auto_awesome;
      debugPrint(
          'خطأ في تحويل اسم الأيقونة: ${widget.category.iconName}، الخطأ: $e');
    }

    return AnimatedBuilder(
      animation: widget.animation,
      builder: (context, child) {
        return Opacity(
          opacity: widget.animation.value,
          child: Transform.translate(
            offset: Offset(0, 50 * (1 - widget.animation.value)),
            child: child,
          ),
        );
      },
      child: MouseRegion(
        onEnter: (_) => _onHover(true),
        onExit: (_) => _onHover(false),
        child: GestureDetector(
          onTapDown: (_) => _onPressed(true),
          onTapUp: (_) {
            _onPressed(false);
            widget.onTap();
          },
          onTapCancel: () => _onPressed(false),
          onTap: () {
            HapticFeedback.mediumImpact();
          },
          child: AnimatedBuilder(
            animation: _hoverController,
            builder: (context, child) {
              return Transform.scale(
                scale: _isPressed
                    ? _scaleAnimation.value * 0.98
                    : _scaleAnimation.value,
                child: Transform.rotate(
                  angle: _rotateAnimation.value,
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(28),
                      boxShadow: [
                        // ظل أساسي
                        BoxShadow(
                          color: prophetPrayersColor.withValues(alpha: 0.16),
                          blurRadius: _elevationAnimation.value,
                          spreadRadius: 1,
                          offset: Offset(0, _elevationAnimation.value / 2),
                        ),
                        // ظل توهج عند التحويم
                        BoxShadow(
                          color: prophetPrayersColor.withValues(
                              alpha: 0.2 * _glowAnimation.value),
                          blurRadius: 25 * _glowAnimation.value,
                          spreadRadius: 3 * _glowAnimation.value,
                          offset: const Offset(0, 0),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(28),
                      child: BackdropFilter(
                        filter: ImageFilter.blur(
                          sigmaX: _isHovered ? 0 : 0,
                          sigmaY: _isHovered ? 0 : 0,
                        ),
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(28),
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: isDarkMode
                                  ? [
                                      Color.lerp(
                                        Colors.grey[900]!,
                                        prophetPrayersColor.withValues(
                                            alpha: 40),
                                        _hoverController.value * 0.7,
                                      )!,
                                      Color.lerp(
                                        Colors.grey[850]!,
                                        prophetPrayersColor.withValues(
                                            alpha: 60),
                                        _hoverController.value * 0.8,
                                      )!,
                                    ]
                                  : [
                                      Color.lerp(
                                        Colors.white,
                                        prophetPrayersColor.withValues(
                                            alpha: 20),
                                        _hoverController.value * 0.8,
                                      )!,
                                      Color.lerp(
                                        Colors.grey[50]!,
                                        prophetPrayersColor.withValues(
                                            alpha: 40),
                                        _hoverController.value * 0.9,
                                      )!,
                                    ],
                            ),
                            // تحسين الحدود لتكون أكثر فخامة
                            border: Border.all(
                              color: widget.displayMode ==
                                      ProphetPrayerCardDisplayMode.list
                                  ? Colors
                                      .transparent // في وضع القائمة، نستخدم حدود الحاوية الخارجية
                                  : _colorAnimation.value ?? Colors.transparent,
                              width: 1.8,
                            ),
                            // إضافة تأثير زجاجي للخلفية في وضع الشبكة
                            boxShadow: widget.displayMode ==
                                    ProphetPrayerCardDisplayMode.grid
                                ? [
                                    BoxShadow(
                                      color: prophetPrayersColor.withValues(
                                          alpha: _hoverController.value * 0.4),
                                      blurRadius: 10,
                                      spreadRadius: 0,
                                      offset: const Offset(0, 4),
                                    ),
                                  ]
                                : [],
                          ),
                          child: Stack(
                            children: [
                              // طبقة التأثير المتحرك (Shimmer Effect)
                              if (_isHovered)
                                Positioned.fill(
                                  child: IgnorePointer(
                                    child: Container(
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          stops: [
                                            0.0,
                                            _shimmerAnimation.value - 0.5,
                                            _shimmerAnimation.value,
                                            _shimmerAnimation.value + 0.5,
                                            1.0,
                                          ],
                                          colors: [
                                            Colors.transparent,
                                            Colors.transparent,
                                            prophetPrayersColor.withValues(
                                                alpha: 0.40),
                                            Colors.transparent,
                                            Colors.transparent,
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),

                              // زخرفة الخلفية - تم تحسينها لتجنب مشكلة ParentDataWidget
                              Positioned(
                                right: -screenSize.width * 0.1,
                                bottom: -screenSize.width * 0.1,
                                child: Opacity(
                                  opacity:
                                      0.05 + (0.05 * _hoverController.value),
                                  child: Transform.rotate(
                                    angle: -math.pi / 6,
                                    child: Icon(
                                      Icons.auto_awesome,
                                      size: screenSize.width * 0.25,
                                      color: prophetPrayersColor,
                                    ),
                                  ),
                                ),
                              ),

                              // المحتوى الرئيسي
                              Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: widget.displayMode ==
                                        ProphetPrayerCardDisplayMode.list
                                    ? _buildListViewLayout(
                                        context,
                                        isDarkMode,
                                        screenSize,
                                        prophetPrayersColor,
                                        iconData)
                                    : Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.stretch,
                                        children: [
                                          // الجزء العلوي: الأيقونة والعنوان
                                          Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            textDirection: TextDirection
                                                .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                                            children: [
                                              // أيقونة الفئة مع تأثير توهج - تم تبسيطها
                                              Container(
                                                width: _getAdaptiveSize(
                                                    context, 42),
                                                height: _getAdaptiveSize(
                                                    context, 42),
                                                decoration: BoxDecoration(
                                                  gradient: LinearGradient(
                                                    colors: [
                                                      prophetPrayersColor
                                                          .withValues(
                                                              alpha: 0.70),
                                                      prophetPrayersColor
                                                          .withValues(
                                                              alpha: 0.120),
                                                    ],
                                                    begin: Alignment
                                                        .topRight, // تغيير اتجاه التدرج ليتناسب مع اللغة العربية
                                                    end: Alignment.bottomLeft,
                                                  ),
                                                  shape: BoxShape.circle,
                                                  boxShadow: [
                                                    BoxShadow(
                                                      color: prophetPrayersColor
                                                          .withValues(
                                                              alpha: 0.60),
                                                      blurRadius: 8,
                                                      spreadRadius: 0,
                                                      offset:
                                                          const Offset(0, 3),
                                                    ),
                                                  ],
                                                ),
                                                child: Icon(
                                                  iconData,
                                                  color: Colors.white,
                                                  size: _getAdaptiveSize(
                                                      context, 22),
                                                ),
                                              ),
                                              const SizedBox(width: 14),

                                              // اسم الفئة وعدد الصلوات
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  textDirection: TextDirection
                                                      .rtl, // تحديد اتجاه العمود من اليمين إلى اليسار
                                                  children: [
                                                    // عنوان مع تأثير تدرج لوني
                                                    ShaderMask(
                                                      shaderCallback: (bounds) {
                                                        return LinearGradient(
                                                          colors: [
                                                            isDarkMode
                                                                ? Colors.white
                                                                : Colors
                                                                    .black87,
                                                            prophetPrayersColor,
                                                          ],
                                                          stops: const [
                                                            0.4,
                                                            1.0
                                                          ],
                                                          begin: Alignment
                                                              .centerRight, // تغيير اتجاه التدرج ليتناسب مع اللغة العربية
                                                          end: Alignment
                                                              .centerLeft,
                                                          tileMode:
                                                              TileMode.clamp,
                                                        ).createShader(bounds);
                                                      },
                                                      child: Text(
                                                        widget.category.name,
                                                        style: TextStyle(
                                                          fontSize:
                                                              _getAdaptiveFontSize(
                                                                  context, 15),
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          color: Colors
                                                              .white, // سيتم تجاهل هذا اللون بسبب الشادر
                                                          height: 1.3,
                                                        ),
                                                        maxLines: 1,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        textDirection: TextDirection
                                                            .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                                        textAlign: TextAlign
                                                            .right, // محاذاة النص إلى اليمين
                                                      ),
                                                    ),
                                                    const SizedBox(height: 8),

                                                    // عدد الصلوات مع أيقونة
                                                    Container(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                        horizontal: 8,
                                                        vertical: 4,
                                                      ),
                                                      decoration: BoxDecoration(
                                                        color:
                                                            prophetPrayersColor
                                                                .withValues(
                                                                    alpha:
                                                                        0.30),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(10),
                                                        border: Border.all(
                                                          color:
                                                              prophetPrayersColor
                                                                  .withValues(
                                                                      alpha:
                                                                          50),
                                                          width: 1,
                                                        ),
                                                      ),
                                                      child: Row(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        textDirection: TextDirection
                                                            .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                                                        children: [
                                                          Icon(
                                                            Icons
                                                                .format_list_numbered,
                                                            size: 12,
                                                            color:
                                                                prophetPrayersColor,
                                                          ),
                                                          const SizedBox(
                                                              width: 4),
                                                          Text(
                                                            '${widget.category.items.length} صيغة',
                                                            style: TextStyle(
                                                              fontSize:
                                                                  _getAdaptiveFontSize(
                                                                      context,
                                                                      11),
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                              color:
                                                                  prophetPrayersColor,
                                                            ),
                                                            textDirection:
                                                                TextDirection
                                                                    .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                                            textAlign: TextAlign
                                                                .right, // محاذاة النص إلى اليمين
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 14),

                                          // وصف الفئة مع خلفية مميزة
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 12,
                                              vertical: 10,
                                            ),
                                            decoration: BoxDecoration(
                                              color: prophetPrayersColor
                                                  .withValues(alpha: 0.20),
                                              borderRadius:
                                                  BorderRadius.circular(14),
                                              border: Border.all(
                                                color: prophetPrayersColor
                                                    .withValues(alpha: 0.40),
                                                width: 1,
                                              ),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: prophetPrayersColor
                                                      .withValues(alpha: 0.10),
                                                  blurRadius: 6,
                                                  spreadRadius: 0,
                                                  offset: const Offset(0, 2),
                                                ),
                                              ],
                                            ),
                                            child: Text(
                                              widget.category.description,
                                              style: TextStyle(
                                                fontSize: _getAdaptiveFontSize(
                                                    context, 12),
                                                height: 1.4,
                                                color: isDarkMode
                                                    ? Colors.grey[300]
                                                    : Colors.grey[800],
                                              ),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                              textDirection: TextDirection
                                                  .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                              textAlign: TextAlign
                                                  .right, // محاذاة النص إلى اليمين
                                            ),
                                          ),
                                          const SizedBox(height: 12),

                                          // شريط السفلي مع مؤشر قابلية التصفح
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            textDirection: TextDirection
                                                .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                                            children: [
                                              // مؤشر يوضح تفرع الأقسام إن وجدت
                                              if (widget
                                                  .category.hasSubcategories)
                                                Container(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                    horizontal: 8,
                                                    vertical: 4,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color: prophetPrayersColor
                                                        .withValues(
                                                            alpha: 0.25),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            10),
                                                    border: Border.all(
                                                      color: prophetPrayersColor
                                                          .withValues(
                                                              alpha: 0.50),
                                                      width: 1,
                                                    ),
                                                  ),
                                                  child: Row(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    textDirection: TextDirection
                                                        .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                                                    children: [
                                                      Icon(
                                                        Icons
                                                            .account_tree_outlined,
                                                        size: 10,
                                                        color:
                                                            prophetPrayersColor,
                                                      ),
                                                      const SizedBox(width: 4),
                                                      Text(
                                                        'أقسام',
                                                        style: TextStyle(
                                                          fontSize: 10,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          color:
                                                              prophetPrayersColor,
                                                        ),
                                                        textDirection: TextDirection
                                                            .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                                        textAlign: TextAlign
                                                            .right, // محاذاة النص إلى اليمين
                                                      ),
                                                    ],
                                                  ),
                                                ),

                                              // زر التصفح مع تأثير نبض
                                              Container(
                                                padding:
                                                    const EdgeInsets.all(8),
                                                decoration: BoxDecoration(
                                                  gradient: LinearGradient(
                                                    begin: Alignment
                                                        .topRight, // تغيير اتجاه التدرج ليتناسب مع اللغة العربية
                                                    end: Alignment.bottomLeft,
                                                    colors: [
                                                      prophetPrayersColor
                                                          .withValues(
                                                              alpha: 0.80),
                                                      prophetPrayersColor
                                                          .withValues(
                                                              alpha: 0.120),
                                                    ],
                                                  ),
                                                  shape: BoxShape.circle,
                                                ),
                                                child: const Icon(
                                                  Icons
                                                      .arrow_back_ios, // تغيير اتجاه السهم ليتناسب مع اللغة العربية
                                                  size: 12,
                                                  color: Colors.white,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
