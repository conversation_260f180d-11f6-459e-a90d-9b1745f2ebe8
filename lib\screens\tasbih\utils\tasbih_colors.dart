// ألوان المسبحة
import 'package:flutter/material.dart';

class TasbihColors {
  // ألوان متناسقة مع azkar_screen (مستوحاة من AppColors.azkarColor)
  static const Color primary = Color(0xFF00897B); // نفس لون azkar
  static const Color secondary = Color(0xFF26A69A); // مشتق من اللون الأساسي
  static const Color tertiary = Color(0xFF4DB6AC); // مشتق من اللون الأساسي

  // ألوان الخرز
  static const List<Color> beadColors = [
    primary,
    secondary,
    tertiary,
    Color(0xFF00796B), // أخضر داكن
    Color(0xFF26A69A), // أخضر فاتح
    Color(0xFF00897B), // أخضر متوسط
    Color(0xFF4DB6AC), // أخضر فيروزي
    Color(0xFF80CBC4), // أخضر فاتح جداً
  ];

  // ألوان الوضع الفاتح
  static const Color lightBackground = Colors.white;
  static const Color lightBackgroundSecondary = Color(0xFFF5F5F5);
  static const Color lightCardColor = Colors.white;
  static const Color lightTextColor = Color(0xFF333333);
  static const Color lightTextSecondary = Color(0xFF757575);

  // ألوان الوضع الداكن
  static const Color darkBackground = Color(0xFF1A1A2E);
  static const Color darkBackgroundSecondary = Color(0xFF16213E);
  static const Color darkCardColor = Color(0xFF252A34);
  static const Color darkTextColor = Color(0xFFE0E0E0);
  static const Color darkTextSecondary = Color(0xFFAAAAAA);

  // الحصول على ألوان متوافقة مع الوضع الحالي
  static Color getCardColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkCardColor
        : lightCardColor;
  }

  static Color getTextColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkTextColor
        : lightTextColor;
  }

  static LinearGradient getBackgroundGradient(bool isDarkMode) {
    return LinearGradient(
      begin: Alignment.topRight,
      end: Alignment.bottomLeft,
      colors: isDarkMode
          ? [darkBackground, darkBackgroundSecondary]
          : [lightBackground, lightBackgroundSecondary],
    );
  }
}
