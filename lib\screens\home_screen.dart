import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;
import 'dart:ui';
import 'dart:async';
import 'package:share_plus/share_plus.dart';
import 'package:provider/provider.dart';
import '../widgets/swipeable_categories.dart';
import '../utils/constants.dart';
import '../utils/app_colors.dart';
import '../models/daily_wisdom.dart';
import '../utils/enhanced_search_delegate.dart';
import '../models/search_result_model.dart';
import '../utils/feature_showcase.dart';
import 'home/models/activity_tracker.dart';
import 'tasbih/providers/tasbih_provider.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  // متغيرات الحكمة اليومية
  late Future<List<DailyWisdom>> _wisdomQuotes;
  PageController? _pageController;
  int _currentPage = 0;

  // متغيرات التحريك (الأنيميشن)
  late AnimationController _mainAnimationController;
  late AnimationController _patternAnimationController;
  late AnimationController _wisdomAnimationController;
  late AnimationController _categoryAnimationController;

  // متغيرات الانزلاق والتلاشي
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  // التحكم بالتمرير
  final ScrollController _scrollController = ScrollController();
  bool _isScrolled = false;

  @override
  void initState() {
    super.initState();

    // تهيئة متحكم الصفحات
    _pageController = PageController();

    // تهيئة الحكم مع معالجة الأخطاء - تحميل حكمة عشوائية في كل مرة يتم فتح التطبيق
    _wisdomQuotes = _loadRandomWisdomQuote();

    // إضافة معالجة الخطأ هنا
    _wisdomQuotes.catchError((error) {
      debugPrint('خطأ في تحميل الحكم: $error');
      // استخدام حكم افتراضية في حالة الخطأ
      return [
        DailyWisdom(
          text: 'كن مع الله يكن معك',
          author: 'حكمة إسلامية',
        ),
      ];
    });

    // إعداد وحدات التحكم بالأنيميشن
    _mainAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    _patternAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 20),
    )..repeat(reverse: true);

    _wisdomAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _categoryAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // إعداد الأنيميشن للانزلاق والتلاشي
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _mainAnimationController,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOutCubic),
      ),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _mainAnimationController,
        curve: const Interval(0.2, 1.0, curve: Curves.easeOut),
      ),
    );

    // إضافة المستمع للتمرير لتنفيذ تأثيرات عند التمرير
    _scrollController.addListener(_onScroll);

    // بدء الأنيميشن بعد تأخير بسيط
    Future.delayed(const Duration(milliseconds: 100), () {
      _mainAnimationController.forward();
    });

    Future.delayed(const Duration(milliseconds: 300), () {
      _wisdomAnimationController.forward();
    });

    Future.delayed(const Duration(milliseconds: 600), () {
      _categoryAnimationController.forward();
    });

    // التحقق من أذونات الإشعارات وجدولة الإشعارات بعد بناء الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkNotificationsAndSchedule();

      // عرض الشرح التوضيحي للصفحة الرئيسية عند فتح التطبيق لأول مرة
      _showHomeScreenTutorial();
    });
  }

  /// عرض الشرح التوضيحي للصفحة الرئيسية
  Future<void> _showHomeScreenTutorial() async {
    try {
      // التحقق مما إذا كانت هذه هي المرة الأولى لفتح التطبيق
      final isFirstTime = await FeatureShowcase.isFirstTimeOpen();
      if (!isFirstTime || !mounted) {
        return;
      }

      // تأخير قصير لضمان بناء الواجهة بشكل كامل
      await Future.delayed(const Duration(milliseconds: 800));

      if (!mounted) return;

      // عرض الشرح التوضيحي للصفحة الرئيسية
      await FeatureShowcase.showHomeScreenTutorial(context);
    } catch (e) {
      debugPrint('خطأ في عرض الشرح التوضيحي: $e');
    }
  }

  /// التحقق من أذونات الإشعارات وجدولة الإشعارات
  Future<void> _checkNotificationsAndSchedule() async {
    try {
      // الحصول على مزود المسبحة
      final tasbihProvider =
          Provider.of<TasbihProvider>(context, listen: false);

      // التحقق من أذونات الإشعارات وجدولة الإشعارات
      await tasbihProvider.scheduleReturnNotifications(context: context);
    } catch (e) {
      debugPrint('خطأ في التحقق من أذونات الإشعارات: $e');
    }
  }

  @override
  void dispose() {
    _pageController?.dispose();
    _mainAnimationController.dispose();
    _patternAnimationController.dispose();
    _wisdomAnimationController.dispose();
    _categoryAnimationController.dispose();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  // تحديث حالة التمرير
  void _onScroll() {
    if (_scrollController.hasClients) {
      final isScrolled = _scrollController.offset > 60;
      if (isScrolled != _isScrolled) {
        setState(() {
          _isScrolled = isScrolled;
        });
      }
    }
  }

  // الحصول على التحية بناءً على وقت اليوم
  String _getGreeting() {
    try {
      final hour = DateTime.now().hour;
      if (hour < 5) {
        return 'تصبح على خير';
      } else if (hour < 12) {
        return 'صباح الخير';
      } else if (hour < 17) {
        return 'مساء الخير';
      } else if (hour < 22) {
        return 'مساء النور';
      } else {
        return 'ليلة سعيدة';
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على التحية: $e');
      return 'مرحباً';
    }
  }

  // الحصول على أيقونة التحية بناءً على وقت اليوم
  IconData _getGreetingIcon() {
    try {
      final hour = DateTime.now().hour;
      if (hour < 5) {
        return Icons.nightlight_round; // ليل
      } else if (hour < 12) {
        return Icons.wb_sunny; // صباح
      } else if (hour < 17) {
        return Icons.wb_twighlight; // ظهر
      } else if (hour < 22) {
        return Icons.nights_stay; // مساء
      } else {
        return Icons.dark_mode; // ليل متأخر
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على أيقونة التحية: $e');
      return Icons.emoji_emotions; // أيقونة افتراضية
    }
  }

  // تحميل حكمة عشوائية في كل مرة يتم فتح التطبيق
  Future<List<DailyWisdom>> _loadRandomWisdomQuote() async {
    try {
      // الحصول على حكمة عشوائية
      final randomWisdom = await DailyWisdom.getRandomQuote();

      // إرجاع قائمة تحتوي على الحكمة العشوائية كعنصر أول
      // ثم تحميل باقي الحكم لاستخدامها عند الضغط على زر التحديث
      final allWisdoms = await DailyWisdom.getAllQuotes();

      // وضع الحكمة العشوائية في بداية القائمة
      final reorderedWisdoms = [randomWisdom];

      // إضافة باقي الحكم مع تجنب التكرار
      for (var wisdom in allWisdoms) {
        if (wisdom.text != randomWisdom.text) {
          reorderedWisdoms.add(wisdom);
        }
      }

      // تسجيل الحكمة المعروضة للتشخيص
      debugPrint(
          'تم عرض حكمة جديدة: ${randomWisdom.text} - ${randomWisdom.author}');

      return reorderedWisdoms;
    } catch (e) {
      debugPrint('خطأ في تحميل الحكمة العشوائية: $e');
      // إرجاع قائمة افتراضية في حالة حدوث خطأ
      return [
        DailyWisdom(
          text: 'كن مع الله يكن معك',
          author: 'حكمة إسلامية',
        ),
        DailyWisdom(
          text: 'العلم ما نفع، وليس ما حُفظ',
          author: 'الإمام الشافعي',
        ),
      ];
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // ضبط شريط الحالة ليتناسب مع سمة التطبيق
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: isDarkMode ? Brightness.light : Brightness.dark,
    ));

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // خلفية الصفحة مع زخارف إسلامية متحركة
          _buildAnimatedBackground(isDarkMode),

          // المحتوى الرئيسي
          CustomScrollView(
            controller: _scrollController,
            physics: const AlwaysScrollableScrollPhysics(
                parent: BouncingScrollPhysics()),
            slivers: [
              // شريط التطبيق المخصص مع ترحيب
              _buildAppBar(isDarkMode),

              // المحتوى الرئيسي
              SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.fromLTRB(
                      16, 8, 16, MediaQuery.of(context).padding.bottom + 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // قسم الحكمة اليومية محسن
                      _buildEnhancedWisdomSection(isDarkMode),

                      SizedBox(
                          height: MediaQuery.of(context).size.height * 0.035),

                      // قسم الأقسام مع أنيميشن
                      _buildCategoriesSection(isDarkMode),

                      SizedBox(
                          height: MediaQuery.of(context).size.height * 0.035),

                      // قسم النشاط الأخير محسن
                      _buildRecentActivitySection(isDarkMode),

                      SizedBox(
                          height: MediaQuery.of(context).size.height * 0.03),

                      // قسم المسبحة محسن
                      _buildTasbihSection(isDarkMode),

                      SizedBox(
                          height: MediaQuery.of(context).size.height * 0.03),

                      // إشعار الأقسام القادمة
                      _buildComingSoonSection(isDarkMode),
                    ],
                  ),
                ),
              ),
            ],
          ),

          // شريط أدوات عائم يظهر عند التمرير
          if (_isScrolled) _buildFloatingToolbar(isDarkMode),
        ],
      ),
    );
  }

  // بناء خلفية متحركة مع زخارف إسلامية وتأثير parallax
  Widget _buildAnimatedBackground(bool isDarkMode) {
    return Stack(
      children: [
        // خلفية متدرجة محسنة
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: isDarkMode
                  ? [
                      const Color(0xFF101823),
                      const Color(0xFF0D141B),
                    ]
                  : [
                      AppColors.azkarColor.withAlpha(13), // 0.05 * 255 = ~13
                      Colors.white,
                    ],
            ),
          ),
        ),

        // تأثير الضباب الخفيف (للوضع الفاتح فقط)
        if (!isDarkMode)
          Positioned.fill(
            child: CustomPaint(
              painter: LightPatternPainter(
                animation: _patternAnimationController,
                color: AppColors.azkarColor,
              ),
            ),
          ),

        // زخرفة متحركة - يمين أعلى مع تأثير parallax
        Positioned(
          top: -60 -
              (_scrollController.hasClients
                  ? _scrollController.offset * 0.1
                  : 0),
          right: -60,
          child: AnimatedBuilder(
            animation: _patternAnimationController,
            builder: (context, child) {
              return Transform.rotate(
                angle: 0.05 *
                    math.pi *
                    math.sin(_patternAnimationController.value * math.pi),
                child: Opacity(
                  opacity: isDarkMode ? 0.05 : 0.07,
                  child: SvgPicture.asset(
                    'assets/images/p2.svg',
                    width: 200,
                    height: 200,
                    colorFilter: const ColorFilter.mode(
                      AppColors.azkarColor,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              );
            },
          ),
        ),

        // زخرفة متحركة - يسار أسفل مع تأثير parallax
        Positioned(
          bottom: 60 +
              (_scrollController.hasClients
                  ? _scrollController.offset * 0.05
                  : 0),
          left: -80,
          child: AnimatedBuilder(
            animation: _patternAnimationController,
            builder: (context, child) {
              return Transform.rotate(
                angle: -0.02 *
                    math.pi *
                    math.sin(_patternAnimationController.value * math.pi * 0.5),
                child: Opacity(
                  opacity: isDarkMode ? 0.04 : 0.06,
                  child: SvgPicture.asset(
                    'assets/images/p2.svg',
                    width: 180,
                    height: 180,
                    colorFilter: const ColorFilter.mode(
                      AppColors.azkarColor,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              );
            },
          ),
        ),

        // زخرفة إضافية متحركة - وسط الشاشة
        if (!isDarkMode)
          Positioned(
            top: MediaQuery.of(context).size.height * 0.4,
            right: MediaQuery.of(context).size.width * 0.6,
            child: AnimatedBuilder(
              animation: _patternAnimationController,
              builder: (context, child) {
                return Transform.rotate(
                  angle: 0.03 *
                      math.pi *
                      math.sin(
                          _patternAnimationController.value * math.pi * 0.7),
                  child: Opacity(
                    opacity: 0.03,
                    child: SvgPicture.asset(
                      'assets/images/islamic_pattern1.svg',
                      width: 120,
                      height: 120,
                      colorFilter: const ColorFilter.mode(
                        AppColors.azkarColor,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
      ],
    );
  }

  // بناء شريط التطبيق المخصص المحسن
  Widget _buildAppBar(bool isDarkMode) {
    return SliverAppBar(
      expandedHeight:
          MediaQuery.of(context).size.height * 0.16, // ارتفاع نسبي للشاشة
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(
              sigmaX: _isScrolled ? 15 : 0, sigmaY: _isScrolled ? 15 : 0),
          child: FlexibleSpaceBar(
            titlePadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            title: FadeTransition(
              opacity: _fadeAnimation,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                padding: EdgeInsets.symmetric(
                  horizontal: _isScrolled ? 0 : 6,
                  vertical: _isScrolled ? 0 : 4,
                ),
                decoration: BoxDecoration(
                  color: _isScrolled
                      ? Colors.transparent
                      : isDarkMode
                          ? Colors.black.withAlpha(20) // 0.08 * 255 = ~20
                          : Colors.white.withAlpha(77), // 0.3 * 255 = ~77
                  borderRadius: BorderRadius.circular(_isScrolled ? 0 : 12),
                ),
                child: Row(
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Row(
                        textDirection: TextDirection
                            .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                        children: [
                          if (!_isScrolled)
                            Container(
                              padding: const EdgeInsets.all(6),
                              margin: const EdgeInsets.only(
                                  left: 8), // تغيير الهامش من اليمين إلى اليسار
                              decoration: BoxDecoration(
                                color: AppColors.azkarColor.withAlpha(
                                    isDarkMode ? 51 : 38), // 0.2/0.15 * 255
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.wb_sunny_outlined,
                                color: AppColors.azkarColor,
                                size: 16,
                              ),
                            ),
                          Flexible(
                            child: Text(
                              'وهج السالك',
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                color: isDarkMode
                                    ? Colors.white
                                    : AppColors.azkarColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 20,
                                letterSpacing: 0.5,
                                shadows: _isScrolled
                                    ? null
                                    : [
                                        Shadow(
                                          color: AppColors.azkarColor
                                              .withAlpha(77), // 0.3 * 255 = ~77
                                          blurRadius: 10,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                              ),
                              textDirection: TextDirection
                                  .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                              textAlign:
                                  TextAlign.right, // محاذاة النص إلى اليمين
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      width: 68,
                      child: Row(
                        textDirection: TextDirection
                            .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(
                            width: 32,
                            height: 32,
                            child: IconButton(
                              padding: EdgeInsets.zero,
                              icon: const Icon(
                                Icons.search,
                                color: AppColors.azkarColor,
                                size: 20,
                              ),
                              onPressed: () {
                                HapticFeedback.selectionClick();
                                _showEnhancedSearch(context);
                              },
                            ),
                          ),
                          const SizedBox(width: 2),
                          SizedBox(
                            width: 32,
                            height: 32,
                            child: IconButton(
                              padding: EdgeInsets.zero,
                              icon: const Icon(
                                Icons.settings,
                                color: AppColors.azkarColor,
                                size: 20,
                              ),
                              onPressed: () {
                                HapticFeedback.lightImpact();
                                Navigator.pushNamed(context, '/settings');
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            background: SizedBox(
              width: double.infinity,
              child: SlideTransition(
                position: _slideAnimation,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Stack(
                    children: [
                      // زخرفة صغيرة متحركة
                      Positioned(
                        top: MediaQuery.of(context).size.height * 0.05,
                        left: 20,
                        child: AnimatedBuilder(
                          animation: _patternAnimationController,
                          builder: (context, child) {
                            return Transform.rotate(
                              angle: 0.02 *
                                  math.pi *
                                  math.sin(_patternAnimationController.value *
                                      math.pi *
                                      2),
                              child: Opacity(
                                opacity: isDarkMode ? 0.1 : 0.18,
                                child: SvgPicture.asset(
                                  'assets/images/p2.svg',
                                  width: 40,
                                  height: 40,
                                  colorFilter: const ColorFilter.mode(
                                    AppColors.azkarColor,
                                    BlendMode.srcIn,
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),

                      // ترحيب بالمستخدم محسن - متوافق مع اتجاه اللغة العربية
                      Positioned(
                        top: MediaQuery.of(context).size.height * 0.05,
                        right: 20, // نحافظ على الموضع من اليمين للغة العربية
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: isDarkMode
                                ? Colors.black.withAlpha(51) // 0.2 * 255 = ~51
                                : Colors.white
                                    .withAlpha(128), // 0.5 * 255 = 128
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: isDarkMode
                                    ? Colors.black
                                        .withAlpha(51) // 0.2 * 255 = ~51
                                    : Colors.black
                                        .withAlpha(13), // 0.05 * 255 = ~13
                                blurRadius: 10,
                                offset: const Offset(0, 3),
                              ),
                            ],
                          ),
                          child: Row(
                            textDirection: TextDirection
                                .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                            children: [
                              Icon(
                                _getGreetingIcon(),
                                color: AppColors.azkarColor,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                _getGreeting(),
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: isDarkMode
                                      ? Colors.white
                                      : AppColors.azkarColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // قسم الحكمة اليومية المحسن مع تأثيرات متقدمة
  Widget _buildEnhancedWisdomSection(bool isDarkMode) {
    return AnimatedBuilder(
      animation: _wisdomAnimationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
            0,
            20 * (1 - _wisdomAnimationController.value),
          ),
          child: Opacity(
            opacity: _wisdomAnimationController.value,
            child: Card(
              elevation: 8,
              shadowColor:
                  AppColors.azkarColor.withAlpha(102), // 0.4 * 255 = ~102
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
                side: BorderSide(
                  color: AppColors.azkarColor.withAlpha(51), // 0.2 * 255 = ~51
                  width: 1,
                ),
              ),
              color: isDarkMode ? const Color(0xFF182530) : Colors.white,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  gradient: LinearGradient(
                    begin: Alignment.topRight,
                    end: Alignment.bottomLeft,
                    colors: isDarkMode
                        ? [
                            const Color(0xFF1D2D3A),
                            const Color(0xFF182530),
                          ]
                        : [
                            Colors.white,
                            AppColors.azkarColor
                                .withAlpha(13), // 0.05 * 255 = ~13
                          ],
                  ),
                ),
                child: Stack(
                  children: [
                    // زخرفة إسلامية متحركة
                    _buildWisdomPatterns(isDarkMode),
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                  color: AppColors.azkarColor
                                      .withAlpha(38), // 0.15 * 255 = ~38
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: AppColors.azkarColor
                                          .withAlpha(26), // 0.1 * 255 = ~26
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: const Icon(
                                  Icons.format_quote,
                                  color: AppColors.azkarColor,
                                  size: 22,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'حكمة اليوم',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                  color: isDarkMode
                                      ? Colors.white
                                      : Colors.black87,
                                ),
                              ),
                              const Spacer(),
                              // زر تحديث الحكمة
                              TweenAnimationBuilder<double>(
                                tween: Tween<double>(begin: 0, end: 1),
                                duration: const Duration(milliseconds: 800),
                                curve: Curves.elasticOut,
                                builder: (context, value, child) {
                                  return Transform.scale(
                                    scale: value,
                                    child: IconButton(
                                      icon: const Icon(
                                        Icons.refresh_rounded,
                                        color: AppColors.azkarColor,
                                      ),
                                      tooltip: 'تحديث الحكمة',
                                      onPressed: () {
                                        HapticFeedback.mediumImpact();
                                        setState(() {
                                          // تحديث الحكمة بشكل عشوائي
                                          final random = math.Random();
                                          _wisdomQuotes.then((wisdoms) {
                                            if (wisdoms.isNotEmpty) {
                                              final randomIndex = random
                                                  .nextInt(wisdoms.length);
                                              _pageController?.animateToPage(
                                                randomIndex,
                                                duration: const Duration(
                                                    milliseconds: 500),
                                                curve: Curves.easeInOut,
                                              );
                                            }
                                          });
                                        });
                                      },
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(
                                vertical: 16, horizontal: 20),
                            decoration: BoxDecoration(
                              color: isDarkMode
                                  ? Colors.black
                                      .withAlpha(38) // 0.15 * 255 = ~38
                                  : AppColors.azkarColor
                                      .withAlpha(13), // 0.05 * 255 = ~13
                              borderRadius: BorderRadius.circular(15),
                              border: Border.all(
                                color: AppColors.azkarColor.withAlpha(isDarkMode
                                    ? 38
                                    : 26), // 0.15/0.1 * 255 = ~38/26
                                width: 1,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.azkarColor
                                      .withAlpha(8), // 0.03 * 255 = ~8
                                  blurRadius: 8,
                                  offset: const Offset(0, 3),
                                ),
                              ],
                            ),
                            child: FutureBuilder<List<DailyWisdom>>(
                              future: _wisdomQuotes,
                              builder: (context, snapshot) {
                                if (snapshot.connectionState ==
                                    ConnectionState.waiting) {
                                  return const Center(
                                    child: CircularProgressIndicator(
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                          AppColors.azkarColor),
                                    ),
                                  );
                                } else if (snapshot.hasError) {
                                  return Text(
                                    'عذراً، حدث خطأ في تحميل الحكمة',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      color: isDarkMode
                                          ? Colors.white70
                                          : Colors.black87,
                                    ),
                                  );
                                } else if (!snapshot.hasData ||
                                    snapshot.data!.isEmpty) {
                                  return Text(
                                    'لا توجد حكم متاحة',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      color: isDarkMode
                                          ? Colors.white70
                                          : Colors.black87,
                                    ),
                                  );
                                } else {
                                  return Column(
                                    children: [
                                      SizedBox(
                                        height: 150,
                                        child: PageView.builder(
                                          controller: _pageController,
                                          itemCount: snapshot.data?.length ?? 0,
                                          onPageChanged: (index) {
                                            setState(() {
                                              _currentPage = index;
                                            });
                                          },
                                          physics:
                                              const ClampingScrollPhysics(),
                                          itemBuilder: (context, index) {
                                            final wisdom =
                                                snapshot.data?[index] ??
                                                    DailyWisdom(
                                                      text:
                                                          'كن مع الله يكن معك',
                                                      author: 'حكمة إسلامية',
                                                    );
                                            return InkWell(
                                              onTap: () {
                                                // تأثير بسيط عند النقر
                                                HapticFeedback.lightImpact();

                                                // عرض نموذج فاخر للحكمة مع الشرح
                                                _showWisdomExplanationModal(
                                                    context,
                                                    wisdom,
                                                    isDarkMode);
                                              },
                                              child: Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Text(
                                                    wisdom.text,
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                      fontSize: 20,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      height: 1.5,
                                                      color: isDarkMode
                                                          ? Colors.white
                                                          : Colors.black87,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 12),
                                                  Align(
                                                    alignment:
                                                        Alignment.centerLeft,
                                                    child: Container(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 12,
                                                          vertical: 6),
                                                      decoration: BoxDecoration(
                                                        color: AppColors
                                                            .azkarColor
                                                            .withAlpha(
                                                                38), // 0.15 * 255 = ~38
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(20),
                                                      ),
                                                      child: Text(
                                                        '- ${wisdom.author}',
                                                        style: const TextStyle(
                                                          color: AppColors
                                                              .azkarColor,
                                                          fontStyle:
                                                              FontStyle.italic,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                      const SizedBox(height: 10),
                                      // مؤشر الصفحات المحسن مع نقاط قابلة للنقر
                                      Container(
                                        margin: const EdgeInsets.only(top: 5),
                                        height: 20,
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: List.generate(
                                            (snapshot.data?.length ?? 0)
                                                .clamp(0, 10),
                                            (index) => GestureDetector(
                                              onTap: () {
                                                _pageController?.animateToPage(
                                                  index,
                                                  duration: const Duration(
                                                      milliseconds: 300),
                                                  curve: Curves.easeInOut,
                                                );
                                                HapticFeedback.selectionClick();
                                              },
                                              child: AnimatedContainer(
                                                duration: const Duration(
                                                    milliseconds: 300),
                                                width: _currentPage == index
                                                    ? 24
                                                    : 8,
                                                height: 8,
                                                margin:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 3),
                                                decoration: BoxDecoration(
                                                  color: _currentPage == index
                                                      ? AppColors.azkarColor
                                                      : AppColors.azkarColor
                                                          .withAlpha(
                                                              77), // 0.3 * 255 = ~77
                                                  borderRadius:
                                                      BorderRadius.circular(4),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  );
                                }
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // زخارف إسلامية متحركة داخل قسم الحكمة
  Widget _buildWisdomPatterns(bool isDarkMode) {
    return SizedBox(
      width: double.infinity,
      height: 200, // تحديد ارتفاع محدد للـ Stack
      child: Stack(
        children: [
          // الزخرفة الأولى
          Positioned(
            right: -40,
            top: -40,
            child: AnimatedBuilder(
              animation: _patternAnimationController,
              builder: (context, child) {
                return Transform.rotate(
                  angle: 0.05 *
                      math.pi *
                      math.sin(_patternAnimationController.value * math.pi),
                  child: Opacity(
                    opacity: isDarkMode ? 0.05 : 0.07,
                    child: SvgPicture.asset(
                      'assets/images/p2.svg',
                      width: 150,
                      height: 150,
                      colorFilter: const ColorFilter.mode(
                        AppColors.azkarColor,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

          // الزخرفة الثانية - زخرفة أصغر في الزاوية السفلية
          Positioned(
            left: -30,
            bottom: -30,
            child: AnimatedBuilder(
              animation: _patternAnimationController,
              builder: (context, child) {
                return Transform.rotate(
                  angle: -0.08 *
                      math.pi *
                      math.sin(
                          _patternAnimationController.value * math.pi * 0.7),
                  child: Opacity(
                    opacity: isDarkMode ? 0.04 : 0.05,
                    child: SvgPicture.asset(
                      'assets/images/p2.svg',
                      width: 100,
                      height: 100,
                      colorFilter: const ColorFilter.mode(
                        AppColors.azkarColor,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // قسم الأقسام بتأثيرات متحركة وتحديثات متقدمة - تصميم أفقي فاخر قابل للتقليب
  Widget _buildCategoriesSection(bool isDarkMode) {
    return AnimatedBuilder(
      animation: _categoryAnimationController,
      builder: (context, child) {
        return Opacity(
          opacity: _categoryAnimationController.value,
          child: Transform.translate(
            offset: Offset(
              0,
              30 * (1 - _categoryAnimationController.value),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // عنوان القسم مع تأثيرات متقدمة
                Padding(
                  padding: const EdgeInsets.only(right: 4.0, bottom: 16.0),
                  child: Row(
                    textDirection: TextDirection
                        .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                    children: [
                      // شريط ملون مع تأثير نبض
                      TweenAnimationBuilder<double>(
                        tween: Tween<double>(begin: 0.8, end: 1.0),
                        duration: const Duration(seconds: 2),
                        curve: Curves.easeInOut,
                        builder: (context, value, child) {
                          return Transform.scale(
                            scaleY: value,
                            alignment: Alignment.center,
                            child: Container(
                              width: 4,
                              height: 24,
                              decoration: BoxDecoration(
                                color: AppColors.azkarColor,
                                borderRadius: BorderRadius.circular(2),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.azkarColor
                                        .withAlpha(77), // 0.3 * 255 = ~77
                                    blurRadius: 4,
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                      const SizedBox(width: 8),
                      // عنوان القسم مع تأثير ظهور تدريجي
                      ShaderMask(
                        shaderCallback: (bounds) {
                          return LinearGradient(
                            colors: [
                              AppColors.azkarColor,
                              isDarkMode ? Colors.white : Colors.black87,
                            ],
                            begin: Alignment
                                .topRight, // تغيير اتجاه التدرج ليتناسب مع اللغة العربية
                            end: Alignment.bottomLeft,
                          ).createShader(bounds);
                        },
                        child: const Text(
                          'الأقسام',
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: Colors
                                .white, // سيتم تجاهل هذا اللون بسبب ShaderMask
                          ),
                          textDirection: TextDirection
                              .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                          textAlign: TextAlign.right, // محاذاة النص إلى اليمين
                        ),
                      ),
                      const Spacer(),
                      // زر لعرض كل الأقسام
                      AnimatedOpacity(
                        opacity: _categoryAnimationController.value,
                        duration: const Duration(milliseconds: 500),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: AppColors.azkarColor
                                .withAlpha(26), // 0.1 * 255 = ~26
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Row(
                            textDirection: TextDirection
                                .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.swipe_rounded,
                                color: AppColors.azkarColor,
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'اسحب للتصفح',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: isDarkMode
                                      ? Colors.white
                                      : AppColors.azkarColor,
                                ),
                                textDirection: TextDirection
                                    .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                textAlign:
                                    TextAlign.right, // محاذاة النص إلى اليمين
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // قائمة الأقسام القابلة للتقليب مع تأثيرات متقدمة
                SwipeableCategories(
                  isDarkMode: isDarkMode,
                  onPageChanged: (index) {
                    // يمكن إضافة أي إجراء عند تغيير الصفحة
                    HapticFeedback.selectionClick();
                  },
                  categories: [
                    // الأذكار - متاح حالياً
                    CategoryItem(
                      title: 'الأذكار',
                      subtitle: 'أذكار الصباح والمساء وأذكار متنوعة',
                      icon: Icons.favorite,
                      color: AppColors.azkarColor,
                      route: '/azkar',
                    ),

                    // المسبحة - متاح حالياً
                    CategoryItem(
                      title: 'المسبحة',
                      subtitle: 'مسبحة إلكترونية مع إحصائيات وأوراد',
                      icon: Icons.panorama_fish_eye,
                      color: AppColors.tasbihColor,
                      route: AppConstants.tasbihRoute,
                    ),

                    // الأدعية - متاح حالياً
                    CategoryItem(
                      title: 'الأدعية',
                      subtitle: 'أدعية من القرآن والسنة النبوية',
                      icon: Icons.auto_awesome,
                      color: AppColors.getDuasColor(isDarkMode),
                      route: AppConstants.duasRoute,
                    ),

                    // الصلاة على النبي - متاح حالياً
                    CategoryItem(
                      title: 'الصلاة على النبي',
                      subtitle: 'صيغ الصلاة على النبي محمد ﷺ',
                      icon: Icons.star,
                      color: AppColors.getProphetPrayersColor(isDarkMode),
                      route: AppConstants.prophetPrayersRoute,
                    ),

                    // المفضلة - متاح حالياً
                    CategoryItem(
                      title: 'المفضلة',
                      subtitle: 'الأذكار والأدعية المفضلة لديك',
                      icon: Icons.bookmark,
                      color: AppColors.favoritesColor,
                      route: AppConstants.favoritesRoute,
                    ),

                    // الكتب - قريباً (معطل مؤقتاً)
                    CategoryItem(
                      title: 'الكتب',
                      subtitle: 'مكتبة إسلامية متنوعة',
                      icon: Icons.book,
                      color: AppColors.booksColor,
                      isComingSoon: true,
                    ),

                    // القصائد - قريباً (معطل مؤقتاً)
                    CategoryItem(
                      title: 'القصائد',
                      subtitle: 'قصائد ومدائح نبوية',
                      icon: Icons.music_note,
                      color: AppColors.poemsColor,
                      isComingSoon: true,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // قسم النشاط الأخير المحسن
  Widget _buildRecentActivitySection(bool isDarkMode) {
    return AnimatedBuilder(
      animation: _categoryAnimationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
            0,
            40 * (1 - _categoryAnimationController.value),
          ),
          child: Opacity(
            opacity: _categoryAnimationController.value,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(right: 4.0, bottom: 16.0),
                  child: Row(
                    textDirection: TextDirection
                        .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                    children: [
                      Container(
                        width: 4,
                        height: 24,
                        decoration: BoxDecoration(
                          color: AppColors.favoritesColor,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'نشاطك الأخير',
                        style: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: isDarkMode ? Colors.white : Colors.black87,
                        ),
                        textDirection: TextDirection
                            .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                        textAlign: TextAlign.right, // محاذاة النص إلى اليمين
                      ),
                    ],
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: isDarkMode ? const Color(0xFF1A2530) : Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black
                            .withAlpha(isDarkMode ? 51 : 20), // 0.2/0.08 * 255
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                    border: Border.all(
                      color: isDarkMode
                          ? Colors.white.withAlpha(13) // 0.05 * 255 = ~13
                          : Colors.grey.withAlpha(26), // 0.1 * 255 = ~26
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      FutureBuilder<List<ActivityData>>(
                        future: ActivityTracker.getRecentActivities(limit: 3),
                        builder: (context, snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            return Column(
                              children: [
                                _buildEnhancedActivityItem(
                                  title: 'جاري التحميل...',
                                  section: 'الأذكار',
                                  icon: Icons.favorite,
                                  color: AppColors.azkarColor,
                                  time: 'الآن',
                                  isDarkMode: isDarkMode,
                                  onTap: () {},
                                ),
                                Divider(
                                  height: 1,
                                  indent: 70,
                                  color: isDarkMode
                                      ? Colors.white
                                          .withAlpha(26) // 0.1 * 255 = ~26
                                      : Colors.grey
                                          .withAlpha(51), // 0.2 * 255 = ~51
                                ),
                                _buildEnhancedActivityItem(
                                  title: 'جاري التحميل...',
                                  section: 'المسبحة',
                                  icon: Icons.panorama_fish_eye,
                                  color: AppColors.tasbihColor,
                                  time: 'الآن',
                                  isDarkMode: isDarkMode,
                                  onTap: () {},
                                ),
                                Divider(
                                  height: 1,
                                  indent: 70,
                                  color: isDarkMode
                                      ? Colors.white
                                          .withAlpha(26) // 0.1 * 255 = ~26
                                      : Colors.grey
                                          .withAlpha(51), // 0.2 * 255 = ~51
                                ),
                                _buildEnhancedActivityItem(
                                  title: 'جاري التحميل...',
                                  section: 'ورد',
                                  icon: Icons.auto_awesome_rounded,
                                  color: AppColors.tasbihColor,
                                  time: 'الآن',
                                  isDarkMode: isDarkMode,
                                  onTap: () {},
                                ),
                              ],
                            );
                          } else if (snapshot.hasError) {
                            return _buildEnhancedActivityItem(
                              title: 'حدث خطأ في تحميل النشاطات',
                              section: 'خطأ',
                              icon: Icons.error_outline,
                              color: Colors.red,
                              time: 'الآن',
                              isDarkMode: isDarkMode,
                              onTap: () {},
                            );
                          } else if (!snapshot.hasData ||
                              snapshot.data!.isEmpty) {
                            return Column(
                              children: [
                                _buildEnhancedActivityItem(
                                  title: 'ورد الصباح',
                                  section: 'الأذكار',
                                  icon: Icons.wb_sunny_rounded,
                                  color: AppColors.azkarColor,
                                  time: 'اليوم',
                                  isDarkMode: isDarkMode,
                                  onTap: () {
                                    Navigator.pushNamed(context, '/azkar');
                                  },
                                ),
                                Divider(
                                  height: 1,
                                  indent: 70,
                                  color: isDarkMode
                                      ? Colors.white
                                          .withAlpha(26) // 0.1 * 255 = ~26
                                      : Colors.grey
                                          .withAlpha(51), // 0.2 * 255 = ~51
                                ),
                                _buildEnhancedActivityItem(
                                  title: 'المسبحة',
                                  section: 'تسبيح',
                                  icon: Icons.panorama_fish_eye,
                                  color: AppColors.tasbihColor,
                                  time: 'اليوم',
                                  isDarkMode: isDarkMode,
                                  onTap: () {
                                    Navigator.pushNamed(
                                        context, AppConstants.tasbihRoute);
                                  },
                                ),
                                Divider(
                                  height: 1,
                                  indent: 70,
                                  color: isDarkMode
                                      ? Colors.white
                                          .withAlpha(26) // 0.1 * 255 = ~26
                                      : Colors.grey
                                          .withAlpha(51), // 0.2 * 255 = ~51
                                ),
                                _buildEnhancedActivityItem(
                                  title: 'أدعية القرآن',
                                  section: 'الأدعية',
                                  icon: Icons.auto_awesome,
                                  color: AppColors.getDuasColor(isDarkMode),
                                  time: 'اليوم',
                                  isDarkMode: isDarkMode,
                                  onTap: () {
                                    Navigator.pushNamed(
                                        context, AppConstants.duasRoute);
                                  },
                                ),
                              ],
                            );
                          } else {
                            final activities = snapshot.data!;
                            return Column(
                              children: [
                                for (int i = 0; i < activities.length; i++) ...[
                                  _buildEnhancedActivityItem(
                                    title: activities[i].title,
                                    section: activities[i].section,
                                    icon: activities[i].icon,
                                    color: activities[i].color,
                                    time: ActivityTracker.getFormattedTime(
                                        activities[i].timestamp),
                                    isDarkMode: isDarkMode,
                                    progress: activities[i].progress,
                                    count: activities[i].count,
                                    onTap: () {
                                      Navigator.pushNamed(
                                          context, activities[i].route);
                                    },
                                  ),
                                  if (i < activities.length - 1)
                                    Divider(
                                      height: 1,
                                      indent: 70,
                                      color: isDarkMode
                                          ? Colors.white
                                              .withAlpha(26) // 0.1 * 255 = ~26
                                          : Colors.grey
                                              .withAlpha(51), // 0.2 * 255 = ~51
                                    ),
                                ],
                              ],
                            );
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // عنصر نشاط محسن مع تأثيرات متقدمة - متوافق مع اتجاه اللغة العربية
  Widget _buildEnhancedActivityItem({
    required String title,
    required String section,
    required IconData icon,
    required Color color,
    required String time,
    required bool isDarkMode,
    required VoidCallback onTap,
    double? progress,
    String? count,
  }) {
    return InkWell(
      onTap: () {
        onTap();
        HapticFeedback.lightImpact();
      },
      borderRadius: BorderRadius.circular(16),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          textDirection:
              TextDirection.rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color
                    .withAlpha(isDarkMode ? 51 : 38), // 0.2/0.15 * 255 = ~51/38
                borderRadius: BorderRadius.circular(14),
                boxShadow: [
                  BoxShadow(
                    color: color.withAlpha(26), // 0.1 * 255 = ~26
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment:
                    CrossAxisAlignment.start, // نحافظ على محاذاة النص من اليمين
                textDirection: TextDirection
                    .rtl, // تحديد اتجاه العمود من اليمين إلى اليسار
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                    textDirection: TextDirection
                        .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                    textAlign: TextAlign.right, // محاذاة النص إلى اليمين
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '$section • $time',
                    style: TextStyle(
                      color: isDarkMode
                          ? Colors.white.withAlpha(153) // 0.6 * 255 = ~153
                          : Colors.black87.withAlpha(153), // 0.6 * 255 = ~153
                      fontSize: 13,
                    ),
                    textDirection: TextDirection
                        .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                    textAlign: TextAlign.right, // محاذاة النص إلى اليمين
                  ),
                  if (progress != null) ...[
                    const SizedBox(height: 6),
                    ClipRRect(
                      borderRadius: BorderRadius.circular(4),
                      child: LinearProgressIndicator(
                        value: progress,
                        minHeight: 4,
                        backgroundColor: isDarkMode
                            ? Colors.grey.withAlpha(51) // 0.2 * 255 = ~51
                            : Colors.grey.withAlpha(26), // 0.1 * 255 = ~26
                        valueColor: AlwaysStoppedAnimation<Color>(
                          color,
                        ),
                      ),
                    ),
                  ],
                  if (count != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      count,
                      style: TextStyle(
                        color: color,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                      textDirection: TextDirection
                          .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                      textAlign: TextAlign.right, // محاذاة النص إلى اليمين
                    ),
                  ],
                ],
              ),
            ),
            Icon(
              Icons
                  .arrow_back_ios, // تغيير اتجاه السهم ليتناسب مع اتجاه اللغة العربية
              size: 16,
              color: color,
            ),
          ],
        ),
      ),
    );
  }

  // قسم المسبحة المحسن مع تأثيرات متقدمة
  Widget _buildTasbihSection(bool isDarkMode) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 600),
      curve: Curves.easeOutCubic,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 50 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: GestureDetector(
              onTap: () {
                Navigator.pushNamed(context, AppConstants.tasbihRoute);
                HapticFeedback.mediumImpact();
              },
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? AppColors.azkarColor.withAlpha(38) // 0.15 * 255 = ~38
                      : AppColors.azkarColor.withAlpha(26), // 0.1 * 255 = ~26
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color:
                          AppColors.azkarColor.withAlpha(26), // 0.1 * 255 = ~26
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                  border: Border.all(
                    color:
                        AppColors.azkarColor.withAlpha(77), // 0.3 * 255 = ~77
                    width: 1.5,
                  ),
                ),
                child: Stack(
                  children: [
                    // زخرفة إسلامية
                    Positioned(
                      right: -20,
                      top: -20,
                      child: Opacity(
                        opacity: isDarkMode ? 0.1 : 0.07,
                        child: SvgPicture.asset(
                          'assets/images/p2.svg',
                          width: 80,
                          height: 80,
                          colorFilter: const ColorFilter.mode(
                            AppColors.azkarColor,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),
                    Row(
                      textDirection: TextDirection
                          .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                      children: [
                        // أيقونة المسبحة المتحركة
                        TweenAnimationBuilder<double>(
                          tween: Tween<double>(begin: 0.9, end: 1.1),
                          duration: const Duration(seconds: 2),
                          curve: Curves.easeInOut,
                          builder: (context, scale, child) {
                            return Transform.scale(
                              scale: scale,
                              child: Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  color: AppColors.azkarColor.withAlpha(
                                      isDarkMode
                                          ? 51
                                          : 38), // 0.2/0.15 * 255 = ~51/38
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: AppColors.azkarColor
                                          .withAlpha(51), // 0.2 * 255 = ~51
                                      blurRadius: 8,
                                      offset: const Offset(0, 4),
                                    ),
                                  ],
                                ),
                                child: const Icon(
                                  Icons.panorama_fish_eye,
                                  size: 32,
                                  color: AppColors.azkarColor,
                                ),
                              ),
                            );
                          },
                        ),
                        const SizedBox(width: 20),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            textDirection: TextDirection
                                .rtl, // تحديد اتجاه العمود من اليمين إلى اليسار
                            children: [
                              Text(
                                'المسبحة الإلكترونية',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: isDarkMode
                                      ? Colors.white
                                      : Colors.black87,
                                ),
                                textDirection: TextDirection
                                    .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                textAlign:
                                    TextAlign.right, // محاذاة النص إلى اليمين
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'سبح وأنت في راحة تامة مع المسبحة الإلكترونية',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: isDarkMode
                                      ? Colors.white
                                          .withAlpha(179) // 0.7 * 255 = ~179
                                      : Colors.black54,
                                ),
                                textDirection: TextDirection
                                    .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                textAlign:
                                    TextAlign.right, // محاذاة النص إلى اليمين
                              ),
                            ],
                          ),
                        ),
                        const Icon(
                          Icons
                              .arrow_back_ios, // تغيير اتجاه السهم ليتناسب مع اتجاه اللغة العربية
                          size: 18,
                          color: AppColors.azkarColor,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // قسم الأقسام القادمة مع تأثيرات متقدمة
  Widget _buildComingSoonSection(bool isDarkMode) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 600),
      curve: Curves.easeOutCubic,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 60 * (1 - value)),
          child: Opacity(
            opacity: value * 0.9,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isDarkMode
                    ? Colors.white.withAlpha(13) // 0.05 * 255 = ~13
                    : Colors.grey.withAlpha(13), // 0.05 * 255 = ~13
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: isDarkMode
                      ? Colors.white.withAlpha(26) // 0.1 * 255 = ~26
                      : Colors.grey.withAlpha(51), // 0.2 * 255 = ~51
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.amber.withAlpha(26), // 0.1 * 255 = ~26
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Icon(
                          Icons.info_outline,
                          color: Colors.amber,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'أقسام قادمة قريباً',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: isDarkMode ? Colors.white : Colors.black87,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'نعمل حالياً على تطوير أقسام جديدة مثل الكتب والقصائد لإثراء تجربتك مع التطبيق. ترقب المزيد من المميزات قريباً!',
                    style: TextStyle(
                      fontSize: 14,
                      height: 1.5,
                      color: isDarkMode
                          ? Colors.white.withAlpha(179) // 0.7 * 255 = ~179
                          : Colors.black54,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // شريط أدوات عائم يظهر عند التمرير
  Widget _buildFloatingToolbar(bool isDarkMode) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: 60 + MediaQuery.of(context).padding.top,
            padding: EdgeInsets.only(
              top: MediaQuery.of(context).padding.top,
              left: 12,
              right: 12,
              bottom: 4,
            ),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? AppColors.azkarColor.withAlpha(26) // 0.1 * 255 = ~26
                  : Colors.white.withAlpha(204), // 0.8 * 255 = ~204
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(13), // 0.05 * 255 = ~13
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
              border: Border(
                bottom: BorderSide(
                  color: AppColors.azkarColor.withAlpha(26), // 0.1 * 255 = ~26
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.home,
                      color: AppColors.azkarColor,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'وهج السالك',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                    ),
                  ],
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: 40,
                      height: 40,
                      child: IconButton(
                        padding: EdgeInsets.zero,
                        icon: const Icon(
                          Icons.search,
                          color: AppColors.azkarColor,
                          size: 22,
                        ),
                        onPressed: () {
                          _showEnhancedSearch(context);
                        },
                      ),
                    ),
                    const SizedBox(width: 4),
                    SizedBox(
                      width: 40,
                      height: 40,
                      child: IconButton(
                        padding: EdgeInsets.zero,
                        icon: const Icon(
                          Icons.settings,
                          color: AppColors.azkarColor,
                          size: 22,
                        ),
                        onPressed: () {
                          Navigator.pushNamed(context, '/settings');
                          HapticFeedback.lightImpact();
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// فئة للتأثيرات المتحركة على الزخارف الإسلامية
class IslamicPatternPainter extends CustomPainter {
  final Animation<double> animation;
  final Color color;
  final bool isDarkMode;

  IslamicPatternPainter({
    required this.animation,
    required this.color,
    required this.isDarkMode,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
          .withAlpha(isDarkMode ? 26 : 13) // 0.1 * 255 = ~26, 0.05 * 255 = ~13
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    final centerX = size.width / 2;
    final centerY = size.height / 2;
    final maxRadius = math.min(size.width, size.height) * 0.4;

    // رسم أنماط دائرية متموجة
    for (int i = 0; i < 5; i++) {
      final radius = maxRadius *
          (0.5 + i * 0.1) *
          (0.9 + 0.1 * math.sin(animation.value * math.pi * 2));
      canvas.drawCircle(Offset(centerX, centerY), radius, paint);
    }

    // رسم نمط نجمي
    final starPath = Path();
    const points = 8;
    const angleStep = 2 * math.pi / points;
    final innerRadius = maxRadius *
        0.4 *
        (0.9 + 0.1 * math.sin(animation.value * math.pi * 2 + math.pi / 2));
    final outerRadius =
        maxRadius * 0.7 * (0.9 + 0.1 * math.sin(animation.value * math.pi * 2));

    for (int i = 0; i < points; i++) {
      final outerAngle = i * angleStep;
      final innerAngle = outerAngle + angleStep / 2;

      final outerX = centerX + math.cos(outerAngle) * outerRadius;
      final outerY = centerY + math.sin(outerAngle) * outerRadius;
      final innerX = centerX + math.cos(innerAngle) * innerRadius;
      final innerY = centerY + math.sin(innerAngle) * innerRadius;

      if (i == 0) {
        starPath.moveTo(outerX, outerY);
      } else {
        starPath.lineTo(outerX, outerY);
      }
      starPath.lineTo(innerX, innerY);
    }
    starPath.close();
    canvas.drawPath(starPath, paint);
  }

  @override
  bool shouldRepaint(IslamicPatternPainter oldDelegate) => true;
}

// رسام للنمط الخفيف للوضع الفاتح
class LightPatternPainter extends CustomPainter {
  final Animation<double> animation;
  final Color color;

  LightPatternPainter({
    required this.animation,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    // رسم نقاط متناثرة متوهجة
    final dotPaint = Paint()
      ..color = color.withAlpha(5) // شفافية منخفضة جداً
      ..style = PaintingStyle.fill;

    // رسم خطوط رقيقة متقاطعة
    final linePaint = Paint()
      ..color = color.withAlpha(3) // شفافية منخفضة جداً
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // رسم النقاط
    final random = math.Random(42); // قيمة ثابتة للبذرة للحصول على نمط ثابت
    for (int i = 0; i < 100; i++) {
      final x = random.nextDouble() * width;
      final y = random.nextDouble() * height;
      final radius = 1.0 +
          random.nextDouble() *
              2.0 *
              (0.8 + 0.2 * math.sin(animation.value * math.pi * 2 + i));
      canvas.drawCircle(Offset(x, y), radius, dotPaint);
    }

    // رسم شبكة خفيفة من الخطوط
    const gridSize = 100.0;
    for (double x = 0; x < width; x += gridSize) {
      final path = Path();
      path.moveTo(x, 0);
      path.lineTo(x, height);
      canvas.drawPath(path, linePaint);
    }

    for (double y = 0; y < height; y += gridSize) {
      final path = Path();
      path.moveTo(0, y);
      path.lineTo(width, y);
      canvas.drawPath(path, linePaint);
    }

    // رسم دوائر كبيرة متموجة
    final wavePaint = Paint()
      ..color = color.withAlpha(2) // شفافية منخفضة جداً
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    final centerX = width / 2;
    final centerY = height / 2;
    final maxRadius = math.max(width, height) * 0.8;

    for (int i = 0; i < 3; i++) {
      final radius = maxRadius *
          (0.5 + i * 0.25) *
          (0.95 + 0.05 * math.sin(animation.value * math.pi + i));
      canvas.drawCircle(Offset(centerX, centerY), radius, wavePaint);
    }
  }

  @override
  bool shouldRepaint(LightPatternPainter oldDelegate) => true;
}

// دالة لعرض نموذج شرح الحكمة بتصميم فاخر
void _showWisdomExplanationModal(
    BuildContext context, DailyWisdom wisdom, bool isDarkMode) {
  // تأكد من وجود شرح للحكمة
  final hasExplanation =
      wisdom.explanation != null && wisdom.explanation!.isNotEmpty;

  // قياسات الشاشة
  final screenSize = MediaQuery.of(context).size;
  final screenHeight = screenSize.height;

  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    barrierColor: Colors.black.withAlpha(150), // 0.6 * 255 = ~150
    builder: (context) {
      return StatefulBuilder(
        builder: (context, setState) {
          return BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              height: screenHeight * 0.7,
              decoration: BoxDecoration(
                color: isDarkMode ? const Color(0xFF1A2530) : Colors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(30),
                  topRight: Radius.circular(30),
                ),
                boxShadow: [
                  BoxShadow(
                    color:
                        AppColors.azkarColor.withAlpha(77), // 0.3 * 255 = ~77
                    blurRadius: 20,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  // زخارف إسلامية في الخلفية
                  Positioned(
                    top: -50,
                    right: -50,
                    child: Opacity(
                      opacity: isDarkMode ? 0.05 : 0.07,
                      child: SvgPicture.asset(
                        'assets/images/p2.svg',
                        width: 200,
                        height: 200,
                        colorFilter: const ColorFilter.mode(
                          AppColors.azkarColor,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: -30,
                    left: -30,
                    child: Opacity(
                      opacity: isDarkMode ? 0.04 : 0.06,
                      child: SvgPicture.asset(
                        'assets/images/p2.svg',
                        width: 150,
                        height: 150,
                        colorFilter: const ColorFilter.mode(
                          AppColors.azkarColor,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),

                  // المحتوى الرئيسي
                  Column(
                    children: [
                      // مقبض السحب
                      Container(
                        margin: const EdgeInsets.only(top: 12),
                        width: 40,
                        height: 5,
                        decoration: BoxDecoration(
                          color: isDarkMode
                              ? Colors.white.withAlpha(77) // 0.3 * 255 = ~77
                              : Colors.grey.withAlpha(77), // 0.3 * 255 = ~77
                          borderRadius: BorderRadius.circular(5),
                        ),
                      ),

                      // عنوان النموذج
                      Padding(
                        padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
                        child: Row(
                          textDirection: TextDirection
                              .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                          children: [
                            Container(
                              padding: const EdgeInsets.all(10),
                              decoration: BoxDecoration(
                                color: AppColors.azkarColor
                                    .withAlpha(38), // 0.15 * 255 = ~38
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Icon(
                                Icons.format_quote,
                                color: AppColors.azkarColor,
                                size: 24,
                              ),
                            ),
                            const SizedBox(width: 15),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment
                                    .end, // تغيير المحاذاة من اليسار إلى اليمين
                                children: [
                                  Text(
                                    'حكمة اليوم',
                                    style: TextStyle(
                                      fontSize: 22,
                                      fontWeight: FontWeight.bold,
                                      color: isDarkMode
                                          ? Colors.white
                                          : Colors.black87,
                                    ),
                                    textDirection: TextDirection
                                        .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                    textAlign: TextAlign
                                        .right, // محاذاة النص إلى اليمين
                                  ),
                                  Text(
                                    'من أقوال ${wisdom.author}',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: isDarkMode
                                          ? Colors.white70
                                          : Colors.black54,
                                    ),
                                    textDirection: TextDirection
                                        .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                    textAlign: TextAlign
                                        .right, // محاذاة النص إلى اليمين
                                  ),
                                ],
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.close,
                                  color: AppColors.azkarColor),
                              onPressed: () => Navigator.pop(context),
                            ),
                          ],
                        ),
                      ),

                      const Divider(),

                      // محتوى الحكمة والشرح
                      Expanded(
                        child: SingleChildScrollView(
                          physics: const BouncingScrollPhysics(),
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment
                                .end, // تغيير المحاذاة من اليسار إلى اليمين
                            children: [
                              // نص الحكمة بتصميم فاخر
                              Container(
                                width: double.infinity,
                                padding: const EdgeInsets.symmetric(
                                    vertical: 30, horizontal: 25),
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topRight,
                                    end: Alignment.bottomLeft,
                                    colors: isDarkMode
                                        ? [
                                            const Color(0xFF1D2D3A),
                                            const Color(0xFF182530),
                                          ]
                                        : [
                                            Colors.white,
                                            AppColors.azkarColor.withAlpha(
                                                13), // 0.05 * 255 = ~13
                                          ],
                                  ),
                                  borderRadius: BorderRadius.circular(20),
                                  boxShadow: [
                                    BoxShadow(
                                      color: AppColors.azkarColor
                                          .withAlpha(26), // 0.1 * 255 = ~26
                                      blurRadius: 10,
                                      offset: const Offset(0, 5),
                                    ),
                                  ],
                                  border: Border.all(
                                    color: AppColors.azkarColor
                                        .withAlpha(38), // 0.15 * 255 = ~38
                                    width: 1,
                                  ),
                                ),
                                child: Column(
                                  children: [
                                    // أيقونة علامة الاقتباس
                                    const Icon(
                                      Icons.format_quote,
                                      color: AppColors.azkarColor,
                                      size: 30,
                                    ),
                                    const SizedBox(height: 15),

                                    // نص الحكمة
                                    Text(
                                      wisdom.text,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontSize: 24,
                                        fontWeight: FontWeight.bold,
                                        height: 1.5,
                                        color: isDarkMode
                                            ? Colors.white
                                            : Colors.black87,
                                      ),
                                      textDirection: TextDirection
                                          .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                    ),

                                    const SizedBox(height: 20),

                                    // المؤلف
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 15, vertical: 8),
                                      decoration: BoxDecoration(
                                        color: AppColors.azkarColor
                                            .withAlpha(38), // 0.15 * 255 = ~38
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: Text(
                                        wisdom.author,
                                        style: const TextStyle(
                                          color: AppColors.azkarColor,
                                          fontWeight: FontWeight.w500,
                                          fontSize: 16,
                                        ),
                                        textDirection: TextDirection
                                            .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                        textAlign: TextAlign
                                            .center, // محاذاة النص في المنتصف
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // شرح الحكمة (إذا كان متوفراً)
                              if (hasExplanation) ...[
                                const SizedBox(height: 30),

                                // عنوان الشرح
                                Row(
                                  textDirection: TextDirection
                                      .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                                  children: [
                                    Container(
                                      width: 4,
                                      height: 20,
                                      decoration: BoxDecoration(
                                        color: AppColors.azkarColor,
                                        borderRadius: BorderRadius.circular(2),
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    Text(
                                      'شرح الحكمة',
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                        color: isDarkMode
                                            ? Colors.white
                                            : Colors.black87,
                                      ),
                                      textDirection: TextDirection
                                          .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                      textAlign: TextAlign
                                          .right, // محاذاة النص إلى اليمين
                                    ),
                                  ],
                                ),

                                const SizedBox(height: 15),

                                // نص الشرح
                                Container(
                                  width: double.infinity,
                                  padding: const EdgeInsets.all(20),
                                  decoration: BoxDecoration(
                                    color: isDarkMode
                                        ? Colors.black
                                            .withAlpha(38) // 0.15 * 255 = ~38
                                        : AppColors.azkarColor
                                            .withAlpha(13), // 0.05 * 255 = ~13
                                    borderRadius: BorderRadius.circular(15),
                                    border: Border.all(
                                      color: AppColors.azkarColor
                                          .withAlpha(26), // 0.1 * 255 = ~26
                                      width: 1,
                                    ),
                                  ),
                                  child: Text(
                                    wisdom.explanation!,
                                    style: TextStyle(
                                      fontSize: 18,
                                      height: 1.6,
                                      color: isDarkMode
                                          ? Colors.white
                                          : Colors.black87,
                                    ),
                                    textAlign: TextAlign.justify,
                                    textDirection: TextDirection
                                        .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                  ),
                                ),
                              ] else ...[
                                // رسالة في حالة عدم وجود شرح
                                const SizedBox(height: 30),
                                Container(
                                  width: double.infinity,
                                  padding: const EdgeInsets.all(20),
                                  decoration: BoxDecoration(
                                    color: isDarkMode
                                        ? Colors.black
                                            .withAlpha(38) // 0.15 * 255 = ~38
                                        : AppColors.azkarColor
                                            .withAlpha(13), // 0.05 * 255 = ~13
                                    borderRadius: BorderRadius.circular(15),
                                    border: Border.all(
                                      color: AppColors.azkarColor
                                          .withAlpha(26), // 0.1 * 255 = ~26
                                      width: 1,
                                    ),
                                  ),
                                  child: Column(
                                    children: [
                                      const Icon(
                                        Icons.info_outline,
                                        color: AppColors.azkarColor,
                                        size: 40,
                                      ),
                                      const SizedBox(height: 10),
                                      Text(
                                        'لا يوجد شرح متوفر لهذه الحكمة حالياً',
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          fontSize: 16,
                                          color: isDarkMode
                                              ? Colors.white70
                                              : Colors.black54,
                                        ),
                                        textDirection: TextDirection
                                            .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                      ),
                                    ],
                                  ),
                                ),
                              ],

                              const SizedBox(height: 30),

                              // زر مشاركة الحكمة
                              Center(
                                child: ElevatedButton.icon(
                                  onPressed: () async {
                                    HapticFeedback.mediumImpact();
                                    final textToShare = hasExplanation
                                        ? '${wisdom.text}\n\n${wisdom.explanation}\n\n- ${wisdom.author}'
                                        : '${wisdom.text}\n\n- ${wisdom.author}';

                                    // مشاركة النص باستخدام حزمة share_plus
                                    try {
                                      await Share.share(
                                        textToShare,
                                        subject: 'حكمة من ${wisdom.author}',
                                      );
                                    } catch (e) {
                                      // في حالة حدوث خطأ في المشاركة
                                      if (context.mounted) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                            content: Text(
                                                'حدث خطأ أثناء المشاركة: $e'),
                                            duration:
                                                const Duration(seconds: 2),
                                          ),
                                        );
                                      }
                                    }
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: AppColors.azkarColor,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 20, vertical: 12),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(15),
                                    ),
                                    elevation: 5,
                                  ),
                                  icon: const Icon(Icons.share),
                                  label: const Text(
                                    'مشاركة الحكمة',
                                    style: TextStyle(fontSize: 16),
                                    textDirection: TextDirection
                                        .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      );
    },
  );
}

/// عرض البحث المحسن
Future<void> _showEnhancedSearch(BuildContext context) async {
  // استخدام متغير محلي لتخزين السياق
  final currentContext = context;

  final result = await showSearch<SearchResult?>(
    context: currentContext,
    delegate: EnhancedSearchDelegate(
      showRecentSearches: true,
      showPopularSearches: true,
      enableVoiceSearch: true,
    ),
  );

  // التحقق من أن الـ widget لا يزال مرتبطاً بشجرة العناصر
  if (result != null && context.mounted) {
    // التنقل إلى نتيجة البحث
    if (result.isAvailable) {
      // التنقل بناءً على القسم
      switch (result.section) {
        case 'الأذكار':
          if (result.id == 'morning_azkar') {
            Navigator.pushNamed(
              context,
              AppConstants.azkarDetailsRoute,
              arguments: 'أذكار الصباح',
            );
          } else if (result.id == 'evening_azkar') {
            Navigator.pushNamed(
              context,
              AppConstants.azkarDetailsRoute,
              arguments: 'أذكار المساء',
            );
          } else {
            Navigator.pushNamed(context, AppConstants.azkarRoute);
          }
          break;
        case 'المسبحة':
          Navigator.pushNamed(context, AppConstants.tasbihRoute);
          break;
        case 'المفضلة':
          Navigator.pushNamed(context, AppConstants.favoritesRoute);
          break;
        default:
          Navigator.pushNamed(context, result.route);
      }
    } else {
      // إذا كانت النتيجة غير متاحة، أظهر رسالة
      _showComingSoonMessage(context, result.section);
    }
  }
}

/// عرض رسالة "قريباً"
void _showComingSoonMessage(BuildContext context, String section) {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Row(
        children: [
          const Icon(
            Icons.info_outline,
            color: Colors.white,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'قسم $section قيد التطوير وسيكون متاحاً قريباً',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
      backgroundColor: AppColors.azkarColor,
      duration: const Duration(seconds: 3),
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      margin: const EdgeInsets.all(12),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
    ),
  );
}
