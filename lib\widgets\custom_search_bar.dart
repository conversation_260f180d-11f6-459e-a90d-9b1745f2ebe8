import 'package:flutter/material.dart';
import '../utils/app_colors.dart';

class CustomSearchBar extends StatefulWidget {
  final ValueChanged<String> onChanged;
  final VoidCallback onClear;
  final String hintText;

  const CustomSearchBar({
    Key? key,
    required this.onChanged,
    required this.onClear,
    this.hintText = 'ابحث عن كتاب أو مؤلف...',
  }) : super(key: key);

  @override
  State<CustomSearchBar> createState() => _CustomSearchBarState();
}

class _CustomSearchBarState extends State<CustomSearchBar>
    with SingleTickerProviderStateMixin {
  final TextEditingController _controller = TextEditingController();
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _controller.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    widget.onChanged(_controller.text);
    setState(() {
      _isExpanded = _controller.text.isNotEmpty;
    });
    if (_isExpanded) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  void _clearSearch() {
    _controller.clear();
    widget.onClear();
    setState(() {
      _isExpanded = false;
    });
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(30),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(26),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة البحث
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Icon(
              Icons.search,
              color: _isExpanded ? AppColors.booksColor : Colors.grey,
            ),
          ),

          // حقل البحث
          Expanded(
            child: TextField(
              controller: _controller,
              decoration: InputDecoration(
                hintText: widget.hintText,
                border: InputBorder.none,
                hintStyle: const TextStyle(color: Colors.grey),
              ),
              textAlignVertical: TextAlignVertical.center,
            ),
          ),

          // زر المسح
          SizeTransition(
            sizeFactor: _expandAnimation,
            axis: Axis.horizontal,
            axisAlignment: -1,
            child: InkWell(
              onTap: _clearSearch,
              borderRadius: BorderRadius.circular(30),
              child: const Padding(
                padding:  EdgeInsets.all(12),
                child:  Icon(
                  Icons.close,
                  color: AppColors.booksColor,
                  size: 20,
                ),
              ),
            ),
          ),

          const SizedBox(width: 4),
        ],
      ),
    );
  }
}
