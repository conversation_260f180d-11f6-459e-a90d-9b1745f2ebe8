// مكون قائمة الصلوات على النبي بتصميم فاخر

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/dua.dart';
import '../utils/app_colors.dart';
import 'prophet_prayer_item.dart';

class ProphetPrayerList extends StatefulWidget {
  final List<Dua> prayers;
  final Animation<double> animation;
  final TextEditingController? searchController;
  final Function(String)? onSearchChanged;

  const ProphetPrayerList({
    Key? key,
    required this.prayers,
    required this.animation,
    this.searchController,
    this.onSearchChanged,
  }) : super(key: key);

  @override
  State<ProphetPrayerList> createState() => _ProphetPrayerListState();
}

class _ProphetPrayerListState extends State<ProphetPrayerList>
    with SingleTickerProviderStateMixin {
  List<Dua> _filteredPrayers = [];
  bool _isSearching = false;
  late AnimationController _listAnimationController;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();

    debugPrint('تهيئة ProphetPrayerList مع ${widget.prayers.length} صلاة');
    _filteredPrayers = widget.prayers;

    // تهيئة محرك الرسوم المتحركة للقائمة - تم تقليل المدة لتحسين الأداء
    _listAnimationController = AnimationController(
      duration: const Duration(
          milliseconds: 600), // تقليل المدة من 800 إلى 600 مللي ثانية
      vsync: this,
    );

    // تهيئة متحكم التمرير
    _scrollController = ScrollController();

    // بدء الرسوم المتحركة
    _listAnimationController.forward();

    // إعداد البحث إذا كان متوفراً
    if (widget.searchController != null) {
      widget.searchController!.addListener(_filterPrayers);
    }
  }

  @override
  void dispose() {
    // تحرير موارد محرك الرسوم المتحركة
    _listAnimationController.dispose();

    // تحرير موارد متحكم التمرير
    _scrollController.dispose();

    // إزالة مستمع البحث
    if (widget.searchController != null) {
      widget.searchController!.removeListener(_filterPrayers);
    }
    super.dispose();
  }

  @override
  void didUpdateWidget(ProphetPrayerList oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.prayers != oldWidget.prayers) {
      debugPrint('تحديث ProphetPrayerList مع ${widget.prayers.length} صلاة');
      setState(() {
        _filteredPrayers = widget.prayers;
      });

      // إذا كان هناك بحث نشط، قم بتطبيق البحث على القائمة الجديدة
      if (widget.searchController != null &&
          widget.searchController!.text.isNotEmpty) {
        _filterPrayers();
      }
    }
  }

  void _filterPrayers() {
    if (widget.searchController == null) return;

    final query = widget.searchController!.text.trim();
    debugPrint('تطبيق البحث في ProphetPrayerList: "$query"');

    setState(() {
      _isSearching = query.isNotEmpty;
      if (query.isEmpty) {
        _filteredPrayers = widget.prayers;
        debugPrint('إعادة تعيين القائمة إلى ${widget.prayers.length} صلاة');
      } else {
        final results = widget.prayers
            .where((prayer) =>
                prayer.text.toLowerCase().contains(query.toLowerCase()) ||
                (prayer.source?.toLowerCase().contains(query.toLowerCase()) ??
                    false) ||
                (prayer.virtue?.toLowerCase().contains(query.toLowerCase()) ??
                    false) ||
                (prayer.explanation
                        ?.toLowerCase()
                        .contains(query.toLowerCase()) ??
                    false))
            .toList();

        _filteredPrayers = results;
        debugPrint('تم العثور على ${results.length} صلاة تطابق البحث');
      }
    });

    // استدعاء دالة التغيير إذا كانت متوفرة
    if (widget.onSearchChanged != null) {
      widget.onSearchChanged!(query);
    }
  }

  // إنشاء تأثير حركي للعناصر - تم تحسينه للأداء
  Animation<double> _getItemAnimation(int index) {
    // تقليل التأخير بين العناصر لتحسين الأداء
    const double delay = 0.03; // تقليل التأخير من 0.05 إلى 0.03
    final double startInterval = index * delay;
    final double endInterval =
        startInterval + 0.4; // تقليل مدة الظهور من 0.5 إلى 0.4

    // تحديد عدد أقصى من العناصر التي تظهر بتأخير (10 عناصر فقط)
    const int maxDelayedItems = 10;
    final double clampedStartInterval = (index < maxDelayedItems)
        ? startInterval
        : (maxDelayedItems - 1) * delay + (index - maxDelayedItems + 1) * 0.01;

    return Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _listAnimationController,
        curve: Interval(
          clampedStartInterval.clamp(
              0.0, 0.8), // تقليل الحد الأقصى من 0.9 إلى 0.8
          endInterval.clamp(0.1, 1.0),
          curve: Curves.easeOut, // تبسيط منحنى الحركة
        ),
      ),
    );
  }

  // إنشاء تأثير حركي للعنوان - تم تحسينه للأداء
  Animation<double> _getHeaderAnimation() {
    return Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _listAnimationController,
        // تقليل مدة الظهور من 0.3 إلى 0.25 لتحسين الأداء
        curve: const Interval(0.0, 0.25, curve: Curves.easeOut),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final prophetPrayersColor = AppColors.getProphetPrayersColor(isDarkMode);

    return AnimatedBuilder(
      animation: widget.animation,
      builder: (context, child) {
        return Opacity(
          opacity: widget.animation.value,
          child: Transform.translate(
            offset: Offset(0, 50 * (1 - widget.animation.value)),
            // Usar SingleChildScrollView para permitir desplazamiento
            child: SingleChildScrollView(
              controller: _scrollController,
              physics: const BouncingScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // حقل البحث إذا كان متوفراً - تم تحسينه للأداء
                  if (widget.searchController != null)
                    RepaintBoundary(
                      child: AnimatedBuilder(
                        animation: _getHeaderAnimation(),
                        builder: (context, child) {
                          return Opacity(
                            opacity: _getHeaderAnimation().value,
                            child: Transform.translate(
                              // تقليل مسافة الحركة من 20 إلى 15 لتحسين الأداء
                              offset: Offset(
                                  0, 15 * (1 - _getHeaderAnimation().value)),
                              child: Padding(
                                padding:
                                    const EdgeInsets.fromLTRB(16, 16, 16, 8),
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: isDarkMode
                                        ? Colors.grey[850]
                                        : Colors.grey[100],
                                    borderRadius: BorderRadius.circular(24),
                                    // تقليل قوة الظلال لتحسين الأداء
                                    boxShadow: [
                                      BoxShadow(
                                        color: prophetPrayersColor.withValues(
                                            alpha: 0.1),
                                        blurRadius:
                                            10, // تقليل قوة البلور من 15 إلى 10
                                        spreadRadius:
                                            0, // تقليل انتشار الظل من 1 إلى 0
                                        offset: const Offset(
                                            0, 2), // تقليل الإزاحة من 3 إلى 2
                                      ),
                                    ],
                                    border: Border.all(
                                      color: prophetPrayersColor.withValues(
                                          alpha: 0.16),
                                      width: 1, // تقليل سمك الحدود من 1.5 إلى 1
                                    ),
                                  ),
                                  child: TextField(
                                    controller: widget.searchController,
                                    decoration: InputDecoration(
                                      hintText: 'ابحث في الصلوات...',
                                      hintStyle: TextStyle(
                                        color: isDarkMode
                                            ? Colors.grey[400]
                                            : Colors.grey[600],
                                        fontSize: 15,
                                      ),
                                      prefixIcon: Icon(
                                        Icons.search,
                                        color: prophetPrayersColor,
                                        size: 22,
                                      ),
                                      suffixIcon: _isSearching
                                          ? Container(
                                              margin: const EdgeInsets.all(8),
                                              decoration: BoxDecoration(
                                                color: isDarkMode
                                                    ? Colors.grey[700]
                                                    : Colors.grey[200],
                                                shape: BoxShape.circle,
                                              ),
                                              child: IconButton(
                                                icon: const Icon(Icons.clear,
                                                    size: 18),
                                                onPressed: () {
                                                  widget.searchController!
                                                      .clear();
                                                  // تقليل قوة الاهتزاز
                                                  HapticFeedback
                                                      .selectionClick();
                                                },
                                                color: isDarkMode
                                                    ? Colors.grey[300]
                                                    : Colors.grey[700],
                                                padding: EdgeInsets.zero,
                                                constraints:
                                                    const BoxConstraints(),
                                                splashRadius: 20,
                                              ),
                                            )
                                          : null,
                                      border: InputBorder.none,
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                        horizontal: 20,
                                        vertical: 16,
                                      ),
                                    ),
                                    style: TextStyle(
                                      color: isDarkMode
                                          ? Colors.white
                                          : Colors.black87,
                                      fontSize: 16,
                                    ),
                                    textInputAction: TextInputAction.search,
                                    textDirection: TextDirection.rtl,
                                    textAlign: TextAlign.right,
                                    onSubmitted: (_) {
                                      // تقليل قوة الاهتزاز
                                      HapticFeedback.selectionClick();
                                    },
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),

                  // عنوان القسم
                  AnimatedBuilder(
                    animation: _getHeaderAnimation(),
                    builder: (context, child) {
                      return Opacity(
                        opacity: _getHeaderAnimation().value,
                        child: Transform.translate(
                          offset:
                              Offset(0, 20 * (1 - _getHeaderAnimation().value)),
                          child: Container(
                            margin: const EdgeInsets.fromLTRB(16, 20, 16, 12),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  prophetPrayersColor.withValues(alpha: 0.16),
                                  prophetPrayersColor.withValues(alpha: 0.08),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: prophetPrayersColor.withValues(
                                      alpha: 0.08),
                                  blurRadius: 8,
                                  spreadRadius: 0,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                              border: Border.all(
                                color:
                                    prophetPrayersColor.withValues(alpha: 0.2),
                                width: 1.5,
                              ),
                            ),
                            child: Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: prophetPrayersColor.withValues(
                                        alpha: 0.16),
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    Icons.format_list_numbered,
                                    color: prophetPrayersColor,
                                    size: 20,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      _isSearching
                                          ? 'نتائج البحث'
                                          : 'جميع الصلوات',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: isDarkMode
                                            ? Colors.white
                                            : Colors.black87,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      '${_filteredPrayers.length} صلاة',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: isDarkMode
                                            ? Colors.grey[400]
                                            : Colors.grey[700],
                                      ),
                                    ),
                                  ],
                                ),
                                const Spacer(),
                                if (_isSearching)
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 6,
                                    ),
                                    decoration: BoxDecoration(
                                      color: prophetPrayersColor.withValues(
                                          alpha: 0.12),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.search,
                                          color: prophetPrayersColor,
                                          size: 16,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          'البحث نشط',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: prophetPrayersColor,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),

                  // قائمة الصلوات
                  if (_filteredPrayers.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: List.generate(
                          _filteredPrayers.length,
                          (index) {
                            final prayer = _filteredPrayers[index];
                            // تطبيق تأثير حركي لكل عنصر - تحسين الأداء
                            return AnimatedBuilder(
                              animation: _getItemAnimation(index),
                              builder: (context, child) {
                                return Opacity(
                                  opacity: _getItemAnimation(index).value,
                                  child: Transform.translate(
                                    offset: Offset(
                                      0,
                                      30 * (1 - _getItemAnimation(index).value),
                                    ),
                                    child: ProphetPrayerItem(
                                      prayer: prayer,
                                      index: index,
                                      isLast:
                                          index == _filteredPrayers.length - 1,
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      ),
                    ),

                  // رسالة عند عدم وجود نتائج
                  if (_filteredPrayers.isEmpty)
                    AnimatedBuilder(
                      animation: _getHeaderAnimation(),
                      builder: (context, child) {
                        return Opacity(
                          opacity: _getHeaderAnimation().value,
                          child: Transform.translate(
                            offset: Offset(
                                0, 20 * (1 - _getHeaderAnimation().value)),
                            child: Container(
                              margin: const EdgeInsets.all(32),
                              padding: const EdgeInsets.all(24),
                              decoration: BoxDecoration(
                                color: isDarkMode
                                    ? Colors.grey[850]!.withValues(alpha: 0.59)
                                    : Colors.grey[100]!.withValues(alpha: 0.59),
                                borderRadius: BorderRadius.circular(24),
                                border: Border.all(
                                  color: prophetPrayersColor.withValues(
                                      alpha: 0.12),
                                  width: 1.5,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: prophetPrayersColor.withValues(
                                        alpha: 0.06),
                                    blurRadius: 10,
                                    spreadRadius: 0,
                                    offset: const Offset(0, 5),
                                  ),
                                ],
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: prophetPrayersColor.withValues(
                                          alpha: 0.08),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      _isSearching
                                          ? Icons.search_off
                                          : Icons.format_list_numbered_rtl,
                                      size: 48,
                                      color: prophetPrayersColor,
                                    ),
                                  ),
                                  const SizedBox(height: 20),
                                  Text(
                                    _isSearching
                                        ? 'لا توجد نتائج مطابقة للبحث'
                                        : 'لا توجد صلوات في هذه الفئة',
                                    style: TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                      color: isDarkMode
                                          ? Colors.white
                                          : Colors.black87,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 12),
                                  Text(
                                    _isSearching
                                        ? 'حاول البحث بكلمات أخرى أو تغيير معايير البحث'
                                        : 'قد تكون هذه الفئة فارغة أو لم يتم تحميل البيانات بشكل صحيح',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: isDarkMode
                                          ? Colors.grey[400]
                                          : Colors.grey[600],
                                      height: 1.5,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 20),
                                  ElevatedButton.icon(
                                    onPressed: () {
                                      if (_isSearching &&
                                          widget.searchController != null) {
                                        widget.searchController!.clear();
                                      } else {
                                        // إعادة تحميل البيانات
                                        if (widget.prayers.isEmpty &&
                                            widget.onSearchChanged != null) {
                                          widget.onSearchChanged!('reload');
                                        }
                                      }
                                      HapticFeedback.mediumImpact();
                                    },
                                    icon: Icon(_isSearching
                                        ? Icons.clear
                                        : Icons.refresh),
                                    label: Text(_isSearching
                                        ? 'مسح البحث'
                                        : 'إعادة تحميل'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: prophetPrayersColor,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 20,
                                        vertical: 12,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
