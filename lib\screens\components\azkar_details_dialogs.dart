part of '../azkar_details_screen.dart';

class _CompletionDialog extends StatelessWidget {
  const _CompletionDialog();

  @override
  Widget build(BuildContext context) {
    // حساب حجم الشاشة
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      elevation: 10,
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? const Color(0xFF252A34) // TasbihColors.darkCardColor
          : Colors.white,
      // استخدام تباعد نسبي
      insetPadding: EdgeInsets.symmetric(
        horizontal: screenSize.width * 0.05,
        vertical: screenSize.height * 0.03,
      ),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: screenSize.width * 0.85,
          maxHeight: screenSize.height * 0.7,
        ),
        child: SingleChildScrollView(
          child: Padding(
            // تباعد نسبي للمحتوى الداخلي
            padding: EdgeInsets.all(screenSize.width * 0.06),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // استخدام AnimatedCheckMark بحجم نسبي
                AnimatedCheckMark(
                  color: AppColors.getAzkarColor(
                      Theme.of(context).brightness == Brightness.dark),
                  size: screenSize.width * 0.2,
                  duration: const Duration(milliseconds: 800),
                  onAnimationEnd: () {
                    // يمكن إضافة إجراء إضافي عند انتهاء الحركة
                  },
                ),
                SizedBox(height: screenSize.height * 0.02),

                // عنوان التهنئة بحجم نسبي
                Text(
                  'أحسنت!',
                  style: TextStyle(
                    fontSize: isSmallScreen ? 20 : 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: screenSize.height * 0.01),

                // رسالة التوضيح
                Text(
                  'لقد أكملت هذا الذكر\nتقبل الله منا ومنكم صالح الأعمال',
                  style: TextStyle(
                    fontSize: isSmallScreen ? 14 : 16,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: screenSize.height * 0.025),

                // زر الإغلاق
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.getAzkarColor(
                        Theme.of(context).brightness == Brightness.dark),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: EdgeInsets.symmetric(
                      horizontal: screenSize.width * 0.08,
                      vertical: screenSize.height * 0.015,
                    ),
                    elevation: 0,
                  ),
                  child: Text(
                    'حسناً',
                    style: TextStyle(
                      fontSize: isSmallScreen ? 14 : 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _FilterBottomSheet extends StatefulWidget {
  final bool showFavoritesOnly;
  final bool showUncompletedOnly;
  final ValueChanged<bool> onFavoritesFilterChanged;
  final ValueChanged<bool> onUncompletedFilterChanged;
  final VoidCallback onResetFilters;

  const _FilterBottomSheet({
    required this.showFavoritesOnly,
    required this.showUncompletedOnly,
    required this.onFavoritesFilterChanged,
    required this.onUncompletedFilterChanged,
    required this.onResetFilters,
  });

  @override
  State<_FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<_FilterBottomSheet> {
  late bool _showFavoritesOnly;
  late bool _showUncompletedOnly;

  @override
  void initState() {
    super.initState();
    _showFavoritesOnly = widget.showFavoritesOnly;
    _showUncompletedOnly = widget.showUncompletedOnly;
  }

  @override
  Widget build(BuildContext context) {
    final hasActiveFilters = _showFavoritesOnly || _showUncompletedOnly;

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF252A34) // TasbihColors.darkCardColor
            : Colors.white,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // عنوان
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'تصفية الأذكار',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.getAzkarColor(
                      Theme.of(context).brightness == Brightness.dark),
                ),
              ),
              if (hasActiveFilters)
                TextButton.icon(
                  onPressed: () {
                    setState(() {
                      _showFavoritesOnly = false;
                      _showUncompletedOnly = false;
                    });
                    widget.onResetFilters();
                  },
                  icon: const Icon(Icons.refresh, size: 18),
                  label: const Text('إعادة تعيين'),
                  style: TextButton.styleFrom(
                    foregroundColor:
                        Theme.of(context).brightness == Brightness.dark
                            ? const Color(
                                0xFFAAAAAA) /* TasbihColors.darkTextSecondary */
                            : Colors.grey[700],
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 24),

          // خيارات التصفية - مُحسّن لتجنب مشكلة ParentDataWidget
          LayoutBuilder(
            builder: (context, constraints) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // المفضلة فقط
                  _buildFilterOption(
                    icon: Icons.favorite,
                    iconColor: Colors.pink,
                    title: 'المفضلة فقط',
                    description: 'عرض الأذكار المفضلة فقط',
                    value: _showFavoritesOnly,
                    onChanged: (value) {
                      setState(() {
                        _showFavoritesOnly = value;
                      });
                      widget.onFavoritesFilterChanged(value);
                    },
                  ),
                  const Divider(height: 24),

                  // غير المكتملة فقط
                  _buildFilterOption(
                    icon: Icons.check_circle_outline,
                    iconColor: Colors.green,
                    title: 'غير المكتملة فقط',
                    description: 'عرض الأذكار التي لم تكتمل بعد',
                    value: _showUncompletedOnly,
                    onChanged: (value) {
                      setState(() {
                        _showUncompletedOnly = value;
                      });
                      widget.onUncompletedFilterChanged(value);
                    },
                  ),
                ],
              );
            },
          ),

          const SizedBox(height: 24),

          // زر الإغلاق
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.getAzkarColor(
                  Theme.of(context).brightness == Brightness.dark),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
            child: const Text(
              'إغلاق',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          // مساحة لأجهزة iOS
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  // بناء خيار التصفية - مُحسّن لتجنب مشكلة ParentDataWidget
  Widget _buildFilterOption({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String description,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return InkWell(
      onTap: () => onChanged(!value),
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: LayoutBuilder(
          builder: (context, constraints) {
            return Row(
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
              children: [
                Switch(
                  value: value,
                  onChanged: onChanged,
                  activeColor: AppColors.getAzkarColor(
                      Theme.of(context).brightness == Brightness.dark),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment:
                        CrossAxisAlignment.end, // تغيير المحاذاة إلى اليمين
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        textDirection: TextDirection
                            .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                        textAlign: TextAlign.right, // محاذاة النص إلى اليمين
                      ),
                      Text(
                        description,
                        style: TextStyle(
                          fontSize: 13,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? const Color(
                                  0xFFAAAAAA) // TasbihColors.darkTextSecondary
                              : Colors.grey[600],
                        ),
                        textDirection: TextDirection
                            .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                        textAlign: TextAlign.right, // محاذاة النص إلى اليمين
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: iconColor.withAlpha(26), // 0.1 * 255 = 26
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    icon,
                    color: iconColor,
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
