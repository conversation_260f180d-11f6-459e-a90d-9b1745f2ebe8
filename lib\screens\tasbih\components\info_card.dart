// بطاقة المعلومات

import 'package:flutter/material.dart';

class InfoCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final bool isDarkMode;

  const InfoCard({
    Key? key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    required this.isDarkMode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // استخدام عرض ثابت ولكن مع مرونة داخلية
    return Container(
      width: MediaQuery.of(context).size.width * 0.28,
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 6),
      decoration: BoxDecoration(
        color: color.withAlpha(26),
        borderRadius: BorderRadius.circular(14),
        border: Border.all(color: color.withAlpha(76), width: 1),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withAlpha(38)
                : Colors.black.withAlpha(13),
            offset: const Offset(0, 2),
            blurRadius: 6,
            spreadRadius: 0,
          ),
        ],
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withAlpha(31),
            color.withAlpha(20),
          ],
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // استخدام FittedBox للقيمة لضمان تناسبها مع المساحة المتاحة
          FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
              value,
              style: TextStyle(
                fontSize:
                    22, // حجم خط ثابت ولكن سيتم تقليصه تلقائيًا إذا لزم الأمر
                color: color,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: color.withAlpha(51),
                    offset: const Offset(0, 1),
                    blurRadius: 1,
                  ),
                ],
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 4),
          // استخدام FittedBox للعنوان والأيقونة لضمان تناسبهما مع المساحة المتاحة
          FittedBox(
            fit: BoxFit.scaleDown,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  size: 14, // حجم أيقونة أصغر
                  color: color.withAlpha(179),
                ),
                const SizedBox(width: 3),
                Text(
                  title,
                  style: TextStyle(
                    fontSize:
                        12, // حجم خط ثابت ولكن سيتم تقليصه تلقائيًا إذا لزم الأمر
                    color: color.withAlpha(204),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
