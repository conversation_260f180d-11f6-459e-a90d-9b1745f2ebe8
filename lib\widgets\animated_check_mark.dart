import 'package:flutter/material.dart';
import 'dart:math' as math;

class Animated<PERSON>heckMark extends StatefulWidget {
  final Color color;
  final double size;
  final Duration duration;
  final VoidCallback? onAnimationEnd;

  const AnimatedCheckMark({
    Key? key,
    required this.color,
    this.size = 80,
    this.duration = const Duration(milliseconds: 600),
    this.onAnimationEnd,
  }) : super(key: key);

  @override
  State<AnimatedCheckMark> createState() => _AnimatedCheckMarkState();
}

class _AnimatedCheckMarkState extends State<AnimatedCheckMark>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );

    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );

    _controller.forward();

    if (widget.onAnimationEnd != null) {
      _controller.addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          widget.onAnimationEnd!();
        }
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return CustomPaint(
          size: Size(widget.size, widget.size),
          painter: _CheckMarkPainter(
            progress: _animation.value,
            color: widget.color,
          ),
        );
      },
    );
  }
}

class _CheckMarkPainter extends CustomPainter {
  final double progress;
  final Color color;

  _CheckMarkPainter({
    required this.progress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeWidth = size.width / 12;

    final double circleProgress = math.min(1.0, progress * 1.5);
    final double checkProgress = math.max(0.0, (progress - 0.6) * 2.5);

    // رسم الدائرة
    final double radius = size.width / 2 - paint.strokeWidth / 2;
    final Offset center = Offset(size.width / 2, size.height / 2);

    if (circleProgress > 0) {
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        -math.pi / 2,
        2 * math.pi * circleProgress,
        false,
        paint,
      );
    }

    // رسم علامة الصح
    if (checkProgress > 0) {
      final Path path = Path();
      final double startX = size.width * 0.25;
      final double midX = size.width * 0.45;
      final double endX = size.width * 0.75;
      final double midY = size.height * 0.65;
      final double endY = size.height * 0.35;

      // نقطة البداية للخط الأول من علامة الصح
      path.moveTo(startX, midY);

      // الجزء الأول من علامة الصح - الخط المائل للأسفل
      if (checkProgress <= 0.5) {
        final double currentMidX =
            startX + (midX - startX) * (checkProgress * 2);
        final double currentMidY = midY;
        path.lineTo(currentMidX, currentMidY);
      } else {
        path.lineTo(midX, midY);

        // الجزء الثاني من علامة الصح - الخط المائل للأعلى
        final double currentEndX =
            midX + (endX - midX) * ((checkProgress - 0.5) * 2);
        final double currentEndY =
            midY - (midY - endY) * ((checkProgress - 0.5) * 2);
        path.lineTo(currentEndX, currentEndY);
      }

      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(_CheckMarkPainter oldDelegate) {
    return oldDelegate.progress != progress || oldDelegate.color != color;
  }
}
