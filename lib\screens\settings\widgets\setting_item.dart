// نشاء ملف للعناصر المشتركة
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class SettingItem extends StatefulWidget {
  final String title;
  final IconData icon;
  final Color iconColor;
  final String? subtitle;
  final Widget? content;
  final Widget? trailing;
  final VoidCallback? onTap;
  final bool isLast;

  const SettingItem({
    super.key,
    required this.title,
    required this.icon,
    required this.iconColor,
    this.subtitle,
    this.content,
    this.trailing,
    this.onTap,
    this.isLast = false,
  });

  @override
  State<SettingItem> createState() => _SettingItemState();
}

class _SettingItemState extends State<SettingItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.98).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.onTap != null) {
      setState(() {
        _isPressed = true;
      });
      _controller.forward();
      HapticFeedback.lightImpact();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (widget.onTap != null) {
      setState(() {
        _isPressed = false;
      });
      _controller.reverse();
    }
  }

  void _handleTapCancel() {
    if (widget.onTap != null) {
      setState(() {
        _isPressed = false;
      });
      _controller.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = isDarkMode
        ? Theme.of(context)
            .cardColor
            .withAlpha(_isPressed ? 77 : 38) // 0.3*255=77, 0.15*255=38
        : Colors.white.withAlpha(_isPressed ? 230 : 255); // 0.9*255=230

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: child,
        );
      },
      child: GestureDetector(
        onTapDown: _handleTapDown,
        onTapUp: _handleTapUp,
        onTapCancel: _handleTapCancel,
        onTap: widget.onTap,
        child: Container(
          margin: EdgeInsets.only(
            left: 16.0,
            right: 16.0,
            top: 8.0,
            bottom: widget.isLast ? 16.0 : 8.0,
          ),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              if (!isDarkMode && !_isPressed)
                BoxShadow(
                  color: Colors.black.withAlpha(8), // 0.03*255=~8
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
            ],
            border: Border.all(
              color: isDarkMode
                  ? Colors.grey.withAlpha(30) // 0.12*255=~30
                  : Colors.grey.withAlpha(15), // 0.06*255=~15
              width: 0.5,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                  children: [
                    // أيقونة مع تأثير تدرج
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            widget.iconColor.withAlpha(230), // 0.9*255=230
                            widget.iconColor.withAlpha(179), // 0.7*255=179
                          ],
                          begin: Alignment
                              .topRight, // تغيير اتجاه التدرج ليتناسب مع اللغة العربية
                          end: Alignment.bottomLeft,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: widget.iconColor.withAlpha(51), // 0.2*255=51
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        widget.icon,
                        size: 22,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        textDirection: TextDirection
                            .rtl, // تحديد اتجاه العمود من اليمين إلى اليسار
                        children: [
                          Text(
                            widget.title,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color:
                                  Theme.of(context).textTheme.bodyLarge?.color,
                            ),
                            textDirection: TextDirection
                                .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                            textAlign:
                                TextAlign.right, // محاذاة النص إلى اليمين
                          ),
                          if (widget.subtitle != null)
                            Padding(
                              padding: const EdgeInsets.only(top: 4.0),
                              child: Text(
                                widget.subtitle!,
                                style: TextStyle(
                                  fontSize: 13,
                                  color: Theme.of(context)
                                      .textTheme
                                      .bodySmall
                                      ?.color,
                                ),
                                textDirection: TextDirection
                                    .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                textAlign:
                                    TextAlign.right, // محاذاة النص إلى اليمين
                              ),
                            ),
                        ],
                      ),
                    ),
                    if (widget.trailing != null) widget.trailing!,
                    if (widget.onTap != null && widget.trailing == null)
                      Icon(
                        Icons
                            .arrow_back_ios, // تغيير اتجاه السهم ليتناسب مع اللغة العربية
                        size: 16,
                        color: Theme.of(context)
                            .textTheme
                            .bodySmall
                            ?.color
                            ?.withAlpha(128), // 0.5*255=128
                      ),
                  ],
                ),
                if (widget.content != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: widget.content!,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
