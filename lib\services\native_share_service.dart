import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import '../models/poem.dart';
import '../models/book.dart';

class NativeShareService {
  static const MethodChannel _shareChannel =
      MethodChannel('com.example.wahaj_alsaalik/share');

  // مشاركة القصيدة باستخدام الواجهة البرمجية الأصلية
  static Future<void> sharePoem(Poem poem) async {
    try {
      // محاولة استخدام الواجهة البرمجية الأصلية
      await _shareChannel.invokeMethod('sharePoem', {
        'title': poem.title,
        'poet': poem.poet,
        'content': poem.content,
      });
    } catch (e) {
      // استخدام مكتبة share_plus كبديل إذا فشلت الواجهة البرمجية الأصلية
      await _sharePoemFallback(poem);
    }
  }

  // مشاركة القصيدة باستخدام مكتبة share_plus
  static Future<void> _sharePoemFallback(Poem poem) async {
    final String shareText = '''
${poem.title}
للشاعر: ${poem.poet}

${poem.content}

مشاركة من تطبيق وهج السالك
''';

    await Share.share(shareText);
  }

  // مشاركة الكتاب باستخدام الواجهة البرمجية الأصلية
  static Future<void> shareBook(Book book) async {
    try {
      // محاولة استخدام الواجهة البرمجية الأصلية
      await _shareChannel.invokeMethod('shareBook', {
        'title': book.title,
        'author': book.author,
        'description': book.description,
      });
    } catch (e) {
      // استخدام مكتبة share_plus كبديل إذا فشلت الواجهة البرمجية الأصلية
      await _shareBookFallback(book);
    }
  }

  // مشاركة الكتاب باستخدام مكتبة share_plus
  static Future<void> _shareBookFallback(Book book) async {
    final String shareText = '''
${book.title}
المؤلف: ${book.author}

${book.description}

مشاركة من تطبيق وهج السالك
''';

    await Share.share(shareText);
  }
}
