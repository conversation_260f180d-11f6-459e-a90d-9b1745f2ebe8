class Poem {
  final String id;
  final String title;
  final String poet;
  final String category;
  final String content;
  final String era;
  final bool isFavorite;
  final List<String> verses; // قائمة الأبيات المنفصلة

  Poem({
    required this.id,
    required this.title,
    required this.poet,
    required this.category,
    required this.content,
    required this.era,
    this.isFavorite = false,
    List<String>? verses,
  }) : verses = verses ?? _splitContent(content);

  // تقسيم محتوى القصيدة إلى أبيات
  static List<String> _splitContent(String content) {
    if (content.isEmpty) return [];
    return content.split('\n').where((line) => line.trim().isNotEmpty).toList();
  }

  factory Poem.fromJson(Map<String, dynamic> json) {
    final content = json['content'] ?? '';
    return Poem(
      id: json['id'].toString(),
      title: json['title'] ?? '',
      poet: json['poet'] ?? '',
      category: json['category'] ?? '',
      content: content,
      era: json['era'] ?? '',
      isFavorite: json['isFavorite'] ?? false,
      verses: _splitContent(content),
    );
  }

  factory Poem.fromMap(Map<String, dynamic> map) {
    final content = map['content'] ?? '';
    return Poem(
      id: map['id'].toString(),
      title: map['title'] ?? '',
      poet: map['poet'] ?? '',
      category: map['category'] ?? '',
      content: content,
      era: map['era'] ?? '',
      isFavorite: map['is_favorite'] == 1 || map['isFavorite'] == true,
      verses: _splitContent(content),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'poet': poet,
      'category': category,
      'content': content,
      'era': era,
      'is_favorite': isFavorite ? 1 : 0,
      'date_added': DateTime.now().millisecondsSinceEpoch,
    };
  }

  // نسخة جديدة من الشعر مع تحديث حالة المفضلة والأبيات
  Poem copyWith({bool? isFavorite, List<String>? verses}) {
    return Poem(
      id: id,
      title: title,
      poet: poet,
      category: category,
      content: content,
      era: era,
      isFavorite: isFavorite ?? this.isFavorite,
      verses: verses ?? this.verses,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Poem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
