// بطاقة الدعاء

import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // مستورد في الكود الأصلي
import '../models/dua.dart';
import '../utils/app_colors.dart';
import '../utils/icon_helper.dart'; // إضافة استيراد IconHelper

// تعريف نوع العرض
enum DuaCardDisplayMode {
  grid, // عرض شبكي
  list, // عرض قائمة
}

class DuaCard extends StatefulWidget {
  final DuaCategory category;
  final Function onTap;
  final Animation<double> animation;
  final DuaCardDisplayMode displayMode; // نوع العرض

  const DuaCard({
    Key? key, // استخدام super.key إذا كان الإصدار يسمح
    required this.category,
    required this.onTap,
    required this.animation,
    this.displayMode =
        DuaCardDisplayMode.grid, // القيمة الافتراضية هي العرض الشبكي
  }) : super(key: key);

  @override
  State<DuaCard> createState() => _DuaCardState();
}

class _DuaCardState extends State<DuaCard> with SingleTickerProviderStateMixin {
  late AnimationController _hoverController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  bool _isHovered = false;

  late Animation<double> _rotateAnimation;
  late Animation<double> _glowAnimation;
  late Animation<Color?>
      _colorAnimation; // كان ColorTween في الأصلي، لكنه يجب أن يكون Animation<Color?>

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(
        parent: _hoverController,
        curve: Curves.easeOutCubic,
      ),
    );

    _elevationAnimation = Tween<double>(begin: 4.0, end: 16.0).animate(
      CurvedAnimation(
        parent: _hoverController,
        curve: Curves.easeOutCubic,
      ),
    );

    _rotateAnimation = Tween<double>(begin: 0.0, end: 0.01).animate(
      CurvedAnimation(
        parent: _hoverController,
        curve: Curves.easeInOutBack,
      ),
    );

    _glowAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _hoverController,
        curve: const Interval(0.0, 0.75, curve: Curves.easeOut),
      ),
    );

    // سيتم تحديد اللون في build
    // تم ترك هذا كما هو لأن اللون يتغير في build
    _colorAnimation = ColorTween(
      begin: Colors.transparent,
      end: Colors.transparent, // سيتم تحديثه في build
    ).animate(_hoverController);
  }

  @override
  void dispose() {
    _hoverController.dispose();
    super.dispose();
  }

  void _onHover(bool isHovered) {
    if (isHovered) {
      _hoverController.forward();
      HapticFeedback.lightImpact(); // الكود الأصلي
    } else {
      _hoverController.reverse();
    }
    // لا يوجد تحقق mounted في الأصلي
    setState(() {
      _isHovered = isHovered;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final screenSize = MediaQuery.of(context).size;
    final duasColor = AppColors.getDuasColor(isDarkMode);

    // تحديث لون الأنيميشن
    _colorAnimation = ColorTween(
      begin: Colors.transparent, // الأصلي
      end: duasColor.withAlpha(100), // الأصلي
    ).animate(_hoverController);

    // تحويل اسم الأيقونة إلى أيقونة باستخدام IconHelper
    IconData iconData;
    try {
      // استخدام IconHelper للحصول على الأيقونة المناسبة
      iconData = IconHelper.getIconForCategory(widget.category.name,
          iconName: widget.category.iconName);
    } catch (e) {
      // استخدام أيقونة افتراضية إذا لم يتم العثور على الأيقونة
      iconData = Icons.auto_awesome; // أيقونة افتراضية كما في الأصلي
      debugPrint(
          'خطأ في تحويل اسم الأيقونة: ${widget.category.iconName}، الخطأ: $e');
    }

    return AnimatedBuilder(
      animation: widget.animation,
      builder: (context, child) {
        return Opacity(
          opacity: widget.animation.value,
          child: Transform.translate(
            offset: Offset(0, 50 * (1 - widget.animation.value)),
            child: child,
          ),
        );
      },
      child: MouseRegion(
        // الأصلي
        onEnter: (_) => _onHover(true),
        onExit: (_) => _onHover(false),
        child: GestureDetector(
          // الأصلي
          onTap: () {
            HapticFeedback.mediumImpact(); // الأصلي
            widget.onTap();
          },
          child: AnimatedBuilder(
            // الأصلي
            animation: _hoverController,
            builder: (context, child) {
              // لا يوجد child parameter في الأصلي
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: Transform.rotate(
                  angle: _rotateAnimation.value,
                  child: Container(
                    // الأصلي
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24),
                      boxShadow: [
                        // ظل أساسي
                        BoxShadow(
                          color: duasColor.withAlpha(30),
                          blurRadius: _elevationAnimation.value,
                          spreadRadius: 1, // الأصلي
                          offset: Offset(0, _elevationAnimation.value / 2),
                        ),
                        // ظل توهج عند التحويم
                        BoxShadow(
                          color: duasColor
                              .withAlpha((30 * _glowAnimation.value).toInt()),
                          blurRadius: 20 * _glowAnimation.value,
                          spreadRadius: 2 * _glowAnimation.value,
                          offset: const Offset(0, 0), // الأصلي
                        ),
                      ],
                    ),
                    child: Card(
                      // الأصلي
                      elevation: 0, // الأصلي
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(24),
                        side: BorderSide(
                          color: _colorAnimation.value ??
                              Colors.transparent, // الأصلي
                          width: 1.5, // الأصلي
                        ),
                      ),
                      color: isDarkMode
                          ? Color.lerp(
                              Colors.grey[900], // الأصلي
                              duasColor.withAlpha(30), // الأصلي
                              _hoverController.value * 0.5, // الأصلي
                            )
                          : Color.lerp(
                              Colors.white, // الأصلي
                              duasColor.withAlpha(15), // الأصلي
                              _hoverController.value * 0.7, // الأصلي
                            ),
                      child: Padding(
                        // الأصلي
                        padding: const EdgeInsets.all(20.0), // الأصلي
                        // استخدام تصميم مختلف بناءً على نوع العرض
                        child: widget.displayMode == DuaCardDisplayMode.list
                            ? _buildListViewLayout(context, isDarkMode,
                                screenSize, duasColor, iconData)
                            : _buildGridViewLayout(context, isDarkMode,
                                screenSize, duasColor, iconData),
                      ),
                    ),
                  ),
                ),
              );
            },
            // لا يوجد child هنا في الأصلي
          ),
        ),
      ),
    );
  }

  // تصميم عرض القائمة - فاخر وسلس
  Widget _buildListViewLayout(BuildContext context, bool isDarkMode,
      Size screenSize, Color duasColor, IconData iconData) {
    return Row(
      textDirection: TextDirection.rtl, // اتجاه من اليمين إلى اليسار
      crossAxisAlignment: CrossAxisAlignment.center, // الأصلي
      children: [
        // أيقونة الفئة مع تأثير توهج - محسنة للعرض الأفقي
        Container(
          // الأصلي
          width: 60, // الأصلي
          height: 60, // الأصلي
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                duasColor.withAlpha(50), // الأصلي
                duasColor.withAlpha(100), // الأصلي
              ],
              begin: Alignment.topLeft, // الأصلي
              end: Alignment.bottomRight, // الأصلي
            ),
            shape: BoxShape.circle, // الأصلي
            boxShadow: [
              // الأصلي
              BoxShadow(
                color: duasColor.withAlpha(50), // الأصلي
                blurRadius: 10, // الأصلي
                spreadRadius: 0, // الأصلي
                offset: const Offset(0, 3), // الأصلي
              ),
            ],
          ),
          child: IconHelper.getIconWidget(
            categoryName: widget.category.name,
            iconPath: widget.category.iconPath,
            iconName: widget.category.iconName,
            size: 30,
            color: duasColor,
            section: 'duas',
          ),
        ),
        const SizedBox(width: 16), // الأصلي

        // معلومات الفئة
        Expanded(
          // الأصلي
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end, // الأصلي
            textDirection: TextDirection.rtl, // الأصلي
            children: [
              // عنوان الفئة
              ShaderMask(
                // الأصلي
                shaderCallback: (bounds) {
                  return LinearGradient(
                    colors: [
                      isDarkMode ? Colors.white : Colors.black87, // الأصلي
                      duasColor, // الأصلي
                    ],
                    stops: const [0.7, 1.0], // الأصلي
                    begin: Alignment.centerRight, // الأصلي
                    end: Alignment.centerLeft, // الأصلي
                  ).createShader(bounds);
                },
                child: Text(
                  // الأصلي
                  widget.category.name,
                  style: TextStyle(
                    fontSize: 18, // الأصلي
                    fontWeight: FontWeight.bold, // الأصلي
                    color: Colors.white, // الأصلي
                  ),
                  textDirection: TextDirection.rtl, // الأصلي
                  textAlign: TextAlign.right, // الأصلي
                  maxLines: 1, // الأصلي
                  overflow: TextOverflow.ellipsis, // الأصلي
                ),
              ),
              const SizedBox(height: 8), // الأصلي

              // وصف الفئة
              Text(
                // الأصلي
                widget.category.description,
                style: TextStyle(
                  fontSize: 14, // الأصلي
                  color: isDarkMode
                      ? Colors.grey[300]
                      : Colors.grey[800], // الأصلي
                ),
                textDirection: TextDirection.rtl, // الأصلي
                textAlign: TextAlign.right, // الأصلي
                maxLines: 2, // الأصلي
                overflow: TextOverflow.ellipsis, // الأصلي
              ),

              const SizedBox(height: 8), // الأصلي

              // معلومات إضافية
              Row(
                // الأصلي
                mainAxisAlignment: MainAxisAlignment.end, // الأصلي
                textDirection: TextDirection.rtl, // الأصلي
                children: [
                  // عدد الأدعية
                  Container(
                    // الأصلي
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8, vertical: 4), // الأصلي
                    decoration: BoxDecoration(
                      color: duasColor.withAlpha(30), // الأصلي
                      borderRadius: BorderRadius.circular(12), // الأصلي
                    ),
                    child: Row(
                      // الأصلي
                      mainAxisSize: MainAxisSize.min, // الأصلي
                      textDirection: TextDirection.rtl, // الأصلي
                      children: [
                        Icon(
                          // الأصلي
                          Icons.format_list_numbered,
                          size: 14, // الأصلي
                          color: duasColor, // الأصلي
                        ),
                        const SizedBox(width: 4), // الأصلي
                        Text(
                          // الأصلي
                          '${widget.category.count} أدعية',
                          style: TextStyle(
                            fontSize: 12, // الأصلي
                            color: duasColor, // الأصلي
                            fontWeight: FontWeight.bold, // الأصلي
                          ),
                          textDirection: TextDirection.rtl, // الأصلي
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(width: 8), // الأصلي

                  // مؤشر الأقسام الفرعية إذا وجدت
                  if (widget.category.hasSubcategories) // الأصلي
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4), // الأصلي
                      decoration: BoxDecoration(
                        color: duasColor.withAlpha(15), // الأصلي
                        borderRadius: BorderRadius.circular(12), // الأصلي
                      ),
                      child: Row(
                        // الأصلي
                        mainAxisSize: MainAxisSize.min, // الأصلي
                        textDirection: TextDirection.rtl, // الأصلي
                        children: [
                          Icon(
                            // الأصلي
                            Icons.account_tree_outlined,
                            size: 14, // الأصلي
                            color: duasColor, // الأصلي
                          ),
                          const SizedBox(width: 4), // الأصلي
                          Text(
                            // الأصلي
                            'أقسام فرعية',
                            style: TextStyle(
                              fontSize: 12, // الأصلي
                              color: duasColor, // الأصلي
                            ),
                            textDirection: TextDirection.rtl, // الأصلي
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),

        // سهم للانتقال
        Container(
          // الأصلي
          margin: const EdgeInsets.only(right: 8), // الأصلي
          padding: const EdgeInsets.all(8), // الأصلي
          decoration: BoxDecoration(
            // الأصلي
            color: duasColor.withAlpha(_isHovered ? 70 : 30), // الأصلي
            shape: BoxShape.circle, // الأصلي
            boxShadow: _isHovered // الأصلي
                ? [
                    BoxShadow(
                      color: duasColor.withAlpha(40), // الأصلي
                      blurRadius: 8, // الأصلي
                      spreadRadius: 1, // الأصلي
                    )
                  ]
                : [],
          ),
          child: Icon(
            // الأصلي
            Icons.arrow_back_ios,
            size: 16, // الأصلي
            color: duasColor, // الأصلي
          ),
        ),
      ],
    );
  }

  // تصميم عرض الشبكة - الأصلي
  Widget _buildGridViewLayout(BuildContext context, bool isDarkMode,
      Size screenSize, Color duasColor, IconData iconData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch, // الأصلي
      children: [
        // الجزء العلوي: الأيقونة والعنوان
        Row(
          // الأصلي
          children: [
            // أيقونة الفئة مع تأثير توهج
            Stack(
              // الأصلي
              alignment: Alignment.center, // الأصلي
              children: [
                // طبقة التوهج الخارجية
                AnimatedBuilder(
                  // الأصلي
                  animation: _hoverController,
                  builder: (context, _) {
                    //  لا يوجد child parameter في الأصلي
                    return AnimatedOpacity(
                      duration: const Duration(milliseconds: 300), // الأصلي
                      opacity: _isHovered ? 1.0 : 0.0, // الأصلي
                      child: Container(
                        // الأصلي
                        width: screenSize.width * 0.14, // الأصلي
                        height: screenSize.width * 0.14, // الأصلي
                        decoration: BoxDecoration(
                          shape: BoxShape.circle, // الأصلي
                          gradient: RadialGradient(
                            // الأصلي
                            colors: [
                              duasColor.withAlpha(100), // الأصلي
                              duasColor.withAlpha(0), // الأصلي
                            ],
                            stops: const [0.1, 1.0], // الأصلي
                          ),
                        ),
                      ),
                    );
                  },
                ),
                // الأيقونة الرئيسية
                Container(
                  // الأصلي
                  width: screenSize.width * 0.12, // الأصلي
                  height: screenSize.width * 0.12, // الأصلي
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      // الأصلي
                      colors: [
                        duasColor.withAlpha(50), // الأصلي
                        duasColor.withAlpha(100), // الأصلي
                      ],
                      begin: Alignment.topLeft, // الأصلي
                      end: Alignment.bottomRight, // الأصلي
                    ),
                    shape: BoxShape.circle, // الأصلي
                    boxShadow: [
                      // الأصلي
                      BoxShadow(
                        color: duasColor.withAlpha(50), // الأصلي
                        blurRadius: 10, // الأصلي
                        spreadRadius: 0, // الأصلي
                        offset: const Offset(0, 3), // الأصلي
                      ),
                    ],
                  ),
                  child: IconHelper.getIconWidget(
                    categoryName: widget.category.name,
                    iconPath: widget.category.iconPath,
                    iconName: widget.category.iconName,
                    size: screenSize.width * 0.06,
                    color: duasColor,
                    section: 'duas',
                  ),
                ),
              ],
            ),
            const SizedBox(width: 16), // الأصلي
            // اسم الفئة وعدد الأدعية
            Expanded(
              // الأصلي
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end, // الأصلي
                textDirection: TextDirection.rtl, // الأصلي
                children: [
                  // عنوان مع تأثير تدرج لوني وتوهج
                  Stack(
                    // الأصلي
                    children: [
                      // تأثير توهج خلف النص عند التحويم
                      if (_isHovered) // الأصلي
                        Positioned.fill(
                          child: AnimatedOpacity(
                            duration:
                                const Duration(milliseconds: 300), // الأصلي
                            opacity: _isHovered ? 0.7 : 0.0, // الأصلي
                            child: TweenAnimationBuilder<double>(
                              // الأصلي
                              tween:
                                  Tween<double>(begin: 0.0, end: 1.0), // الأصلي
                              duration:
                                  const Duration(milliseconds: 800), // الأصلي
                              curve: Curves.easeOutCubic, // الأصلي
                              builder: (context, value, child) {
                                //  لا يوجد child parameter في الأصلي
                                return Container(
                                  decoration: BoxDecoration(
                                    borderRadius:
                                        BorderRadius.circular(8), // الأصلي
                                    boxShadow: [
                                      BoxShadow(
                                        color:
                                            duasColor.withAlpha(30), // الأصلي
                                        blurRadius: 15 * value, // الأصلي
                                        spreadRadius: 1 * value, // الأصلي
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      // النص مع تأثير تدرج لوني
                      ShaderMask(
                        // الأصلي
                        shaderCallback: (bounds) {
                          return LinearGradient(
                            colors: [
                              isDarkMode
                                  ? Colors.white
                                  : Colors.black87, // الأصلي
                              duasColor, // الأصلي
                            ],
                            stops: const [0.5, 1.0], // الأصلي
                            begin: Alignment.centerRight, // الأصلي
                            end: Alignment.centerLeft, // الأصلي
                            tileMode: TileMode.clamp, // الأصلي
                          ).createShader(bounds);
                        },
                        child: Text(
                          // الأصلي
                          widget.category.name,
                          style: TextStyle(
                            fontSize: screenSize.width * 0.04, // الأصلي
                            fontWeight: FontWeight.bold, // الأصلي
                            color: Colors.white, // الأصلي
                            letterSpacing: _isHovered ? 0.5 : 0.0, // الأصلي
                          ),
                          textDirection: TextDirection.rtl, // الأصلي
                          textAlign: TextAlign.right, // الأصلي
                          maxLines: 2, // الأصلي
                          overflow: TextOverflow.ellipsis, // الأصلي
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8), // الأصلي
                  // عدد الأدعية مع أيقونة
                  Row(
                    // الأصلي
                    mainAxisSize: MainAxisSize.min, // الأصلي
                    textDirection: TextDirection.rtl, // الأصلي
                    children: [
                      Icon(
                        // الأصلي
                        Icons.format_list_numbered,
                        size: 14, // الأصلي
                        color: isDarkMode
                            ? Colors.grey[400]
                            : Colors.grey[700], // الأصلي
                      ),
                      const SizedBox(width: 4), // الأصلي
                      Text(
                        // الأصلي
                        '${widget.category.count} أدعية',
                        style: TextStyle(
                          fontSize: screenSize.width * 0.03, // الأصلي
                          color: isDarkMode
                              ? Colors.grey[400]
                              : Colors.grey[700], // الأصلي
                        ),
                        textDirection: TextDirection.rtl, // الأصلي
                        textAlign: TextAlign.right, // الأصلي
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 20), // الأصلي
        // وصف الفئة مع خلفية مميزة
        Container(
          // الأصلي
          padding: const EdgeInsets.symmetric(
            horizontal: 12, // الأصلي
            vertical: 10, // الأصلي
          ),
          decoration: BoxDecoration(
            color: duasColor.withAlpha(15), // الأصلي
            borderRadius: BorderRadius.circular(12), // الأصلي
            border: Border.all(
              color: duasColor.withAlpha(30), // الأصلي
              width: 1, // الأصلي
            ),
          ),
          child: Text(
            // الأصلي
            widget.category.description,
            style: TextStyle(
              fontSize: screenSize.width * 0.032, // الأصلي
              height: 1.4, // الأصلي
              color: isDarkMode ? Colors.grey[300] : Colors.grey[800], // الأصلي
            ),
            textDirection: TextDirection.rtl, // الأصلي
            textAlign: TextAlign.right, // الأصلي
            maxLines: 2, // الأصلي
            overflow: TextOverflow.ellipsis, // الأصلي
          ),
        ),
        const Spacer(), // الأصلي
        // شريط السفلي مع مؤشر قابلية التصفح
        Row(
          // الأصلي
          mainAxisAlignment: MainAxisAlignment.spaceBetween, // الأصلي
          textDirection: TextDirection.rtl, // الأصلي
          children: [
            // مؤشر يوضح تفرع الأقسام إن وجدت
            if (widget.category.hasSubcategories) // الأصلي
              Flexible(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 10, // الأصلي
                    vertical: 6, // الأصلي
                  ),
                  decoration: BoxDecoration(
                    color: duasColor.withAlpha(15), // الأصلي
                    borderRadius: BorderRadius.circular(12), // الأصلي
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min, // الأصلي
                    textDirection: TextDirection.rtl, // الأصلي
                    children: [
                      Icon(
                        // الأصلي
                        Icons.account_tree_outlined,
                        size: screenSize.width * 0.035, // الأصلي
                        color: duasColor.withAlpha(179), // الأصلي
                      ),
                      SizedBox(width: screenSize.width * 0.01), // الأصلي
                      Flexible(
                        // الأصلي
                        child: Text(
                          'أقسام فرعية',
                          style: TextStyle(
                            fontSize: screenSize.width * 0.025, // الأصلي
                            fontWeight: FontWeight.w500, // الأصلي
                            color: duasColor.withAlpha(179), // الأصلي
                          ),
                          textDirection: TextDirection.rtl, // الأصلي
                          textAlign: TextAlign.right, // الأصلي
                          overflow: TextOverflow.ellipsis, // الأصلي
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            // زر التصفح مع تأثير نبض
            TweenAnimationBuilder<double>(
              // الأصلي
              tween: Tween<double>(
                begin: 0.0, // الأصلي
                end: _isHovered ? 1.0 : 0.0, // الأصلي
              ),
              duration: const Duration(milliseconds: 500), // الأصلي
              builder: (context, value, child) {
                //  لا يوجد child parameter في الأصلي
                return Container(
                  padding: const EdgeInsets.all(10), // الأصلي
                  decoration: BoxDecoration(
                    color: Color.lerp(
                      // الأصلي
                      duasColor.withAlpha(30),
                      duasColor.withAlpha(70),
                      value,
                    ),
                    shape: BoxShape.circle, // الأصلي
                    boxShadow: [
                      // الأصلي
                      if (value > 0) // الأصلي
                        BoxShadow(
                          color: duasColor
                              .withAlpha((40 * value).toInt()), // الأصلي
                          blurRadius: 8 * value, // الأصلي
                          spreadRadius: 1 * value, // الأصلي
                        ),
                    ],
                  ),
                  child: Icon(
                    // الأصلي
                    Icons.arrow_back_ios,
                    size: screenSize.width * 0.035, // الأصلي
                    color: Color.lerp(
                      // الأصلي
                      duasColor,
                      Colors.white,
                      value * 0.5,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ],
    );
  }
}
