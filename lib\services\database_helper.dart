import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/book.dart';
import '../models/poem.dart';
import '../models/zikr.dart';
import '../models/favorite.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  factory DatabaseHelper() => _instance;

  DatabaseHelper._internal();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'wahaj_alsaalik.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _createDb,
    );
  }

  Future<void> _createDb(Database db, int version) async {
    // Create books table
    await db.execute('''
      CREATE TABLE books(
        id INTEGER PRIMARY KEY,
        title TEXT,
        author TEXT,
        content TEXT,
        image TEXT
      )
    ''');

    // Create poems table
    await db.execute('''
      CREATE TABLE poems(
        id INTEGER PRIMARY KEY,
        title TEXT,
        poet TEXT,
        content TEXT
      )
    ''');

    // Create azkar table
    await db.execute('''
      CREATE TABLE azkar(
        id INTEGER PRIMARY KEY,
        category TEXT,
        content TEXT,
        count INTEGER
      )
    ''');

    // Create favorites table
    await db.execute('''
      CREATE TABLE favorites(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        item_id TEXT NOT NULL,
        type TEXT NOT NULL,
        date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(item_id, type)
      )
    ''');

    // Insert initial data
    await _insertInitialData(db);
  }

  Future<void> _insertInitialData(Database db) async {
    try {
      // Load books from JSON file
      final String booksJson =
          await rootBundle.loadString('assets/data/books.json');
      final List<dynamic> booksData = json.decode(booksJson);
      for (var bookData in booksData) {
        await db.insert('books', bookData);
      }

      // Load poems from JSON file
      final String poemsJson =
          await rootBundle.loadString('assets/data/poems.json');
      final List<dynamic> poemsData = json.decode(poemsJson);
      for (var poemData in poemsData) {
        await db.insert('poems', poemData);
      }

      // تحميل بيانات الأذكار بالطريقة الصحيحة
      final String azkarJson =
          await rootBundle.loadString('assets/data/azkar.json');
      final Map<String, dynamic> azkarData = json.decode(azkarJson);

      if (azkarData['categories'] != null) {
        final List<dynamic> categories = azkarData['categories'];

        for (var categoryData in categories) {
          // حفظ الفئة الرئيسية
          final categoryMap = {
            'id': categoryData['id'],
            'category': categoryData['name'],
            'content': categoryData['description'],
            'count': categoryData['count'] ?? 0,
          };
          await db.insert('azkar', categoryMap);

          // حفظ الأذكار في الفئة
          if (categoryData['items'] != null) {
            for (var item in categoryData['items']) {
              final itemMap = {
                'id': item['id'],
                'category': categoryData['name'],
                'content': item['text'],
                'count': item['count'] ?? 1,
              };
              await db.insert('azkar', itemMap);
            }
          }

          // حفظ الفئات الفرعية
          if (categoryData['subcategories'] != null) {
            for (var subcategoryData in categoryData['subcategories']) {
              final subcategoryMap = {
                'id': subcategoryData['id'],
                'category':
                    '${categoryData['name']} - ${subcategoryData['name']}',
                'content': subcategoryData['description'],
                'count': subcategoryData['count'] ?? 0,
              };
              await db.insert('azkar', subcategoryMap);

              // حفظ الأذكار في الفئة الفرعية
              if (subcategoryData['items'] != null) {
                for (var item in subcategoryData['items']) {
                  final itemMap = {
                    'id': item['id'],
                    'category':
                        '${categoryData['name']} - ${subcategoryData['name']}',
                    'content': item['text'],
                    'count': item['count'] ?? 1,
                  };
                  await db.insert('azkar', itemMap);
                }
              }
            }
          }
        }
      }
    } catch (e) {
      debugPrint("خطأ في إدخال البيانات الأولية: $e");
      rethrow;
    }
  }

  // Books methods
  Future<List<Book>> getBooks() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('books');
    return List.generate(maps.length, (i) => Book.fromMap(maps[i]));
  }

  Future<Book?> getBook(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'books',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Book.fromMap(maps.first);
    }
    return null;
  }

  // Poems methods - تم تحديثها لتحميل قصائد محمد هزاع باعلوي فقط
  Future<List<Poem>> getPoems() async {
    try {
      // التحقق من وجود القصائد في قاعدة البيانات أولاً
      final db = await database;
      final List<Map<String, dynamic>> dbPoems = await db
          .query('poems', where: "poet = ?", whereArgs: ["محمد هزاع باعلوي"]);

      // إذا كانت قاعدة البيانات تحتوي على القصائد
      if (dbPoems.isNotEmpty) {
        // Verificar si la tabla tiene la columna 'type' o 'item_type'
        final columns = await db.rawQuery('PRAGMA table_info(favorites)');
        final columnNames = columns.map((c) => c['name'].toString()).toList();

        // Determinar qué columna usar
        final typeColumnName =
            columnNames.contains('item_type') ? 'item_type' : 'type';

        // الحصول على حالة المفضلة من جدول المفضلة
        final List<Map<String, dynamic>> favorites = await db.query('favorites',
            where: '$typeColumnName = ?', whereArgs: ['poem']);
        final Set<String> favoriteIds =
            favorites.map((f) => f['item_id'].toString()).toSet();

        return dbPoems.map((map) {
          final poem = Poem.fromMap(map);
          // تحديث حالة المفضلة
          return poem.copyWith(isFavorite: favoriteIds.contains(poem.id));
        }).toList();
      }

      // إذا لم تكن موجودة في قاعدة البيانات، قم بتحميلها من ملف JSON
      final String jsonString =
          await rootBundle.loadString('assets/data/poems_baalawi.json');
      final Map<String, dynamic> data = json.decode(jsonString);
      final List<dynamic> poemsData = data['poems'];

      // الحصول على حالة المفضلة من جدول المفضلة
      final List<Map<String, dynamic>> favorites =
          await db.query('favorites', where: 'type = ?', whereArgs: ['poem']);
      final Set<String> favoriteIds =
          favorites.map((f) => f['item_id'].toString()).toSet();

      // تحويل البيانات إلى قائمة من القصائد
      final poems = poemsData.map((item) {
        final poem = Poem.fromJson(item);
        // تحديث حالة المفضلة
        return poem.copyWith(isFavorite: favoriteIds.contains(poem.id));
      }).toList();

      // حفظ القصائد في قاعدة البيانات للاستخدام المستقبلي
      for (var poem in poems) {
        await db.insert(
            'poems',
            {
              'id': poem.id,
              'title': poem.title,
              'poet': poem.poet,
              'category': poem.category,
              'content': poem.content,
              'era': poem.era,
            },
            conflictAlgorithm: ConflictAlgorithm.replace);
      }

      return poems;
    } catch (e) {
      debugPrint('Error loading poems: $e');
      // في حالة الخطأ، حاول تحميل القصائد من ملف JSON فقط
      try {
        final String jsonString =
            await rootBundle.loadString('assets/data/poems_baalawi.json');
        final Map<String, dynamic> data = json.decode(jsonString);
        final List<dynamic> poemsData = data['poems'];
        return poemsData.map((item) => Poem.fromJson(item)).toList();
      } catch (innerError) {
        debugPrint('Error loading poems from JSON: $innerError');
        return [];
      }
    }
  }

  Future<Poem?> getPoem(String id) async {
    try {
      final db = await database;

      // البحث عن القصيدة في قاعدة البيانات
      final List<Map<String, dynamic>> maps = await db.query(
        'poems',
        where: 'id = ?',
        whereArgs: [id],
      );

      // Verificar si la tabla tiene la columna 'type' o 'item_type'
      final columns = await db.rawQuery('PRAGMA table_info(favorites)');
      final columnNames = columns.map((c) => c['name'].toString()).toList();

      // Determinar qué columna usar
      final typeColumnName =
          columnNames.contains('item_type') ? 'item_type' : 'type';

      // التحقق من حالة المفضلة
      final List<Map<String, dynamic>> favorites = await db.query(
        'favorites',
        where: 'item_id = ? AND $typeColumnName = ?',
        whereArgs: [id, 'poem'],
      );
      final bool isPoemFavorite = favorites.isNotEmpty;

      if (maps.isNotEmpty) {
        final poem = Poem.fromMap(maps.first);
        return poem.copyWith(isFavorite: isPoemFavorite);
      }

      // إذا لم تكن موجودة في قاعدة البيانات، ابحث في ملف JSON الخاص بمحمد هزاع باعلوي
      final String jsonString =
          await rootBundle.loadString('assets/data/poems_baalawi.json');
      final Map<String, dynamic> data = json.decode(jsonString);
      final List<dynamic> poemsData = data['poems'];

      dynamic poemData;
      try {
        poemData = poemsData.firstWhere(
          (item) => item['id'].toString() == id,
        );
      } catch (_) {
        poemData = null;
      }

      if (poemData != null) {
        final poem = Poem.fromJson(poemData);
        return poem.copyWith(isFavorite: isPoemFavorite);
      }

      return null;
    } catch (e) {
      debugPrint('Error getting poem: $e');
      return null;
    }
  }

  // Azkar methods
  Future<List<Zikr>> getAzkar() async {
    final String response =
        await rootBundle.loadString('assets/data/azkar.json');
    final data = json.decode(response);

    if (data['categories'] != null) {
      return List<Zikr>.from(
          data['categories'].map((category) => Zikr.fromJson(category)));
    }

    return [];
  }

  Future<List<ZikrItem>> getAzkarByCategory(String categoryId) async {
    final String response =
        await rootBundle.loadString('assets/data/azkar.json');
    final data = json.decode(response);

    if (data['categories'] != null) {
      final categories = List<Zikr>.from(
          data['categories'].map((category) => Zikr.fromJson(category)));

      // البحث عن الفئة المطلوبة
      final category = categories.firstWhere(
        (cat) => cat.id == categoryId,
        orElse: () => throw Exception('فئة الأذكار غير موجودة'),
      );

      return category.items ?? [];
    }

    return [];
  }

  // Favorites methods
  Future<List<Favorite>> getFavorites() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('favorites');
    return List.generate(maps.length, (i) => Favorite.fromMap(maps[i]));
  }

  Future<void> addFavorite(int itemId, String type) async {
    try {
      final db = await database;

      // التحقق من وجود جدول المفضلة
      final tables = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='favorites'");
      if (tables.isEmpty) {
        // إنشاء الجدول إذا لم يكن موجودًا
        await db.execute('''
          CREATE TABLE favorites(
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            item_id TEXT NOT NULL,
            item_type TEXT NOT NULL,
            timestamp INTEGER DEFAULT (strftime('%s','now') * 1000),
            UNIQUE(item_id, item_type)
          )
        ''');
      }

      // Verificar si la tabla tiene la columna 'type' o 'item_type'
      final columns = await db.rawQuery('PRAGMA table_info(favorites)');
      final columnNames = columns.map((c) => c['name'].toString()).toList();

      // Determinar qué columna usar
      final typeColumnName =
          columnNames.contains('item_type') ? 'item_type' : 'type';

      await db.insert(
        'favorites',
        {
          'item_id': itemId.toString(),
          typeColumnName: type,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      debugPrint(
          'Elemento añadido a favoritos con éxito usando columna: $typeColumnName');
    } catch (e) {
      debugPrint('Error adding to favorites: $e');
      rethrow;
    }
  }

  Future<void> removeFavorite(int itemId, String type) async {
    try {
      final db = await database;

      // التحقق من وجود جدول المفضلة
      final tables = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='favorites'");
      if (tables.isEmpty) {
        return; // لا داعي لإزالة العنصر إذا لم يكن هناك جدول
      }

      // Verificar si la tabla tiene la columna 'type' o 'item_type'
      final columns = await db.rawQuery('PRAGMA table_info(favorites)');
      final columnNames = columns.map((c) => c['name'].toString()).toList();

      // Determinar qué columna usar
      final typeColumnName =
          columnNames.contains('item_type') ? 'item_type' : 'type';

      await db.delete(
        'favorites',
        where: 'item_id = ? AND $typeColumnName = ?',
        whereArgs: [itemId.toString(), type],
      );

      debugPrint(
          'Elemento eliminado de favoritos con éxito usando columna: $typeColumnName');
    } catch (e) {
      debugPrint('Error removing from favorites: $e');
      rethrow;
    }
  }

  Future<bool> isFavorite(int itemId, String type) async {
    try {
      final db = await database;

      // التحقق من وجود جدول المفضلة
      final tables = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='favorites'");
      if (tables.isEmpty) {
        // إنشاء الجدول إذا لم يكن موجودًا
        await db.execute('''
          CREATE TABLE favorites(
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            item_id TEXT NOT NULL,
            item_type TEXT NOT NULL,
            timestamp INTEGER DEFAULT (strftime('%s','now') * 1000),
            UNIQUE(item_id, item_type)
          )
        ''');
        return false;
      }

      // Verificar si la tabla tiene la columna 'type' o 'item_type'
      final columns = await db.rawQuery('PRAGMA table_info(favorites)');
      final columnNames = columns.map((c) => c['name'].toString()).toList();

      // Determinar qué columna usar
      final typeColumnName =
          columnNames.contains('item_type') ? 'item_type' : 'type';

      final result = await db.query(
        'favorites',
        where: 'item_id = ? AND $typeColumnName = ?',
        whereArgs: [itemId.toString(), type],
      );

      return result.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking favorite status: $e');
      rethrow;
    }
  }

  // مسح جميع البيانات
  Future<void> clearAllData() async {
    final db = await database;

    try {
      // التحقق من وجود جدول المفضلة
      final tables = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='favorites'");
      if (tables.isNotEmpty) {
        await db.delete('favorites');
      }

      // هنا يمكنك إضافة مسح لجداول أخرى إذا كانت موجودة
    } catch (e) {
      debugPrint('Error clearing data: $e');
      rethrow;
    }
  }
}
