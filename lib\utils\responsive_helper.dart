import 'package:flutter/material.dart';

class ResponsiveHelper {
  // حدود الأجهزة المختلفة
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;

  // التحقق من نوع الجهاز
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < desktopBreakpoint;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktopBreakpoint;
  }

  // الحصول على عدد الأعمدة المناسب لحجم الشاشة
  static int getColumnCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < mobileBreakpoint) {
      return 2; // هاتف
    } else if (width < tabletBreakpoint) {
      return 3; // جهاز لوحي صغير
    } else if (width < desktopBreakpoint) {
      return 4; // جهاز لوحي كبير
    } else {
      return 5; // سطح المكتب
    }
  }

  // الحصول على حجم الخط المناسب لحجم الشاشة
  static double getFontSize(BuildContext context, double baseFontSize) {
    if (isTablet(context)) {
      return baseFontSize * 1.2;
    } else if (isDesktop(context)) {
      return baseFontSize * 1.4;
    }
    return baseFontSize;
  }

  // الحصول على حجم الأيقونة المناسب لحجم الشاشة
  static double getIconSize(BuildContext context, double baseIconSize) {
    if (isTablet(context)) {
      return baseIconSize * 1.2;
    } else if (isDesktop(context)) {
      return baseIconSize * 1.4;
    }
    return baseIconSize;
  }

  // الحصول على المسافة الداخلية المناسبة لحجم الشاشة
  static EdgeInsets getPadding(BuildContext context, EdgeInsets basePadding) {
    if (isTablet(context)) {
      return basePadding * 1.5;
    } else if (isDesktop(context)) {
      return basePadding * 2;
    }
    return basePadding;
  }
}
