import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:math' as math;
import '../../utils/app_colors.dart';

class AboutScreen extends StatefulWidget {
  const AboutScreen({super.key});

  @override
  State<AboutScreen> createState() => _AboutScreenState();
}

class _AboutScreenState extends State<AboutScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late ScrollController _scrollController;
  double _scrollProgress = 0.0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 20),
    )..repeat();

    _scrollController = ScrollController();
    _scrollController.addListener(_updateScrollProgress);
  }

  void _updateScrollProgress() {
    if (_scrollController.position.maxScrollExtent > 0) {
      setState(() {
        _scrollProgress = _scrollController.offset /
            _scrollController.position.maxScrollExtent;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final size = MediaQuery.of(context).size;
    final appColor = AppColors.getAzkarColor(isDarkMode);

    return Scaffold(
      body: Stack(
        children: [
          // خلفية متحركة
          AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return CustomPaint(
                size: Size(size.width, size.height),
                painter: BackgroundPainter(
                  animation: _animationController,
                  isDarkMode: isDarkMode,
                  scrollProgress: _scrollProgress,
                ),
              );
            },
          ),

          // زخارف إسلامية
          Positioned(
            top: -50,
            right: -50,
            child: Opacity(
              opacity: 0.1,
              child: SvgPicture.asset(
                'assets/images/p2.svg',
                width: 200,
                height: 200,
                colorFilter: ColorFilter.mode(
                  appColor,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),

          Positioned(
            bottom: -50,
            left: -50,
            child: Opacity(
              opacity: 0.1,
              child: SvgPicture.asset(
                'assets/images/p2.svg',
                width: 200,
                height: 200,
                colorFilter: ColorFilter.mode(
                  appColor,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),

          // محتوى الصفحة
          SafeArea(
            child: CustomScrollView(
              controller: _scrollController,
              physics: const BouncingScrollPhysics(),
              slivers: [
                // شريط العنوان
                SliverAppBar(
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  pinned: true,
                  expandedHeight: 200,
                  flexibleSpace: FlexibleSpaceBar(
                    background: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            appColor.withAlpha(40),
                            Colors.transparent,
                          ],
                        ),
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                color: appColor.withAlpha(26),
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: appColor.withAlpha(51),
                                    blurRadius: 20,
                                    spreadRadius: 5,
                                  ),
                                ],
                              ),
                              child: Center(
                                child: Icon(
                                  Icons.auto_awesome,
                                  size: 40,
                                  color: appColor,
                                ),
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'وهج السالك',
                              style: TextStyle(
                                fontSize: 28,
                                fontWeight: FontWeight.bold,
                                color:
                                    isDarkMode ? Colors.white : Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'ينابيع الحكمة وأنوار المعرفة',
                              style: TextStyle(
                                fontSize: 16,
                                color: isDarkMode
                                    ? Colors.white.withAlpha(179)
                                    : Colors.black87.withAlpha(179),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                // محتوى الصفحة
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildSectionTitle('رسالة من المطور', appColor),
                        const SizedBox(height: 16),
                        _buildMessageCard(context, isDarkMode, appColor),
                        const SizedBox(height: 32),
                        _buildSectionTitle('عن التطبيق', appColor),
                        const SizedBox(height: 16),
                        _buildAboutCard(context, isDarkMode, appColor),
                        const SizedBox(height: 32),
                        _buildSectionTitle('المميزات', appColor),
                        const SizedBox(height: 16),
                        _buildFeaturesList(context, isDarkMode, appColor),
                        const SizedBox(height: 32),
                        _buildSectionTitle('معلومات التطبيق', appColor),
                        const SizedBox(height: 16),
                        _buildInfoCard(context, isDarkMode, appColor),
                        const SizedBox(height: 40),
                        Center(
                          child: Text(
                            'جميع الحقوق محفوظة © ${DateTime.now().year}',
                            style: TextStyle(
                              fontSize: 14,
                              color: isDarkMode
                                  ? Colors.white.withAlpha(153)
                                  : Colors.black87.withAlpha(153),
                            ),
                          ),
                        ),
                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title, Color appColor) {
    return Row(
      // تحديد اتجاه الصف من اليمين إلى اليسار
      textDirection: TextDirection.rtl,
      children: [
        Container(
          width: 4,
          height: 24,
          decoration: BoxDecoration(
            color: appColor,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: appColor,
          ),
          textDirection: TextDirection.rtl,
        ),
      ],
    );
  }

  Widget _buildMessageCard(
      BuildContext context, bool isDarkMode, Color appColor) {
    return Card(
      elevation: 4,
      shadowColor: appColor.withAlpha(40),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: appColor.withAlpha(40),
          width: 1,
        ),
      ),
      color: isDarkMode ? const Color(0xFF1A2530).withAlpha(230) : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Row(
              textDirection: TextDirection.rtl,
              children: [
                CircleAvatar(
                  backgroundColor: appColor.withAlpha(40),
                  child: Icon(
                    Icons.format_quote,
                    color: appColor,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'بسم الله الرحمن الرحيم',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'الحمد لله رب العالمين، والصلاة والسلام على أشرف المرسلين، سيدنا محمد وعلى آله وصحبه أجمعين.',
              style: TextStyle(
                fontSize: 16,
                height: 1.6,
                color:
                    isDarkMode ? Colors.white.withAlpha(230) : Colors.black87,
              ),
              textDirection: TextDirection.rtl,
            ),
            const SizedBox(height: 12),
            Text(
              'أخي المسلم، أختي المسلمة، أضع بين أيديكم هذا التطبيق المتواضع "وهج السالك" ليكون عوناً لكم على ذكر الله وتسبيحه في كل وقت وحين. فالذكر هو غذاء الروح، وبه تطمئن القلوب، كما قال تعالى: "أَلَا بِذِكْرِ اللَّهِ تَطْمَئِنُّ الْقُلُوبُ".',
              style: TextStyle(
                fontSize: 16,
                height: 1.6,
                color:
                    isDarkMode ? Colors.white.withAlpha(230) : Colors.black87,
              ),
              textDirection: TextDirection.rtl,
            ),
            const SizedBox(height: 12),
            Text(
              'سعيت في هذا التطبيق أن يكون سهل الاستخدام، جميل المظهر، غني بالمحتوى، ليساعدكم على المداومة على الأذكار والأوراد اليومية. وأسأل الله أن يجعله في ميزان حسناتي وحسناتكم، وأن ينفع به المسلمين في مشارق الأرض ومغاربها.',
              style: TextStyle(
                fontSize: 16,
                height: 1.6,
                color:
                    isDarkMode ? Colors.white.withAlpha(230) : Colors.black87,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'وأخيراً، أرجو منكم الدعاء لي ولوالدي ولجميع المسلمين بالهداية والتوفيق والسداد.',
              style: TextStyle(
                fontSize: 16,
                height: 1.6,
                color:
                    isDarkMode ? Colors.white.withAlpha(230) : Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            Align(
              alignment: Alignment.centerRight,
              child: Text(
                'أخوكم في الله محمد بركات محمد باعلوي',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black87,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAboutCard(
      BuildContext context, bool isDarkMode, Color appColor) {
    return Card(
      elevation: 4,
      shadowColor: appColor.withAlpha(40),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: appColor.withAlpha(40),
          width: 1,
        ),
      ),
      color: isDarkMode ? const Color(0xFF1A2530).withAlpha(230) : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              'تطبيق "وهج السالك" هو تطبيق إسلامي متكامل يهدف إلى مساعدة المسلمين على الاستمرار في ذكر الله وتسبيحه في كل وقت وحين. يحتوي التطبيق على مجموعة من الأقسام المتنوعة التي تلبي احتياجات المسلم اليومية من الأذكار والأوراد.',
              style: TextStyle(
                fontSize: 16,
                height: 1.6,
                color:
                    isDarkMode ? Colors.white.withAlpha(230) : Colors.black87,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'تم تصميم التطبيق بعناية فائقة ليكون سهل الاستخدام وجميل المظهر، مع التركيز على تجربة المستخدم وسهولة الوصول إلى المحتوى. كما روعي في التصميم توافق التطبيق مع مختلف أحجام الشاشات والأجهزة.',
              style: TextStyle(
                fontSize: 16,
                height: 1.6,
                color:
                    isDarkMode ? Colors.white.withAlpha(230) : Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturesList(
      BuildContext context, bool isDarkMode, Color appColor) {
    // --- تم التعديل هنا ---
    // تم تغيير مفتاح 'icon' إلى 'svgIcon' للميزات الأربعة الأولى فقط
    final features = [
      {
        'title': 'الأذكار',
        'description': 'مجموعة شاملة من الأذكار اليومية مع شرح فضلها ومصدرها',
        'svgIcon': 'assets/icons/azkar/azkar.svg', // تم التعديل هنا
        'color': Colors.green,
        'isNew': false,
      },
      {
        'title': 'الصلاة على النبي',
        'description': 'مجموعة متنوعة من صيغ الصلاة على النبي بتصميم فاخر',
        'svgIcon': 'assets/icons/prophet_prayers/prophetPrayers.svg', // تم التعديل هنا
        'color': AppColors.prophetPrayersColor,
        'isNew': true,
      },
      {
        'title': 'الأدعية',
        'description': 'مجموعة من الأدعية المأثورة للمناسبات المختلفة',
        'svgIcon': 'assets/icons/azkar/duas.svg', // تم التعديل هنا
        'color': AppColors.duasColor,
        'isNew': true,
      },
      {
        'title': 'المسبحة الإلكترونية',
        'description':
            'مسبحة إلكترونية مع إمكانية تخصيص الأذكار وتتبع الإحصائيات',
        'svgIcon': 'assets/icons/azkar/tasbih.svg', // تم التعديل هنا
        'color': AppColors.tasbihColor,
        'isNew': false,
      },
      // الميزات المتبقية تستخدم 'icon' كما هي
      {
        'title': 'الأوراد اليومية',
        'description': 'إنشاء وتنظيم الأوراد اليومية ',
        'icon': Icons.auto_awesome_motion,
        'color': AppColors.tasbihColor,
        'isNew': false,
      },
      {
        'title': 'المفضلة',
        'description': 'حفظ العناصر المفضلة لديك من الأذكار والأدعية والصلوات',
        'icon': Icons.bookmark_rounded,
        'color': AppColors.favoritesColor,
        'isNew': false,
      },
      {
        'title': 'الوضع المظلم',
        'description':
            'دعم كامل للوضع المظلم لراحة العين أثناء الاستخدام الليلي',
        'icon': Icons.dark_mode_rounded,
        'color': Colors.indigo,
        'isNew': false,
      },
      {
        'title': 'تصميم فاخر',
        'description': 'واجهة مستخدم فاخرة مع تأثيرات حركية وتصميم متناسق',
        'icon': Icons.diamond_rounded,
        'color': Colors.amber,
        'isNew': false,
      },
    ];

    return Column(
      children: features.asMap().entries.map((entry) {
        final index = entry.key;
        final feature = entry.value;
        final featureColor = feature['color'] as Color;
        final isNew = feature['isNew'] as bool;

        // --- تم التعديل هنا ---
        // بناء ويدجت الأيقونة بشكل شرطي
        // إذا كانت الميزة تحتوي على 'svgIcon'، استخدم SvgPicture
        // وإلا، استخدم Icon كالسابق
        Widget iconWidget;
        if (feature.containsKey('svgIcon')) {
          iconWidget = SvgPicture.asset(
            feature['svgIcon'] as String,
            colorFilter: const ColorFilter.mode(
              Colors.white, // يجب أن يكون أبيض ليعمل ShaderMask
              BlendMode.srcIn,
            ),
            width: 34,
            height: 34,
          );
        } else {
          iconWidget = Icon(
            feature['icon'] as IconData,
            color: Colors.white, // يجب أن يكون أبيض ليعمل ShaderMask
            size: 30,
          );
        }

        return TweenAnimationBuilder<double>(
          tween: Tween<double>(begin: 0.0, end: 1.0),
          duration: Duration(milliseconds: 500 + (index * 100)),
          curve: Curves.easeOutCubic,
          builder: (context, value, child) {
            return Transform.translate(
              offset: Offset(0, 20 * (1 - value)),
              child: Opacity(
                opacity: value,
                child: Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: featureColor.withAlpha(20),
                        blurRadius: 10,
                        spreadRadius: 0,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        HapticFeedback.mediumImpact();
                      },
                      borderRadius: BorderRadius.circular(20),
                      child: Ink(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              isDarkMode
                                  ? const Color(0xFF1A2530).withAlpha(230)
                                  : Colors.white,
                              isDarkMode
                                  ? const Color(0xFF1A2530).withAlpha(230)
                                  : Colors.white,
                            ],
                          ),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: featureColor.withAlpha(40),
                            width: 1.5,
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Row(
                            textDirection: TextDirection.rtl,
                            children: [
                              Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      featureColor.withAlpha(40),
                                      featureColor.withAlpha(20),
                                    ],
                                  ),
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: featureColor.withAlpha(30),
                                      blurRadius: 8,
                                      spreadRadius: 0,
                                      offset: const Offset(0, 3),
                                    ),
                                  ],
                                  border: Border.all(
                                    color: featureColor.withAlpha(60),
                                    width: 1.5,
                                  ),
                                ),
                                child: Center(
                                  child: ShaderMask(
                                    shaderCallback: (bounds) {
                                      return LinearGradient(
                                        colors: [
                                          featureColor,
                                          featureColor.withAlpha(200),
                                        ],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ).createShader(bounds);
                                    },
                                    // --- تم التعديل هنا ---
                                    // استخدام الويدجت الذي تم إنشاؤه شرطياً
                                    child: iconWidget,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Row(
                                      textDirection: TextDirection.rtl,
                                      children: [
                                        Text(
                                          feature['title'] as String,
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                            color: isDarkMode
                                                ? Colors.white
                                                : Colors.black87,
                                          ),
                                          textDirection: TextDirection.rtl,
                                        ),
                                        if (isNew) ...[
                                          const SizedBox(width: 8),
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 2,
                                            ),
                                            decoration: BoxDecoration(
                                              color: featureColor.withAlpha(40),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                              border: Border.all(
                                                color: featureColor
                                                    .withAlpha(100),
                                                width: 1,
                                              ),
                                            ),
                                            child: Text(
                                              'جديد',
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                                color: featureColor,
                                              ),
                                              textDirection:
                                                  TextDirection.rtl,
                                            ),
                                          ),
                                        ],
                                      ],
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      feature['description'] as String,
                                      style: TextStyle(
                                        fontSize: 14,
                                        height: 1.4,
                                        color: isDarkMode
                                            ? Colors.white.withAlpha(179)
                                            : Colors.black87.withAlpha(179),
                                      ),
                                      textDirection: TextDirection.rtl,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        );
      }).toList(),
    );
  }

  Widget _buildInfoCard(BuildContext context, bool isDarkMode, Color appColor) {
    final infoItems = [
      {
        'title': 'الإصدار',
        'value': '1.0.5',
        'icon': Icons.info_outline,
        'action': null,
      },
      {
        'title': 'تاريخ الإصدار',
        'value': '2025',
        'icon': Icons.calendar_today_outlined,
        'action': null,
      },
    ];

    return Column(
      children: [
        Card(
          elevation: 4,
          shadowColor: appColor.withAlpha(40),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(
              color: appColor.withAlpha(40),
              width: 1,
            ),
          ),
          color: isDarkMode
              ? const Color(0xFF1A2530).withAlpha(230)
              : Colors.white,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: infoItems.map((item) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Row(
                    textDirection: TextDirection.rtl,
                    children: [
                      Icon(
                        item['icon'] as IconData,
                        size: 20,
                        color: appColor,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        '${item['title']}:',
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                          color: isDarkMode ? Colors.white : Colors.black87,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          item['value'] as String,
                          style: TextStyle(
                            fontSize: 15,
                            color: isDarkMode
                                ? Colors.white.withAlpha(204)
                                : Colors.black87.withAlpha(204),
                          ),
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ),
        const SizedBox(height: 32),
        _buildDeveloperSection(context, isDarkMode, appColor),
      ],
    );
  }

  Widget _buildDeveloperSection(
      BuildContext context, bool isDarkMode, Color appColor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('عن المطور', appColor),
        const SizedBox(height: 16),
        Card(
          elevation: 8,
          shadowColor: appColor.withAlpha(60),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
            side: BorderSide(
              color: appColor.withAlpha(50),
              width: 1.5,
            ),
          ),
          color: isDarkMode
              ? const Color(0xFF1A2530).withAlpha(230)
              : Colors.white,
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        appColor.withAlpha(40),
                        appColor.withAlpha(80),
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: appColor.withAlpha(60),
                        blurRadius: 15,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: Center(
                    child: Icon(
                      Icons.person,
                      size: 50,
                      color: appColor,
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  'محمد بركات محمد باعلوي',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'مطور تطبيقات إسلامية',
                  style: TextStyle(
                    fontSize: 16,
                    color: isDarkMode
                        ? Colors.white.withAlpha(204)
                        : Colors.black87.withAlpha(204),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                _buildContactButton(
                  context: context,
                  icon: Icons.email_outlined,
                  title: 'البريد الإلكتروني',
                  value: '<EMAIL>',
                  appColor: appColor,
                  isDarkMode: isDarkMode,
                  onTap: () => _contactDeveloper(context),
                ),
                const SizedBox(height: 16),
                _buildContactButton(
                  context: context,
                  icon: Icons.language,
                  title: 'الموقع الإلكتروني',
                  value: 'www.wahajalsalik.com',
                  appColor: appColor,
                  isDarkMode: isDarkMode,
                  onTap: () => _openWebsite(context),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContactButton({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String value,
    required Color appColor,
    required bool isDarkMode,
    required VoidCallback onTap,
  }) {
    bool isPressed = false;

    return StatefulBuilder(
      builder: (context, setState) {
        return GestureDetector(
          onTap: onTap,
          onTapDown: (_) {
            setState(() {
              isPressed = true;
            });
            HapticFeedback.lightImpact();
          },
          onTapUp: (_) {
            setState(() {
              isPressed = false;
            });
          },
          onTapCancel: () {
            setState(() {
              isPressed = false;
            });
          },
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeOutCubic,
            transform: Matrix4.identity()..scale(isPressed ? 0.97 : 1.0),
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  appColor.withAlpha(isPressed ? 40 : 20),
                  appColor.withAlpha(isPressed ? 60 : 40),
                ],
              ),
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: appColor.withAlpha(isPressed ? 30 : 20),
                  blurRadius: isPressed ? 5 : 8,
                  spreadRadius: isPressed ? 0 : 1,
                  offset: isPressed ? const Offset(0, 1) : const Offset(0, 2),
                ),
              ],
              border: Border.all(
                color: appColor.withAlpha(isPressed ? 60 : 30),
                width: 1,
              ),
            ),
            child: Row(
              textDirection: TextDirection.rtl,
              children: [
                AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: appColor.withAlpha(isPressed ? 50 : 30),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: appColor.withAlpha(isPressed ? 40 : 20),
                        blurRadius: isPressed ? 4 : 2,
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Icon(
                    icon,
                    size: 22,
                    color: appColor,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: isDarkMode
                              ? Colors.white.withAlpha(230)
                              : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      ShaderMask(
                        shaderCallback: (bounds) {
                          return LinearGradient(
                            colors: [
                              appColor,
                              appColor.withAlpha(220),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ).createShader(bounds);
                        },
                        child: Text(
                          value,
                          style: const TextStyle(
                            fontSize: 15,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  transform: Matrix4.identity()
                    ..translate(
                        isPressed ? 2.0 : 0.0, 0.0),
                  child: Icon(
                    Icons.arrow_back_ios,
                    size: 16,
                    color: appColor.withAlpha(isPressed ? 200 : 150),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _contactDeveloper(BuildContext context) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      query: _encodeQueryParameters({
        'subject': 'تواصل من تطبيق وهج السالك',
        'body': 'مرحباً،\n\nأرسلت هذا البريد لـ:\n\n',
      }),
    );
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    try {
      if (!await launchUrl(emailUri)) {
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: const Text('لا يمكن فتح تطبيق البريد الإلكتروني'),
              backgroundColor: Colors.red[700],
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: const Text('لا يمكن فتح تطبيق البريد الإلكتروني'),
            backgroundColor: Colors.red[700],
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  void _openWebsite(BuildContext context) async {
    final Uri url = Uri.parse('https://www.wahajalsalik.com');
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    try {
      if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: const Text('لا يمكن فتح الموقع الإلكتروني'),
              backgroundColor: Colors.red[700],
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: const Text('لا يمكن فتح الموقع الإلكتروني'),
            backgroundColor: Colors.red[700],
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  String? _encodeQueryParameters(Map<String, String> params) {
    return params.entries
        .map((e) =>
            '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');
  }
}

class BackgroundPainter extends CustomPainter {
  final Animation<double> animation;
  final bool isDarkMode;
  final double scrollProgress;

  BackgroundPainter({
    required this.animation,
    required this.isDarkMode,
    required this.scrollProgress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;
    final backgroundColor = isDarkMode
        ? const Color(0xFF1A1A2E)
        : const Color(0xFFF8F8F8);
    final backgroundPaint = Paint()..color = backgroundColor;
    canvas.drawRect(Rect.fromLTWH(0, 0, width, height), backgroundPaint);
    final decorationColor = AppColors.getAzkarColor(isDarkMode);
    final topGlowPaint = Paint()
      ..shader = RadialGradient(
        colors: [
          decorationColor.withAlpha(13),
          decorationColor.withAlpha(0),
        ],
        stops: const [0.0, 1.0],
        radius: 0.8,
      ).createShader(
        Rect.fromCircle(
          center: Offset(width * 0.2, height * 0.2 - scrollProgress * 100),
          radius: width * 0.8,
        ),
      );
    canvas.drawCircle(
      Offset(width * 0.2, height * 0.2 - scrollProgress * 100),
      width * 0.8,
      topGlowPaint,
    );
    final bottomGlowPaint = Paint()
      ..shader = RadialGradient(
        colors: [
          decorationColor.withAlpha(13),
          decorationColor.withAlpha(0),
        ],
        stops: const [0.0, 1.0],
        radius: 0.8,
      ).createShader(
        Rect.fromCircle(
          center: Offset(width * 0.8, height * 0.8 + scrollProgress * 100),
          radius: width * 0.8,
        ),
      );
    canvas.drawCircle(
      Offset(width * 0.8, height * 0.8 + scrollProgress * 100),
      width * 0.8,
      bottomGlowPaint,
    );
    final patternPaint = Paint()
      ..color = decorationColor.withAlpha(5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    for (int i = 0; i < 5; i++) {
      final radius = 50.0 + i * 40.0;
      final offset = 10.0 * math.sin(animation.value * math.pi * 2 + i);
      canvas.drawCircle(
        Offset(width * 0.5, height * 0.5 + scrollProgress * 50),
        radius + offset,
        patternPaint,
      );
    }
    final starPath = Path();
    const points = 8;
    const angleStep = 2 * math.pi / points;
    const outerRadius = 120.0;
    final innerRadius = 60.0 *
        (0.9 + 0.1 * math.sin(animation.value * math.pi * 2 + math.pi / 2));
    for (int i = 0; i < points; i++) {
      final outerAngle = i * angleStep;
      final innerAngle = outerAngle + angleStep / 2;
      final outerX = width * 0.5 + math.cos(outerAngle) * outerRadius;
      final outerY = height * 0.5 +
          math.sin(outerAngle) * outerRadius +
          scrollProgress * 50;
      final innerX = width * 0.5 + math.cos(innerAngle) * innerRadius;
      final innerY = height * 0.5 +
          math.sin(innerAngle) * innerRadius +
          scrollProgress * 50;
      if (i == 0) {
        starPath.moveTo(outerX, outerY);
      } else {
        starPath.lineTo(outerX, outerY);
      }
      starPath.lineTo(innerX, innerY);
    }
    starPath.close();
    canvas.drawPath(starPath, patternPaint);
  }

  @override
  bool shouldRepaint(BackgroundPainter oldDelegate) =>
      oldDelegate.animation.value != animation.value ||
      oldDelegate.scrollProgress != scrollProgress;
}
