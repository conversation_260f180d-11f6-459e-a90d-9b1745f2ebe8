// lib/utils/feature_showcase.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:ui';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/app_colors.dart';

/// مكتبة لعرض الشرح التوضيحي للميزات بتصميم فاخر
class FeatureShowcase {
  // مفتاح لتخزين حالة عرض الشرح للمرة الأولى
  static const String firstTimeKey = 'first_time_app_open';

  /// التحقق مما إذا كان هذا هو فتح التطبيق لأول مرة
  static Future<bool> isFirstTimeOpen() async {
    final prefs = await SharedPreferences.getInstance();
    return !(prefs.getBool(firstTimeKey) ?? false);
  }

  /// تعيين حالة فتح التطبيق
  static Future<void> setFirstTimeOpened() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(firstTimeKey, true);
  }

  /// عرض شرح متعدد الخطوات للصفحة الرئيسية
  static Future<void> showHomeScreenTutorial(BuildContext context) async {
    // التحقق مما إذا كان هذا هو فتح التطبيق لأول مرة
    final isFirstTime = await isFirstTimeOpen();
    if (!isFirstTime) {
      return;
    }

    // تعيين حالة فتح التطبيق
    await setFirstTimeOpened();

    // تأخير قصير لضمان بناء الواجهة بشكل كامل
    await Future.delayed(const Duration(milliseconds: 500));

    // التحقق من أن السياق لا يزال صالحًا
    if (!context.mounted) return;

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // عرض شرح ترحيبي
    await _showWelcomeMessage(context, isDarkMode);

    // التحقق من أن السياق لا يزال صالحًا
    if (!context.mounted) return;

    // عرض شرح الأقسام المختلفة
    await _showSectionsTutorial(context, isDarkMode);

    // التحقق من أن السياق لا يزال صالحًا
    if (!context.mounted) return;

    // عرض شرح الحكمة
    await _showWisdomTutorial(context, isDarkMode);
  }

  /// عرض رسالة ترحيبية
  static Future<void> _showWelcomeMessage(
      BuildContext context, bool isDarkMode) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          elevation: 0,
          child: TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: 0.8, end: 1.0),
            duration: const Duration(milliseconds: 500),
            curve: Curves.elasticOut,
            builder: (context, value, child) {
              return Transform.scale(
                scale: value,
                child: child,
              );
            },
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: isDarkMode ? const Color(0xFF1A2530) : Colors.white,
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.azkarColor.withOpacity(0.3),
                    blurRadius: 20,
                    spreadRadius: 5,
                  ),
                ],
                border: Border.all(
                  color: AppColors.azkarColor.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // أيقونة الترحيب
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: AppColors.azkarColor.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: SvgPicture.asset(
                        'assets/images/p2.svg',
                        width: 50,
                        height: 50,
                        colorFilter: const ColorFilter.mode(
                          AppColors.azkarColor,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // عنوان الترحيب
                  const Text(
                    'مرحباً بك في وهج السالك',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: AppColors.azkarColor,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // وصف التطبيق
                  Text(
                    'تطبيق إسلامي متكامل يساعدك على الاستمرار في ذكر الله وتلاوة الأدعية والصلاة على النبي بتصميم فاخر وسلس',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      height: 1.5,
                      color: isDarkMode
                          ? Colors.white.withOpacity(0.9)
                          : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 24),

                  // زر البدء
                  ElevatedButton(
                    onPressed: () {
                      HapticFeedback.mediumImpact();
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.azkarColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      elevation: 4,
                    ),
                    child: const Text(
                      'هيا نبدأ',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// عرض شرح الأقسام
  static Future<void> _showSectionsTutorial(
      BuildContext context, bool isDarkMode) async {
    return showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          elevation: 0,
          insetPadding: const EdgeInsets.all(16),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: isDarkMode ? const Color(0xFF1A2530) : Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: AppColors.azkarColor.withOpacity(0.3),
                  blurRadius: 15,
                  spreadRadius: 2,
                ),
              ],
              border: Border.all(
                color: AppColors.azkarColor.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // عنوان الشرح
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color:
                            AppColors.azkarColor.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.category_rounded,
                        color: AppColors.azkarColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 15),
                    const Expanded(
                      child: Text(
                        'أقسام التطبيق',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.azkarColor,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(
                        Icons.close,
                        color: AppColors.azkarColor,
                      ),
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // شرح الأقسام
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.azkarColor.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AppColors.azkarColor.withOpacity(0.1),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      _buildFeatureItem(
                        icon: Icons.favorite_rounded,
                        title: 'الأذكار',
                        description: 'مجموعة من الأذكار اليومية والمأثورة',
                        isDarkMode: isDarkMode,
                      ),
                      const SizedBox(height: 16),
                      _buildFeatureItem(
                        icon: Icons.panorama_fish_eye,
                        title: 'المسبحة الإلكترونية',
                        description: 'سبّح بسهولة مع إحصائيات دقيقة',
                        isDarkMode: isDarkMode,
                      ),
                      const SizedBox(height: 16),
                      _buildFeatureItem(
                        icon: Icons.volunteer_activism_rounded,
                        title: 'الأدعية',
                        description: 'مجموعة من الأدعية المأثورة',
                        isDarkMode: isDarkMode,
                      ),
                      const SizedBox(height: 16),
                      _buildFeatureItem(
                        icon: Icons.auto_awesome_rounded,
                        title: 'الصلاة على النبي',
                        description: 'صيغ متنوعة للصلاة على النبي ﷺ',
                        isDarkMode: isDarkMode,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),

                // نصيحة للتصفح
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.amber.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.amber.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.swipe_rounded,
                        color: Colors.amber,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'يمكنك التمرير أفقياً للتنقل بين الأقسام المختلفة',
                          style: TextStyle(
                            fontSize: 14,
                            color: isDarkMode ? Colors.white : Colors.black87,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),

                // زر الفهم
                ElevatedButton(
                  onPressed: () {
                    HapticFeedback.mediumImpact();
                    Navigator.of(context).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.azkarColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'فهمت',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// عرض شرح الحكمة
  static Future<void> _showWisdomTutorial(
      BuildContext context, bool isDarkMode) async {
    return showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          elevation: 0,
          insetPadding: const EdgeInsets.all(16),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: isDarkMode ? const Color(0xFF1A2530) : Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: AppColors.azkarColor.withOpacity(0.3),
                  blurRadius: 15,
                  spreadRadius: 2,
                ),
              ],
              border: Border.all(
                color: AppColors.azkarColor.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // عنوان الشرح
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color:
                            AppColors.azkarColor.withValues(alpha: 0.15 * 255),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.format_quote,
                        color: AppColors.azkarColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 15),
                    const Expanded(
                      child: Text(
                        'حكمة اليوم',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.azkarColor,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(
                        Icons.close,
                        color: AppColors.azkarColor,
                      ),
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // شرح الحكمة
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.azkarColor.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AppColors.azkarColor.withOpacity(0.1),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      Text(
                        'تعرض لك حكمة جديدة في كل مرة تفتح فيها التطبيق',
                        style: TextStyle(
                          fontSize: 16,
                          height: 1.5,
                          color: isDarkMode ? Colors.white : Colors.black87,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: _buildWisdomFeature(
                              icon: Icons.touch_app,
                              text: 'اضغط على الحكمة لعرض شرحها',
                              isDarkMode: isDarkMode,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildWisdomFeature(
                              icon: Icons.refresh,
                              text: 'اضغط على زر التحديث لعرض حكمة أخرى',
                              isDarkMode: isDarkMode,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: _buildWisdomFeature(
                              icon: Icons.share,
                              text: 'يمكنك مشاركة الحكمة مع الآخرين',
                              isDarkMode: isDarkMode,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildWisdomFeature(
                              icon: Icons.swipe,
                              text: 'اسحب يميناً أو يساراً لتصفح الحكم',
                              isDarkMode: isDarkMode,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),

                // زر الفهم
                ElevatedButton(
                  onPressed: () {
                    HapticFeedback.mediumImpact();
                    Navigator.of(context).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.azkarColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'فهمت',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء عنصر ميزة
  static Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
    required bool isDarkMode,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.azkarColor.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: AppColors.azkarColor,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black87,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(
                  fontSize: 14,
                  color: isDarkMode ? Colors.white70 : Colors.black54,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء ميزة الحكمة
  static Widget _buildWisdomFeature({
    required IconData icon,
    required String text,
    required bool isDarkMode,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.azkarColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.azkarColor.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: AppColors.azkarColor,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: isDarkMode ? Colors.white70 : Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// عرض الشرح التوضيحي للميزة
  static Future<void> showFeature({
    required BuildContext context,
    required String featureId,
    required Widget targetWidget,
    required String title,
    required String description,
    String? svgIconPath,
    IconData? icon,
    Color? accentColor,
    VoidCallback? onFinish,
    bool forceShow = false,
  }) async {
    // التحقق من عرض الشرح سابقاً
    if (!forceShow) {
      final prefs = await SharedPreferences.getInstance();
      final hasShownBefore = prefs.getBool('showcase_$featureId') ?? false;
      if (hasShownBefore) {
        return;
      }
    }

    // تحديد اللون الرئيسي
    final color = accentColor ?? AppColors.azkarColor;

    // التحقق من أن السياق لا يزال صالحًا
    if (!context.mounted) return;

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // الحصول على موقع العنصر المستهدف
    // التحقق من أن السياق لا يزال صالحًا
    if (!context.mounted) return;

    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    // عرض الشرح التوضيحي
    // التحقق من أن السياق لا يزال صالحًا
    if (!context.mounted) return;

    await showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: 'showcase_barrier',
      barrierColor: Colors.black.withOpacity(0.5),
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation1, animation2) {
        return Material(
          type: MaterialType.transparency,
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 2, sigmaY: 2),
            child: Stack(
              children: [
                // منطقة الضغط لإغلاق الشرح
                Positioned.fill(
                  child: GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                      if (onFinish != null) {
                        onFinish();
                      }
                    },
                    behavior: HitTestBehavior.opaque,
                    child: Container(
                      color: Colors.transparent,
                    ),
                  ),
                ),

                // بطاقة الشرح
                Positioned(
                  left: 20,
                  right: 20,
                  bottom: 40,
                  child: TweenAnimationBuilder<double>(
                    tween: Tween<double>(begin: 0, end: 1),
                    duration: const Duration(milliseconds: 500),
                    curve: Curves.easeOutBack,
                    builder: (context, value, child) {
                      return Transform.translate(
                        offset: Offset(0, 50 * (1 - value)),
                        child: Opacity(
                          opacity: value,
                          child: child,
                        ),
                      );
                    },
                    child: Card(
                      elevation: 10,
                      shadowColor: color.withOpacity(0.3),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                        side: BorderSide(
                          color: color.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      color:
                          isDarkMode ? const Color(0xFF1A2530) : Colors.white,
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          gradient: LinearGradient(
                            begin: Alignment.topRight,
                            end: Alignment.bottomLeft,
                            colors: isDarkMode
                                ? [
                                    const Color(0xFF1D2D3A),
                                    const Color(0xFF182530),
                                  ]
                                : [
                                    Colors.white,
                                    color.withOpacity(0.05),
                                  ],
                          ),
                        ),
                        child: Stack(
                          children: [
                            // زخارف إسلامية في الخلفية
                            Positioned(
                              top: -30,
                              right: -30,
                              child: Opacity(
                                opacity: isDarkMode ? 0.05 : 0.07,
                                child: SvgPicture.asset(
                                  'assets/images/p2.svg',
                                  width: 100,
                                  height: 100,
                                  colorFilter: ColorFilter.mode(
                                    color,
                                    BlendMode.srcIn,
                                  ),
                                ),
                              ),
                            ),
                            Positioned(
                              bottom: -20,
                              left: -20,
                              child: Opacity(
                                opacity: isDarkMode ? 0.04 : 0.06,
                                child: SvgPicture.asset(
                                  'assets/images/p2.svg',
                                  width: 80,
                                  height: 80,
                                  colorFilter: ColorFilter.mode(
                                    color,
                                    BlendMode.srcIn,
                                  ),
                                ),
                              ),
                            ),

                            // محتوى الشرح
                            Padding(
                              padding: const EdgeInsets.all(20),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  // أيقونة وعنوان الميزة
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(10),
                                        decoration: BoxDecoration(
                                          color: color.withValues(alpha: 0.15),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          boxShadow: [
                                            BoxShadow(
                                              color: color.withOpacity(0.1),
                                              blurRadius: 8,
                                              offset: const Offset(0, 2),
                                            ),
                                          ],
                                        ),
                                        child: svgIconPath != null
                                            ? SvgPicture.asset(
                                                svgIconPath,
                                                width: 24,
                                                height: 24,
                                                colorFilter: ColorFilter.mode(
                                                  color,
                                                  BlendMode.srcIn,
                                                ),
                                              )
                                            : Icon(
                                                icon ?? Icons.lightbulb_outline,
                                                color: color,
                                                size: 24,
                                              ),
                                      ),
                                      const SizedBox(width: 15),
                                      Expanded(
                                        child: Text(
                                          title,
                                          style: TextStyle(
                                            fontSize: 20,
                                            fontWeight: FontWeight.bold,
                                            color: isDarkMode
                                                ? Colors.white
                                                : Colors.black87,
                                          ),
                                        ),
                                      ),
                                      IconButton(
                                        icon: Icon(
                                          Icons.close,
                                          color: color,
                                          size: 20,
                                        ),
                                        onPressed: () {
                                          Navigator.of(context).pop();
                                          if (onFinish != null) {
                                            onFinish();
                                          }
                                        },
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 15),

                                  // وصف الميزة
                                  Container(
                                    padding: const EdgeInsets.all(15),
                                    decoration: BoxDecoration(
                                      color:
                                          color.withOpacity(0.05),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color:
                                            color.withOpacity(0.1),
                                        width: 1,
                                      ),
                                    ),
                                    child: Text(
                                      description,
                                      style: TextStyle(
                                        fontSize: 16,
                                        height: 1.5,
                                        color: isDarkMode
                                            ? Colors.white
                                                .withOpacity(0.9)
                                            : Colors.black87,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(height: 20),

                                  // زر الفهم
                                  Center(
                                    child: ElevatedButton(
                                      onPressed: () async {
                                        HapticFeedback.mediumImpact();
                                        Navigator.of(context).pop();

                                        // حفظ حالة عرض الشرح
                                        if (!forceShow) {
                                          final prefs = await SharedPreferences
                                              .getInstance();
                                          await prefs.setBool(
                                              'showcase_$featureId', true);
                                        }

                                        if (onFinish != null) {
                                          onFinish();
                                        }
                                      },
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: color,
                                        foregroundColor: Colors.white,
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 24,
                                          vertical: 12,
                                        ),
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                        elevation: 4,
                                      ),
                                      child: const Text(
                                        'فهمت',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                // سهم يشير إلى العنصر المستهدف
                Positioned(
                  top: offset.dy + size.height + 10,
                  left: offset.dx + size.width / 2 - 15,
                  child: TweenAnimationBuilder<double>(
                    tween: Tween<double>(begin: 0, end: 1),
                    duration: const Duration(milliseconds: 800),
                    curve: Curves.elasticOut,
                    builder: (context, value, child) {
                      return Transform.scale(
                        scale: value,
                        child: child,
                      );
                    },
                    child: Icon(
                      Icons.arrow_upward_rounded,
                      color: color,
                      size: 30,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: child,
        );
      },
    );
  }
}
