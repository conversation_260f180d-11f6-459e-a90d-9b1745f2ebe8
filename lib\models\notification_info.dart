/// كلاس لتخزين معلومات الإشعار

class NotificationInfo {
  int id;
  String title;
  String body;
  String channelKey;
  DateTime displayTime;
  bool isOpened;

  NotificationInfo({
    required this.id,
    required this.title,
    required this.body,
    required this.channel<PERSON><PERSON>,
    required this.displayTime,
    this.isOpened = false,
  });

  // تحويل الكائن إلى Map للتخزين
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'channelKey': channelKey,
      'displayTime': displayTime.toIso8601String(),
      'isOpened': isOpened,
    };
  }

  // إنشاء كائن من Map
  factory NotificationInfo.fromJson(Map<String, dynamic> json) {
    return NotificationInfo(
      id: json['id'],
      title: json['title'],
      body: json['body'],
      channelKey: json['channelKey'],
      displayTime: DateTime.parse(json['displayTime']),
      isOpened: json['isOpened'] ?? false,
    );
  }
}
