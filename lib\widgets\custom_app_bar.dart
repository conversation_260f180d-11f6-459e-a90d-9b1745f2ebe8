import 'package:flutter/material.dart';

class CustomAppBar extends PreferredSize {
  final String title;
  final bool showBackButton;
  final PreferredSizeWidget? bottom;
  final List<Widget>? actions;

  CustomAppBar({
    super.key,
    required this.title,
    this.showBackButton = false,
    this.bottom,
    this.actions,
  }) : super(
          preferredSize: Size.fromHeight(
            kToolbarHeight +
                (bottom != null ? bottom.preferredSize.height : 0.0),
          ),
          child: const SizedBox(),
        );

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(title),
      centerTitle: true,
      leading: showBackButton
          ? IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
            )
          : null,
      bottom: bottom,
      actions: actions,
    );
  }
}
