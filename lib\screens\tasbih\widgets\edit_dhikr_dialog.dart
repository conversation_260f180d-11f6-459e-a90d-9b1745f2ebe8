import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:wahaj_alsaalik/screens/tasbih/models/wird_model.dart';
import 'package:wahaj_alsaalik/screens/tasbih/providers/wird_provider.dart';
import 'package:wahaj_alsaalik/screens/tasbih/utils/tasbih_colors.dart';

/// نموذج تعديل ذكر في الورد
/// تم تصميمه بشكل احترافي لتعديل الأذكار في الورد
class EditDhikrDialog extends StatefulWidget {
  final WirdModel wird;
  final WirdItemModel item;

  const EditDhikrDialog({
    Key? key,
    required this.wird,
    required this.item,
  }) : super(key: key);

  @override
  State<EditDhikrDialog> createState() => _EditDhikrDialogState();
}

class _EditDhikrDialogState extends State<EditDhikrDialog> {
  // متغيرات الحالة
  late String _targetCountText;
  late final TextEditingController _targetCountController;
  final FocusNode _targetCountFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // تهيئة القيم الافتراضية من العنصر الحالي
    _targetCountText = widget.item.targetCount.toString();
    _targetCountController = TextEditingController(text: _targetCountText);
  }

  @override
  void dispose() {
    _targetCountController.dispose();
    _targetCountFocusNode.dispose();
    super.dispose();
  }

  // زيادة العدد المستهدف
  void _incrementTargetCount() {
    final currentCount =
        int.tryParse(_targetCountText) ?? widget.item.targetCount;
    final newCount = currentCount + 1;
    setState(() {
      _targetCountText = newCount.toString();
      _targetCountController.text = _targetCountText;
    });
  }

  // تقليل العدد المستهدف
  void _decrementTargetCount() {
    final currentCount =
        int.tryParse(_targetCountText) ?? widget.item.targetCount;
    if (currentCount > 1) {
      final newCount = currentCount - 1;
      setState(() {
        _targetCountText = newCount.toString();
        _targetCountController.text = _targetCountText;
      });
    }
  }

  // تحديث الذكر في الورد
  Future<void> _updateDhikrInWird() async {
    // إخفاء لوحة المفاتيح
    FocusScope.of(context).unfocus();

    // الحصول على مزود البيانات
    final wirdProvider = Provider.of<WirdProvider>(context, listen: false);

    // قراءة العدد المستهدف من حقل الإدخال
    int targetCount = widget.item.targetCount;
    try {
      final parsedCount = int.tryParse(_targetCountController.text.trim());
      if (parsedCount != null && parsedCount > 0) {
        targetCount = parsedCount;
      }
    } catch (e) {
      debugPrint('خطأ في قراءة العدد المستهدف: $e');
    }

    try {
      // إغلاق الحوار
      if (mounted) {
        Navigator.of(context).pop();
      }

      // إنشاء نسخة محدثة من العنصر
      final updatedItem = widget.item.copyWith(
        targetCount: targetCount,
      );

      // تحديث العنصر في الورد
      await wirdProvider.updateWirdItem(widget.wird.id, updatedItem);

      // إعادة تحميل الأوراد
      await wirdProvider.reloadWirds();

      // عرض رسالة نجاح
      if (mounted) {
        _showSuccessMessage(
            'تم تحديث ذكر "${widget.item.dhikr.name}" في الورد بنجاح');
      }
    } catch (e) {
      debugPrint('خطأ في تحديث الذكر: $e');
      if (mounted) {
        _showErrorMessage('حدث خطأ أثناء تحديث الذكر: $e');
      }
    }
  }

  // عرض رسالة خطأ
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  // عرض رسالة نجاح
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: TasbihColors.primary,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return AlertDialog(
      backgroundColor: isDarkMode ? TasbihColors.darkCardColor : Colors.white,
      title: Row(
        children: [
          Icon(
            Icons.edit,
            color: isDarkMode ? Colors.white : TasbihColors.primary,
            size: 24,
          ),
          const SizedBox(width: 8),
          Text(
            'تعديل الذكر',
            style: TextStyle(
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
        ],
      ),
      contentPadding: const EdgeInsets.all(16.0),
      content: SizedBox(
        width: double.maxFinite,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات الذكر
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: TasbihColors.primary.withAlpha(15),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: TasbihColors.primary.withAlpha(30),
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // اسم الذكر
                    Text(
                      widget.item.dhikr.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    if (widget.item.dhikr.arabicText.isNotEmpty &&
                        widget.item.dhikr.arabicText !=
                            widget.item.dhikr.name) ...[
                      const SizedBox(height: 8),
                      Text(
                        widget.item.dhikr.arabicText,
                        style: TextStyle(
                          fontSize: 16,
                          color: isDarkMode
                              ? TasbihColors.darkTextSecondary
                              : Colors.grey[700],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // العدد المستهدف
              const Text('العدد المستهدف',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Row(
                children: [
                  IconButton(
                    onPressed: _decrementTargetCount,
                    icon: const Icon(Icons.remove_circle_outline),
                    color: TasbihColors.primary,
                  ),
                  Expanded(
                    child: TextField(
                      textAlign: TextAlign.center,
                      keyboardType: TextInputType.number,
                      controller: _targetCountController,
                      focusNode: _targetCountFocusNode,
                      decoration: InputDecoration(
                        border: const OutlineInputBorder(),
                        contentPadding: const EdgeInsets.symmetric(vertical: 8),
                        filled: true,
                        fillColor: isDarkMode
                            ? TasbihColors.darkCardColor
                            : Colors.white,
                      ),
                      onChanged: (value) {
                        // تخزين القيمة فقط، بدون تحديث الحالة
                        _targetCountText = value;
                      },
                      onEditingComplete: () {
                        // تحديث الحالة عند الانتهاء من التحرير
                        final parsedCount = int.tryParse(_targetCountText);
                        if (parsedCount != null && parsedCount > 0) {
                          setState(() {
                            _targetCountText = parsedCount.toString();
                            _targetCountController.text = _targetCountText;
                          });
                        } else {
                          // إعادة القيمة الافتراضية إذا كانت القيمة غير صالحة
                          setState(() {
                            _targetCountText =
                                widget.item.targetCount.toString();
                            _targetCountController.text = _targetCountText;
                          });
                        }
                        // إخفاء لوحة المفاتيح
                        _targetCountFocusNode.unfocus();
                      },
                    ),
                  ),
                  IconButton(
                    onPressed: _incrementTargetCount,
                    icon: const Icon(Icons.add_circle_outline),
                    color: TasbihColors.primary,
                  ),
                ],
              ),

              // معلومات إضافية
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? const Color(0xFF0D2B4B).withAlpha(76)
                      : Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                      color: isDarkMode
                          ? const Color(0xFF1A3A5A)
                          : Colors.blue[100]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue[700], size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'يمكنك تعديل العدد المستهدف للذكر في الورد',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.blue[700],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'إلغاء',
            style: TextStyle(
              color: isDarkMode ? Colors.white : TasbihColors.primary,
            ),
          ),
        ),
        ElevatedButton(
          onPressed: _updateDhikrInWird,
          style:
              ElevatedButton.styleFrom(backgroundColor: TasbihColors.primary),
          child: const Text(
            'تحديث',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }
}

/// دالة مساعدة لعرض حوار تعديل ذكر
Future<void> showEditDhikrDialog(
    BuildContext context, WirdModel wird, WirdItemModel item) async {
  return showDialog(
    context: context,
    barrierDismissible: true,
    builder: (context) => EditDhikrDialog(wird: wird, item: item),
  );
}
