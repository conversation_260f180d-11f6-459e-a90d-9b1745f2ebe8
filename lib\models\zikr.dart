class Zikr {
  final String id;
  final String name;
  final String description;
  final int count;
  final String? iconName;
  final List<ZikrItem>? items;
  final bool _hasSubcategoriesFlag;
  final List<Zikr>? subcategories;
  final bool isFeatured; // إضافة خاصية للأقسام المميزة

  Zikr({
    required this.id,
    required this.name,
    required this.description,
    required this.count,
    this.iconName,
    this.items,
    bool hasSubcategories = false,
    this.subcategories,
    this.isFeatured = false, // القيمة الافتراضية هي false
  }) : _hasSubcategoriesFlag = hasSubcategories;

  factory Zikr.fromJson(Map<String, dynamic> json) {
    List<ZikrItem>? items;
    if (json['items'] != null) {
      items = List<ZikrItem>.from(
          json['items'].map((item) => ZikrItem.fromJson(item)));
    } else if (json['azkar'] != null) {
      items = List<ZikrItem>.from(
          json['azkar'].map((item) => ZikrItem.fromJson(item)));
    }

    int declaredCount = json['count'] ?? 0;
    int actualCount = items?.length ?? 0;

    int finalCount = (declaredCount == 0 ||
            (actualCount > 0 && declaredCount > actualCount * 2))
        ? actualCount
        : declaredCount;

    List<Zikr>? subcategories;
    if (json['subcategories'] != null) {
      subcategories = List<Zikr>.from(
          json['subcategories'].map((sub) => Zikr.fromJson(sub)));
    }

    return Zikr(
      id: json['id'] ?? '',
      name: json['name'] ?? json['title'] ?? '',
      description: json['description'] ?? '',
      count: finalCount,
      iconName: json['iconName'],
      items: items,
      hasSubcategories: json['hasSubcategories'] ?? false,
      subcategories: subcategories,
      isFeatured: json['isFeatured'] ?? false, // قراءة خاصية التمييز من JSON
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'count': count,
      'iconName': iconName,
      'items': items?.map((item) => item.toJson()).toList(),
      'hasSubcategories': _hasSubcategoriesFlag,
      'subcategories': subcategories?.map((sub) => sub.toJson()).toList(),
      'isFeatured': isFeatured, // إضافة خاصية التمييز إلى JSON
    };
  }

  // للتوافق مع AzkarCategory
  String get imageUrl => 'assets/images/$id.jpg';
  List<ZikrItem> get azkar => items ?? [];

  // للتحقق مما إذا كان الذكر يحتوي على أقسام فرعية
  bool get hasSubcategories =>
      _hasSubcategoriesFlag ||
      (subcategories != null && subcategories!.isNotEmpty);
}

class ZikrItem {
  final String id;
  final String text;
  final int count;
  final String? source;
  final String? fadl;
  final bool isFeatured; // خاصية للأذكار المميزة
  final String? iconName; // إضافة خاصية لأيقونة الذكر الفردي

  ZikrItem({
    required this.id,
    required this.text,
    required this.count,
    this.source,
    this.fadl,
    this.isFeatured = false, // القيمة الافتراضية هي false
    this.iconName, // أيقونة اختيارية للذكر
  });

  factory ZikrItem.fromJson(Map<String, dynamic> json) {
    return ZikrItem(
      id: json['id'] ?? '',
      text: json['text'] ?? '',
      count: json['count'] ?? 1,
      source: json['source'],
      fadl: json['fadl'],
      isFeatured: json['isFeatured'] ?? false, // قراءة خاصية التمييز من JSON
      iconName: json['iconName'], // قراءة اسم الأيقونة من JSON
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'count': count,
      'source': source,
      'fadl': fadl,
      'isFeatured': isFeatured, // إضافة خاصية التمييز إلى JSON
      'iconName': iconName, // إضافة اسم الأيقونة إلى JSON
    };
  }

  // نسخة جديدة من الذكر مع تحديث بعض الخصائص
  ZikrItem copyWith({
    String? id,
    String? text,
    int? count,
    String? source,
    String? fadl,
    bool? isFeatured,
    String? iconName,
  }) {
    return ZikrItem(
      id: id ?? this.id,
      text: text ?? this.text,
      count: count ?? this.count,
      source: source ?? this.source,
      fadl: fadl ?? this.fadl,
      isFeatured: isFeatured ?? this.isFeatured,
      iconName: iconName ?? this.iconName,
    );
  }
}
