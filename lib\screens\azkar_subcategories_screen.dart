import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:ui';
import '../models/zikr.dart';
import '../utils/app_colors.dart';
import '../utils/icon_helper.dart';
import 'azkar_details_screen.dart';

class AzkarSubcategoriesScreen extends StatefulWidget {
  final String mainCategory;
  final List<Zikr> subcategories;

  const AzkarSubcategoriesScreen({
    super.key,
    required this.mainCategory,
    required this.subcategories,
  });

  @override
  State<AzkarSubcategoriesScreen> createState() =>
      _AzkarSubcategoriesScreenState();
}

class _AzkarSubcategoriesScreenState extends State<AzkarSubcategoriesScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late ScrollController _scrollController;
  bool _isAppBarCollapsed = false;

  // قيمة البحث
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isSearchMode = false;

  // الفئات الفرعية المفلترة
  late List<Zikr> _filteredSubcategories;

  // إضافة متغير جديد للتحكم في الرسوم المتحركة للضوء
  late AnimationController _lightAnimationController;

  @override
  void initState() {
    super.initState();

    _filteredSubcategories = List.from(widget.subcategories);

    _animationController = AnimationController(
      vsync: this,
      // تقليل مدة الرسوم المتحركة من 800 إلى 600 مللي ثانية لتحسين الأداء
      duration: const Duration(milliseconds: 600),
    );

    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);

    Future.delayed(const Duration(milliseconds: 100), () {
      _animationController.forward();
    });

    // إضافة وحدة تحكم جديدة للرسوم المتحركة للضوء مع تقليل المدة
    _lightAnimationController = AnimationController(
      vsync: this,
      // تقليل مدة الرسوم المتحركة من 3000 إلى 2000 مللي ثانية لتحسين الأداء
      duration: const Duration(milliseconds: 2000),
    )..repeat(reverse: true);
  }

  void _onScroll() {
    if (_scrollController.hasClients) {
      final scrollOffset = _scrollController.offset;
      final isCollapsed = scrollOffset > 80;

      if (isCollapsed != _isAppBarCollapsed) {
        setState(() {
          _isAppBarCollapsed = isCollapsed;
        });
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    _lightAnimationController.dispose();
    super.dispose();
  }

  void _updateSearchQuery(String query) {
    setState(() {
      _searchQuery = query;
      _filterSubcategories();
    });
  }

  void _toggleSearchMode() {
    setState(() {
      _isSearchMode = !_isSearchMode;
      if (!_isSearchMode) {
        _searchQuery = '';
        _searchController.clear();
        _filterSubcategories();
      }
    });
  }

  void _filterSubcategories() {
    if (_searchQuery.isEmpty) {
      _filteredSubcategories = List.from(widget.subcategories);
    } else {
      _filteredSubcategories = widget.subcategories
          .where((subcategory) =>
              subcategory.name.contains(_searchQuery) ||
              subcategory.description.contains(_searchQuery))
          .toList();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.light,
      child: Scaffold(
        body: Stack(
          children: [
            // خلفية متدرجة للخلفية
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: Theme.of(context).brightness == Brightness.dark
                      ? const [
                          Color(0xFF1A1A2E), // TasbihColors.darkBackground
                          Color(
                              0xFF16213E), // TasbihColors.darkBackgroundSecondary
                        ]
                      : const [
                          Color(0xFFEEF7F6),
                          Colors.white,
                        ],
                ),
              ),
            ),

            CustomScrollView(
              controller: _scrollController,
              physics: const BouncingScrollPhysics(),
              slivers: [
                _buildAppBar(),

                // كتابة ترحيبية
                if (!_isSearchMode)
                  SliverToBoxAdapter(
                    child: Container(
                      margin: const EdgeInsets.fromLTRB(20, 10, 20, 0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'فئات ${widget.mainCategory}',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? const Color(
                                      0xFFE0E0E0) /* TasbihColors.darkTextColor */
                                  : Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'اختر فئة من أذكار ${widget.mainCategory} للاطلاع على الأذكار',
                            style: TextStyle(
                              fontSize: 14,
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? const Color(
                                      0xFFAAAAAA) /* TasbihColors.darkTextSecondary */
                                  : Colors.grey[700],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                // رسالة عند عدم وجود نتائج بحث
                if (_searchQuery.isNotEmpty && _filteredSubcategories.isEmpty)
                  SliverToBoxAdapter(
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.all(50.0),
                        child: Column(
                          children: [
                            Icon(
                              Icons.search_off,
                              size: 60,
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? const Color(
                                      0xFFAAAAAA) /* TasbihColors.darkTextSecondary */
                                  : Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'لم يتم العثور على نتائج لـ "$_searchQuery"',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? const Color(
                                        0xFFAAAAAA) /* TasbihColors.darkTextSecondary */
                                    : Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'حاول البحث بكلمات مختلفة',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 14,
                                color: Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? const Color(
                                        0xFFAAAAAA) /* TasbihColors.darkTextSecondary */
                                    : Colors.grey[500],
                              ),
                            ),
                            const SizedBox(height: 24),
                            TextButton.icon(
                              onPressed: () {
                                setState(() {
                                  _searchQuery = '';
                                  _searchController.clear();
                                  _filterSubcategories();
                                });
                              },
                              icon: const Icon(Icons.refresh),
                              label: const Text('عرض جميع الفئات'),
                              style: TextButton.styleFrom(
                                foregroundColor: AppColors.getAzkarColor(
                                    Theme.of(context).brightness ==
                                        Brightness.dark),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                // قائمة الفئات الفرعية
                SliverPadding(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
                  sliver: _filteredSubcategories.isNotEmpty
                      ? SliverGrid(
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount:
                                MediaQuery.of(context).size.width > 600 ? 3 : 2,
                            childAspectRatio:
                                MediaQuery.of(context).size.width > 600
                                    ? 0.85
                                    : 0.75,
                            crossAxisSpacing:
                                MediaQuery.of(context).size.width * 0.03,
                            mainAxisSpacing:
                                MediaQuery.of(context).size.height * 0.02,
                          ),
                          delegate: SliverChildBuilderDelegate(
                            (context, index) {
                              final subcategory = _filteredSubcategories[index];
                              return _buildAnimatedSubcategoryCard(
                                  context, subcategory, index);
                            },
                            childCount: _filteredSubcategories.length,
                          ),
                        )
                      : const SliverToBoxAdapter(child: SizedBox.shrink()),
                ),

                // مساحة إضافية في النهاية
                const SliverToBoxAdapter(
                  child: SizedBox(height: 20),
                ),
              ],
            ),

            // شريط علوي عائم يظهر عند التمرير
            if (_isAppBarCollapsed && !_isSearchMode) _buildFloatingHeader(),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingHeader() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      // استخدام RepaintBoundary لتحسين الأداء
      child: RepaintBoundary(
        child: ClipRect(
          child: BackdropFilter(
            // تقليل قيمة التمويه لتحسين الأداء
            filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
            child: Container(
              padding: EdgeInsets.only(
                top: MediaQuery.of(context).padding.top,
                bottom: 8,
                left: 8,
                right: 8,
              ),
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFF1A1A2E).withAlpha(
                        217) // TasbihColors.darkBackground, 0.85 * 255 = 217
                    : Colors.white.withAlpha(217), // 0.85 * 255 = 217
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(13), // 0.05 * 255 = 13
                    // تقليل قيمة التمويه لتحسين الأداء
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              // صف العنوان - مُحسّن لتجنب مشكلة ParentDataWidget
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return Row(
                    textDirection: TextDirection
                        .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                    children: [
                      IconButton(
                        icon: const Icon(Icons.search),
                        onPressed: _toggleSearchMode,
                        tooltip: 'بحث',
                      ),
                      Expanded(
                        child: Text(
                          widget.mainCategory,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textDirection: TextDirection
                              .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                          textAlign: TextAlign.right, // محاذاة النص إلى اليمين
                        ),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        icon: const Icon(Icons.arrow_back),
                        onPressed: () => Navigator.of(context).pop(),
                        tooltip: 'العودة',
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  SliverAppBar _buildAppBar() {
    final screenSize = MediaQuery.of(context).size;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return SliverAppBar(
      expandedHeight: _isSearchMode ? 120.0 : screenSize.height * 0.25,
      floating: false,
      pinned: true,
      stretch: true,
      backgroundColor: AppColors.getAzkarColor(isDarkMode),
      title: _isSearchMode
          ? null
          : Text(
              widget.mainCategory,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: Colors.black26,
                    offset: Offset(0, 1),
                    blurRadius: 2,
                  ),
                ],
              ),
            ),
      leading: IconButton(
        icon: _isSearchMode
            ? const Icon(Icons.close, color: Colors.white)
            : const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: _isSearchMode
            ? _toggleSearchMode
            : () => Navigator.of(context).pop(),
      ),
      actions: [
        if (!_isSearchMode) ...[
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: _toggleSearchMode,
            tooltip: 'بحث',
          ),
        ],
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: _isSearchMode
            ? null
            : Stack(
                fit: StackFit.expand,
                children: [
                  // خلفية متدرجة متقدمة مع تدرجات متعددة
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topRight,
                        end: Alignment.bottomLeft,
                        colors: isDarkMode
                            ? [
                                AppColors.getAzkarColorWithOpacity(
                                    isDarkMode, AppColors.opacity95),
                                AppColors.getAzkarColorWithOpacity(
                                    isDarkMode, AppColors.opacity85),
                                AppColors.getAzkarColorWithOpacity(
                                    isDarkMode, AppColors.opacity75),
                              ]
                            : [
                                AppColors.getAzkarColor(isDarkMode),
                                AppColors.getAzkarColorWithOpacity(
                                    isDarkMode, AppColors.opacity90),
                                AppColors.getAzkarColorWithOpacity(
                                    isDarkMode, AppColors.opacity80),
                              ],
                        stops: const [0.2, 0.6, 1.0],
                      ),
                    ),
                  ),

                  // تأثير إضاءة متقدم
                  AnimatedBuilder(
                    animation: _lightAnimationController,
                    builder: (context, child) {
                      return CustomPaint(
                        painter: SubcategoryLightingEffect(
                          progress: _lightAnimationController.value,
                          isDarkMode: isDarkMode,
                        ),
                        child: const SizedBox.expand(),
                      );
                    },
                  ),

                  // موجات متحركة للخلفية
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: SizedBox(
                      height: screenSize.height * 0.1,
                      child: CustomPaint(
                        painter: SubcategoryWavePainter(
                          animationValue: _lightAnimationController.value,
                          isDarkMode: isDarkMode,
                        ),
                        size: Size(screenSize.width, screenSize.height * 0.1),
                      ),
                    ),
                  ),

                  // زخرفة إسلامية رئيسية - مبسطة لتحسين الأداء
                  Stack(
                    children: [
                      // زخرفة يمينية - ثابتة بدون رسوم متحركة لتحسين الأداء
                      Positioned(
                        right: -25,
                        top: -20,
                        child: Opacity(
                          opacity: 0.16,
                          child: SvgPicture.asset(
                            'assets/images/p2.svg',
                            width: screenSize.width * 0.55,
                            height: screenSize.width * 0.55,
                            colorFilter: ColorFilter.mode(
                              isDarkMode
                                  ? Colors.white
                                      .withAlpha(217) // 0.85 * 255 = 217
                                  : Colors.white
                                      .withAlpha(242), // 0.95 * 255 = 242
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ),

                      // زخرفة يسارية للعمق - ثابتة بدون رسوم متحركة لتحسين الأداء
                      Positioned(
                        left: -30,
                        bottom: -screenSize.height * 0.12,
                        child: Opacity(
                          opacity: 0.12,
                          child: SvgPicture.asset(
                            'assets/images/p2.svg',
                            width: screenSize.width * 0.45,
                            height: screenSize.width * 0.45,
                            colorFilter: ColorFilter.mode(
                              Colors.white.withAlpha(204), // 0.8 * 255 = 204
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),

                  // ديكور إضافي في الخلفية مع تحسينات التدرج
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      height: 40,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            isDarkMode
                                ? Colors.black.withAlpha(64) // 0.25 * 255 = 64
                                : Colors.black.withAlpha(31), // 0.12 * 255 = 31
                            isDarkMode
                                ? Colors.black.withAlpha(89) // 0.35 * 255 = 89
                                : Colors.black.withAlpha(46), // 0.18 * 255 = 46
                          ],
                          stops: const [0.0, 0.6, 1.0],
                        ),
                      ),
                    ),
                  ),

                  // معلومات الفئة - مبسطة لتحسين الأداء
                  Positioned(
                    bottom: 20,
                    left: 20,
                    child: Row(
                      children: [
                        // أيقونة مع تأثيرات محسنة - مبسطة لتحسين الأداء
                        Container(
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color:
                                Colors.white.withAlpha(64), // 0.25 * 255 = 64
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black
                                    .withAlpha(38), // 0.15 * 255 = 38
                                // تقليل قيمة التمويه لتحسين الأداء
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                            border: Border.all(
                              color:
                                  Colors.white.withAlpha(77), // 0.3 * 255 = 77
                              width: 0.5,
                            ),
                          ),
                          child: Icon(
                            Icons.format_list_bulleted,
                            color: Colors.white,
                            size: 24,
                            shadows: [
                              Shadow(
                                color: Colors.black
                                    .withAlpha(77), // 0.3 * 255 = 77
                                blurRadius: 2,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 10),

                        // عدد الفئات محسّن - مبسط لتحسين الأداء
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 14, vertical: 7),
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black
                                    .withAlpha(26), // 0.1 * 255 = 26
                                // تقليل قيمة التمويه لتحسين الأداء
                                blurRadius: 3,
                                offset: const Offset(0, 2),
                              ),
                            ],
                            border: Border.all(
                              color:
                                  Colors.white.withAlpha(64), // 0.25 * 255 = 64
                              width: 0.5,
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.format_list_numbered,
                                color: Colors.white,
                                size: 16,
                                shadows: [
                                  Shadow(
                                    color: Colors.black
                                        .withAlpha(77), // 0.3 * 255 = 77
                                    blurRadius: 2,
                                  ),
                                ],
                              ),
                              const SizedBox(width: 6),
                              Text(
                                '${_filteredSubcategories.length} فئة',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 13,
                                  fontWeight: FontWeight.w500,
                                  shadows: [
                                    Shadow(
                                      color: Colors.black
                                          .withAlpha(77), // 0.3 * 255 = 77
                                      blurRadius: 2,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
      ),
      bottom: _isSearchMode
          ? PreferredSize(
              preferredSize: const Size.fromHeight(80),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: _buildEnhancedSearchField(context),
              ),
            )
          : null,
    );
  }

  // مربع بحث محسن - مُحسّن للأداء
  Widget _buildEnhancedSearchField(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Container(
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.white10 : Colors.white24,
        borderRadius: BorderRadius.circular(16),
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            // تقليل قيمة التمويه لتحسين الأداء
            blurRadius: 6,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        // استخدام RepaintBoundary لتحسين الأداء
        child: RepaintBoundary(
          child: BackdropFilter(
            // تقليل قيمة التمويه لتحسين الأداء
            filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
            child: TextField(
              controller: _searchController,
              autofocus: true,
              onChanged: _updateSearchQuery,
              style: const TextStyle(color: Colors.white),
              cursorColor: Colors.white70,
              textInputAction: TextInputAction.search,
              decoration: InputDecoration(
                hintText: 'البحث في فئات ${widget.mainCategory}...',
                hintStyle: TextStyle(
                    color: isDarkMode ? Colors.white60 : Colors.white70),
                prefixIcon: Container(
                  // إزالة الرسوم المتحركة لتحسين الأداء
                  padding: const EdgeInsets.all(12),
                  child: Icon(
                    Icons.search,
                    color: isDarkMode ? Colors.white60 : Colors.white70,
                  ),
                ),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear, color: Colors.white70),
                        onPressed: () {
                          _searchController.clear();
                          _updateSearchQuery('');
                        },
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 14,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedSubcategoryCard(
      BuildContext context, Zikr subcategory, int index) {
    // تقليل التأخير بين البطاقات لتسريع ظهور القائمة بالكامل
    final delay = index * 0.03;

    // تبسيط الرسوم المتحركة - استخدام انزلاق وتلاشي فقط بدون تكبير/تصغير لتحسين الأداء
    final slideAnimation = Tween<Offset>(
      // تقليل مسافة الانزلاق لتحسين الأداء
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Interval(
          delay.clamp(0.0, 0.9),
          // تقليل مدة الرسم المتحرك لتحسين الأداء
          (delay + 0.3).clamp(0.0, 1.0),
          curve: Curves.easeOut,
        ),
      ),
    );

    final fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Interval(
          delay.clamp(0.0, 0.9),
          // تقليل مدة الرسم المتحرك لتحسين الأداء
          (delay + 0.2).clamp(0.0, 1.0),
          curve: Curves.easeOut,
        ),
      ),
    );

    // استخدام RepaintBoundary لتحسين الأداء عن طريق تقليل مساحة إعادة الرسم
    return RepaintBoundary(
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: fadeAnimation,
            child: SlideTransition(
              position: slideAnimation,
              child: child!,
            ),
          );
        },
        child: _buildSubcategoryCard(context, subcategory),
      ),
    );
  }

  Widget _buildSubcategoryCard(BuildContext context, Zikr subcategory) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Hero(
      tag: 'subcategory_${subcategory.id}',
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            // تأثير اهتزاز خفيف
            HapticFeedback.lightImpact();

            Navigator.push(
              context,
              PageRouteBuilder(
                pageBuilder: (context, animation, secondaryAnimation) =>
                    AzkarDetailsScreen(
                  category: subcategory.name,
                  azkarItems: subcategory.items,
                ),
                transitionsBuilder:
                    (context, animation, secondaryAnimation, child) {
                  const begin = Offset(0.0, 0.05);
                  const end = Offset.zero;
                  const curve = Curves.easeInOut;

                  var tween = Tween(begin: begin, end: end)
                      .chain(CurveTween(curve: curve));
                  var offsetAnimation = animation.drive(tween);

                  return SlideTransition(
                    position: offsetAnimation,
                    child: FadeTransition(
                      opacity: animation,
                      child: child,
                    ),
                  );
                },
              ),
            );
          },
          customBorder: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Container(
            decoration: BoxDecoration(
              color: AppColors.getCardColor(isDarkMode),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: AppColors.getAzkarColorWithOpacity(
                      isDarkMode, AppColors.opacity10),
                  blurRadius: isDarkMode ? 10 : 8,
                  offset: const Offset(0, 2),
                ),
              ],
              // إضافة حدود خفيفة في الوضع المظلم لتحسين التباين
              border: isDarkMode
                  ? Border.all(
                      color: AppColors.getAzkarColorWithOpacity(
                          isDarkMode, AppColors.opacity15),
                      width: 0.5,
                    )
                  : null,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // قسم الأيقونة المحسن
                Expanded(
                  flex: 6,
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(20),
                      ),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          isDarkMode
                              ? AppColors.azkarColor
                                  .withAlpha(56) // 0.22 * 255 = 56
                              : AppColors.azkarColor
                                  .withAlpha(46), // 0.18 * 255 = 46
                          isDarkMode
                              ? AppColors.azkarColor
                                  .withAlpha(20) // 0.08 * 255 = 20
                              : AppColors.azkarColor
                                  .withAlpha(13), // 0.05 * 255 = 13
                        ],
                        stops: const [0.0, 1.0],
                      ),
                    ),
                    child: Stack(
                      fit: StackFit.expand,
                      children: [
                        // زخرفة خلفية خفيفة
                        Positioned(
                          right: -20,
                          top: -15,
                          child: Opacity(
                            opacity: isDarkMode ? 0.09 : 0.07,
                            child: SvgPicture.asset(
                              'assets/images/p2.svg',
                              width: 80,
                              height: 80,
                              colorFilter: const ColorFilter.mode(
                                AppColors.azkarColor,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                        ),

                        // مركز الأيقونة المحسن - تم تبسيطه لتحسين الأداء
                        Center(
                          child: Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              color:
                                  isDarkMode ? Colors.grey[850] : Colors.white,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: isDarkMode
                                      ? AppColors.azkarColor
                                          .withAlpha(77) // 0.3 * 255 = 77
                                      : AppColors.azkarColor
                                          .withAlpha(64), // 0.25 * 255 = 64
                                  // تقليل قيمة التمويه لتحسين الأداء
                                  blurRadius: 6,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                              border: Border.all(
                                color: isDarkMode
                                    ? AppColors.azkarColor
                                        .withAlpha(38) // 0.15 * 255 = 38
                                    : AppColors.azkarColor
                                        .withAlpha(26), // 0.1 * 255 = 26
                                width: 1,
                              ),
                            ),
                            child: IconHelper.getIconWidget(
                              categoryName: subcategory.name,
                              iconPath: subcategory.iconPath,
                              iconName: subcategory.iconName,
                              size: 28,
                              color: AppColors.azkarColor,
                              section: 'azkar',
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // قسم المعلومات المحسن
                Expanded(
                  flex: 7,
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          subcategory.name,
                          style: TextStyle(
                            // زيادة حجم الخط قليلاً في الوضع المظلم
                            fontSize: isDarkMode ? 14.5 : 14,
                            // زيادة وزن الخط في الوضع المظلم
                            fontWeight:
                                isDarkMode ? FontWeight.w700 : FontWeight.bold,
                            color: AppColors.getTextColor(isDarkMode),
                            height: isDarkMode ? 1.4 : 1.3,
                            // إضافة ظل خفيف للنص في الوضع المظلم
                            shadows: isDarkMode
                                ? [
                                    Shadow(
                                      color: Colors.black.withAlpha(40),
                                      blurRadius: 0.5,
                                    )
                                  ]
                                : null,
                            // إضافة تباعد بين الأحرف في الوضع المظلم
                            letterSpacing: isDarkMode ? 0.2 : 0,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),

                        // خط فاصل محسن
                        Container(
                          height: isDarkMode ? 2.5 : 2,
                          width: 30,
                          margin: const EdgeInsets.only(bottom: 6, top: 2),
                          decoration: BoxDecoration(
                            color: AppColors.getAzkarColorWithOpacity(
                                isDarkMode,
                                isDarkMode
                                    ? AppColors.opacity40
                                    : AppColors.opacity30),
                            borderRadius:
                                BorderRadius.circular(isDarkMode ? 1.5 : 1),
                            // إضافة ظل خفيف في الوضع المظلم
                            boxShadow: isDarkMode
                                ? [
                                    BoxShadow(
                                      color: AppColors.getAzkarColorWithOpacity(
                                          isDarkMode, AppColors.opacity15),
                                      blurRadius: 2,
                                      offset: const Offset(0, 1),
                                    )
                                  ]
                                : null,
                          ),
                        ),

                        Expanded(
                          child: Text(
                            subcategory.description,
                            style: TextStyle(
                              // زيادة حجم الخط قليلاً في الوضع المظلم
                              fontSize: isDarkMode ? 12.5 : 12,
                              color: isDarkMode
                                  ? Colors.grey[
                                      300] // تحسين التباين في الوضع المظلم
                                  : Colors.grey[600],
                              height: isDarkMode ? 1.3 : 1.2,
                              // إضافة وزن خط أكبر في الوضع المظلم
                              fontWeight: isDarkMode
                                  ? FontWeight.w400
                                  : FontWeight.normal,
                              // إضافة تباعد بين الأحرف في الوضع المظلم
                              letterSpacing: isDarkMode ? 0.1 : 0,
                            ),
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // عدد الأذكار المحسن
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: isDarkMode ? 12 : 10,
                                vertical: isDarkMode ? 5 : 4,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.getAzkarColorWithOpacity(
                                    isDarkMode,
                                    isDarkMode
                                        ? AppColors.opacity18
                                        : AppColors.opacity12),
                                borderRadius:
                                    BorderRadius.circular(isDarkMode ? 14 : 12),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.getAzkarColorWithOpacity(
                                        isDarkMode, AppColors.opacity10),
                                    blurRadius: isDarkMode ? 5 : 4,
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                                border: Border.all(
                                  color: AppColors.getAzkarColorWithOpacity(
                                      isDarkMode,
                                      isDarkMode
                                          ? AppColors.opacity20
                                          : AppColors.opacity15),
                                  width: isDarkMode ? 0.7 : 0.5,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.format_list_numbered,
                                    size: 12,
                                    color: isDarkMode
                                        ? AppColors.getAzkarColorWithOpacity(
                                            isDarkMode, AppColors.opacity90)
                                        : AppColors.getAzkarColor(isDarkMode),
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${subcategory.count} ذكر',
                                    style: TextStyle(
                                      // زيادة حجم الخط قليلاً في الوضع المظلم
                                      fontSize: isDarkMode ? 11.5 : 11,
                                      color: isDarkMode
                                          ? AppColors.getAzkarColorWithOpacity(
                                              isDarkMode, AppColors.opacity90)
                                          : AppColors.getAzkarColor(isDarkMode),
                                      // زيادة وزن الخط في الوضع المظلم
                                      fontWeight: isDarkMode
                                          ? FontWeight.w600
                                          : FontWeight.w500,
                                      // إضافة ظل خفيف للنص في الوضع المظلم
                                      shadows: isDarkMode
                                          ? [
                                              Shadow(
                                                color:
                                                    Colors.black.withAlpha(30),
                                                blurRadius: 0.5,
                                              )
                                            ]
                                          : null,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// رسام إضاءة متقدم للزخارف الإسلامية - مُحسّن للأداء
class SubcategoryLightingEffect extends CustomPainter {
  final double progress;
  final bool isDarkMode;

  SubcategoryLightingEffect({
    required this.progress,
    required this.isDarkMode,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);

    // تأثير وهج في الزاوية العلوية اليمنى - مبسط للأداء
    final topRightGlow = RadialGradient(
      center: const Alignment(0.7, -0.6),
      // تبسيط الحساب الرياضي لتحسين الأداء
      radius: 0.9,
      colors: [
        // تقليل شفافية التأثير لتحسين الأداء
        Colors.white.withAlpha((0.12 * 255).toInt()),
        Colors.white.withAlpha((0.05 * 255).toInt()),
        Colors.transparent,
      ],
      stops: const [0.0, 0.4, 0.8],
    ).createShader(rect);

    canvas.drawRect(rect, Paint()..shader = topRightGlow);

    // تأثير وهج في الزاوية السفلية اليسرى - مبسط للأداء
    final bottomLeftGlow = RadialGradient(
      center: const Alignment(-0.7, 0.7),
      // تبسيط الحساب الرياضي لتحسين الأداء
      radius: 0.8,
      colors: [
        // تقليل شفافية التأثير لتحسين الأداء
        Colors.white.withAlpha((0.07 * 255).toInt()),
        Colors.white.withAlpha((0.03 * 255).toInt()),
        Colors.transparent,
      ],
      stops: const [0.0, 0.3, 0.7],
    ).createShader(rect);

    canvas.drawRect(rect, Paint()..shader = bottomLeftGlow);

    // تقليل عدد نقاط الضوء المتوهجة لتحسين الأداء (نقطة واحدة فقط)
    _drawLightSpot(canvas, size, 0.6, -0.3, 0.15);
  }

  void _drawLightSpot(
      Canvas canvas, Size size, double dx, double dy, double intensity) {
    final center = Offset(
      size.width * 0.5 + (dx * size.width * 0.5),
      size.height * 0.5 + (dy * size.height * 0.5),
    );

    // تقليل حجم نقطة الضوء لتحسين الأداء
    final radius = size.width * 0.1;

    final spotGradient = RadialGradient(
      colors: [
        Colors.white.withAlpha((intensity * 255).toInt()),
        Colors.white.withAlpha((intensity * 0.3 * 255).toInt()),
        Colors.transparent,
      ],
      stops: const [0.0, 0.3, 1.0],
    ).createShader(Rect.fromCircle(center: center, radius: radius));

    canvas.drawCircle(
      center,
      radius,
      Paint()..shader = spotGradient,
    );
  }

  @override
  bool shouldRepaint(SubcategoryLightingEffect oldDelegate) =>
      // تبسيط شرط إعادة الرسم لتحسين الأداء - فقط عند تغير وضع الإضاءة
      oldDelegate.isDarkMode != isDarkMode;
}

// رسام موجات متقدم لقاع الشريط العلوي - مُحسّن للأداء
class SubcategoryWavePainter extends CustomPainter {
  final double animationValue;
  final bool isDarkMode;

  SubcategoryWavePainter({
    required this.animationValue,
    required this.isDarkMode,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final baseOpacity = isDarkMode ? 0.07 : 0.09;

    // موجة واحدة فقط بدلاً من اثنتين لتحسين الأداء
    _drawSimplifiedWave(
      canvas: canvas,
      size: size,
      height: size.height * 0.5,
      controlPoint1: size.width * 0.25,
      // تقليل مدى حركة نقاط التحكم لتحسين الأداء
      controlHeight1: size.height * (0.4 + animationValue * 0.1),
      controlPoint2: size.width * 0.75,
      // تقليل مدى حركة نقاط التحكم لتحسين الأداء
      controlHeight2: size.height * (0.6 - animationValue * 0.1),
      opacity: baseOpacity + 0.01,
    );
  }

  void _drawSimplifiedWave({
    required Canvas canvas,
    required Size size,
    required double height,
    required double controlPoint1,
    required double controlHeight1,
    required double controlPoint2,
    required double controlHeight2,
    required double opacity,
  }) {
    final path = Path();

    path.moveTo(0, height);
    path.quadraticBezierTo(
      controlPoint1,
      controlHeight1,
      size.width * 0.5,
      height,
    );
    path.quadraticBezierTo(
      controlPoint2,
      controlHeight2,
      size.width,
      height,
    );
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    final wavePaint = Paint()
      ..color = Colors.white.withAlpha((opacity * 255).toInt())
      ..style = PaintingStyle.fill
      // تقليل قيمة التمويه لتحسين الأداء
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 1.5);

    canvas.drawPath(path, wavePaint);
  }

  @override
  bool shouldRepaint(SubcategoryWavePainter oldDelegate) =>
      // تبسيط شرط إعادة الرسم لتحسين الأداء - فقط عند تغير وضع الإضاءة
      oldDelegate.isDarkMode != isDarkMode;
}
