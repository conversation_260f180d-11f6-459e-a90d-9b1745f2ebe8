// لبطاقة الإشعارات

import 'package:flutter/material.dart';

class NotificationCard extends StatelessWidget {
  final String title;
  final IconData icon;
  final Color iconColor;
  final TimeOfDay? time;
  final bool isEnabled;
  final ValueChanged<bool>? onToggle;
  final VoidCallback? onTap;

  const NotificationCard({
    super.key,
    required this.title,
    required this.icon,
    required this.iconColor,
    this.time,
    required this.isEnabled,
    this.onToggle,
    this.onTap,
  });

  // مصنع لبطاقة تأجيل الإشعارات
  factory NotificationCard.snooze({
    required int snoozeMinutes,
    required VoidCallback onTap,
  }) {
    return NotificationCard(
      title: 'مدة تأجيل الإشعارات',
      icon: Icons.snooze_outlined,
      iconColor: Colors.purple,
      time: null,
      isEnabled: true,
      onTap: onTap,
      onToggle: null,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  color: iconColor.withAlpha(26), // 0.1 * 255 = 25.5 ≈ 26
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  icon,
                  color: iconColor,
                  size: 22,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: theme.textTheme.titleLarge?.color,
                      ),
                    ),
                    const SizedBox(height: 4),
                    if (title == 'مدة تأجيل الإشعارات')
                      Text(
                        '${time?.format(context) ?? ''} دقيقة',
                        style: TextStyle(
                          fontSize: 13,
                          color: theme.textTheme.bodySmall?.color,
                        ),
                      )
                    else
                      Text(
                        time != null
                            ? 'يومياً الساعة ${time!.format(context)}'
                            : 'غير مفعّل',
                        style: TextStyle(
                          fontSize: 13,
                          color: theme.textTheme.bodySmall?.color,
                        ),
                      ),
                  ],
                ),
              ),
              if (onToggle != null)
                Transform.scale(
                  scale: 0.8,
                  child: Switch(
                    value: isEnabled,
                    activeColor: iconColor,
                    onChanged: onToggle,
                  ),
                )
              else
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: isDark ? Colors.white60 : Colors.black45,
                ),
            ],
          ),
        ),
      ),
    );
  }
}
