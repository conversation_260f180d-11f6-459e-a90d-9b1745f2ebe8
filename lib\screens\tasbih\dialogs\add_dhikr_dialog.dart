// حوار اختيار الذكر

import 'package:flutter/material.dart';

import '../utils/tasbih_colors.dart';

Future<String?> showAddDhikrDialog(BuildContext context) async {
  final TextEditingController controller = TextEditingController();
  final formKey = GlobalKey<FormState>();

  // استخدام showGeneralDialog بدلاً من showDialog لتجنب مشكلة الـ overflow
  return showGeneralDialog<String>(
    context: context,
    barrierDismissible: true,
    barrierLabel: 'إضافة ذكر جديد',
    transitionDuration: const Duration(milliseconds: 250),
    pageBuilder: (context, animation1, animation2) {
      return Container(); // لن يتم استخدامه
    },
    transitionBuilder: (context, animation, secondaryAnimation, child) {
      // تأثير الظهور
      final curvedAnimation = CurvedAnimation(
        parent: animation,
        curve: Curves.easeInOut,
      );

      return ScaleTransition(
        scale: Tween<double>(begin: 0.8, end: 1.0).animate(curvedAnimation),
        child: FadeTransition(
          opacity: curvedAnimation,
          child: SafeArea(
            child: Center(
              child: Material(
                color: Colors.transparent,
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.9,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Theme.of(context).dialogBackgroundColor,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // العنوان
                      const Text(
                        'إضافة ذكر جديد',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: TasbihColors.primary,
                        ),
                      ),
                      const SizedBox(height: 20),

                      // حقل إدخال النص
                      Form(
                        key: formKey,
                        child: TextFormField(
                          controller: controller,
                          decoration: InputDecoration(
                            hintText: 'اكتب الذكر هنا',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                              borderSide: const BorderSide(
                                color: TasbihColors.primary,
                                width: 2,
                              ),
                            ),
                            // إضافة تعبئة أكبر لتسهيل النقر
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 16,
                            ),
                          ),
                          textAlign: TextAlign.center,
                          textDirection: TextDirection.rtl,
                          maxLines: 2,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال نص الذكر';
                            }
                            return null;
                          },
                          autovalidateMode: AutovalidateMode.onUserInteraction,
                        ),
                      ),
                      const SizedBox(height: 20),

                      // أزرار الإجراءات
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            style: TextButton.styleFrom(
                              foregroundColor: Colors.grey[600],
                            ),
                            child: const Text('إلغاء'),
                          ),
                          const SizedBox(width: 16),
                          TextButton(
                            onPressed: () {
                              if (formKey.currentState?.validate() ?? false) {
                                Navigator.pop(context, controller.text.trim());
                              }
                            },
                            style: TextButton.styleFrom(
                              foregroundColor: TasbihColors.primary,
                            ),
                            child: const Text('إضافة'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      );
    },
  );
}
