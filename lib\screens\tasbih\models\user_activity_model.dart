// نموذج نشاط المستخدم

/// نموذج لتتبع نشاط المستخدم في المسبحة
class UserActivity {
  /// تاريخ آخر استخدام للمسبحة
  final DateTime lastUsedDate;

  /// تاريخ آخر إشعار تم إرساله
  final DateTime? lastNotificationDate;

  /// عدد الإشعارات التي تم إرسالها
  final int notificationCount;

  /// ما إذا كان المستخدم قد عطل الإشعارات
  final bool notificationsEnabled;

  /// الوقت المفضل للمستخدم لاستخدام المسبحة (بالساعات، 0-23)
  final int preferredHour;

  /// عدد الأيام منذ آخر استخدام (يتم تمريره من الخارج)
  final int daysSinceLastUsed;

  UserActivity({
    required this.lastUsedDate,
    this.lastNotificationDate,
    this.notificationCount = 0,
    this.notificationsEnabled = true,
    this.preferredHour = 19, // الافتراضي: 7 مساءً
    this.daysSinceLastUsed = 0,
  });

  /// إنشاء نموذج من Map
  factory UserActivity.fromMap(Map<String, dynamic> map) {
    final lastUsedDate = DateTime.fromMillisecondsSinceEpoch(
        map['lastUsedDate'] ?? DateTime.now().millisecondsSinceEpoch);

    // حساب عدد الأيام منذ آخر استخدام
    final now = DateTime.now();
    final difference = now.difference(lastUsedDate);
    final calculatedDaysSinceLastUsed = difference.inDays;

    return UserActivity(
      lastUsedDate: lastUsedDate,
      lastNotificationDate: map['lastNotificationDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['lastNotificationDate'])
          : null,
      notificationCount: map['notificationCount'] ?? 0,
      notificationsEnabled: map['notificationsEnabled'] ?? true,
      preferredHour: map['preferredHour'] ?? 19,
      daysSinceLastUsed: calculatedDaysSinceLastUsed,
    );
  }

  /// إنشاء نموذج من JSON
  factory UserActivity.fromJson(Map<String, dynamic> json) {
    return UserActivity.fromMap(json);
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'lastUsedDate': lastUsedDate.millisecondsSinceEpoch,
      'lastNotificationDate': lastNotificationDate?.millisecondsSinceEpoch,
      'notificationCount': notificationCount,
      'notificationsEnabled': notificationsEnabled,
      'preferredHour': preferredHour,
      'daysSinceLastUsed': daysSinceLastUsed,
    };
  }

  /// تحويل النموذج إلى JSON
  Map<String, dynamic> toJson() {
    return toMap();
  }

  /// إنشاء نسخة جديدة مع تحديث بعض القيم
  UserActivity copyWith({
    DateTime? lastUsedDate,
    DateTime? lastNotificationDate,
    int? notificationCount,
    bool? notificationsEnabled,
    int? preferredHour,
    int? daysSinceLastUsed,
  }) {
    return UserActivity(
      lastUsedDate: lastUsedDate ?? this.lastUsedDate,
      lastNotificationDate: lastNotificationDate ?? this.lastNotificationDate,
      notificationCount: notificationCount ?? this.notificationCount,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      preferredHour: preferredHour ?? this.preferredHour,
      daysSinceLastUsed: daysSinceLastUsed ?? this.daysSinceLastUsed,
    );
  }

  /// حساب عدد الأيام منذ آخر استخدام (للاستخدام الداخلي)
  int calculateDaysSinceLastUsed() {
    final now = DateTime.now();
    final difference = now.difference(lastUsedDate);
    return difference.inDays;
  }

  /// التحقق مما إذا كان المستخدم غير نشط
  bool get isInactive => daysSinceLastUsed >= 3;

  /// التحقق مما إذا كان يجب إرسال إشعار
  bool shouldSendNotification() {
    if (!notificationsEnabled || !isInactive) {
      return false;
    }

    // إذا لم يتم إرسال إشعار من قبل
    if (lastNotificationDate == null) {
      return true;
    }

    final now = DateTime.now();
    final daysSinceLastNotification =
        now.difference(lastNotificationDate!).inDays;

    // تحديد الفترة بين الإشعارات بناءً على مدة عدم النشاط
    if (daysSinceLastUsed >= 30) {
      // بعد شهر: إشعار كل أسبوعين
      return daysSinceLastNotification >= 14;
    } else if (daysSinceLastUsed >= 14) {
      // بعد أسبوعين: إشعار كل أسبوع
      return daysSinceLastNotification >= 7;
    } else if (daysSinceLastUsed >= 7) {
      // بعد أسبوع: إشعار كل 3 أيام
      return daysSinceLastNotification >= 3;
    } else {
      // بعد 3 أيام: إشعار واحد فقط
      return notificationCount == 0;
    }
  }

  /// الحصول على وقت مناسب لإرسال الإشعار
  DateTime getOptimalNotificationTime() {
    final now = DateTime.now();

    // استخدام الوقت المفضل للمستخدم إذا كان في المستقبل
    final preferredTime =
        DateTime(now.year, now.month, now.day, preferredHour, 0);

    if (preferredTime.isAfter(now)) {
      return preferredTime;
    }

    // إذا كان الوقت المفضل قد مر، استخدم نفس الوقت في اليوم التالي
    return preferredTime.add(const Duration(days: 1));
  }

  /// تحديد نوع الرسالة بناءً على مدة عدم النشاط
  String getMessageType() {
    if (daysSinceLastUsed >= 30) {
      return 'long_absence';
    } else if (daysSinceLastUsed >= 14) {
      return 'medium_absence';
    } else if (daysSinceLastUsed >= 7) {
      return 'short_absence';
    } else {
      return 'recent_absence';
    }
  }
}
