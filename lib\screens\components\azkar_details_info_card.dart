part of '../azkar_details_screen.dart';

Widget _buildCategoryInfoCard({
  required Zikr category,
  required int totalAzkar,
  required int completedAzkar,
  required int filteredCount,
  required bool hasFilters,
  required BuildContext context,
  bool showTitle = false,
}) {
  final isDarkMode = Theme.of(context).brightness == Brightness.dark;

  // استخدام RepaintBoundary لتحسين أداء الرسوم المتحركة
  return RepaintBoundary(
    child: Card(
      // تقليل قيمة الارتفاع لتحسين الأداء
      elevation: 3,
      shadowColor: isDarkMode
          ? Colors.black54
          : AppColors.getAzkarColor(isDarkMode).withAlpha(51), // 0.2 * 255 = 51
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: AppColors.getAzkarColor(isDarkMode)
              .withA<PERSON><PERSON>(26), // 0.1 * 255 = 26
          width: 0.5,
        ),
      ),
      color: isDarkMode
          ? const Color(0xFF252A34) /* TasbihColors.darkCardColor */
          : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment:
              CrossAxisAlignment.end, // تغيير المحاذاة إلى اليمين
          children: [
            // عنوان البطاقة
            Row(
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.getAzkarColor(isDarkMode),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  showTitle ? category.name : 'معلومات الأذكار',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: isDarkMode
                        ? AppColors.getAzkarColor(isDarkMode)
                            .withAlpha(230) // 0.9 * 255 = 230
                        : AppColors.getAzkarColor(isDarkMode),
                  ),
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                  textAlign: TextAlign.right, // محاذاة النص إلى اليمين
                ),
              ],
            ),
            const Divider(height: 24),

            // وصف الأذكار
            Text(
              category.description,
              style: TextStyle(
                fontSize: 14,
                height: 1.5,
                color: isDarkMode
                    ? const Color(0xFFE0E0E0) /* TasbihColors.darkTextColor */
                    : Colors.black87,
              ),
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
              textAlign: TextAlign.right, // محاذاة النص إلى اليمين
            ),
            const SizedBox(height: 16),

            // المؤشرات
            Wrap(
              spacing: 12,
              runSpacing: 12,
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
              children: [
                _buildInfoChip(
                  icon: Icons.format_list_numbered,
                  label: '$totalAzkar ذكر',
                  context: context,
                ),
                _buildInfoChip(
                  icon: Icons.check_circle_outline,
                  label: '$completedAzkar مكتمل',
                  context: context,
                ),

                // في حالة وجود فلاتر نشطة
                if (hasFilters)
                  _buildInfoChip(
                    icon: Icons.filter_list,
                    label: 'يظهر $filteredCount ذكر',
                    backgroundColor: AppColors.getAzkarColor(isDarkMode)
                        .withAlpha(38), // 0.15 * 255 = 38
                    context: context,
                  ),
              ],
            ),
          ],
        ),
      ),
    ),
  );
}

Widget _buildInfoChip({
  required IconData icon,
  required String label,
  Color? backgroundColor,
  required BuildContext context,
}) {
  final isDarkMode = Theme.of(context).brightness == Brightness.dark;

  // استخدام RepaintBoundary لتحسين أداء الرسوم المتحركة
  return RepaintBoundary(
    child: Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 12,
        vertical: 8,
      ),
      decoration: BoxDecoration(
        color: backgroundColor ??
            (isDarkMode
                ? AppColors.getAzkarColor(isDarkMode)
                    .withAlpha(38) // 0.15 * 255 = 38
                : AppColors.getAzkarColor(isDarkMode)
                    .withAlpha(26)), // 0.1 * 255 = 26
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        textDirection:
            TextDirection.rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: isDarkMode
                ? AppColors.getAzkarColor(isDarkMode)
                    .withAlpha(230) // 0.9 * 255 = 230
                : AppColors.getAzkarColor(isDarkMode),
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: isDarkMode
                  ? AppColors.getAzkarColor(isDarkMode)
                      .withAlpha(230) // 0.9 * 255 = 230
                  : AppColors.getAzkarColor(isDarkMode),
              fontWeight: FontWeight.w500,
            ),
            textDirection:
                TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
            textAlign: TextAlign.right, // محاذاة النص إلى اليمين
          ),
        ],
      ),
    ),
  );
}
