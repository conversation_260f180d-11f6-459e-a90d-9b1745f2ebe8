import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../utils/category_colors.dart';
import '../models/book.dart';

class BookCoverWidget extends StatelessWidget {
  final Book book;
  final double width;
  final double height;
  final BoxFit fit;
  final bool showCategory;
  final bool showShadow;
  final bool useHero;
  final String? heroTag;
  final VoidCallback? onTap;
  final BorderRadius? borderRadius;

  const BookCoverWidget({
    Key? key,
    required this.book,
    this.width = 120,
    this.height = 180,
    this.fit = BoxFit.cover,
    this.showCategory = true,
    this.showShadow = true,
    this.useHero = false,
    this.heroTag,
    this.onTap,
    this.borderRadius,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final effectiveBorderRadius = borderRadius ?? BorderRadius.circular(12);
    final coverWidget = _buildCoverImage(effectiveBorderRadius);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: width,
        height: height,
        decoration: showShadow
            ? BoxDecoration(
                borderRadius: effectiveBorderRadius,
                boxShadow: [
                  BoxShadow(
                    color:
                        Colors.black.withValues(alpha: 51), // 0.2 * 255 = ~51
                    blurRadius: 8,
                    offset: const Offset(0, 3),
                  ),
                ],
              )
            : null,
        child: useHero && heroTag != null
            ? Hero(
                tag: heroTag!,
                child: coverWidget,
              )
            : coverWidget,
      ),
    );
  }

  Widget _buildCoverImage(BorderRadius borderRadius) {
    return Stack(
      fit: StackFit.expand,
      children: [
        // غلاف الكتاب
        ClipRRect(
          borderRadius: borderRadius,
          child: _buildImageSource(),
        ),

        // ظل تدريجي في الأسفل لتحسين ظهور النص
        if (showCategory)
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 50,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  bottomLeft: borderRadius.bottomLeft,
                  bottomRight: borderRadius.bottomRight,
                ),
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 179), // 0.7 * 255 = ~179
                  ],
                ),
              ),
            ),
          ),

        // تصنيف الكتاب
        if (showCategory && book.category.isNotEmpty)
          Positioned(
            bottom: 8,
            left: 8,
            right: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: CategoryColors.getColorForCategory(book.category)
                    .withValues(alpha: 204), // 0.8 * 255 = ~204
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                book.category,
                style: TextStyle(
                  color: CategoryColors.getTextColorForBackground(
                    CategoryColors.getColorForCategory(book.category),
                  ),
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildImageSource() {
    // الأولوية للصورة المحلية إذا كانت متوفرة
    if (book.localCoverPath != null && book.localCoverPath!.isNotEmpty) {
      return Image.asset(
        book.localCoverPath!,
        fit: fit,
        errorBuilder: (context, error, stackTrace) => _buildFallbackCover(),
      );
    }
    // ثم الصورة من الإنترنت إذا كانت متوفرة
    else if (book.coverUrl.isNotEmpty) {
      return CachedNetworkImage(
        imageUrl: book.coverUrl,
        fit: fit,
        placeholder: (context, url) => _buildLoadingPlaceholder(),
        errorWidget: (context, url, error) => _buildFallbackCover(),
      );
    }
    // إذا لم تتوفر أي صورة، نعرض غلافاً افتراضياً
    else {
      return _buildFallbackCover();
    }
  }

  Widget _buildLoadingPlaceholder() {
    return Container(
      color: Colors.grey[300],
      child: const Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
        ),
      ),
    );
  }

  Widget _buildFallbackCover() {
    // غلاف افتراضي يعرض عنوان الكتاب والمؤلف
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: borderRadius ?? BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[400]!),
      ),
      padding: const EdgeInsets.all(8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.book, size: 40, color: Colors.grey),
          const SizedBox(height: 8),
          Text(
            book.title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            book.author,
            style: const TextStyle(
              fontSize: 10,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
