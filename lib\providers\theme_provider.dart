import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeProvider extends ChangeNotifier {
  bool _isDarkMode = false;
  int _fontIndex = 0;
  // حجم الخط ثابت - لا يمكن تغييره من واجهة المستخدم
  static const double _fontSize = 14.0;
  ThemeData? _lightTheme;
  ThemeData? _darkTheme;

  // قائمة بأنواع الخطوط المتاحة
  static const List<String> availableFonts = [
    'النظام',
    'Cairo',
    'Amiri',
    'Tajawal',
    'Almarai',
    'Scheherazade New',
    'Noto <PERSON>',
    'Chang<PERSON>',
    '<PERSON> Me<PERSON>',
  ];

  // الوصول إلى الخصائص
  bool get isDarkMode => _isDarkMode;
  int get fontIndex => _fontIndex;
  ThemeMode get themeMode => _isDarkMode ? ThemeMode.dark : ThemeMode.light;
  ThemeData get lightTheme => _lightTheme ?? _buildTheme(false);
  ThemeData get darkTheme => _darkTheme ?? _buildTheme(true);
  String get currentFontName => availableFonts[_fontIndex];

  // بناء المزود واستدعاء التحميل
  ThemeProvider() {
    _loadSettings();
  }

  // تحميل الإعدادات المحفوظة
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _isDarkMode = prefs.getBool('darkMode') ?? false;
      _fontIndex = prefs.getInt('fontType') ?? 0;
      // حجم الخط ثابت الآن ولا يتم تحميله من التخزين

      _updateThemes();
      notifyListeners();
    } catch (e) {
      // إذا حدث خطأ، استخدم القيم الافتراضية
      _isDarkMode = false;
      _fontIndex = 0;
      // حجم الخط ثابت
      _updateThemes();
      notifyListeners();
    }
  }

  // تحديث السمات
  void _updateThemes() {
    try {
      _lightTheme = _buildTheme(false);
      _darkTheme = _buildTheme(true);
    } catch (e) {
      // في حالة حدوث خطأ، استخدم سمات افتراضية بسيطة
      _lightTheme = ThemeData.light();
      _darkTheme = ThemeData.dark();
    }
  }

  // بناء سمة بناءً على الإعدادات
  ThemeData _buildTheme(bool isDark) {
    final baseTheme = isDark ? ThemeData.dark() : ThemeData.light();

    // استخدام خط النظام
    if (_fontIndex == 0) {
      return baseTheme.copyWith(
        textTheme: _applyTextTheme(baseTheme.textTheme, isDark),
      );
    }

    // استخدام الخط المحلي
    String fontFamily;

    try {
      switch (availableFonts[_fontIndex]) {
        case 'Cairo':
          fontFamily = 'Cairo';
          break;
        case 'Amiri':
          fontFamily = 'Amiri';
          break;
        case 'Tajawal':
          fontFamily = 'Tajawal';
          break;
        case 'Almarai':
          fontFamily = 'Almarai';
          break;
        case 'Scheherazade New':
          fontFamily = 'ScheherazadeNew';
          break;
        case 'Noto Kufi Arabic':
          fontFamily = 'NotoKufiArabic';
          break;
        case 'Changa':
          fontFamily = 'Changa';
          break;
        case 'El Messiri':
          fontFamily = 'ElMessiri';
          break;
        default:
          fontFamily = '';
      }
    } catch (e) {
      // في حالة حدوث خطأ، استخدم خط النظام
      fontFamily = '';
    }

    // إنشاء سمة جديدة مع الخط المحدد
    if (fontFamily.isEmpty) {
      return baseTheme.copyWith(
        textTheme: _applyTextTheme(baseTheme.textTheme, isDark),
      );
    } else {
      // استخدام ThemeData مباشرة لتعيين fontFamily
      return ThemeData(
        // نسخ الخصائص الأساسية من baseTheme
        brightness: baseTheme.brightness,
        colorScheme: baseTheme.colorScheme,
        primaryColor: baseTheme.primaryColor,
        scaffoldBackgroundColor: baseTheme.scaffoldBackgroundColor,
        cardColor: baseTheme.cardColor,
        dividerColor: baseTheme.dividerColor,
        focusColor: baseTheme.focusColor,
        hoverColor: baseTheme.hoverColor,
        splashColor: baseTheme.splashColor,
        highlightColor: baseTheme.highlightColor,

        // تعيين الخط
        fontFamily: fontFamily,

        // تطبيق TextTheme مع الخط المحدد
        textTheme: _applyTextTheme(
            baseTheme.textTheme.apply(fontFamily: fontFamily), isDark),
        primaryTextTheme:
            baseTheme.primaryTextTheme.apply(fontFamily: fontFamily),
      );
    }
  }

  // تطبيق إعدادات النص بشكل آمن
  TextTheme _applyTextTheme(TextTheme textTheme, bool isDark) {
    final Color textColor = isDark ? Colors.white : Colors.black87;
    final Color displayColor = isDark ? Colors.white : Colors.black;

    // طريقة آمنة لتطبيق حجم الخط
    return TextTheme(
      // تحديد كل نمط نصي بشكل صريح مع ضبط الحجم
      displayLarge: textTheme.displayLarge?.copyWith(
        fontSize: textTheme.displayLarge?.fontSize != null
            ? textTheme.displayLarge!.fontSize! * (_fontSize / 14.0)
            : _fontSize * 2.0,
        color: displayColor,
      ),
      displayMedium: textTheme.displayMedium?.copyWith(
        fontSize: textTheme.displayMedium?.fontSize != null
            ? textTheme.displayMedium!.fontSize! * (_fontSize / 14.0)
            : _fontSize * 1.8,
        color: displayColor,
      ),
      displaySmall: textTheme.displaySmall?.copyWith(
        fontSize: textTheme.displaySmall?.fontSize != null
            ? textTheme.displaySmall!.fontSize! * (_fontSize / 14.0)
            : _fontSize * 1.6,
        color: displayColor,
      ),
      headlineLarge: textTheme.headlineLarge?.copyWith(
        fontSize: textTheme.headlineLarge?.fontSize != null
            ? textTheme.headlineLarge!.fontSize! * (_fontSize / 14.0)
            : _fontSize * 1.5,
        color: displayColor,
      ),
      headlineMedium: textTheme.headlineMedium?.copyWith(
        fontSize: textTheme.headlineMedium?.fontSize != null
            ? textTheme.headlineMedium!.fontSize! * (_fontSize / 14.0)
            : _fontSize * 1.35,
        color: displayColor,
      ),
      headlineSmall: textTheme.headlineSmall?.copyWith(
        fontSize: textTheme.headlineSmall?.fontSize != null
            ? textTheme.headlineSmall!.fontSize! * (_fontSize / 14.0)
            : _fontSize * 1.2,
        color: displayColor,
      ),
      titleLarge: textTheme.titleLarge?.copyWith(
        fontSize: textTheme.titleLarge?.fontSize != null
            ? textTheme.titleLarge!.fontSize! * (_fontSize / 14.0)
            : _fontSize * 1.15,
        color: displayColor,
      ),
      titleMedium: textTheme.titleMedium?.copyWith(
        fontSize: textTheme.titleMedium?.fontSize != null
            ? textTheme.titleMedium!.fontSize! * (_fontSize / 14.0)
            : _fontSize * 1.05,
        color: textColor,
      ),
      titleSmall: textTheme.titleSmall?.copyWith(
        fontSize: textTheme.titleSmall?.fontSize != null
            ? textTheme.titleSmall!.fontSize! * (_fontSize / 14.0)
            : _fontSize * 0.95,
        color: textColor,
      ),
      bodyLarge: textTheme.bodyLarge?.copyWith(
        fontSize: textTheme.bodyLarge?.fontSize != null
            ? textTheme.bodyLarge!.fontSize! * (_fontSize / 14.0)
            : _fontSize * 1.0,
        color: textColor,
      ),
      bodyMedium: textTheme.bodyMedium?.copyWith(
        fontSize: textTheme.bodyMedium?.fontSize != null
            ? textTheme.bodyMedium!.fontSize! * (_fontSize / 14.0)
            : _fontSize * 0.95,
        color: textColor,
      ),
      bodySmall: textTheme.bodySmall?.copyWith(
        fontSize: textTheme.bodySmall?.fontSize != null
            ? textTheme.bodySmall!.fontSize! * (_fontSize / 14.0)
            : _fontSize * 0.85,
        color: textColor.withAlpha(204), // 0.8 * 255 = 204
      ),
      labelLarge: textTheme.labelLarge?.copyWith(
        fontSize: textTheme.labelLarge?.fontSize != null
            ? textTheme.labelLarge!.fontSize! * (_fontSize / 14.0)
            : _fontSize * 0.95,
        color: textColor,
      ),
      labelMedium: textTheme.labelMedium?.copyWith(
        fontSize: textTheme.labelMedium?.fontSize != null
            ? textTheme.labelMedium!.fontSize! * (_fontSize / 14.0)
            : _fontSize * 0.9,
        color: textColor,
      ),
      labelSmall: textTheme.labelSmall?.copyWith(
        fontSize: textTheme.labelSmall?.fontSize != null
            ? textTheme.labelSmall!.fontSize! * (_fontSize / 14.0)
            : _fontSize * 0.8,
        color: textColor.withAlpha(178), // 0.7 * 255 = 178
      ),
    );
  }

  // تغيير الوضع الداكن
  Future<void> toggleDarkMode() async {
    _isDarkMode = !_isDarkMode;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('darkMode', _isDarkMode);
    } catch (e) {
      // تجاهل أخطاء الكتابة في التخزين
    }

    notifyListeners();
  }

  // تغيير نوع الخط
  Future<void> setFontType(int index) async {
    if (index < 0 || index >= availableFonts.length) return;

    _fontIndex = index;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('fontType', _fontIndex);
    } catch (e) {
      // تجاهل أخطاء الكتابة في التخزين
    }

    _updateThemes();
    notifyListeners();
  }

  // تم إلغاء ميزة تغيير حجم الخط
}
