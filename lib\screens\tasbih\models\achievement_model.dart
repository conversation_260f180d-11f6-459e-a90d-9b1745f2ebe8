// نموذج الإنجازات

/// نموذج لتمثيل إنجاز في المسبحة
class Achievement {
  /// معرف الإنجاز
  final String id;

  /// عنوان الإنجاز
  final String title;

  /// وصف الإنجاز
  final String description;

  /// رمز الأيقونة (كود Unicode)
  final String icon;

  /// ما إذا كان الإنجاز مكتمل
  final bool isCompleted;

  /// تاريخ إكمال الإنجاز
  final DateTime? completedDate;

  /// نسبة التقدم (0.0 - 1.0)
  final double progress;

  Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    this.isCompleted = false,
    this.completedDate,
    this.progress = 0.0,
  });

  /// إنشاء نموذج من Map
  factory Achievement.fromMap(Map<String, dynamic> map) {
    return Achievement(
      id: map['id'] as String,
      title: map['title'] as String,
      description: map['description'] as String,
      icon: map['icon'] as String,
      isCompleted: map['isCompleted'] as bool? ?? false,
      completedDate: map['completedDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['completedDate'] as int)
          : null,
      progress: (map['progress'] as num?)?.toDouble() ?? 0.0,
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'icon': icon,
      'isCompleted': isCompleted,
      'completedDate': completedDate?.millisecondsSinceEpoch,
      'progress': progress,
    };
  }

  /// إنشاء نسخة جديدة مع تحديث بعض القيم
  Achievement copyWith({
    String? id,
    String? title,
    String? description,
    String? icon,
    bool? isCompleted,
    DateTime? completedDate,
    double? progress,
  }) {
    return Achievement(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      isCompleted: isCompleted ?? this.isCompleted,
      completedDate: completedDate ?? this.completedDate,
      progress: progress ?? this.progress,
    );
  }
}
