// lib/services/search_service.dart
import 'package:flutter/material.dart';
import '../models/search_result_model.dart';
import '../utils/app_colors.dart';
import '../utils/constants.dart';

/// خدمة البحث المركزية للتطبيق
class SearchService {
  // قائمة بالنتائج المقترحة
  static final List<SearchResult> _suggestedResults = [
    SearchResult.azkar(
      id: 'morning_azkar',
      title: 'أذكار الصباح',
      subtitle: 'مجموعة أذكار الصباح اليومية',
    ),
    SearchResult.azkar(
      id: 'evening_azkar',
      title: 'أذكار المساء',
      subtitle: 'مجموعة أذكار المساء اليومية',
    ),
    SearchResult.azkar(
      id: 'sleep_azkar',
      title: 'أذكار النوم',
      subtitle: 'مجموعة أذكار النوم اليومية',
    ),
    SearchResult.tasbih(
      id: 'tasbih',
      title: 'المسبحة الإلكترونية',
      subtitle: 'سبّح بسهولة مع إحصائيات دقيقة',
    ),
    SearchResult.dua(
      id: 'duas',
      title: 'الأدعية',
      subtitle: 'مجموعة من الأدعية المأثورة',
    ),
    SearchResult.prophetPrayer(
      id: 'prophet_prayers',
      title: 'الصلاة على النبي',
      subtitle: 'صيغ متنوعة للصلاة على النبي ﷺ',
    ),
    SearchResult.favorite(
      id: 'favorites',
      title: 'المفضلة',
      subtitle: 'العناصر المفضلة لديك',
    ),
    SearchResult.book(
      id: 'books_coming_soon',
      title: 'الكتب - قريباً',
      subtitle: 'قسم الكتب قيد التطوير',
    ),
    SearchResult.poem(
      id: 'poems_coming_soon',
      title: 'القصائد - قريباً',
      subtitle: 'قسم القصائد قيد التطوير',
    ),
  ];

  /// الحصول على النتائج المقترحة
  static List<SearchResult> getSuggestedResults() {
    return _suggestedResults;
  }

  /// البحث في التطبيق
  static Future<List<SearchResult>> search(String query) async {
    // إذا كان البحث فارغاً، أعد النتائج المقترحة
    if (query.isEmpty) {
      return _suggestedResults;
    }

    // محاكاة تأخير البحث
    await Future.delayed(const Duration(milliseconds: 300));

    // تحويل البحث إلى حروف صغيرة للمقارنة
    final normalizedQuery = query.toLowerCase();

    // تصفية النتائج بناءً على البحث
    return _suggestedResults.where((result) {
      return result.title.toLowerCase().contains(normalizedQuery) ||
          result.subtitle.toLowerCase().contains(normalizedQuery) ||
          result.section.toLowerCase().contains(normalizedQuery);
    }).toList();
  }

  /// الحصول على نتائج البحث الشائعة
  static List<Map<String, dynamic>> getPopularSearchTerms() {
    return [
      {
        'term': 'أذكار الصباح',
        'section': 'الأذكار',
        'id': 'morning_azkar',
      },
      {
        'term': 'أذكار المساء',
        'section': 'الأذكار',
        'id': 'evening_azkar',
      },
      {
        'term': 'أذكار النوم',
        'section': 'الأذكار',
        'id': 'sleep_azkar',
      },
      {
        'term': 'المسبحة',
        'section': 'المسبحة',
        'id': 'tasbih',
      },
      {
        'term': 'الأدعية',
        'section': 'الأدعية',
        'id': 'duas',
      },
      {
        'term': 'الاستغفار',
        'section': 'المسبحة',
        'id': 'istighfar',
      },
      {
        'term': 'الصلاة على النبي',
        'section': 'الصلاة على النبي',
        'id': 'prophet_prayers',
      },
    ];
  }

  /// التنقل إلى نتيجة البحث
  static void navigateToResult(BuildContext context, SearchResult result) {
    if (!result.isAvailable) {
      // إذا كانت النتيجة غير متاحة، أظهر رسالة
      _showComingSoonMessage(context, result.section);
      return;
    }

    // التنقل بناءً على القسم
    switch (result.section) {
      case 'الأذكار':
        if (result.id == 'morning_azkar') {
          Navigator.pushNamed(
            context,
            AppConstants.azkarDetailsRoute,
            arguments: 'أذكار الصباح',
          );
        } else if (result.id == 'evening_azkar') {
          Navigator.pushNamed(
            context,
            AppConstants.azkarDetailsRoute,
            arguments: 'أذكار المساء',
          );
        } else if (result.id == 'sleep_azkar') {
          Navigator.pushNamed(
            context,
            AppConstants.azkarDetailsRoute,
            arguments: 'أذكار النوم',
          );
        } else {
          Navigator.pushNamed(context, AppConstants.azkarRoute);
        }
        break;
      case 'المسبحة':
        if (result.id == 'istighfar') {
          // التنقل إلى المسبحة مع تحديد الاستغفار
          Navigator.pushNamed(
            context,
            AppConstants.tasbihRoute,
            arguments: 'الاستغفار',
          );
        } else {
          Navigator.pushNamed(context, AppConstants.tasbihRoute);
        }
        break;
      case 'الأدعية':
        Navigator.pushNamed(context, AppConstants.duasRoute);
        break;
      case 'الصلاة على النبي':
        Navigator.pushNamed(context, AppConstants.prophetPrayersRoute);
        break;
      case 'المفضلة':
        Navigator.pushNamed(context, AppConstants.favoritesRoute);
        break;
      default:
        Navigator.pushNamed(context, result.route);
    }
  }

  /// الحصول على لون القسم
  static Color _getSectionColor(String section) {
    switch (section) {
      case 'الأدعية':
        return AppColors.duasColor;
      case 'الصلاة على النبي':
        return AppColors.prophetPrayersColor;
      case 'المسبحة':
        return AppColors.tasbihColor;
      case 'الكتب':
        return AppColors.booksColor;
      case 'القصائد':
        return AppColors.poemsColor;
      case 'المفضلة':
        return AppColors.favoritesColor;
      default:
        return AppColors.azkarColor;
    }
  }

  /// عرض رسالة "قريباً"
  static void _showComingSoonMessage(BuildContext context, String section) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.info_outline,
              color: Colors.white,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'قسم $section قيد التطوير وسيكون متاحاً قريباً',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: _getSectionColor(section),
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        margin: const EdgeInsets.all(12),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      ),
    );
  }
}
