import 'dart:convert';
import 'package:flutter/material.dart';
import '../models/zikr.dart';

/// أداة للتحقق من صحة ملفات JSON وتقديم تقارير عن الأخطاء
class JsonValidator {
  /// التحقق من صحة ملف JSON للأذكار
  static List<String> validateA<PERSON><PERSON><PERSON><PERSON>(String jsonString) {
    List<String> errors = [];
    
    try {
      // تحويل النص إلى كائن JSON
      final data = json.decode(jsonString);
      
      // التحقق من وجود مفتاح categories
      if (!data.containsKey('categories')) {
        errors.add('خطأ: المفتاح "categories" غير موجود في ملف JSON');
        return errors;
      }
      
      // التحقق من أن categories هو مصفوفة
      if (data['categories'] is! List) {
        errors.add('خطأ: المفتاح "categories" ليس مصفوفة');
        return errors;
      }
      
      // التحقق من كل فئة
      final List<dynamic> categories = data['categories'];
      final Set<String> categoryIds = {};
      
      for (int i = 0; i < categories.length; i++) {
        final category = categories[i];
        final categoryIndex = i + 1;
        
        // التحقق من أن الفئة هي كائن
        if (category is! Map<String, dynamic>) {
          errors.add('خطأ: الفئة رقم $categoryIndex ليست كائناً صالحاً');
          continue;
        }
        
        // التحقق من وجود الحقول المطلوبة
        if (!category.containsKey('id') || category['id'] == null || category['id'].toString().isEmpty) {
          errors.add('خطأ: الفئة رقم $categoryIndex تفتقد إلى معرف "id" صالح');
        } else {
          // التحقق من عدم تكرار المعرفات
          final categoryId = category['id'].toString();
          if (categoryIds.contains(categoryId)) {
            errors.add('خطأ: معرف الفئة "$categoryId" مكرر');
          } else {
            categoryIds.add(categoryId);
          }
        }
        
        if (!category.containsKey('name') || category['name'] == null || category['name'].toString().isEmpty) {
          errors.add('خطأ: الفئة رقم $categoryIndex تفتقد إلى اسم "name" صالح');
        }
        
        if (!category.containsKey('description')) {
          errors.add('خطأ: الفئة رقم $categoryIndex تفتقد إلى وصف "description"');
        }
        
        // التحقق من وجود إما items أو subcategories
        final hasItems = category.containsKey('items') && category['items'] is List;
        final hasSubcategories = category.containsKey('subcategories') && category['subcategories'] is List;
        final hasSubcategoriesFlag = category['hasSubcategories'] == true;
        
        if (!hasItems && !hasSubcategories) {
          errors.add('خطأ: الفئة رقم $categoryIndex لا تحتوي على "items" أو "subcategories"');
        }
        
        if (hasSubcategoriesFlag && !hasSubcategories) {
          errors.add('خطأ: الفئة رقم $categoryIndex تحتوي على hasSubcategories=true ولكن لا توجد subcategories');
        }
        
        if (!hasSubcategoriesFlag && hasSubcategories) {
          errors.add('تحذير: الفئة رقم $categoryIndex تحتوي على subcategories ولكن hasSubcategories=false');
        }
        
        // التحقق من عناصر items
        if (hasItems) {
          final List<dynamic> items = category['items'];
          final Set<String> itemIds = {};
          
          for (int j = 0; j < items.length; j++) {
            final item = items[j];
            final itemIndex = j + 1;
            
            // التحقق من أن العنصر هو كائن
            if (item is! Map<String, dynamic>) {
              errors.add('خطأ: العنصر رقم $itemIndex في الفئة رقم $categoryIndex ليس كائناً صالحاً');
              continue;
            }
            
            // التحقق من وجود الحقول المطلوبة
            if (!item.containsKey('id') || item['id'] == null || item['id'].toString().isEmpty) {
              errors.add('خطأ: العنصر رقم $itemIndex في الفئة رقم $categoryIndex يفتقد إلى معرف "id" صالح');
            } else {
              // التحقق من عدم تكرار المعرفات
              final itemId = item['id'].toString();
              if (itemIds.contains(itemId)) {
                errors.add('خطأ: معرف العنصر "$itemId" مكرر في الفئة رقم $categoryIndex');
              } else {
                itemIds.add(itemId);
              }
              
              // التحقق من تنسيق المعرف
              final categoryId = category['id'].toString();
              if (!itemId.startsWith('${categoryId}_')) {
                errors.add('تحذير: معرف العنصر "$itemId" لا يبدأ بمعرف الفئة "${categoryId}_"');
              }
            }
            
            if (!item.containsKey('text') || item['text'] == null || item['text'].toString().isEmpty) {
              errors.add('خطأ: العنصر رقم $itemIndex في الفئة رقم $categoryIndex يفتقد إلى نص "text" صالح');
            }
          }
          
          // التحقق من تطابق العدد
          if (category.containsKey('count') && category['count'] is int) {
            final declaredCount = category['count'] as int;
            final actualCount = items.length;
            
            if (declaredCount != actualCount) {
              errors.add('تحذير: عدد العناصر المعلن عنه ($declaredCount) لا يتطابق مع العدد الفعلي ($actualCount) في الفئة رقم $categoryIndex');
            }
          }
        }
        
        // التحقق من الفئات الفرعية
        if (hasSubcategories) {
          final List<dynamic> subcategories = category['subcategories'];
          final Set<String> subcategoryIds = {};
          
          for (int j = 0; j < subcategories.length; j++) {
            final subcategory = subcategories[j];
            final subcategoryIndex = j + 1;
            
            // التحقق من أن الفئة الفرعية هي كائن
            if (subcategory is! Map<String, dynamic>) {
              errors.add('خطأ: الفئة الفرعية رقم $subcategoryIndex في الفئة رقم $categoryIndex ليست كائناً صالحاً');
              continue;
            }
            
            // التحقق من وجود الحقول المطلوبة
            if (!subcategory.containsKey('id') || subcategory['id'] == null || subcategory['id'].toString().isEmpty) {
              errors.add('خطأ: الفئة الفرعية رقم $subcategoryIndex في الفئة رقم $categoryIndex تفتقد إلى معرف "id" صالح');
            } else {
              // التحقق من عدم تكرار المعرفات
              final subcategoryId = subcategory['id'].toString();
              if (subcategoryIds.contains(subcategoryId)) {
                errors.add('خطأ: معرف الفئة الفرعية "$subcategoryId" مكرر في الفئة رقم $categoryIndex');
              } else {
                subcategoryIds.add(subcategoryId);
              }
              
              // التحقق من تنسيق المعرف
              final categoryId = category['id'].toString();
              if (!subcategoryId.startsWith('${categoryId}_')) {
                errors.add('تحذير: معرف الفئة الفرعية "$subcategoryId" لا يبدأ بمعرف الفئة الرئيسية "${categoryId}_"');
              }
            }
            
            if (!subcategory.containsKey('name') || subcategory['name'] == null || subcategory['name'].toString().isEmpty) {
              errors.add('خطأ: الفئة الفرعية رقم $subcategoryIndex في الفئة رقم $categoryIndex تفتقد إلى اسم "name" صالح');
            }
            
            if (!subcategory.containsKey('description')) {
              errors.add('خطأ: الفئة الفرعية رقم $subcategoryIndex في الفئة رقم $categoryIndex تفتقد إلى وصف "description"');
            }
            
            // التحقق من وجود items
            if (!subcategory.containsKey('items') || subcategory['items'] is! List) {
              errors.add('خطأ: الفئة الفرعية رقم $subcategoryIndex في الفئة رقم $categoryIndex لا تحتوي على "items" صالحة');
            } else {
              final List<dynamic> items = subcategory['items'];
              final Set<String> itemIds = {};
              
              for (int k = 0; k < items.length; k++) {
                final item = items[k];
                final itemIndex = k + 1;
                
                // التحقق من أن العنصر هو كائن
                if (item is! Map<String, dynamic>) {
                  errors.add('خطأ: العنصر رقم $itemIndex في الفئة الفرعية رقم $subcategoryIndex في الفئة رقم $categoryIndex ليس كائناً صالحاً');
                  continue;
                }
                
                // التحقق من وجود الحقول المطلوبة
                if (!item.containsKey('id') || item['id'] == null || item['id'].toString().isEmpty) {
                  errors.add('خطأ: العنصر رقم $itemIndex في الفئة الفرعية رقم $subcategoryIndex في الفئة رقم $categoryIndex يفتقد إلى معرف "id" صالح');
                } else {
                  // التحقق من عدم تكرار المعرفات
                  final itemId = item['id'].toString();
                  if (itemIds.contains(itemId)) {
                    errors.add('خطأ: معرف العنصر "$itemId" مكرر في الفئة الفرعية رقم $subcategoryIndex في الفئة رقم $categoryIndex');
                  } else {
                    itemIds.add(itemId);
                  }
                }
                
                if (!item.containsKey('text') || item['text'] == null || item['text'].toString().isEmpty) {
                  errors.add('خطأ: العنصر رقم $itemIndex في الفئة الفرعية رقم $subcategoryIndex في الفئة رقم $categoryIndex يفتقد إلى نص "text" صالح');
                }
              }
              
              // التحقق من تطابق العدد
              if (subcategory.containsKey('count') && subcategory['count'] is int) {
                final declaredCount = subcategory['count'] as int;
                final actualCount = items.length;
                
                if (declaredCount != actualCount) {
                  errors.add('تحذير: عدد العناصر المعلن عنه ($declaredCount) لا يتطابق مع العدد الفعلي ($actualCount) في الفئة الفرعية رقم $subcategoryIndex في الفئة رقم $categoryIndex');
                }
              }
            }
          }
        }
      }
    } catch (e) {
      errors.add('خطأ في تحليل ملف JSON: $e');
    }
    
    return errors;
  }
  
  /// محاولة تحويل نص JSON إلى كائنات Dart
  static List<Zikr>? tryParseAzkarJson(String jsonString) {
    try {
      final data = json.decode(jsonString);
      if (data['categories'] != null) {
        return List<Zikr>.from(
            data['categories'].map((category) => Zikr.fromJson(category)));
      }
    } catch (e) {
      debugPrint('خطأ في تحويل JSON إلى كائنات Dart: $e');
    }
    return null;
  }
}
