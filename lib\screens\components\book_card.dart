import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../models/book.dart';
import '../../utils/app_colors.dart';
import '../../utils/responsive_helper.dart';

class BookCard extends StatelessWidget {
  final Book book;
  final VoidCallback onTap;
  final Animation<double>? animation;
  final bool showRating;
  final bool showDownloadStatus;

  const BookCard({
    Key? key,
    required this.book,
    required this.onTap,
    this.animation,
    this.showRating = true,
    this.showDownloadStatus = true,
  }) : super(key: key);

  // الحصول على لون الفئة
  Color _getCategoryColor(String category) {
    return AppColors.getCategoryColor(category);
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = ResponsiveHelper.isTablet(context);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // تحديد حجم الخط بناءً على حجم الشاشة
    final titleFontSize = isTablet ? 16.0 : 14.0;
    final authorFontSize = isTablet ? 14.0 : 12.0;
    final categoryFontSize = isTablet ? 12.0 : 10.0;

    // بناء البطاقة مع تحسين التوافق مع الوضع الداكن
    Widget card = Container(
      decoration: BoxDecoration(
        color: isDarkMode ? Theme.of(context).cardColor : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withAlpha(51) // 0.2 * 255 = 51
                : Colors.black.withAlpha(13), // 0.05 * 255 = 13
            blurRadius: isDarkMode ? 8 : 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // صورة الغلاف
                Expanded(
                  flex: 3,
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      // صورة الغلاف
                      _buildCoverImage(context),

                      // مؤشر التنزيل
                      if (showDownloadStatus &&
                          book.downloadProgress > 0 &&
                          book.downloadProgress < 100)
                        Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          child: LinearProgressIndicator(
                            value: book.downloadProgress / 100,
                            backgroundColor: Colors.black26,
                            valueColor: AlwaysStoppedAnimation<Color>(
                                _getCategoryColor(book.category)),
                          ),
                        ),

                      // أيقونة المفضلة
                      if (book.isFavorite)
                        Positioned(
                          top: 8,
                          right: 8,
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Colors.black
                                  .withAlpha(153), // 0.6 * 255 = 153
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.favorite,
                              color: Colors.red,
                              size: 16,
                            ),
                          ),
                        ),

                      // أيقونة التنزيل
                      if (book.isDownloaded)
                        Positioned(
                          top: 8,
                          left: 8,
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Colors.black
                                  .withAlpha(153), // 0.6 * 255 = 153
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.download_done,
                              color: Colors.green,
                              size: 16,
                            ),
                          ),
                        ),

                      // شريط الفئة
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          color: _getCategoryColor(book.category)
                              .withAlpha(204), // 0.8 * 255 = 204
                          child: Text(
                            book.category,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: categoryFontSize,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // معلومات الكتاب
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // عنوان الكتاب
                        Text(
                          book.title,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: titleFontSize,
                            fontWeight: FontWeight.bold,
                            color: isDarkMode ? Colors.white : Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 4),

                        // اسم المؤلف
                        Text(
                          book.author,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: authorFontSize,
                            color: isDarkMode ? Colors.white70 : Colors.black54,
                          ),
                        ),

                        // التقييم
                        if (showRating && book.rating > 0)
                          Padding(
                            padding: const EdgeInsets.only(top: 4),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.star,
                                  size: 14,
                                  color: Colors.amber,
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  book.rating.toStringAsFixed(1),
                                  style: TextStyle(
                                    fontSize: authorFontSize,
                                    color: isDarkMode
                                        ? Colors.white70
                                        : Colors.black54,
                                  ),
                                ),
                              ],
                            ),
                          ),

                        // عدد الصفحات
                        if (book.pages > 0)
                          Padding(
                            padding: const EdgeInsets.only(top: 4),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.menu_book_outlined,
                                  size: 14,
                                  color: isDarkMode
                                      ? Colors.white70
                                      : Colors.black54,
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  '${book.pages} صفحة',
                                  style: TextStyle(
                                    fontSize: authorFontSize,
                                    color: isDarkMode
                                        ? Colors.white70
                                        : Colors.black54,
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    // تطبيق الرسوم المتحركة إذا كانت متوفرة
    if (animation != null) {
      return FadeTransition(
        opacity: animation!,
        child: SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, 0.2),
            end: Offset.zero,
          ).animate(animation!),
          child: card,
        ),
      );
    }

    return card;
  }

  // بناء صورة الغلاف
  Widget _buildCoverImage(BuildContext context) {
    // التحقق من وجود صورة محلية أولاً
    if (book.localCoverPath != null && book.localCoverPath!.isNotEmpty) {
      return Hero(
        tag: 'book-cover-${book.id}',
        child: Image.asset(
          book.localCoverPath!,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            // إذا فشل تحميل الصورة المحلية، تحقق من الرابط الخارجي
            return _buildFallbackOrNetworkImage();
          },
        ),
      );
    } else {
      // إذا لم تكن هناك صورة محلية، استخدم الرابط الخارجي أو الصورة الاحتياطية
      return _buildFallbackOrNetworkImage();
    }
  }

  Widget _buildFallbackOrNetworkImage() {
    if (book.coverUrl.isNotEmpty) {
      return Hero(
        tag: 'book-cover-${book.id}',
        child: CachedNetworkImage(
          imageUrl: book.coverUrl,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            color: _getCategoryColor(book.category)
                .withAlpha(77), // 0.3 * 255 = 77
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          ),
          errorWidget: (context, url, error) => _buildFallbackCover(),
        ),
      );
    } else {
      return _buildFallbackCover();
    }
  }

  Widget _buildFallbackCover() {
    // صورة احتياطية تعرض الحرف الأول من عنوان الكتاب
    return Container(
      color: _getCategoryColor(book.category),
      child: Center(
        child: Text(
          book.title.isNotEmpty
              ? book.title.substring(0, 1).toUpperCase()
              : '?',
          style: const TextStyle(
            fontSize: 50,
            fontWeight: FontWeight.bold,
            color: Colors.white54,
          ),
        ),
      ),
    );
  }
}
