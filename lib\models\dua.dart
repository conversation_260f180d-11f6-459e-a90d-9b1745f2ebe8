// نموذج الدعاء

class DuaCategory {
  final String id;
  final String name;
  final String description;
  final int count;
  final String iconName;
  final String? iconPath; // مسار أيقونة SVG
  final List<Dua> items;
  final List<DuaCategory>? subcategories;
  final bool _hasSubcategoriesFlag;

  DuaCategory({
    required this.id,
    required this.name,
    required this.description,
    required int? count,
    required this.iconName,
    this.iconPath, // مسار أيقونة SVG
    required this.items,
    this.subcategories,
    bool hasSubcategories = false,
  })  :
        // استخدام عدد العناصر الفعلي إذا كانت قيمة count غير محددة أو صفر
        count = count != null && count > 0 ? count : items.length,
        _hasSubcategoriesFlag = hasSubcategories;

  factory DuaCategory.fromJson(Map<String, dynamic> json) {
    // استخراج العناصر أولاً لاستخدامها في حساب العدد الفعلي
    final items = (json['items'] as List<dynamic>?)
            ?.map((item) => Dua.fromJson(item))
            .toList() ??
        [];

    return DuaCategory(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      count: json['count'], // تمرير القيمة كما هي، وسيتم التحقق منها في المنشئ
      iconName: json['iconName'] ?? 'favorite',
      iconPath: json['iconPath'], // قراءة مسار أيقونة SVG من JSON
      items: items,
      subcategories: (json['subcategories'] as List<dynamic>?)
          ?.map((sub) => DuaCategory.fromJson(sub))
          .toList(),
      hasSubcategories: json['hasSubcategories'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'count': count,
      'iconName': iconName,
      'iconPath': iconPath, // إضافة مسار أيقونة SVG إلى JSON
      'items': items.map((item) => item.toJson()).toList(),
      'hasSubcategories': hasSubcategories,
      'subcategories': subcategories?.map((sub) => sub.toJson()).toList(),
    };
  }

  String get imageUrl => 'assets/images/duas/$id.jpg';

  List<Dua> get duas => items;

  bool get hasSubcategories =>
      _hasSubcategoriesFlag ||
      (subcategories != null && subcategories!.isNotEmpty);
}

class Dua {
  final String id;
  final String text;
  final String? translation;
  final String? transliteration;
  final String? source;
  final String? explanation;
  final bool isFeatured;
  final String? iconName;
  final String? reference;
  final String? occasion;
  final String? virtue;
  final bool isFavorite;

  Dua({
    required this.id,
    required this.text,
    this.translation,
    this.transliteration,
    this.source,
    this.explanation,
    this.isFeatured = false,
    this.iconName,
    this.reference,
    this.occasion,
    this.virtue,
    this.isFavorite = false,
  });

  factory Dua.fromJson(Map<String, dynamic> json) {
    return Dua(
      id: json['id'] ?? '',
      text: json['text'] ?? '',
      translation: json['translation'],
      transliteration: json['transliteration'],
      source: json['source'],
      explanation: json['explanation'],
      isFeatured: json['isFeatured'] ?? false,
      iconName: json['iconName'],
      reference: json['reference'],
      occasion: json['occasion'],
      virtue: json['virtue'],
      isFavorite: json['isFavorite'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      if (translation != null) 'translation': translation,
      if (transliteration != null) 'transliteration': transliteration,
      if (source != null) 'source': source,
      if (explanation != null) 'explanation': explanation,
      'isFeatured': isFeatured,
      if (iconName != null) 'iconName': iconName,
      if (reference != null) 'reference': reference,
      if (occasion != null) 'occasion': occasion,
      if (virtue != null) 'virtue': virtue,
      'isFavorite': isFavorite,
    };
  }

  // نسخة جديدة من الدعاء مع تحديث حالة المفضلة
  Dua copyWith({bool? isFavorite}) {
    return Dua(
      id: id,
      text: text,
      translation: translation,
      transliteration: transliteration,
      source: source,
      explanation: explanation,
      isFeatured: isFeatured,
      iconName: iconName,
      reference: reference,
      occasion: occasion,
      virtue: virtue,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Dua && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
