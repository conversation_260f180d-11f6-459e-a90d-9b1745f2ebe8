// مزود الصلاة على النبي

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/dua.dart';
import '../database/database_helper.dart';

class ProphetPrayersProvider extends ChangeNotifier {
  List<DuaCategory> _categories = [];
  List<Dua> _featuredPrayers = [];
  bool _isLoading = true;
  String _searchQuery = '';
  final Set<String> _favoritePrayerIds = {}; // مجموعة معرفات الصلوات المفضلة
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // الحصول على قائمة الفئات
  List<DuaCategory> get categories => _categories;

  // الحصول على قائمة الصلوات المميزة
  List<Dua> get featuredPrayers => _featuredPrayers;

  // الحصول على حالة التحميل
  bool get isLoading => _isLoading;

  // الحصول على نص البحث
  String get searchQuery => _searchQuery;

  // الحصول على قائمة الصلوات المفضلة
  List<Dua> get favoritePrayers {
    List<Dua> favorites = [];

    // دالة مساعدة لجمع الصلوات المفضلة من فئة وفئاتها الفرعية
    void collectFromCategory(DuaCategory category) {
      // جمع الصلوات المفضلة من الفئة الحالية
      for (var prayer in category.items) {
        if (prayer.isFavorite) {
          favorites.add(prayer);
        }
      }

      // جمع الصلوات المفضلة من الفئات الفرعية
      if (category.subcategories != null) {
        for (var subcategory in category.subcategories!) {
          collectFromCategory(subcategory);
        }
      }
    }

    // جمع الصلوات المفضلة من جميع الفئات
    for (var category in _categories) {
      collectFromCategory(category);
    }

    return favorites;
  }

  // تهيئة المزود
  Future<void> initialize() async {
    await loadProphetPrayers();
    await loadFavorites();
  }

  // تحميل الصلوات على النبي من ملف JSON
  Future<void> loadProphetPrayers() async {
    try {
      _isLoading = true;
      notifyListeners();

      debugPrint('بدء تحميل ملف الصلوات على النبي...');

      // قراءة ملف الصلوات على النبي
      final String jsonString =
          await rootBundle.loadString('assets/data/prophet_prayers.json');

      debugPrint(
          'تم قراءة ملف JSON بنجاح، حجم البيانات: ${jsonString.length} حرف');

      // معالجة البيانات للتأكد من صحة التنسيق
      String cleanedJsonString = jsonString
          .replaceAll('\n', ' ') // استبدال أي أسطر جديدة بمسافات
          .replaceAll('\r', ' '); // استبدال أي عودة سطر بمسافات

      debugPrint('تم تنظيف البيانات، محاولة تحليل JSON...');

      // تحليل البيانات
      final Map<String, dynamic> jsonData = json.decode(cleanedJsonString);

      debugPrint(
          'تم تحليل JSON بنجاح، عدد الفئات: ${(jsonData['categories'] as List).length}');

      // تحويل البيانات إلى نماذج
      _categories = (jsonData['categories'] as List<dynamic>)
          .map((category) => DuaCategory.fromJson(category))
          .toList();

      debugPrint(
          'تم تحويل البيانات إلى نماذج، عدد الفئات: ${_categories.length}');

      // استخراج الصلوات المميزة
      _extractFeaturedPrayers();

      debugPrint(
          'تم استخراج الصلوات المميزة، العدد: ${_featuredPrayers.length}');

      _isLoading = false;
      notifyListeners();

      debugPrint('تم الانتهاء من تحميل الصلوات على النبي بنجاح');
    } catch (e) {
      debugPrint('خطأ في تحميل الصلوات على النبي: $e');
      // محاولة تحميل البيانات بطريقة بديلة
      try {
        debugPrint('محاولة تحميل البيانات بطريقة بديلة...');

        // إنشاء فئة افتراضية للتأكد من عرض شيء للمستخدم
        _categories = [
          DuaCategory(
            id: 'default',
            name: 'الصيغ المأثورة',
            description: 'صيغ الصلاة على النبي المأثورة',
            count: 1,
            iconName: 'auto_awesome',
            items: [
              Dua(
                id: 'default_1',
                text:
                    'اللَّهُمَّ صَلِّ عَلَى مُحَمَّدٍ وَعَلَى آلِ مُحَمَّدٍ، كَمَا صَلَّيْتَ عَلَى إِبْرَاهِيمَ وَعَلَى آلِ إِبْرَاهِيمَ، إِنَّكَ حَمِيدٌ مَجِيدٌ',
                translation:
                    'اللهم صل على محمد وعلى آل محمد، كما صليت على إبراهيم وعلى آل إبراهيم، إنك حميد مجيد',
                source: 'متفق عليه',
                explanation: 'الصيغة الإبراهيمية المشهورة',
                isFeatured: true,
              ),
            ],
          ),
        ];

        // استخراج الصلوات المميزة
        _extractFeaturedPrayers();

        debugPrint('تم إنشاء بيانات افتراضية بنجاح');
      } catch (e2) {
        debugPrint('فشل أيضاً في إنشاء بيانات افتراضية: $e2');
      }

      _isLoading = false;
      notifyListeners();
    }
  }

  // استخراج الصلوات المميزة من جميع الفئات
  void _extractFeaturedPrayers() {
    _featuredPrayers = [];

    // دالة مساعدة لاستخراج الصلوات المميزة من فئة وفئاتها الفرعية
    void extractFromCategory(DuaCategory category) {
      // استخراج الصلوات المميزة من الفئة الحالية
      for (var prayer in category.items) {
        if (prayer.isFeatured) {
          _featuredPrayers.add(prayer);
        }
      }

      // استخراج الصلوات المميزة من الفئات الفرعية
      if (category.subcategories != null) {
        for (var subcategory in category.subcategories!) {
          extractFromCategory(subcategory);
        }
      }
    }

    // استخراج الصلوات المميزة من جميع الفئات
    for (var category in _categories) {
      extractFromCategory(category);
    }

    // ترتيب الصلوات المميزة عشوائياً (اختياري)
    // _featuredPrayers.shuffle();

    // عرض جميع الصلوات المميزة بدلاً من تحديدها بـ 5 فقط
    // if (_featuredPrayers.length > 5) {
    //   _featuredPrayers = _featuredPrayers.sublist(0, 5);
    // }
  }

  // تحديث نص البحث
  void updateSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  // البحث عن الصلوات
  List<DuaCategory> searchProphetPrayers(String query) {
    if (query.isEmpty) {
      return _categories;
    }

    // دالة مساعدة للبحث في فئة وفئاتها الفرعية
    DuaCategory? searchInCategory(DuaCategory category) {
      // البحث في الفئة الحالية
      final matchingItems = category.items.where((prayer) {
        return prayer.text.contains(query) ||
            (prayer.translation != null &&
                prayer.translation!.contains(query)) ||
            (prayer.source != null && prayer.source!.contains(query)) ||
            (prayer.explanation != null &&
                prayer.explanation!.contains(query)) ||
            (prayer.occasion != null && prayer.occasion!.contains(query));
      }).toList();

      // البحث في الفئات الفرعية
      List<DuaCategory> matchingSubcategories = [];
      if (category.subcategories != null) {
        for (var subcategory in category.subcategories!) {
          final matchingSubcategory = searchInCategory(subcategory);
          if (matchingSubcategory != null) {
            matchingSubcategories.add(matchingSubcategory);
          }
        }
      }

      // إذا لم يتم العثور على أي نتائج، أرجع null
      if (matchingItems.isEmpty && matchingSubcategories.isEmpty) {
        return null;
      }

      // إنشاء فئة جديدة تحتوي على العناصر المطابقة فقط
      return DuaCategory(
        id: category.id,
        name: category.name,
        description: category.description,
        count: matchingItems.length,
        iconName: category.iconName,
        items: matchingItems,
        subcategories:
            matchingSubcategories.isEmpty ? null : matchingSubcategories,
      );
    }

    // البحث في جميع الفئات
    List<DuaCategory> results = [];
    for (var category in _categories) {
      final matchingCategory = searchInCategory(category);
      if (matchingCategory != null) {
        results.add(matchingCategory);
      }
    }

    return results;
  }

  // تحميل الصلوات المفضلة
  Future<void> loadFavorites() async {
    try {
      debugPrint('جاري تحميل الصلوات المفضلة...');

      // تحميل المفضلة من قاعدة البيانات
      final favorites = await _databaseHelper.getFavorites();

      // تحديث مجموعة معرفات الصلوات المفضلة
      _favoritePrayerIds.clear();
      for (var favorite in favorites) {
        if (favorite['item_type'] == 'prophet_prayer') {
          _favoritePrayerIds.add(favorite['item_id']);
        }
      }

      // تحديث حالة المفضلة في نماذج الصلوات
      _updateFavoriteStatus();

      debugPrint('تم تحميل ${_favoritePrayerIds.length} صلاة مفضلة');
    } catch (e) {
      debugPrint('خطأ في تحميل الصلوات المفضلة: $e');
    }
  }

  // تحديث حالة المفضلة في نماذج الصلوات
  void _updateFavoriteStatus() {
    // دالة مساعدة لتحديث حالة المفضلة في فئة وفئاتها الفرعية
    void updateInCategory(DuaCategory category) {
      // تحديث الصلوات في الفئة الحالية
      for (int i = 0; i < category.items.length; i++) {
        final prayer = category.items[i];
        final isFavorite = _favoritePrayerIds.contains(prayer.id);

        // إذا كانت حالة المفضلة مختلفة، قم بتحديث النموذج
        if (prayer.isFavorite != isFavorite) {
          category.items[i] = prayer.copyWith(isFavorite: isFavorite);
        }
      }

      // تحديث الصلوات في الفئات الفرعية
      if (category.subcategories != null) {
        for (var subcategory in category.subcategories!) {
          updateInCategory(subcategory);
        }
      }
    }

    // تحديث الصلوات في جميع الفئات
    for (var category in _categories) {
      updateInCategory(category);
    }

    // تحديث الصلوات المميزة
    for (int i = 0; i < _featuredPrayers.length; i++) {
      final prayer = _featuredPrayers[i];
      final isFavorite = _favoritePrayerIds.contains(prayer.id);

      if (prayer.isFavorite != isFavorite) {
        _featuredPrayers[i] = prayer.copyWith(isFavorite: isFavorite);
      }
    }

    // إخطار المستمعين بالتغييرات
    notifyListeners();
  }

  // إضافة صلاة إلى المفضلة
  Future<bool> addToFavorites(Dua prayer) async {
    try {
      // إضافة إلى قاعدة البيانات
      await _databaseHelper.addToFavorites(prayer.id,
          itemType: 'prophet_prayer');

      // تحديث مجموعة المعرفات
      _favoritePrayerIds.add(prayer.id);

      // تحديث حالة المفضلة في النماذج
      _updateFavoriteStatus();

      debugPrint('تمت إضافة الصلاة إلى المفضلة: ${prayer.id}');
      return true;
    } catch (e) {
      debugPrint('خطأ في إضافة الصلاة إلى المفضلة: $e');
      return false;
    }
  }

  // إزالة صلاة من المفضلة
  Future<bool> removeFromFavorites(Dua prayer) async {
    try {
      // إزالة من قاعدة البيانات
      await _databaseHelper.removeFromFavorites(prayer.id,
          itemType: 'prophet_prayer');

      // تحديث مجموعة المعرفات
      _favoritePrayerIds.remove(prayer.id);

      // تحديث حالة المفضلة في النماذج
      _updateFavoriteStatus();

      debugPrint('تمت إزالة الصلاة من المفضلة: ${prayer.id}');
      return true;
    } catch (e) {
      debugPrint('خطأ في إزالة الصلاة من المفضلة: $e');
      return false;
    }
  }

  // تبديل حالة المفضلة
  Future<bool> toggleFavorite(Dua prayer) async {
    if (prayer.isFavorite) {
      return await removeFromFavorites(prayer);
    } else {
      return await addToFavorites(prayer);
    }
  }

  // التحقق مما إذا كانت الصلاة مفضلة
  bool isFavorite(String prayerId) {
    return _favoritePrayerIds.contains(prayerId);
  }

  // الحصول على صلاة عشوائية
  Dua? getRandomPrayer() {
    if (_categories.isEmpty) {
      return null;
    }

    // جمع جميع الصلوات من جميع الفئات
    List<Dua> allPrayers = [];

    void collectPrayersFromCategory(DuaCategory category) {
      allPrayers.addAll(category.items);

      if (category.subcategories != null) {
        for (var subcategory in category.subcategories!) {
          collectPrayersFromCategory(subcategory);
        }
      }
    }

    for (var category in _categories) {
      collectPrayersFromCategory(category);
    }

    if (allPrayers.isEmpty) {
      return null;
    }

    // اختيار صلاة عشوائية
    allPrayers.shuffle();
    return allPrayers.first;
  }

  // الحصول على الصلوات حسب الفئة
  List<Dua> getPrayersByCategory(String categoryId) {
    List<Dua> prayers = [];

    debugPrint('البحث عن الصلوات للفئة: $categoryId');

    // التحقق من وجود فئات
    if (_categories.isEmpty) {
      debugPrint('لا توجد فئات متاحة');
      return prayers;
    }

    // البحث عن الفئة بالمعرف
    DuaCategory? findCategory(List<DuaCategory> categories, String id) {
      for (var category in categories) {
        debugPrint('فحص الفئة: ${category.id} (${category.name})');
        if (category.id == id) {
          debugPrint('تم العثور على الفئة: ${category.id} (${category.name})');
          debugPrint('عدد الصلوات في الفئة: ${category.items.length}');
          return category;
        }

        if (category.subcategories != null &&
            category.subcategories!.isNotEmpty) {
          debugPrint(
              'البحث في الفئات الفرعية لـ ${category.id} (${category.subcategories!.length} فئة فرعية)');
          final found = findCategory(category.subcategories!, id);
          if (found != null) {
            return found;
          }
        }
      }

      return null;
    }

    // البحث عن الفئة
    final category = findCategory(_categories, categoryId);

    // إذا تم العثور على الفئة، جمع الصلوات منها ومن فئاتها الفرعية
    if (category != null) {
      // جمع الصلوات من الفئة الحالية
      prayers.addAll(category.items);
      debugPrint(
          'تم إضافة ${category.items.length} صلاة من الفئة ${category.id}');

      // جمع الصلوات من الفئات الفرعية
      void collectFromSubcategories(List<DuaCategory>? subcategories) {
        if (subcategories == null || subcategories.isEmpty) return;

        for (var subcategory in subcategories) {
          prayers.addAll(subcategory.items);
          debugPrint(
              'تم إضافة ${subcategory.items.length} صلاة من الفئة الفرعية ${subcategory.id}');
          collectFromSubcategories(subcategory.subcategories);
        }
      }

      collectFromSubcategories(category.subcategories);
    } else {
      debugPrint('لم يتم العثور على الفئة: $categoryId');

      // محاولة البحث عن الفئة بطريقة أخرى (مطابقة جزئية)
      for (var category in _categories) {
        if (category.id.contains(categoryId) ||
            categoryId.contains(category.id)) {
          debugPrint('تم العثور على مطابقة جزئية: ${category.id}');
          prayers.addAll(category.items);
          break;
        }
      }
    }

    debugPrint('إجمالي عدد الصلوات التي تم العثور عليها: ${prayers.length}');
    return prayers;
  }
}
