// مزود المسبحة

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/dhikr_model.dart';
import 'package:wahaj_alsaalik/database/database_helper.dart';
import 'package:vibration/vibration.dart';
import 'tasbih_stats_provider.dart';
// import '../services/awesome_notification_service.dart'; // تم تعطيل النظام القديم
import '../../../services/notification_manager.dart'; // النظام الجديد
import '../../../services/permission_manager.dart';

class TasbihProvider extends ChangeNotifier {
  // متغيرات المسبحة
  int _dhikrCount = 0;
  int _targetCount = 33;
  int _sessionCount = 0;
  int _totalCount = 0;
  bool _vibrationEnabled = true;
  List<double> _beadPositions = List.generate(33, (index) => 0);
  bool _isRippleActive = false;

  // خدمة الإشعارات الجديدة
  final NotificationManager _notificationManager = NotificationManager();

  // الذكر المحدد
  DhikrModel _selectedDhikr = DhikrModel(
    id: -1,
    name: 'سبحان الله',
    count: 33,
  );

  // قائمة الأذكار
  List<DhikrModel> _availableDhikrs = [];

  // قائمة أعداد العد المتاحة
  final List<int> _availableCounts = [33, 99, 100];

  // القيم المستخرجة
  int get dhikrCount => _dhikrCount;
  int get targetCount => _targetCount;
  int get sessionCount => _sessionCount;
  int get totalCount => _totalCount;
  bool get vibrationEnabled => _vibrationEnabled;
  List<double> get beadPositions => _beadPositions;
  bool get isRippleActive => _isRippleActive;
  DhikrModel get selectedDhikr => _selectedDhikr;
  List<DhikrModel> get availableDhikrs => _availableDhikrs;
  List<DhikrModel> get dhikrs => _availableDhikrs; // إضافة getter للأذكار
  List<int> get availableCounts => _availableCounts;

  // دالة التهيئة
  Future<void> initialize() async {
    // تهيئة خدمة الإشعارات الجديدة
    await _notificationManager.init();

    await _loadTasbihSettings();
    await loadDhikrs();

    // ملاحظة: تم إزالة تعطيل إشعارات العودة التلقائي
    // لإتاحة الفرصة للمستخدم لتفعيل الإشعارات

    // ملاحظة: سيتم جدولة إشعارات العودة لاحقاً عند توفر السياق
    // لضمان التحقق من الأذونات بشكل صحيح
  }

  // تعطيل إشعارات العودة تلقائياً - تم تعليقه لإتاحة الفرصة للمستخدم لتفعيل الإشعارات
  Future<void> _disableReturnNotificationsAutomatically() async {
    try {
      // تم تعليق هذا الكود لإتاحة الفرصة للمستخدم لتفعيل إشعارات العودة
      // await toggleReturnNotificationsTemporarilyDisabled(true);
      debugPrint('تم تعطيل دالة تعطيل إشعارات العودة تلقائياً عند بدء التطبيق');
    } catch (e) {
      debugPrint('خطأ في تعطيل إشعارات العودة تلقائياً: $e');
    }
  }

  // تحميل إعدادات المسبحة من التخزين المحلي
  Future<void> _loadTasbihSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _dhikrCount = prefs.getInt('dhikrCount') ?? 0;
      _targetCount = prefs.getInt('targetCount') ?? 33;
      _sessionCount = prefs.getInt('sessionCount') ?? 0;
      _totalCount = prefs.getInt('totalCount') ?? 0;
      _vibrationEnabled = prefs.getBool('vibrationEnabled') ?? true;

      // إنشاء مصفوفة الخرز بناءً على العدد المستهدف
      _ensureBeadPositionsCapacity(_targetCount);

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحميل إعدادات المسبحة: $e');
    }
  }

  // حفظ إعدادات المسبحة في التخزين المحلي - تم تحسين الأداء
  Future<void> _saveTasbihSettings() async {
    try {
      // تحسين: استخدام عملية واحدة للحصول على SharedPreferences
      final prefs = await SharedPreferences.getInstance();

      // تحسين: استخدام عمليات متوازية لتحسين الأداء
      await Future.wait([
        prefs.setInt('dhikrCount', _dhikrCount),
        prefs.setInt('targetCount', _targetCount),
        prefs.setInt('sessionCount', _sessionCount),
        prefs.setInt('totalCount', _totalCount),
        prefs.setBool('vibrationEnabled', _vibrationEnabled),
        prefs.setInt('selectedDhikrId', _selectedDhikr.id),
      ]);
    } catch (e) {
      debugPrint('خطأ في حفظ إعدادات المسبحة: $e');
    }
  }

  // الحصول على قائمة الأذكار الافتراضية (بدون ترجمة أو نطق لاتيني)
  List<DhikrModel> _getDefaultDhikrs() {
    return [
      DhikrModel(
          id: 1,
          name: 'سبحان الله',
          count: 33,
          isDefault: true,
          arabicText: 'سُبْحَانَ اللهِ',
          transliteration: '',
          translation: ''),
      DhikrModel(
          id: 2,
          name: 'الحمد لله',
          count: 33,
          isDefault: true,
          arabicText: 'الْحَمْدُ لِلَّهِ',
          transliteration: '',
          translation: ''),
      DhikrModel(
          id: 3,
          name: 'الله أكبر',
          count: 33,
          isDefault: true,
          arabicText: 'اللهُ أَكْبَرُ',
          transliteration: '',
          translation: ''),
      DhikrModel(
          id: 4,
          name: 'لا إله إلا الله',
          count: 33,
          isDefault: true,
          arabicText: 'لَا إِلَهَ إِلَّا اللهُ',
          transliteration: '',
          translation: ''),
      DhikrModel(
          id: 5,
          name: 'استغفر الله',
          count: 33,
          isDefault: true,
          arabicText: 'أَسْتَغْفِرُ اللهَ',
          transliteration: '',
          translation: ''),
      DhikrModel(
          id: 6,
          name: 'سبحان الله وبحمده',
          count: 33,
          isDefault: true,
          arabicText: 'سُبْحَانَ اللهِ وَبِحَمْدِهِ',
          transliteration: '',
          translation: ''),
      DhikrModel(
          id: 7,
          name: 'لا حول ولا قوة إلا بالله',
          count: 33,
          isDefault: true,
          arabicText: 'لَا حَوْلَ وَلَا قُوَّةَ إِلَّا بِاللهِ',
          transliteration: '',
          translation: ''),
      DhikrModel(
          id: 8,
          name: 'اللهم صل على محمد',
          count: 33,
          isDefault: true,
          arabicText: 'اللَّهُمَّ صَلِّ عَلَى مُحَمَّدٍ وَعَلَى آلِ مُحَمَّدٍ',
          transliteration: '',
          translation: ''),
    ];
  }

  // تحميل الأذكار من قاعدة البيانات
  Future<void> loadDhikrs() async {
    try {
      final dbHelper = DatabaseHelper();
      final dhikrsData = await dbHelper.getDhikrs();

      debugPrint('تم تحميل ${dhikrsData.length} ذكر من قاعدة البيانات');

      // طباعة معلومات عن الأذكار المحملة
      for (var dhikr in dhikrsData) {
        debugPrint(
            'ذكر: ${dhikr['name']}, المعرف: ${dhikr['id']}, العدد: ${dhikr['count']}');
      }

      _availableDhikrs = dhikrsData
          .map((dhikr) => DhikrModel(
                id: dhikr['id'] as int,
                name: dhikr['name'] as String,
                count: dhikr['count'] as int,
                isDefault: dhikr['isDefault'] as bool? ??
                    (dhikr['id'] as int) <=
                        8, // الأذكار الافتراضية هي التي معرفها أقل من أو يساوي 8
                arabicText: dhikr['arabicText'] as String? ?? '',
                // تعيين النطق اللاتيني والترجمة إلى نص فارغ
                transliteration: '',
                translation: '',
              ))
          .toList();

      // طباعة معلومات عن الأذكار بعد التحويل
      for (var dhikr in _availableDhikrs) {
        debugPrint(
            'ذكر محول: ${dhikr.name}, المعرف: ${dhikr.id}, النص العربي: ${dhikr.arabicText}');
      }

      // التحقق من وجود الأذكار الافتراضية
      bool hasAllDefaultDhikrs = true;
      final defaultDhikrs = _getDefaultDhikrs();

      // التحقق من وجود كل ذكر افتراضي
      for (var defaultDhikr in defaultDhikrs) {
        bool found =
            _availableDhikrs.any((dhikr) => dhikr.id == defaultDhikr.id);
        if (!found) {
          hasAllDefaultDhikrs = false;
          break;
        }
      }

      // إذا كانت القائمة فارغة أو لا تحتوي على جميع الأذكار الافتراضية
      if (_availableDhikrs.isEmpty || !hasAllDefaultDhikrs) {
        debugPrint('إضافة الأذكار الافتراضية المفقودة...');

        // الاحتفاظ بالأذكار المخصصة
        final customDhikrs =
            _availableDhikrs.where((dhikr) => !dhikr.isDefault).toList();

        // دمج الأذكار الافتراضية مع الأذكار المخصصة
        _availableDhikrs = [...defaultDhikrs, ...customDhikrs];

        // حفظ القائمة المحدثة في قاعدة البيانات
        await dbHelper.saveDhikrs(_availableDhikrs
            .map((dhikr) => {
                  'id': dhikr.id,
                  'name': dhikr.name,
                  'count': dhikr.count,
                  'isDefault': dhikr.isDefault,
                  'arabicText': dhikr.arabicText,
                  'transliteration': '', // تعيين النطق اللاتيني إلى نص فارغ
                  'translation': '', // تعيين الترجمة إلى نص فارغ
                })
            .toList());

        // طباعة معلومات عن الأذكار بعد التحديث
        debugPrint(
            'تم تحديث قائمة الأذكار. العدد الجديد: ${_availableDhikrs.length}');
        for (var dhikr in _availableDhikrs) {
          debugPrint(
              'ذكر بعد التحديث: ${dhikr.name}, المعرف: ${dhikr.id}, النص العربي: ${dhikr.arabicText}');
        }
      } else {
        // تحديث الأذكار الموجودة لإزالة الترجمة والنطق اللاتيني
        await _updateExistingDhikrsToRemoveTranslation();
      }

      // استرجاع الذكر المحدد من التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      final savedDhikrId = prefs.getInt('selectedDhikrId');

      if (savedDhikrId != null) {
        // البحث عن الذكر في القائمة
        final foundDhikr = _availableDhikrs.firstWhere(
          (dhikr) => dhikr.id == savedDhikrId,
          orElse: () => _availableDhikrs.first,
        );
        _selectedDhikr = foundDhikr;
        _targetCount = foundDhikr.count;
      } else {
        _selectedDhikr = _availableDhikrs.first;
      }

      _ensureBeadPositionsCapacity(_targetCount);
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحميل الأذكار: $e');
    }
  }

  // التأكد من وجود العدد الكافي من الخرزات
  void _ensureBeadPositionsCapacity(int count) {
    if (_beadPositions.length < count) {
      _beadPositions = List.generate(count, (index) {
        if (index < _beadPositions.length) {
          return _beadPositions[index];
        } else {
          return 0.0;
        }
      });
    }
  }

  // زيادة عدد الذكر - تم تحسين الأداء
  Future<void> incrementCount() async {
    final startTime = DateTime.now();
    _dhikrCount++;
    _totalCount++;

    bool completedSession = false;
    if (_dhikrCount >= _targetCount) {
      _dhikrCount = 0;
      _sessionCount++;
      completedSession = true;

      // اهتزاز فقط عند إكمال الدورة
      if (_vibrationEnabled) {
        _performLightVibration();
      }
    }

    // تحريك الخرز - تم تحسين الأداء
    _animateBeadsOptimized();

    // تفعيل تأثير الدوائر
    _isRippleActive = true;

    // استخدام متغير محلي لتجنب تحديث الواجهة مرتين
    bool shouldNotify = true;

    // تأخير إلغاء تنشيط تأثير الدوائر
    Future.delayed(const Duration(milliseconds: 500), () {
      _isRippleActive = false;

      // تحديث الواجهة فقط إذا لم يتم تحديثها بالفعل
      if (shouldNotify) {
        notifyListeners();
        shouldNotify = false;
      }
    });

    // حفظ الإعدادات بشكل غير متزامن
    _saveTasbihSettings();

    // تحديث الإحصائيات بشكل غير متزامن
    _updateStatsAsync(startTime, completedSession);

    // تحديث الواجهة مرة واحدة فقط
    if (shouldNotify) {
      notifyListeners();
      shouldNotify = false;
    }
  }

  // تحديث الإحصائيات بشكل غير متزامن
  void _updateStatsAsync(DateTime startTime, bool completedSession) {
    Future(() async {
      try {
        // الحصول على مزود الإحصائيات
        final statsProvider = TasbihStatsProvider();
        final usageTime = DateTime.now().difference(startTime).inSeconds;
        statsProvider.updateStats(
          count: 1,
          sessionCount: completedSession ? 1 : 0,
          dhikr: _selectedDhikr,
          usageTime: usageTime,
        );

        // التحقق مما إذا كانت إشعارات العودة معطلة مؤقتاً
        final isDisabled = await areReturnNotificationsTemporarilyDisabled();
        if (!isDisabled) {
          // جدولة إشعارات العودة في النظام الجديد - بشكل غير متزامن
          _notificationManager.scheduleReturnReminderNotification();
        }
      } catch (e) {
        debugPrint('خطأ في تحديث إحصائيات المسبحة: $e');
      }
    });
  }

  // اهتزاز خفيف عند إكمال دورة كاملة
  Future<void> _performLightVibration() async {
    if (!_vibrationEnabled) return;

    try {
      // التحقق من دعم الجهاز للاهتزاز
      bool? hasVibrator = await Vibration.hasVibrator();
      bool? hasAmplitudeControl = await Vibration.hasAmplitudeControl();

      if (hasVibrator == true) {
        if (hasAmplitudeControl == true) {
          // استخدام نمط اهتزاز خفيف جداً مع التحكم في الشدة
          Vibration.vibrate(
            duration: 40, // اهتزاز خفيف لمدة 40 مللي ثانية فقط
            amplitude: 60, // شدة منخفضة جداً
          );
        } else {
          // استخدام اهتزاز قصير جداً للأجهزة التي لا تدعم التحكم في الشدة
          Vibration.vibrate(duration: 30);
        }

        // اهتزاز ثاني بعد فترة قصيرة لتحسين التجربة
        await Future.delayed(const Duration(milliseconds: 100));
        if (hasAmplitudeControl == true) {
          Vibration.vibrate(
            duration: 30,
            amplitude: 40, // شدة أقل للاهتزاز الثاني
          );
        } else {
          Vibration.vibrate(duration: 20);
        }
      } else {
        // استخدام HapticFeedback كبديل خفيف
        HapticFeedback.lightImpact();
        await Future.delayed(const Duration(milliseconds: 100));
        HapticFeedback.selectionClick();
      }
    } catch (e) {
      // في حالة حدوث خطأ، استخدم HapticFeedback كبديل
      HapticFeedback.lightImpact();
    }
  }

  // تحريك الخرز - نسخة محسنة للأداء
  void _animateBeadsOptimized() {
    // تحسين: استخدام مصفوفة جديدة بدلاً من تعديل المصفوفة الحالية مباشرة
    final int beadCount = _beadPositions.length;
    final List<double> newPositions = List.filled(beadCount, 0.0);

    // ضبط وضع الخرز المكتملة فقط
    for (int i = 0; i < _dhikrCount && i < beadCount; i++) {
      newPositions[i] = 1.0;
    }

    // تحسين: تحديد الخرزة الجديدة بطريقة أكثر كفاءة
    int newActiveBead = -1;
    if (_dhikrCount > 0 && _dhikrCount <= beadCount) {
      newActiveBead = _dhikrCount - 1;
    }

    // تحديث جميع الخرزات ما عدا الخرزة الجديدة
    for (int i = 0; i < beadCount; i++) {
      if (i != newActiveBead) {
        _beadPositions[i] = newPositions[i];
      }
    }

    // تحريك الخرزة الجديدة بتأخير قصير
    if (newActiveBead >= 0 && newActiveBead < beadCount) {
      // تحسين: تقليل وقت التأخير
      Future.delayed(const Duration(milliseconds: 30), () {
        if (newActiveBead < _beadPositions.length) {
          _beadPositions[newActiveBead] = 1.0;
          notifyListeners();
        }
      });
    }

    // إذا كانت الدورة قد اكتملت وتم إعادة ضبط العداد
    if (_dhikrCount == 0) {
      // تحسين: تقليل وقت التأخير
      Future.delayed(const Duration(milliseconds: 300), () {
        for (int i = 0; i < beadCount; i++) {
          _beadPositions[i] = 0.0;
        }
        notifyListeners();
      });
    }
  }

  // إعادة ضبط العدادات (جميع البيانات)
  void resetCounter() {
    if (_vibrationEnabled) {
      HapticFeedback.heavyImpact();
    }

    _dhikrCount = 0;
    _sessionCount = 0;
    _totalCount = 0; // إعادة ضبط العدد الإجمالي أيضًا
    _beadPositions = List.generate(_targetCount, (index) => 0.0);

    _saveTasbihSettings();
    notifyListeners();
  }

  // إعادة ضبط العداد الحالي فقط
  void resetCurrentCount() {
    if (_vibrationEnabled) {
      HapticFeedback.mediumImpact();
    }

    _dhikrCount = 0;
    _beadPositions = List.generate(_targetCount, (index) => 0.0);

    _saveTasbihSettings();
    notifyListeners();
  }

  // تحديث الذكر المحدد
  void updateSelectedDhikr(DhikrModel dhikr) {
    _selectedDhikr = dhikr;
    _targetCount = dhikr.count;
    _dhikrCount = 0;
    _ensureBeadPositionsCapacity(_targetCount);
    _beadPositions = List.generate(_targetCount, (index) => 0.0);

    _saveTasbihSettings();
    notifyListeners();
  }

  // تحديث العدد المستهدف مع رسوم متحركة محسنة
  void updateTargetCount(int count) {
    // حفظ العدد القديم للاستخدام في الرسوم المتحركة
    final oldCount = _targetCount;

    // تحديث العدد المستهدف
    _targetCount = count;
    _dhikrCount = 0;

    // التأكد من وجود مساحة كافية للخرز الجديدة
    _ensureBeadPositionsCapacity(count);

    // إذا كان العدد الجديد أكبر من العدد القديم، نضيف رسوم متحركة للخرز الجديدة
    if (count > oldCount) {
      // نحتفظ بمواضع الخرز القديمة ونضيف مواضع جديدة للخرز الإضافية
      final List<double> newPositions = List.generate(count, (index) {
        if (index < oldCount) {
          // الخرز القديمة تحتفظ بمواضعها
          return _beadPositions[index];
        } else {
          // الخرز الجديدة تبدأ بقيمة 0
          return 0.0;
        }
      });

      _beadPositions = newPositions;

      // إخطار المستمعين بالتغيير الأولي
      notifyListeners();

      // إضافة تأثير ظهور تدريجي للخرز الجديدة
      for (int i = oldCount; i < count; i++) {
        final delay = (i - oldCount) * 20; // تأخير متزايد لكل خرزة
        Future.delayed(Duration(milliseconds: delay), () {
          // لا نقوم بتحديث الخرز إذا تم تغيير العدد المستهدف مرة أخرى
          if (_targetCount == count) {
            _beadPositions[i] = 0.0; // تأكيد أن الخرزة غير نشطة
            notifyListeners();
          }
        });
      }
    } else {
      // إذا كان العدد الجديد أصغر من أو يساوي العدد القديم
      // نعيد تعيين جميع مواضع الخرز
      _beadPositions = List.generate(count, (index) => 0.0);

      // إخطار المستمعين بالتغيير
      notifyListeners();
    }

    // حفظ الإعدادات
    _saveTasbihSettings();
  }

  // تحديث وضع الاهتزاز
  void toggleVibration(bool isEnabled) {
    _vibrationEnabled = isEnabled;
    _saveTasbihSettings();
    notifyListeners();
  }

  // إضافة الأذكار الافتراضية
  Future<void> addDefaultDhikrs() async {
    try {
      debugPrint('جاري إضافة الأذكار الافتراضية...');

      // الحصول على قائمة الأذكار الافتراضية
      final defaultDhikrs = _getDefaultDhikrs();

      // إضافة الأذكار الافتراضية إلى القائمة
      _availableDhikrs = defaultDhikrs;

      // حفظ الأذكار في قاعدة البيانات
      final dbHelper = DatabaseHelper();
      await dbHelper.saveDhikrs(_availableDhikrs
          .map((dhikr) => {
                'id': dhikr.id,
                'name': dhikr.name,
                'count': dhikr.count,
                'isDefault': dhikr.isDefault,
                'arabicText': dhikr.arabicText,
                'transliteration': '', // تعيين النطق اللاتيني إلى نص فارغ
                'translation': '', // تعيين الترجمة إلى نص فارغ
              })
          .toList());

      // تحديث الذكر المحدد
      _selectedDhikr = _availableDhikrs.first;
      _targetCount = _selectedDhikr.count;
      _ensureBeadPositionsCapacity(_targetCount);

      debugPrint('تم إضافة ${_availableDhikrs.length} ذكر افتراضي');
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في إضافة الأذكار الافتراضية: $e');
    }
  }

  // إعادة تعيين الأذكار الافتراضية
  Future<void> resetDefaultDhikrs() async {
    try {
      debugPrint('جاري إعادة تعيين الأذكار الافتراضية...');

      // الحصول على قائمة الأذكار الافتراضية
      final defaultDhikrs = _getDefaultDhikrs();

      // الحصول على الأذكار المخصصة الحالية
      final customDhikrs =
          _availableDhikrs.where((dhikr) => !dhikr.isDefault).toList();

      // دمج الأذكار الافتراضية مع الأذكار المخصصة
      _availableDhikrs = [...defaultDhikrs, ...customDhikrs];

      // حفظ الأذكار في قاعدة البيانات
      final dbHelper = DatabaseHelper();
      await dbHelper.saveDhikrs(_availableDhikrs
          .map((dhikr) => {
                'id': dhikr.id,
                'name': dhikr.name,
                'count': dhikr.count,
                'isDefault': dhikr.isDefault,
                'arabicText': dhikr.arabicText,
                'transliteration': '', // تعيين النطق اللاتيني إلى نص فارغ
                'translation': '', // تعيين الترجمة إلى نص فارغ
              })
          .toList());

      debugPrint('تم إعادة تعيين ${defaultDhikrs.length} ذكر افتراضي');
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في إعادة تعيين الأذكار الافتراضية: $e');
    }
  }

  // إضافة ذكر جديد
  Future<bool> addNewDhikr(String name, int count) async {
    if (name.isEmpty) return false;

    // التأكد من أن العدد صحيح وموجب
    final validCount = count > 0 ? count : 33;
    debugPrint('إضافة ذكر جديد: $name, العدد: $validCount');

    try {
      // إنشاء معرف فريد للذكر الجديد
      final id = DateTime.now().millisecondsSinceEpoch;

      // إضافة الذكر إلى القائمة المحلية
      // تعيين isDefault إلى false للإشارة إلى أنه ذكر مخصص أضافه المستخدم
      final newDhikr = DhikrModel(
        id: id,
        name: name,
        count: validCount,
        isDefault: false,
        arabicText: name, // استخدام الاسم كنص عربي افتراضي
        transliteration: '', // تعيين النطق اللاتيني إلى نص فارغ
        translation: '', // تعيين الترجمة إلى نص فارغ
      );
      _availableDhikrs.add(newDhikr);

      debugPrint('تمت إضافة ذكر جديد: $name, المعرف: $id');

      // حفظ القائمة المحدثة في التخزين المحلي
      final dbHelper = DatabaseHelper();
      await dbHelper.saveDhikrs(_availableDhikrs
          .map((dhikr) => {
                'id': dhikr.id,
                'name': dhikr.name,
                'count': dhikr.count,
                'isDefault': dhikr.isDefault,
                'arabicText': dhikr.arabicText,
                'transliteration': '', // تعيين النطق اللاتيني إلى نص فارغ
                'translation': '', // تعيين الترجمة إلى نص فارغ
              })
          .toList());

      // تحديث الذكر المحدد
      updateSelectedDhikr(newDhikr);

      debugPrint('تم إضافة ذكر جديد: $name');
      return true;
    } catch (e) {
      debugPrint('خطأ في إضافة الذكر: $e');
      return false;
    }
  }

  // تحديث هدف الذكر
  Future<bool> updateDhikrTarget(int dhikrId, int newCount) async {
    try {
      // تحديث في القائمة المحلية
      final index = _availableDhikrs.indexWhere((dhikr) => dhikr.id == dhikrId);
      if (index != -1) {
        // تحديث العدد المستهدف للذكر (سواء كان افتراضيًا أو مخصصًا)
        _availableDhikrs[index] =
            _availableDhikrs[index].copyWith(count: newCount);

        // حفظ القائمة المحدثة في التخزين المحلي
        final dbHelper = DatabaseHelper();
        await dbHelper.saveDhikrs(_availableDhikrs
            .map((dhikr) => {
                  'id': dhikr.id,
                  'name': dhikr.name,
                  'count': dhikr.count,
                  'isDefault': dhikr.isDefault,
                  'arabicText': dhikr.arabicText,
                  'transliteration': '', // تعيين النطق اللاتيني إلى نص فارغ
                  'translation': '', // تعيين الترجمة إلى نص فارغ
                })
            .toList());

        // تحديث الذكر المحدد إذا كان هو المستهدف
        if (_selectedDhikr.id == dhikrId) {
          _selectedDhikr = _availableDhikrs[index];
          updateTargetCount(newCount);
        }
      }

      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث هدف الذكر: $e');
      return false;
    }
  }

  // تحديث اسم الذكر
  Future<bool> updateDhikrName(int dhikrId, String newName) async {
    try {
      // تحديث في القائمة المحلية
      final index = _availableDhikrs.indexWhere((dhikr) => dhikr.id == dhikrId);
      if (index != -1) {
        // التحقق مما إذا كان الذكر افتراضيًا أم لا
        final dhikr = _availableDhikrs[index];

        // يمكن تحديث الأذكار المخصصة فقط (غير الافتراضية)
        if (dhikr.isDefault) {
          // لا يمكن تعديل الأذكار الافتراضية
          debugPrint('لا يمكن تعديل الأذكار الافتراضية');
          return false;
        }

        _availableDhikrs[index] =
            _availableDhikrs[index].copyWith(name: newName);

        // حفظ القائمة المحدثة في التخزين المحلي
        final dbHelper = DatabaseHelper();
        await dbHelper.saveDhikrs(_availableDhikrs
            .map((dhikr) => {
                  'id': dhikr.id,
                  'name': dhikr.name,
                  'count': dhikr.count,
                  'isDefault': dhikr.isDefault,
                  'arabicText': dhikr.arabicText,
                  'transliteration': '', // تعيين النطق اللاتيني إلى نص فارغ
                  'translation': '', // تعيين الترجمة إلى نص فارغ
                })
            .toList());

        // تحديث الذكر المحدد إذا كان هو المستهدف
        if (_selectedDhikr.id == dhikrId) {
          _selectedDhikr = _availableDhikrs[index];
        }
      }

      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث اسم الذكر: $e');
      return false;
    }
  }

  // تفعيل أو تعطيل إشعارات العودة
  Future<void> toggleReturnNotifications(bool enabled) async {
    // تفعيل/تعطيل الإشعارات في النظام الجديد
    if (enabled) {
      // جدولة إشعارات العودة في النظام الجديد
      await _notificationManager.scheduleReturnReminderNotification();
    }

    // حفظ الإعدادات في التخزين المحلي
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('notifications_enabled', enabled);

    notifyListeners();
  }

  // تحديث الأذكار الموجودة لإزالة الترجمة والنطق اللاتيني
  Future<void> _updateExistingDhikrsToRemoveTranslation() async {
    try {
      debugPrint(
          'جاري تحديث الأذكار الموجودة لإزالة الترجمة والنطق اللاتيني...');

      // تحديث الأذكار في الذاكرة
      for (int i = 0; i < _availableDhikrs.length; i++) {
        final dhikr = _availableDhikrs[i];
        _availableDhikrs[i] = DhikrModel(
          id: dhikr.id,
          name: dhikr.name,
          count: dhikr.count,
          isDefault: dhikr.isDefault,
          arabicText: dhikr.arabicText,
          transliteration: '', // تعيين النطق اللاتيني إلى نص فارغ
          translation: '', // تعيين الترجمة إلى نص فارغ
        );
      }

      // حفظ الأذكار المحدثة في قاعدة البيانات
      final dbHelper = DatabaseHelper();
      await dbHelper.saveDhikrs(_availableDhikrs
          .map((dhikr) => {
                'id': dhikr.id,
                'name': dhikr.name,
                'count': dhikr.count,
                'isDefault': dhikr.isDefault,
                'arabicText': dhikr.arabicText,
                'transliteration': '', // تعيين النطق اللاتيني إلى نص فارغ
                'translation': '', // تعيين الترجمة إلى نص فارغ
              })
          .toList());

      debugPrint(
          'تم تحديث ${_availableDhikrs.length} ذكر لإزالة الترجمة والنطق اللاتيني');
    } catch (e) {
      debugPrint('خطأ في تحديث الأذكار لإزالة الترجمة والنطق اللاتيني: $e');
    }
  }

  // الحصول على حالة تفعيل إشعارات العودة
  Future<bool> getReturnNotificationsEnabled() async {
    // استخدام التخزين المحلي بدلاً من النظام القديم
    final prefs = await SharedPreferences.getInstance();

    // التحقق مما إذا كانت إشعارات العودة معطلة مؤقتاً
    final tempDisabled =
        prefs.getBool('return_notifications_temp_disabled') ?? false;
    if (tempDisabled) {
      return false;
    }

    return prefs.getBool('notifications_enabled') ?? false;
  }

  // التحقق مما إذا كانت إشعارات العودة معطلة مؤقتاً
  Future<bool> areReturnNotificationsTemporarilyDisabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('return_notifications_temp_disabled') ?? false;
  }

  // تفعيل أو تعطيل إشعارات العودة مؤقتاً
  Future<void> toggleReturnNotificationsTemporarilyDisabled(
      bool disabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('return_notifications_temp_disabled', disabled);

    notifyListeners();
  }

  // تعيين الوقت المفضل للإشعارات
  Future<void> setPreferredNotificationTime(int hour) async {
    // التحقق من صحة الساعة
    if (hour < 0 || hour > 23) {
      hour = 8; // ساعة افتراضية
    }

    // حفظ الوقت المفضل في التخزين المحلي
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('preferred_notification_hour', hour);

    notifyListeners();
  }

  // الحصول على الوقت المفضل للإشعارات
  Future<int> getPreferredNotificationTime() async {
    // استخدام التخزين المحلي بدلاً من النظام القديم
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt('preferred_notification_hour') ??
        8; // 8 صباحاً كوقت افتراضي
  }

  // جدولة إشعارات العودة
  Future<bool> scheduleReturnNotifications({BuildContext? context}) async {
    try {
      // التحقق مما إذا كانت إشعارات العودة معطلة مؤقتاً
      final isDisabled = await areReturnNotificationsTemporarilyDisabled();
      if (isDisabled) {
        debugPrint('إشعارات العودة معطلة مؤقتاً، لن يتم جدولة إشعارات العودة');
        return false;
      }

      // تم تعليق التحقق من أذونات الإشعارات
      /*
      // التحقق من أذونات الإشعارات بدون استخدام السياق
      // نستخدم طريقة أبسط لتجنب مشاكل استخدام BuildContext عبر فجوات غير متزامنة
      final hasPermission =
          await _notificationManager.requestNotificationPermissions();
      if (!hasPermission) {
        debugPrint('لم يتم منح أذونات الإشعارات، لا يمكن جدولة إشعارات العودة');
        return false;
      }
      */

      // جدولة إشعارات العودة في النظام الجديد
      final success =
          await _notificationManager.scheduleReturnReminderNotification();
      return success;
    } catch (e) {
      debugPrint('خطأ في جدولة إشعارات العودة: $e');
      return false;
    }
  }

  // التحقق من أذونات الإشعارات
  Future<bool> checkNotificationPermissions(BuildContext context) async {
    try {
      final permissionManager = PermissionManager();
      return await permissionManager
          .checkNotificationPermissionsBeforeScheduling(context);
    } catch (e) {
      debugPrint('خطأ في التحقق من أذونات الإشعارات: $e');
      return false;
    }
  }

  // طلب أذونات الإشعارات
  Future<bool> requestNotificationPermissions() async {
    try {
      return await _notificationManager.requestNotificationPermissions();
    } catch (e) {
      debugPrint('خطأ في طلب أذونات الإشعارات: $e');
      return false;
    }
  }

  // إرسال إشعار تأكيدي لتفعيل إشعارات العودة
  Future<void> sendReturnNotificationConfirmation() async {
    // إرسال إشعار تأكيدي باستخدام النظام الجديد
    await _notificationManager.scheduleReturnReminderNotification();

    // تفعيل الإشعارات في الإعدادات
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('notifications_enabled', true);
  }

  // الحصول على ذكر بواسطة المعرف
  DhikrModel? getDhikrById(int dhikrId) {
    try {
      for (var dhikr in _availableDhikrs) {
        if (dhikr.id == dhikrId) {
          return dhikr;
        }
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على الذكر: $e');
      return null;
    }
  }
}
