// إنشاء ملف لقسم المظهر

import 'package:flutter/material.dart';
import '../../../providers/theme_provider.dart';
import '../widgets/setting_item.dart';

class AppearanceSection extends StatelessWidget {
  final ThemeProvider themeProvider;
  final Color settingsColor;
  final Function(Widget, {double delay}) buildSlideAnimation;
  final Function(BuildContext, String, IconData) buildSectionTitle;

  const AppearanceSection({
    super.key,
    required this.themeProvider,
    required this.settingsColor,
    required this.buildSlideAnimation,
    required this.buildSectionTitle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end, // تغيير المحاذاة إلى اليمين
      textDirection:
          TextDirection.rtl, // تحديد اتجاه العمود من اليمين إلى اليسار
      children: [
        buildSectionTitle(context, 'المظهر', Icons.palette_outlined),

        // إعداد السمة الداكنة
        buildSlideAnimation(
          SettingItem(
            title: 'الوضع الداكن',
            icon: Icons.dark_mode_outlined,
            iconColor: settingsColor,
            trailing: Switch(
              value: themeProvider.isDarkMode,
              onChanged: (value) => themeProvider.toggleDarkMode(),
              activeColor: settingsColor,
            ),
          ),
          delay: 0.1,
        ),

        // إعداد نوع الخط
        buildSlideAnimation(
          SettingItem(
            title: 'نوع الخط',
            icon: Icons.font_download_outlined,
            iconColor: settingsColor,
            subtitle: 'الخط الحالي: ${themeProvider.currentFontName}',
            onTap: () => _selectFont(context),
          ),
          delay: 0.2,
        ),
      ],
    );
  }

  // دالة اختيار الخط
  void _selectFont(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        title: Text(
          'اختيار نوع الخط',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: settingsColor,
            fontSize: 20,
          ),
        ),
        content: _buildFontSelectionContent(context),
        actions: [
          Center(
            child: TextButton(
              onPressed: () => Navigator.pop(context),
              style: TextButton.styleFrom(
                backgroundColor: settingsColor.withAlpha(26), // 0.1 * 255 = ~26
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'إلغاء',
                style: TextStyle(
                  color: settingsColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: BorderSide(
            color: settingsColor.withAlpha(51), // 0.2 * 255 = ~51
            width: 1,
          ),
        ),
        elevation: 10,
      ),
    );
  }

  // الحصول على نمط الخط للعرض
  TextStyle _getFontStyle(int index) {
    if (index == 0) {
      return const TextStyle(); // خط النظام
    }

    final fontName = ThemeProvider.availableFonts[index];
    String fontFamily;

    switch (fontName) {
      case 'Cairo':
        fontFamily = 'Cairo';
        break;
      case 'Amiri':
        fontFamily = 'Amiri';
        break;
      case 'Almarai':
        fontFamily = 'Almarai';
        break;
      case 'Tajawal':
        fontFamily = 'Tajawal';
        break;
      case 'Scheherazade New':
        fontFamily = 'ScheherazadeNew';
        break;
      case 'Noto Kufi Arabic':
        fontFamily = 'NotoKufiArabic';
        break;
      case 'Noto Naskh Arabic':
        fontFamily = 'NotoNaskhArabic';
        break;
      case 'Changa':
        fontFamily = 'Changa';
        break;
      case 'El Messiri':
        fontFamily = 'ElMessiri';
        break;
      default:
        fontFamily = 'Tajawal';
    }

    return TextStyle(fontFamily: fontFamily);
  }

  // بناء محتوى اختيار الخط
  Widget _buildFontSelectionContent(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      width: double.maxFinite,
      height: 400, // ارتفاع ثابت للقائمة
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: isDarkMode ? const Color(0xFF252525) : Colors.grey[50],
      ),
      child: Column(
        children: [
          // شريط البحث المزخرف (للمظهر فقط)
          Container(
            margin: const EdgeInsets.all(12),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: isDarkMode ? const Color(0xFF2C2C2C) : Colors.white,
              border: Border.all(
                color: settingsColor.withAlpha(40),
                width: 1,
              ),
            ),
            child: Row(
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
              children: [
                Icon(
                  Icons.search,
                  size: 20,
                  color: settingsColor.withAlpha(150),
                ),
                const SizedBox(width: 8),
                Text(
                  'اختر الخط المناسب',
                  style: TextStyle(
                    color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    fontSize: 14,
                  ),
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                  textAlign: TextAlign.right, // محاذاة النص إلى اليمين
                ),
              ],
            ),
          ),

          // قائمة الخطوط
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              itemCount: ThemeProvider.availableFonts.length,
              itemBuilder: (context, index) {
                final fontName = ThemeProvider.availableFonts[index];
                final isSelected = themeProvider.fontIndex == index;

                return Container(
                  margin:
                      const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: isSelected
                        ? settingsColor.withAlpha(40)
                        : (isDarkMode ? const Color(0xFF2A2A2A) : Colors.white),
                    border: Border.all(
                      color: isSelected
                          ? settingsColor
                          : settingsColor.withAlpha(20),
                      width: isSelected ? 1.5 : 1,
                    ),
                    boxShadow: isSelected
                        ? [
                            BoxShadow(
                              color: settingsColor.withAlpha(30),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            )
                          ]
                        : null,
                  ),
                  child: InkWell(
                    onTap: () {
                      Navigator.pop(context);
                      themeProvider.setFontType(index);
                    },
                    borderRadius: BorderRadius.circular(12),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          vertical: 12, horizontal: 16),
                      child: Row(
                        textDirection: TextDirection
                            .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                        children: [
                          // أيقونة الاختيار
                          Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: isSelected
                                  ? settingsColor
                                  : (isDarkMode
                                      ? Colors.grey[800]
                                      : Colors.grey[200]),
                              border: Border.all(
                                color: isSelected
                                    ? settingsColor
                                    : (isDarkMode
                                        ? Colors.grey[700]!
                                        : Colors.grey[300]!),
                                width: 1.5,
                              ),
                            ),
                            child: isSelected
                                ? const Icon(
                                    Icons.check,
                                    size: 16,
                                    color: Colors.white,
                                  )
                                : null,
                          ),

                          const SizedBox(width: 16),

                          // اسم الخط ونموذج
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              textDirection: TextDirection
                                  .rtl, // تحديد اتجاه العمود من اليمين إلى اليسار
                              children: [
                                // اسم الخط
                                Text(
                                  fontName,
                                  style: TextStyle(
                                    fontWeight: isSelected
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                                    color: isSelected
                                        ? settingsColor
                                        : (isDarkMode
                                            ? Colors.white
                                            : Colors.black87),
                                  ),
                                  textDirection: TextDirection
                                      .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                  textAlign:
                                      TextAlign.right, // محاذاة النص إلى اليمين
                                ),

                                // نموذج للخط
                                if (index > 0) // لا نعرض نموذج لخط النظام
                                  Padding(
                                    padding: const EdgeInsets.only(top: 8),
                                    child: Text(
                                      'بسم الله الرحمن الرحيم',
                                      style: TextStyle(
                                        fontFamily:
                                            _getFontStyle(index).fontFamily,
                                        fontSize: 16,
                                        color: isDarkMode
                                            ? Colors.grey[300]
                                            : Colors.grey[700],
                                      ),
                                      textDirection: TextDirection
                                          .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                      textAlign: TextAlign
                                          .right, // محاذاة النص إلى اليمين
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
