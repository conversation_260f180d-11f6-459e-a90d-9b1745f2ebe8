// مكون قائمة الصلوات على النبي بتصميم فاخر

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/dua.dart';
import '../utils/app_colors.dart';
import 'prophet_prayer_item.dart';

class ProphetPrayerList extends StatefulWidget {
  final List<Dua> prayers;
  final Animation<double> animation;
  final TextEditingController? searchController;
  final Function(String)? onSearchChanged;

  const ProphetPrayerList({
    Key? key,
    required this.prayers,
    required this.animation,
    this.searchController,
    this.onSearchChanged,
  }) : super(key: key);

  @override
  State<ProphetPrayerList> createState() => _ProphetPrayerListState();
}

class _ProphetPrayerListState extends State<ProphetPrayerList>
    with SingleTickerProviderStateMixin {
  List<Dua> _filteredPrayers = [];
  bool _isSearching = false;
  late AnimationController _listAnimationController;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _filteredPrayers = widget.prayers;

    // تهيئة محرك الرسوم المتحركة للقائمة
    _listAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // تهيئة متحكم التمرير
    _scrollController = ScrollController();

    // بدء الرسوم المتحركة
    _listAnimationController.forward();

    // إعداد البحث إذا كان متوفراً
    if (widget.searchController != null) {
      widget.searchController!.addListener(_filterPrayers);
    }
  }

  @override
  void dispose() {
    // تحرير موارد محرك الرسوم المتحركة
    _listAnimationController.dispose();

    // تحرير موارد متحكم التمرير
    _scrollController.dispose();

    // إزالة مستمع البحث
    if (widget.searchController != null) {
      widget.searchController!.removeListener(_filterPrayers);
    }
    super.dispose();
  }

  @override
  void didUpdateWidget(ProphetPrayerList oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.prayers != oldWidget.prayers) {
      _filterPrayers();
    }
  }

  void _filterPrayers() {
    if (widget.searchController == null) return;

    final query = widget.searchController!.text.trim();
    setState(() {
      _isSearching = query.isNotEmpty;
      if (query.isEmpty) {
        _filteredPrayers = widget.prayers;
      } else {
        _filteredPrayers = widget.prayers
            .where((prayer) =>
                prayer.text.contains(query) ||
                (prayer.source?.contains(query) ?? false) ||
                (prayer.virtue?.contains(query) ?? false))
            .toList();
      }
    });

    // استدعاء دالة التغيير إذا كانت متوفرة
    if (widget.onSearchChanged != null) {
      widget.onSearchChanged!(query);
    }
  }

  // إنشاء تأثير حركي للعناصر
  Animation<double> _getItemAnimation(int index) {
    // تأخير ظهور كل عنصر عن العنصر السابق
    const double delay = 0.05;
    final double startInterval = index * delay;
    final double endInterval = startInterval + 0.5; // مدة الظهور لكل عنصر

    return Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _listAnimationController,
        curve: Interval(
          startInterval.clamp(0.0, 0.9),
          endInterval.clamp(0.1, 1.0),
          curve: Curves.easeOutCubic,
        ),
      ),
    );
  }

  // إنشاء تأثير حركي للعنوان
  Animation<double> _getHeaderAnimation() {
    return Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _listAnimationController,
        curve: const Interval(0.0, 0.3, curve: Curves.easeOut),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final prophetPrayersColor = AppColors.getProphetPrayersColor(isDarkMode);

    return AnimatedBuilder(
      animation: widget.animation,
      builder: (context, child) {
        return Opacity(
          opacity: widget.animation.value,
          child: Transform.translate(
            offset: Offset(0, 50 * (1 - widget.animation.value)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // حقل البحث إذا كان متوفراً
                if (widget.searchController != null)
                  AnimatedBuilder(
                    animation: _getHeaderAnimation(),
                    builder: (context, child) {
                      return Opacity(
                        opacity: _getHeaderAnimation().value,
                        child: Transform.translate(
                          offset:
                              Offset(0, 20 * (1 - _getHeaderAnimation().value)),
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                            child: Container(
                              decoration: BoxDecoration(
                                color: isDarkMode
                                    ? Colors.grey[850]
                                    : Colors.grey[100],
                                borderRadius: BorderRadius.circular(24),
                                boxShadow: [
                                  BoxShadow(
                                    color: prophetPrayersColor.withAlpha(30),
                                    blurRadius: 15,
                                    spreadRadius: 1,
                                    offset: const Offset(0, 3),
                                  ),
                                ],
                                border: Border.all(
                                  color: prophetPrayersColor.withAlpha(40),
                                  width: 1.5,
                                ),
                              ),
                              child: TextField(
                                controller: widget.searchController,
                                decoration: InputDecoration(
                                  hintText: 'ابحث في الصلوات...',
                                  hintStyle: TextStyle(
                                    color: isDarkMode
                                        ? Colors.grey[400]
                                        : Colors.grey[600],
                                    fontSize: 15,
                                  ),
                                  prefixIcon: Icon(
                                    Icons.search,
                                    color: prophetPrayersColor,
                                    size: 22,
                                  ),
                                  suffixIcon: _isSearching
                                      ? Container(
                                          margin: const EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                            color: isDarkMode
                                                ? Colors.grey[700]
                                                : Colors.grey[200],
                                            shape: BoxShape.circle,
                                          ),
                                          child: IconButton(
                                            icon: const Icon(Icons.clear,
                                                size: 18),
                                            onPressed: () {
                                              widget.searchController!.clear();
                                              HapticFeedback.lightImpact();
                                            },
                                            color: isDarkMode
                                                ? Colors.grey[300]
                                                : Colors.grey[700],
                                            padding: EdgeInsets.zero,
                                            constraints: const BoxConstraints(),
                                            splashRadius: 20,
                                          ),
                                        )
                                      : null,
                                  border: InputBorder.none,
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 16,
                                  ),
                                ),
                                style: TextStyle(
                                  color: isDarkMode
                                      ? Colors.white
                                      : Colors.black87,
                                  fontSize: 16,
                                ),
                                textInputAction: TextInputAction.search,
                                textDirection: TextDirection.rtl,
                                textAlign: TextAlign.right,
                                onSubmitted: (_) {
                                  HapticFeedback.mediumImpact();
                                },
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),

                // عنوان القسم
                AnimatedBuilder(
                  animation: _getHeaderAnimation(),
                  builder: (context, child) {
                    return Opacity(
                      opacity: _getHeaderAnimation().value,
                      child: Transform.translate(
                        offset:
                            Offset(0, 20 * (1 - _getHeaderAnimation().value)),
                        child: Container(
                          margin: const EdgeInsets.fromLTRB(16, 20, 16, 12),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 12),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                prophetPrayersColor.withAlpha(40),
                                prophetPrayersColor.withAlpha(20),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: prophetPrayersColor.withAlpha(20),
                                blurRadius: 8,
                                spreadRadius: 0,
                                offset: const Offset(0, 2),
                              ),
                            ],
                            border: Border.all(
                              color: prophetPrayersColor.withAlpha(50),
                              width: 1.5,
                            ),
                          ),
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: prophetPrayersColor.withAlpha(40),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.format_list_numbered,
                                  color: prophetPrayersColor,
                                  size: 20,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    _isSearching
                                        ? 'نتائج البحث'
                                        : 'جميع الصلوات',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: isDarkMode
                                          ? Colors.white
                                          : Colors.black87,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    '${_filteredPrayers.length} صلاة',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: isDarkMode
                                          ? Colors.grey[400]
                                          : Colors.grey[700],
                                    ),
                                  ),
                                ],
                              ),
                              const Spacer(),
                              if (_isSearching)
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 6,
                                  ),
                                  decoration: BoxDecoration(
                                    color: prophetPrayersColor.withAlpha(30),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.search,
                                        color: prophetPrayersColor,
                                        size: 16,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        'البحث نشط',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: prophetPrayersColor,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),

                // قائمة الصلوات
                if (_filteredPrayers.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _filteredPrayers.length,
                      itemBuilder: (context, index) {
                        final prayer = _filteredPrayers[index];
                        // تطبيق تأثير حركي لكل عنصر
                        return AnimatedBuilder(
                          animation: _getItemAnimation(index),
                          builder: (context, child) {
                            return Opacity(
                              opacity: _getItemAnimation(index).value,
                              child: Transform.translate(
                                offset: Offset(
                                  0,
                                  30 * (1 - _getItemAnimation(index).value),
                                ),
                                child: ProphetPrayerItem(
                                  prayer: prayer,
                                  index: index,
                                  isLast: index == _filteredPrayers.length - 1,
                                ),
                              ),
                            );
                          },
                        );
                      },
                    ),
                  ),

                // رسالة عند عدم وجود نتائج
                if (_filteredPrayers.isEmpty)
                  AnimatedBuilder(
                    animation: _getHeaderAnimation(),
                    builder: (context, child) {
                      return Opacity(
                        opacity: _getHeaderAnimation().value,
                        child: Transform.translate(
                          offset:
                              Offset(0, 20 * (1 - _getHeaderAnimation().value)),
                          child: Container(
                            margin: const EdgeInsets.all(32),
                            padding: const EdgeInsets.all(24),
                            decoration: BoxDecoration(
                              color: isDarkMode
                                  ? Colors.grey[850]!.withAlpha(150)
                                  : Colors.grey[100]!.withAlpha(150),
                              borderRadius: BorderRadius.circular(24),
                              border: Border.all(
                                color: prophetPrayersColor.withAlpha(30),
                                width: 1.5,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: prophetPrayersColor.withAlpha(15),
                                  blurRadius: 10,
                                  spreadRadius: 0,
                                  offset: const Offset(0, 5),
                                ),
                              ],
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: prophetPrayersColor.withAlpha(20),
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    Icons.search_off,
                                    size: 48,
                                    color: prophetPrayersColor,
                                  ),
                                ),
                                const SizedBox(height: 20),
                                Text(
                                  'لا توجد نتائج مطابقة',
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: isDarkMode
                                        ? Colors.white
                                        : Colors.black87,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 12),
                                Text(
                                  'حاول البحث بكلمات أخرى أو تغيير معايير البحث',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: isDarkMode
                                        ? Colors.grey[400]
                                        : Colors.grey[600],
                                    height: 1.5,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 20),
                                ElevatedButton.icon(
                                  onPressed: () {
                                    if (widget.searchController != null) {
                                      widget.searchController!.clear();
                                    }
                                    HapticFeedback.mediumImpact();
                                  },
                                  icon: const Icon(Icons.refresh),
                                  label: const Text('إعادة ضبط البحث'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: prophetPrayersColor,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 20,
                                      vertical: 12,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}
