// بطاقة الدعاء - محسنة لدعم عرض القائمة بطريقة فاخرة

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/dua.dart';
import '../utils/app_colors.dart';
import '../utils/icon_helper.dart'; // إضافة استيراد IconHelper

// تعريف نوع العرض
enum DuaCardDisplayMode {
  grid, // عرض شبكي
  list, // عرض قائمة
}

class DuaCard extends StatefulWidget {
  final DuaCategory category;
  final Function onTap;
  final Animation<double> animation;
  final DuaCardDisplayMode displayMode; // نوع العرض

  const DuaCard({
    Key? key,
    required this.category,
    required this.onTap,
    required this.animation,
    this.displayMode =
        DuaCardDisplayMode.grid, // القيمة الافتراضية هي العرض الشبكي
  }) : super(key: key);

  @override
  State<DuaCard> createState() => _DuaCardState();
}

class _DuaCardState extends State<DuaCard> with SingleTickerProviderStateMixin {
  late AnimationController _hoverController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  bool _isHovered = false;

  late Animation<double> _rotateAnimation;
  late Animation<double> _glowAnimation;
  late Animation<Color?> _colorAnimation;

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(
        parent: _hoverController,
        curve: Curves.easeOutCubic,
      ),
    );

    _elevationAnimation = Tween<double>(begin: 4.0, end: 16.0).animate(
      CurvedAnimation(
        parent: _hoverController,
        curve: Curves.easeOutCubic,
      ),
    );

    _rotateAnimation = Tween<double>(begin: 0.0, end: 0.01).animate(
      CurvedAnimation(
        parent: _hoverController,
        curve: Curves.easeInOutBack,
      ),
    );

    _glowAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _hoverController,
        curve: const Interval(0.0, 0.75, curve: Curves.easeOut),
      ),
    );

    // سيتم تحديد اللون في build
    _colorAnimation = ColorTween(
      begin: Colors.transparent,
      end: Colors.transparent, // سيتم تحديثه في build
    ).animate(_hoverController);
  }

  @override
  void dispose() {
    _hoverController.dispose();
    super.dispose();
  }

  void _onHover(bool isHovered) {
    if (isHovered) {
      _hoverController.forward();
      HapticFeedback.lightImpact();
    } else {
      _hoverController.reverse();
    }
    setState(() {
      _isHovered = isHovered;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final screenSize = MediaQuery.of(context).size;
    final duasColor = AppColors.getDuasColor(isDarkMode);

    // تحديث لون الأنيميشن
    _colorAnimation = ColorTween(
      begin: Colors.transparent,
      end: duasColor.withAlpha(100),
    ).animate(_hoverController);

    // تحويل اسم الأيقونة إلى أيقونة باستخدام IconHelper
    IconData iconData;
    try {
      // استخدام IconHelper للحصول على الأيقونة المناسبة
      iconData = IconHelper.getIconForCategory(widget.category.name,
          iconName: widget.category.iconName);
    } catch (e) {
      // استخدام أيقونة افتراضية إذا لم يتم العثور على الأيقونة
      iconData = Icons.auto_awesome;
      debugPrint(
          'خطأ في تحويل اسم الأيقونة: ${widget.category.iconName}، الخطأ: $e');
    }

    return AnimatedBuilder(
      animation: widget.animation,
      builder: (context, child) {
        return Opacity(
          opacity: widget.animation.value,
          child: Transform.translate(
            offset: Offset(0, 50 * (1 - widget.animation.value)),
            child: child,
          ),
        );
      },
      child: MouseRegion(
        onEnter: (_) => _onHover(true),
        onExit: (_) => _onHover(false),
        child: GestureDetector(
          onTap: () {
            HapticFeedback.mediumImpact();
            widget.onTap();
          },
          child: AnimatedBuilder(
            animation: _hoverController,
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: Transform.rotate(
                  angle: _rotateAnimation.value,
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24),
                      boxShadow: [
                        // ظل أساسي
                        BoxShadow(
                          color: duasColor.withAlpha(30),
                          blurRadius: _elevationAnimation.value,
                          spreadRadius: 1,
                          offset: Offset(0, _elevationAnimation.value / 2),
                        ),
                        // ظل توهج عند التحويم
                        BoxShadow(
                          color: duasColor
                              .withAlpha((30 * _glowAnimation.value).toInt()),
                          blurRadius: 20 * _glowAnimation.value,
                          spreadRadius: 2 * _glowAnimation.value,
                          offset: const Offset(0, 0),
                        ),
                      ],
                    ),
                    child: Card(
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(24),
                        side: BorderSide(
                          color: _colorAnimation.value ?? Colors.transparent,
                          width: 1.5,
                        ),
                      ),
                      color: isDarkMode
                          ? Color.lerp(
                              Colors.grey[900],
                              duasColor.withAlpha(30),
                              _hoverController.value * 0.5,
                            )
                          : Color.lerp(
                              Colors.white,
                              duasColor.withAlpha(15),
                              _hoverController.value * 0.7,
                            ),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        // استخدام تصميم مختلف بناءً على نوع العرض
                        child: widget.displayMode == DuaCardDisplayMode.list
                            ? _buildListViewLayout(context, isDarkMode,
                                screenSize, duasColor, iconData)
                            : _buildGridViewLayout(context, isDarkMode,
                                screenSize, duasColor, iconData),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  // تصميم عرض القائمة - فاخر وسلس
  Widget _buildListViewLayout(BuildContext context, bool isDarkMode,
      Size screenSize, Color duasColor, IconData iconData) {
    return Row(
      textDirection: TextDirection.rtl, // اتجاه من اليمين إلى اليسار
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // أيقونة الفئة مع تأثير توهج - محسنة للعرض الأفقي
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                duasColor.withAlpha(50),
                duasColor.withAlpha(100),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: duasColor.withAlpha(50),
                blurRadius: 10,
                spreadRadius: 0,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Icon(
            iconData,
            color: duasColor,
            size: 30,
          ),
        ),
        const SizedBox(width: 16),

        // معلومات الفئة
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            textDirection: TextDirection.rtl,
            children: [
              // عنوان الفئة
              ShaderMask(
                shaderCallback: (bounds) {
                  return LinearGradient(
                    colors: [
                      isDarkMode ? Colors.white : Colors.black87,
                      duasColor,
                    ],
                    stops: const [0.7, 1.0],
                    begin: Alignment.centerRight,
                    end: Alignment.centerLeft,
                  ).createShader(bounds);
                },
                child: Text(
                  widget.category.name,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white, // سيتم تجاهل هذا اللون بسبب الشادر
                  ),
                  textDirection: TextDirection.rtl,
                  textAlign: TextAlign.right,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(height: 8),

              // وصف الفئة
              Text(
                widget.category.description,
                style: TextStyle(
                  fontSize: 14,
                  color: isDarkMode ? Colors.grey[300] : Colors.grey[800],
                ),
                textDirection: TextDirection.rtl,
                textAlign: TextAlign.right,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 8),

              // معلومات إضافية
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                textDirection: TextDirection.rtl,
                children: [
                  // عدد الأدعية
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: duasColor.withAlpha(30),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      textDirection: TextDirection.rtl,
                      children: [
                        Icon(
                          Icons.format_list_numbered,
                          size: 14,
                          color: duasColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${widget.category.items.length} أدعية',
                          style: TextStyle(
                            fontSize: 12,
                            color: duasColor,
                            fontWeight: FontWeight.bold,
                          ),
                          textDirection: TextDirection.rtl,
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(width: 8),

                  // مؤشر الأقسام الفرعية إذا وجدت
                  if (widget.category.hasSubcategories)
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: duasColor.withAlpha(15),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        textDirection: TextDirection.rtl,
                        children: [
                          Icon(
                            Icons.account_tree_outlined,
                            size: 14,
                            color: duasColor,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'أقسام فرعية',
                            style: TextStyle(
                              fontSize: 12,
                              color: duasColor,
                            ),
                            textDirection: TextDirection.rtl,
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),

        // سهم للانتقال
        Container(
          margin: const EdgeInsets.only(right: 8),
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: duasColor.withAlpha(_isHovered ? 70 : 30),
            shape: BoxShape.circle,
            boxShadow: _isHovered
                ? [
                    BoxShadow(
                      color: duasColor.withAlpha(40),
                      blurRadius: 8,
                      spreadRadius: 1,
                    )
                  ]
                : [],
          ),
          child: Icon(
            Icons.arrow_back_ios,
            size: 16,
            color: duasColor,
          ),
        ),
      ],
    );
  }

  // تصميم عرض الشبكة - الأصلي
  Widget _buildGridViewLayout(BuildContext context, bool isDarkMode,
      Size screenSize, Color duasColor, IconData iconData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // الجزء العلوي: الأيقونة والعنوان
        Row(
          children: [
            // أيقونة الفئة مع تأثير توهج
            Stack(
              alignment: Alignment.center,
              children: [
                // طبقة التوهج الخارجية
                AnimatedBuilder(
                  animation: _hoverController,
                  builder: (context, _) {
                    return AnimatedOpacity(
                      duration: const Duration(milliseconds: 300),
                      opacity: _isHovered ? 1.0 : 0.0,
                      child: Container(
                        width: screenSize.width * 0.14,
                        height: screenSize.width * 0.14,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(
                            colors: [
                              duasColor.withAlpha(100),
                              duasColor.withAlpha(0),
                            ],
                            stops: const [0.1, 1.0],
                          ),
                        ),
                      ),
                    );
                  },
                ),
                // الأيقونة الرئيسية
                Container(
                  width: screenSize.width * 0.12,
                  height: screenSize.width * 0.12,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        duasColor.withAlpha(50),
                        duasColor.withAlpha(100),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: duasColor.withAlpha(50),
                        blurRadius: 10,
                        spreadRadius: 0,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: Icon(
                    iconData,
                    color: duasColor,
                    size: screenSize.width * 0.06,
                  ),
                ),
              ],
            ),
            const SizedBox(width: 16),
            // اسم الفئة وعدد الأدعية
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                textDirection: TextDirection.rtl,
                children: [
                  // عنوان مع تأثير تدرج لوني وتوهج
                  Stack(
                    children: [
                      // تأثير توهج خلف النص عند التحويم
                      if (_isHovered)
                        Positioned.fill(
                          child: AnimatedOpacity(
                            duration: const Duration(milliseconds: 300),
                            opacity: _isHovered ? 0.7 : 0.0,
                            child: TweenAnimationBuilder<double>(
                              tween: Tween<double>(begin: 0.0, end: 1.0),
                              duration: const Duration(milliseconds: 800),
                              curve: Curves.easeOutCubic,
                              builder: (context, value, child) {
                                return Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    boxShadow: [
                                      BoxShadow(
                                        color: duasColor.withAlpha(30),
                                        blurRadius: 15 * value,
                                        spreadRadius: 1 * value,
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      // النص مع تأثير تدرج لوني
                      ShaderMask(
                        shaderCallback: (bounds) {
                          return LinearGradient(
                            colors: [
                              isDarkMode ? Colors.white : Colors.black87,
                              duasColor,
                            ],
                            stops: const [0.5, 1.0],
                            begin: Alignment.centerRight,
                            end: Alignment.centerLeft,
                            tileMode: TileMode.clamp,
                          ).createShader(bounds);
                        },
                        child: Text(
                          widget.category.name,
                          style: TextStyle(
                            fontSize: screenSize.width * 0.04,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            letterSpacing: _isHovered ? 0.5 : 0.0,
                          ),
                          textDirection: TextDirection.rtl,
                          textAlign: TextAlign.right,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // عدد الأدعية مع أيقونة
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    textDirection: TextDirection.rtl,
                    children: [
                      Icon(
                        Icons.format_list_numbered,
                        size: 14,
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${widget.category.items.length} أدعية',
                        style: TextStyle(
                          fontSize: screenSize.width * 0.03,
                          color:
                              isDarkMode ? Colors.grey[400] : Colors.grey[700],
                        ),
                        textDirection: TextDirection.rtl,
                        textAlign: TextAlign.right,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 20),
        // وصف الفئة مع خلفية مميزة
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 10,
          ),
          decoration: BoxDecoration(
            color: duasColor.withAlpha(15),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: duasColor.withAlpha(30),
              width: 1,
            ),
          ),
          child: Text(
            widget.category.description,
            style: TextStyle(
              fontSize: screenSize.width * 0.032,
              height: 1.4,
              color: isDarkMode ? Colors.grey[300] : Colors.grey[800],
            ),
            textDirection: TextDirection.rtl,
            textAlign: TextAlign.right,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const Spacer(),
        // شريط السفلي مع مؤشر قابلية التصفح
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          textDirection: TextDirection.rtl,
          children: [
            // مؤشر يوضح تفرع الأقسام إن وجدت
            if (widget.category.hasSubcategories)
              Flexible(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 10,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: duasColor.withAlpha(15),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    textDirection: TextDirection.rtl,
                    children: [
                      Icon(
                        Icons.account_tree_outlined,
                        size: screenSize.width * 0.035,
                        color: duasColor.withAlpha(179),
                      ),
                      SizedBox(width: screenSize.width * 0.01),
                      Flexible(
                        child: Text(
                          'أقسام فرعية',
                          style: TextStyle(
                            fontSize: screenSize.width * 0.025,
                            fontWeight: FontWeight.w500,
                            color: duasColor.withAlpha(179),
                          ),
                          textDirection: TextDirection.rtl,
                          textAlign: TextAlign.right,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            // زر التصفح مع تأثير نبض
            TweenAnimationBuilder<double>(
              tween: Tween<double>(
                begin: 0.0,
                end: _isHovered ? 1.0 : 0.0,
              ),
              duration: const Duration(milliseconds: 500),
              builder: (context, value, child) {
                return Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Color.lerp(
                      duasColor.withAlpha(30),
                      duasColor.withAlpha(70),
                      value,
                    ),
                    shape: BoxShape.circle,
                    boxShadow: [
                      if (value > 0)
                        BoxShadow(
                          color: duasColor.withAlpha((40 * value).toInt()),
                          blurRadius: 8 * value,
                          spreadRadius: 1 * value,
                        ),
                    ],
                  ),
                  child: Icon(
                    Icons.arrow_back_ios,
                    size: screenSize.width * 0.035,
                    color: Color.lerp(
                      duasColor,
                      Colors.white,
                      value * 0.5,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ],
    );
  }
}
