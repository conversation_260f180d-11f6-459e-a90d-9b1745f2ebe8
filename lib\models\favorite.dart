class Favorite {
  final int id;
  final dynamic itemId; // تغيير النوع إلى dynamic لدعم الأنواع المختلفة
  final String type; // 'book', 'poem', 'azkar'
  final int timestamp; // تاريخ الإضافة إلى المفضلة

  Favorite({
    required this.id,
    required this.itemId,
    required this.type,
    required this.timestamp,
  });

  DateTime get addedDate => DateTime.fromMillisecondsSinceEpoch(timestamp);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'item_id': itemId.toString(), // تحويل إلى نص للتوافق
      'item_type': type, // استخدام item_type بدلاً من type
      'timestamp': timestamp,
    };
  }

  factory Favorite.fromMap(Map<String, dynamic> map) {
    // التعامل مع المعرف
    dynamic rawItemId = map['item_id'] ?? map['book_id'] ?? 0;

    // محاولة تحويل المعرف إلى رقم إذا كان نصًا
    if (rawItemId is String) {
      try {
        rawItemId = int.parse(rawItemId);
      } catch (e) {
        // إذا فشل التحويل، احتفظ به كنص
      }
    }

    return Favorite(
      id: map['id'] ?? 0,
      itemId: rawItemId,
      type: map['item_type'] ?? map['type'] ?? 'unknown', // دعم الاسمين
      timestamp: map['timestamp'] ??
          map['date_added'] ??
          DateTime.now().millisecondsSinceEpoch,
    );
  }

  @override
  String toString() {
    return 'Favorite{id: $id, itemId: $itemId, type: $type, timestamp: $timestamp}';
  }
}
