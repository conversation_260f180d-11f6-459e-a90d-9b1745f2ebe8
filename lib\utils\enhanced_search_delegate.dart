// lib/utils/enhanced_search_delegate.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../models/search_result_model.dart';
import '../services/search_service.dart';
import '../utils/app_colors.dart';

/// مندوب البحث المحسن للتطبيق
class EnhancedSearchDelegate extends SearchDelegate<SearchResult?> {
  final bool showRecentSearches;
  final bool showPopularSearches;
  final bool enableVoiceSearch;

  EnhancedSearchDelegate({
    this.showRecentSearches = true,
    this.showPopularSearches = true,
    this.enableVoiceSearch = true,
  });

  @override
  String get searchFieldLabel => 'ابحث في التطبيق...';

  @override
  TextStyle get searchFieldStyle => const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.normal,
      );

  @override
  ThemeData appBarTheme(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // تحديد لون الأيقونات بناءً على نص البحث
    final Color iconColor = _getDecorationColor(query);

    return theme.copyWith(
      appBarTheme: AppBarTheme(
        backgroundColor: isDarkMode ? const Color(0xFF1A2530) : Colors.white,
        elevation: 0,
        iconTheme: IconThemeData(color: iconColor),
        titleTextStyle: TextStyle(
          color: theme.textTheme.bodyLarge?.color,
          fontSize: 16,
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: InputBorder.none,
        hintStyle: TextStyle(
          color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
        ),
      ),
    );
  }

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      if (query.isNotEmpty)
        IconButton(
          icon: const Icon(Icons.clear),
          onPressed: () {
            HapticFeedback.selectionClick();
            query = '';
            showSuggestions(context);
          },
          tooltip: 'مسح',
        ),
      if (enableVoiceSearch)
        IconButton(
          icon: const Icon(Icons.mic),
          onPressed: () {
            HapticFeedback.mediumImpact();
            // هنا يمكن إضافة البحث الصوتي في المستقبل
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('البحث الصوتي قيد التطوير'),
                duration: Duration(seconds: 2),
              ),
            );
          },
          tooltip: 'بحث صوتي',
        ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: AnimatedIcon(
        icon: AnimatedIcons.menu_arrow,
        progress: transitionAnimation,
      ),
      onPressed: () {
        HapticFeedback.selectionClick();
        close(context, null);
      },
      tooltip: 'رجوع',
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return FutureBuilder<List<SearchResult>>(
      future: SearchService.search(query),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return _buildLoadingIndicator(context);
        }

        if (snapshot.hasError) {
          return _buildErrorWidget(context, snapshot.error.toString());
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return _buildNoResultsWidget(context);
        }

        return _buildSearchResultsList(context, snapshot.data!);
      },
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return FutureBuilder<List<SearchResult>>(
      future: SearchService.search(query),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return _buildLoadingIndicator(context);
        }

        return SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // عرض البحث الشائع إذا كان البحث فارغاً
                if (query.isEmpty && showPopularSearches) ...[
                  _buildSectionHeader(
                    context,
                    'عمليات بحث شائعة',
                    Icons.trending_up_rounded,
                  ),
                  const SizedBox(height: 12),
                  _buildPopularSearchTerms(context),
                  const SizedBox(height: 24),
                ],

                // عرض الأقسام المقترحة
                _buildSectionHeader(
                  context,
                  query.isEmpty ? 'الأقسام' : 'نتائج البحث',
                  query.isEmpty ? Icons.category_rounded : Icons.search_rounded,
                ),
                const SizedBox(height: 12),

                // عرض نتائج البحث
                if (snapshot.hasData && snapshot.data!.isNotEmpty)
                  ...snapshot.data!.map((result) => _buildSearchResultItem(
                        context,
                        result,
                        isDarkMode,
                      )),

                // عرض رسالة إذا لم تكن هناك نتائج
                if (snapshot.hasData &&
                    snapshot.data!.isEmpty &&
                    query.isNotEmpty)
                  _buildNoResultsMessage(context),

                const SizedBox(height: 24),

                // زخرفة في أسفل الصفحة
                Center(
                  child: Opacity(
                    opacity: isDarkMode ? 0.05 : 0.08,
                    child: SvgPicture.asset(
                      'assets/images/p2.svg',
                      width: 150,
                      height: 150,
                      colorFilter: ColorFilter.mode(
                        _getDecorationColor(query),
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء مؤشر التحميل
  Widget _buildLoadingIndicator(BuildContext context) {
    // تحديد اللون المناسب
    final Color loadingColor = _getDecorationColor(query);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(loadingColor),
            strokeWidth: 3,
          ),
          const SizedBox(height: 16),
          Text(
            'جاري البحث...',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
          ),
        ],
      ),
    );
  }

  /// بناء رسالة الخطأ
  Widget _buildErrorWidget(BuildContext context, String error) {
    // تحديد اللون المناسب
    final Color errorColor = _getDecorationColor(query);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: errorColor.withAlpha(isDarkMode ? 40 : 30),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.error_outline,
              size: 48,
              color: errorColor,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ أثناء البحث',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: isDarkMode ? Colors.white : Colors.black87,
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              error,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: isDarkMode ? Colors.white70 : Colors.black54,
                  ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () {
              showResults(context);
            },
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: _getDecorationColor(query),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 10,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء رسالة عدم وجود نتائج
  Widget _buildNoResultsWidget(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    // تحديد اللون المناسب
    final Color noResultsColor = _getDecorationColor(query);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: noResultsColor.withAlpha(isDarkMode ? 30 : 20),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.search_off_rounded,
              size: 64,
              color: noResultsColor.withAlpha(isDarkMode ? 180 : 150),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد نتائج لـ "$query"',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black87,
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              'حاول البحث بكلمات مختلفة أو تصفح الأقسام',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: isDarkMode ? Colors.white60 : Colors.black54,
                    fontSize: 15,
                  ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              query = '';
              showSuggestions(context);
            },
            icon: const Icon(Icons.refresh),
            label: const Text('عرض الاقتراحات'),
            style: ElevatedButton.styleFrom(
              backgroundColor: _getDecorationColor(query),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء رسالة عدم وجود نتائج (مختصرة)
  Widget _buildNoResultsMessage(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // تحديد اللون المناسب
    final Color messageColor = _getDecorationColor(query);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: messageColor.withAlpha(isDarkMode ? 20 : 15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: messageColor.withAlpha(isDarkMode ? 40 : 30),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.search_off_rounded,
            color: messageColor.withAlpha(isDarkMode ? 180 : 150),
            size: 22,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'لا توجد نتائج لـ "$query"',
              style: TextStyle(
                color: isDarkMode ? Colors.white70 : Colors.black87,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة نتائج البحث
  Widget _buildSearchResultsList(
      BuildContext context, List<SearchResult> results) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return ListView.builder(
      itemCount: results.length,
      physics: const BouncingScrollPhysics(),
      padding: const EdgeInsets.all(16),
      itemBuilder: (context, index) {
        final result = results[index];
        return _buildSearchResultItem(context, result, isDarkMode);
      },
    );
  }

  /// بناء عنصر نتيجة البحث
  Widget _buildSearchResultItem(
      BuildContext context, SearchResult result, bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          HapticFeedback.selectionClick();
          close(context, result);
        },
        borderRadius: BorderRadius.circular(16),
        child: Ink(
          decoration: BoxDecoration(
            color: isDarkMode
                ? Colors.black.withAlpha(40) // 0.15 * 255 = ~40
                : Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: isDarkMode
                    ? Colors.black.withAlpha(40) // 0.15 * 255 = ~40
                    : Colors.black.withAlpha(10), // 0.04 * 255 = ~10
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
            border: Border.all(
              color: isDarkMode
                  ? Colors.white.withAlpha(10) // 0.04 * 255 = ~10
                  : Colors.grey.withAlpha(20), // 0.08 * 255 = ~20
              width: 0.5,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // أيقونة القسم
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: result.color.withAlpha(isDarkMode ? 40 : 30),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    result.icon,
                    color: result.color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                // معلومات النتيجة
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        result.title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: isDarkMode ? Colors.white : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        result.subtitle,
                        style: TextStyle(
                          fontSize: 14,
                          color: isDarkMode ? Colors.white70 : Colors.black54,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                // شارة القسم
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: result.color.withAlpha(isDarkMode ? 40 : 30),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    result.section,
                    style: TextStyle(
                      fontSize: 12,
                      color: result.color,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                // شارة "قريباً" إذا كان القسم غير متاح
                if (!result.isAvailable) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.amber.withAlpha(isDarkMode ? 40 : 30),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'قريباً',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.amber,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء عنوان القسم
  Widget _buildSectionHeader(
      BuildContext context, String title, IconData icon) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // تحديد اللون المناسب بناءً على العنوان
    Color headerColor;
    if (title.contains('الأدعية') || title == 'الأدعية') {
      headerColor = AppColors.duasColor;
    } else if (title.contains('الصلاة على النبي') ||
        title == 'الصلاة على النبي') {
      headerColor = AppColors.prophetPrayersColor;
    } else if (title.contains('المسبحة') || title == 'المسبحة') {
      headerColor = AppColors.tasbihColor;
    } else {
      // استخدام لون الأذكار كلون افتراضي
      headerColor = AppColors.azkarColor;
    }

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: headerColor.withAlpha(isDarkMode ? 40 : 30),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: headerColor,
            size: 18,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.black87,
          ),
        ),
      ],
    );
  }

  /// الحصول على لون الزخرفة المناسب بناءً على نص البحث
  Color _getDecorationColor(String query) {
    // إذا كان البحث فارغاً، استخدم لون الأذكار كلون افتراضي
    if (query.isEmpty) {
      return AppColors.azkarColor;
    }

    // تحديد اللون بناءً على نص البحث
    if (query.contains('الأدعية') || query.contains('دعاء')) {
      return AppColors.duasColor;
    } else if (query.contains('الصلاة على النبي') || query.contains('صلاة')) {
      return AppColors.prophetPrayersColor;
    } else if (query.contains('المسبحة') ||
        query.contains('تسبيح') ||
        query.contains('استغفار')) {
      return AppColors.tasbihColor;
    } else {
      // استخدام لون الأذكار كلون افتراضي
      return AppColors.azkarColor;
    }
  }

  /// بناء مصطلحات البحث الشائعة
  Widget _buildPopularSearchTerms(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final terms = SearchService.getPopularSearchTerms();

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: terms.map((termData) {
        final String term = termData['term'] as String;
        final String section = termData['section'] as String;

        // تحديد لون الشريحة بناءً على القسم
        Color chipColor;
        switch (section) {
          case 'الأذكار':
            chipColor = AppColors.azkarColor;
            break;
          case 'المسبحة':
            chipColor = AppColors.tasbihColor;
            break;
          case 'الأدعية':
            chipColor = AppColors.duasColor;
            break;
          case 'الصلاة على النبي':
            chipColor = AppColors.prophetPrayersColor;
            break;
          default:
            chipColor = AppColors.azkarColor;
        }

        return ActionChip(
          avatar: Icon(
            Icons.search,
            size: 16,
            color: chipColor,
          ),
          label: Text(term),
          backgroundColor: isDarkMode
              ? chipColor.withAlpha(40) // 0.15 * 255 = ~40
              : chipColor.withAlpha(20), // 0.08 * 255 = ~20
          labelStyle: TextStyle(
            color: isDarkMode ? Colors.white : Colors.black87,
          ),
          onPressed: () {
            HapticFeedback.selectionClick();
            query = term;

            // إنشاء نتيجة بحث مناسبة بناءً على القسم
            SearchResult? result;
            final String id = termData['id'] as String;

            switch (section) {
              case 'الأذكار':
                result = SearchResult.azkar(
                  id: id,
                  title: term,
                  subtitle: 'قسم الأذكار',
                );
                break;
              case 'المسبحة':
                result = SearchResult.tasbih(
                  id: id,
                  title: term,
                  subtitle: 'قسم المسبحة',
                );
                break;
              case 'الأدعية':
                result = SearchResult.dua(
                  id: id,
                  title: term,
                  subtitle: 'قسم الأدعية',
                );
                break;
              case 'الصلاة على النبي':
                result = SearchResult.prophetPrayer(
                  id: id,
                  title: term,
                  subtitle: 'قسم الصلاة على النبي',
                );
                break;
              default:
                // استخدام البحث العادي إذا لم يتم العثور على قسم مناسب
                showResults(context);
                return;
            }

            // إغلاق البحث وإرجاع النتيجة
            close(context, result);
          },
        );
      }).toList(),
    );
  }
}
