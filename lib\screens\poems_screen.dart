import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../utils/constants.dart';
import '../utils/app_colors.dart';
import '../models/poem.dart';
import '../services/database_helper.dart';

class PoemsScreen extends StatefulWidget {
  const PoemsScreen({super.key});

  @override
  State<PoemsScreen> createState() => _PoemsScreenState();
}

class _PoemsScreenState extends State<PoemsScreen>
    with SingleTickerProviderStateMixin {
  // للتحكم في الحركات
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  // للبحث
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  String _searchQuery = "";

  // قائمة القصائد
  late Future<List<Poem>> _poems;
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  @override
  void initState() {
    super.initState();

    // تحميل القصائد
    _poems = _loadPoems();

    // إعداد الحركات
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
      ),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.3, 1.0, curve: Curves.easeOut),
      ),
    );

    // بدء الحركة
    Future.delayed(const Duration(milliseconds: 200), () {
      _animationController.forward();
    });

    // مراقبة البحث
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  // تحميل القصائد
  Future<List<Poem>> _loadPoems() async {
    try {
      return await _databaseHelper.getPoems();
    } catch (e) {
      // عرض رسالة خطأ إذا فشل التحميل
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء تحميل القصائد: $e')),
        );
      }
      return [];
    }
  }

  // تصفية وبحث القصائد
  List<Poem> _filterAndSearchPoems(List<Poem> poems) {
    if (_searchQuery.isEmpty) {
      return poems;
    }

    return poems
        .where((poem) =>
            poem.title.contains(_searchQuery) ||
            poem.poet.contains(_searchQuery) ||
            poem.content.contains(_searchQuery))
        .toList();
  }

  // إضافة إلى المفضلة
  void _addToFavorites(Poem poem) async {
    try {
      int poemId;
      try {
        poemId = int.parse(poem.id);
      } catch (e) {
        // إذا كان المعرف نصياً، استخدم hashCode
        poemId = poem.id.hashCode;
      }

      await _databaseHelper.addFavorite(poemId, 'poem');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('تمت الإضافة إلى المفضلة'),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        physics: const BouncingScrollPhysics(),
        slivers: [
          // شريط التطبيق مع العنوان وأيقونة البحث
          SliverAppBar(
            expandedHeight: _isSearching ? 0 : 220.0,
            pinned: true,
            floating: true,
            stretch: true,
            elevation: 0,
            backgroundColor: Theme.of(context).scaffoldBackgroundColor,
            title: _isSearching
                ? _buildSearchField()
                : Text(
                    'المنهل الراوي',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
            actions: [
              IconButton(
                icon: Icon(
                  _isSearching ? Icons.close : Icons.search,
                  color: Theme.of(context).colorScheme.primary,
                ),
                onPressed: () {
                  setState(() {
                    _isSearching = !_isSearching;
                    if (!_isSearching) {
                      _searchController.clear();
                      _searchQuery = "";
                    }
                  });
                },
              ),
            ],
            flexibleSpace: _isSearching
                ? null
                : FlexibleSpaceBar(
                    background: Stack(
                      children: [
                        // خلفية متدرجة
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                AppColors.poemsColor.withAlpha(38),
                                Theme.of(context).scaffoldBackgroundColor,
                              ],
                            ),
                          ),
                        ),
                        // زخرفة إسلامية
                        Positioned(
                          top: -20,
                          right: -20,
                          child: Opacity(
                            opacity: 0.1,
                            child: SvgPicture.asset(
                              'assets/images/p1.svg',
                              width: 200,
                              height: 200,
                              colorFilter: const ColorFilter.mode(
                                AppColors.poemsColor,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                        ),
                        // صورة دائرية توضيحية
                        Positioned(
                          bottom: 15,
                          right: 20,
                          child: Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: AppColors.poemsColor.withAlpha(38),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.poemsColor.withAlpha(38),
                                  blurRadius: 20,
                                  spreadRadius: 5,
                                ),
                              ],
                            ),
                            child: const Center(
                              child: Icon(
                                Icons.menu_book,
                                size: 40,
                                color: AppColors.poemsColor,
                              ),
                            ),
                          ),
                        ),
                        // عنوان الصفحة وشرح
                        Positioned(
                          bottom: 20,
                          left: 20,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    width: 4,
                                    height: 20,
                                    decoration: BoxDecoration(
                                      color: AppColors.poemsColor,
                                      borderRadius: BorderRadius.circular(2),
                                    ),
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    'المنهل الراوي',
                                    style: Theme.of(context)
                                        .textTheme
                                        .headlineSmall!
                                        .copyWith(
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.poemsColor,
                                        ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'ديوان السيد محمد هزاع الحضرمي',
                                style: TextStyle(
                                  color: AppColors.poemsColor.withAlpha(200),
                                  fontSize: 14,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'قصيدة لامية العرب',
                                style: TextStyle(
                                  color: AppColors.poemsColor.withAlpha(180),
                                  fontSize: 13,
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
          ),

          // بيت شعر مميز
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
              child: SlideTransition(
                position: _slideAnimation,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.poemsColor.withAlpha(25),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColors.poemsColor.withAlpha(50),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'أقيموا بني أمي صدور مطيكم ... فإني إلى قوم سواكم لأميلُ',
                          style: TextStyle(
                            fontSize: 16,
                            height: 1.8,
                            color: Theme.of(context).textTheme.bodyLarge?.color,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),

          // عنوان القسم
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: SlideTransition(
                position: _slideAnimation,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Row(
                    children: [
                      Container(
                        width: 4,
                        height: 20,
                        decoration: BoxDecoration(
                          color: AppColors.poemsColor,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      const SizedBox(width: 8),
                     const Text(
                        'عن الشاعر',
                        style:  TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.poemsColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // معلومات عن الشاعر
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: SlideTransition(
                position: _slideAnimation,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Card(
                    elevation: 2,
                    shadowColor: AppColors.poemsColor.withAlpha(50),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // صورة الشاعر
                          Container(
                            width: 100,
                            height: 100,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.white,
                              border: Border.all(
                                color: AppColors.poemsColor.withAlpha(100),
                                width: 2,
                              ),
                              image: const DecorationImage(
                                image: AssetImage('assets/images/allah.png'),
                                fit: BoxFit.contain,
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          // اسم الشاعر
                          Text(
                            'الشنفرى',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color:
                                  Theme.of(context).textTheme.titleLarge?.color,
                            ),
                          ),
                          const SizedBox(height: 8),
                          // وصف مختصر
                          Text(
                            'شاعر صعلوك جاهلي',
                            style: TextStyle(
                              fontSize: 16,
                              color: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.color
                                  ?.withAlpha(180),
                            ),
                          ),
                          const SizedBox(height: 16),
                          // نبذة عن الشاعر
                          Text(
                            'من أشهر شعراء العرب في العصر الجاهلي، عُرف بقصيدته المشهورة "لامية العرب" التي تعد من روائع الشعر العربي القديم. عاش حياة الصعلكة والترحال في الصحراء، وتميز شعره بوصف الحياة البرية والفخر بالنفس.',
                            style: TextStyle(
                              fontSize: 14,
                              height: 1.6,
                              color:
                                  Theme.of(context).textTheme.bodyMedium?.color,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),

          // عنوان أشهر القصائد
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
              child: SlideTransition(
                position: _slideAnimation,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Row(
                    children: [
                      Container(
                        width: 4,
                        height: 20,
                        decoration: BoxDecoration(
                          color: AppColors.poemsColor,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      const SizedBox(width: 8),
                     const Text(
                        'من أشهر قصائده',
                        style:  TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.poemsColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // قائمة القصائد
          FutureBuilder<List<Poem>>(
            future: _poems,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const SliverFillRemaining(
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                );
              } else if (snapshot.hasError) {
                return SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 60,
                          color: Colors.red.withAlpha(200),
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'حدث خطأ أثناء تحميل البيانات',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton.icon(
                          onPressed: () {
                            setState(() {
                              _poems = _loadPoems();
                            });
                          },
                          icon: const Icon(Icons.refresh),
                          label: const Text('إعادة المحاولة'),
                        ),
                      ],
                    ),
                  ),
                );
              } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                return SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.menu_book_outlined,
                          size: 80,
                          color: Colors.grey.withAlpha(150),
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'لا توجد قصائد متاحة',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              } else {
                final filteredPoems = _filterAndSearchPoems(snapshot.data!);

                if (filteredPoems.isEmpty && _searchQuery.isNotEmpty) {
                  return SliverFillRemaining(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.search_off,
                            size: 60,
                            color: Colors.grey.withAlpha(180),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'لا توجد نتائج لـ "$_searchQuery"',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  );
                }

                return SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final poem = filteredPoems[index];
                      return SlideTransition(
                        position: _slideAnimation,
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
                            child: _buildPoemCard(poem),
                          ),
                        ),
                      );
                    },
                    childCount: filteredPoems.length,
                  ),
                );
              }
            },
          ),

          // مساحة إضافية في النهاية
          const SliverToBoxAdapter(
            child: SizedBox(height: 32),
          ),
        ],
      ),
    );
  }

  // بناء حقل البحث
  Widget _buildSearchField() {
    return TextField(
      controller: _searchController,
      autofocus: true,
      decoration: InputDecoration(
        hintText: 'ابحث في القصائد...',
        border: InputBorder.none,
        hintStyle: TextStyle(color: Colors.grey[400]),
      ),
      style: TextStyle(
        color: Theme.of(context).colorScheme.primary,
        fontSize: 16.0,
      ),
      cursorColor: AppColors.poemsColor,
    );
  }

  // بناء بطاقة قصيدة
  Widget _buildPoemCard(Poem poem) {
    return Card(
      elevation: 2,
      shadowColor: AppColors.poemsColor.withAlpha(50),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: () {
          Navigator.pushNamed(
            context,
            AppConstants.poemDetailsRoute,
            arguments: poem,
          );
        },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // أيقونة للقصيدة
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  color: AppColors.poemsColor.withAlpha(40),
                  shape: BoxShape.circle,
                ),
                child: const Center(
                  child: Icon(
                    Icons.music_note,
                    color: AppColors.poemsColor,
                    size: 24,
                  ),
                ),
              ),
              const SizedBox(width: 16),

              // بيانات القصيدة
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // عنوان القصيدة
                    Text(
                      poem.title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),

                    // بيت شعر من القصيدة
                    if (poem.content.isNotEmpty)
                      Text(
                        _getFirstLine(poem.content),
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.color
                              ?.withAlpha(180),
                          fontStyle: FontStyle.italic,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                    const SizedBox(height: 8),

                    // اسم الشاعر
                    Row(
                      children: [
                        Icon(
                          Icons.person,
                          size: 12,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          poem.poet,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // زر الإضافة للمفضلة
              IconButton(
                icon: const Icon(Icons.favorite_border),
                color: AppColors.poemsColor,
                onPressed: () => _addToFavorites(poem),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // استخراج أول بيت من القصيدة
  String _getFirstLine(String content) {
    if (content.isEmpty) return '';
    final lines = content.split('\n');
    if (lines.isEmpty) return '';
    return lines.first;
  }
}
