// مؤشر تقدم الإحصائيات

import 'package:flutter/material.dart';

class StatsProgressIndicator extends StatelessWidget {
  final String title;
  final String value;
  final double progress;
  final Color color;

  const StatsProgressIndicator({
    Key? key,
    required this.title,
    required this.value,
    required this.progress,
    required this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final textColor = isDarkMode ? Colors.white : Colors.black87;
    final size = MediaQuery.of(context).size;

    // تحديد الأحجام بناءً على حجم الشاشة
    final verticalPadding = size.height * 0.01; // 1% من ارتفاع الشاشة
    final titleFontSize = size.width * 0.035; // 3.5% من عرض الشاشة
    final valueFontSize = size.width * 0.035; // 3.5% من عرض الشاشة
    final progressBarHeight = size.height * 0.01; // 1% من ارتفاع الشاشة
    final progressBarRadius = size.width * 0.01; // 1% من عرض الشاشة
    final spacingHeight = size.height * 0.008; // 0.8% من ارتفاع الشاشة

    return Padding(
      padding: EdgeInsets.symmetric(vertical: verticalPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // عنوان الذكر - متجاوب مع حجم الشاشة
              Expanded(
                flex: 3,
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: titleFontSize,
                    color: textColor.withAlpha(204), // 0.8 * 255 = 204
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
              // قيمة العداد - متجاوبة مع حجم الشاشة
              Expanded(
                flex: 1,
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: valueFontSize,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                  textAlign: TextAlign.end,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          SizedBox(height: spacingHeight),
          // شريط التقدم - متجاوب مع حجم الشاشة
          ClipRRect(
            borderRadius: BorderRadius.circular(progressBarRadius),
            child: LinearProgressIndicator(
              value: progress,
              backgroundColor: color.withAlpha(26), // 0.1 * 255 = 26
              valueColor: AlwaysStoppedAnimation<Color>(color),
              minHeight: progressBarHeight,
            ),
          ),
        ],
      ),
    );
  }
}
