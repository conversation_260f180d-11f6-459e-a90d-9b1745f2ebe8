class Book {
  final int id;
  final String title;
  final String author;
  final String description;
  final String coverUrl;
  final String? localCoverPath;
  final String category;
  final String pdfUrl;
  final String? pdfPath;
  final String? localPdfPath;
  final int pages;
  final List<String> tags;
  final bool isFavorite;
  final double rating;
  final int
      downloadProgress; // 0-100 for percentage, -1 for not downloaded, 100 for completed
  final int lastReadPage;
  final DateTime? lastReadDate;

  Book({
    required this.id,
    required this.title,
    required this.author,
    required this.description,
    required this.coverUrl,
    this.localCoverPath,
    required this.category,
    this.pdfUrl = '',
    this.pdfPath,
    this.localPdfPath,
    this.pages = 0,
    this.tags = const [],
    this.isFavorite = false,
    this.rating = 0.0,
    this.downloadProgress = -1,
    this.lastReadPage = 0,
    this.lastReadDate,
  });

  factory Book.fromJson(Map<String, dynamic> json) {
    // تحويل المعرف من النص إلى رقم إذا كان نصياً
    int bookId;
    if (json['id'] is String) {
      final idStr = json['id'].toString();
      bookId = int.tryParse(idStr.replaceAll(RegExp(r'[^0-9]'), '')) ?? 0;
    } else if (json['id'] is int) {
      bookId = json['id'];
    } else {
      bookId = 0;
    }

    // تحويل الوسوم من قائمة أو نص إلى قائمة
    List<String> bookTags = [];
    if (json['tags'] != null) {
      if (json['tags'] is List) {
        bookTags = List<String>.from(json['tags'].map((tag) => tag.toString()));
      } else if (json['tags'] is String) {
        bookTags = json['tags']
            .toString()
            .split(',')
            .map((tag) => tag.trim())
            .toList();
      }
    }

    // تحويل التقييم إلى رقم عشري
    double bookRating = 0.0;
    if (json['rating'] != null) {
      if (json['rating'] is num) {
        bookRating = (json['rating'] as num).toDouble();
      } else if (json['rating'] is String) {
        bookRating = double.tryParse(json['rating']) ?? 0.0;
      }
    }

    // تحويل تاريخ آخر قراءة إلى كائن DateTime إذا كان موجوداً
    DateTime? lastReadDate;
    if (json['lastReadDate'] != null) {
      if (json['lastReadDate'] is String) {
        try {
          lastReadDate = DateTime.parse(json['lastReadDate']);
        } catch (_) {}
      } else if (json['lastReadDate'] is int) {
        try {
          lastReadDate =
              DateTime.fromMillisecondsSinceEpoch(json['lastReadDate']);
        } catch (_) {}
      }
    }

    return Book(
      id: bookId,
      title: json['title'] ?? '',
      author: json['author'] ?? '',
      description: json['description'] ?? '',
      coverUrl: json['coverUrl'] ?? '',
      localCoverPath: json['localCoverPath'],
      category: json['category'] ?? '',
      pdfUrl: json['pdfUrl'] ?? '',
      pdfPath: json['pdfPath'],
      localPdfPath: json['localPdfPath'],
      pages: json['pages'] is num ? (json['pages'] as num).toInt() : 0,
      tags: bookTags,
      isFavorite: json['isFavorite'] == true,
      rating: bookRating,
      downloadProgress: json['downloadProgress'] is num
          ? (json['downloadProgress'] as num).toInt()
          : -1,
      lastReadPage: json['lastReadPage'] is num
          ? (json['lastReadPage'] as num).toInt()
          : 0,
      lastReadDate: lastReadDate,
    );
  }

  factory Book.fromMap(Map<String, dynamic> map) {
    // تحويل المعرف من النص إلى رقم إذا كان نصياً
    int bookId;
    if (map['id'] is String) {
      final idStr = map['id'].toString();
      bookId = int.tryParse(idStr.replaceAll(RegExp(r'[^0-9]'), '')) ?? 0;
    } else if (map['id'] is int) {
      bookId = map['id'];
    } else {
      bookId = 0;
    }

    // تحويل الوسوم من نص إلى قائمة
    List<String> bookTags = [];
    if (map['tags'] != null) {
      if (map['tags'] is List) {
        bookTags = List<String>.from(map['tags'].map((tag) => tag.toString()));
      } else if (map['tags'] is String) {
        final tagsStr = map['tags'].toString();
        if (tagsStr.isNotEmpty) {
          bookTags = tagsStr.split(',').map((tag) => tag.trim()).toList();
        }
      }
    }

    // تحويل تاريخ آخر قراءة إلى كائن DateTime إذا كان موجوداً
    DateTime? lastReadDate;
    if (map['last_read_date'] != null) {
      if (map['last_read_date'] is String) {
        try {
          lastReadDate = DateTime.parse(map['last_read_date']);
        } catch (_) {}
      } else if (map['last_read_date'] is int) {
        try {
          lastReadDate =
              DateTime.fromMillisecondsSinceEpoch(map['last_read_date']);
        } catch (_) {}
      }
    }

    return Book(
      id: bookId,
      title: map['title'] ?? '',
      author: map['author'] ?? '',
      description: map['description'] ?? '',
      coverUrl: map['cover_url'] ?? '',
      localCoverPath: map['local_cover_path'],
      category: map['category'] ?? '',
      pdfUrl: map['pdf_url'] ?? '',
      pdfPath: map['pdf_path'],
      localPdfPath: map['local_pdf_path'],
      pages: map['pages'] is num ? (map['pages'] as num).toInt() : 0,
      tags: bookTags,
      isFavorite: map['is_favorite'] == 1 || map['is_favorite'] == true,
      rating: map['rating'] is num ? (map['rating'] as num).toDouble() : 0.0,
      downloadProgress: map['download_progress'] is num
          ? (map['download_progress'] as num).toInt()
          : -1,
      lastReadPage: map['last_read_page'] is num
          ? (map['last_read_page'] as num).toInt()
          : 0,
      lastReadDate: lastReadDate,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'author': author,
      'description': description,
      'coverUrl': coverUrl,
      'localCoverPath': localCoverPath,
      'category': category,
      'pdfUrl': pdfUrl,
      'pdfPath': pdfPath,
      'localPdfPath': localPdfPath,
      'pages': pages,
      'tags': tags,
      'isFavorite': isFavorite,
      'rating': rating,
      'downloadProgress': downloadProgress,
      'lastReadPage': lastReadPage,
      'lastReadDate': lastReadDate?.toIso8601String(),
    };
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'author': author,
      'description': description,
      'cover_url': coverUrl,
      'local_cover_path': localCoverPath,
      'category': category,
      'pdf_url': pdfUrl,
      'pdf_path': pdfPath,
      'local_pdf_path': localPdfPath,
      'pages': pages,
      'tags': tags.join(', '),
      'is_favorite': isFavorite ? 1 : 0,
      'rating': rating,
      'download_progress': downloadProgress,
      'last_read_page': lastReadPage,
      'last_read_date': lastReadDate?.millisecondsSinceEpoch,
    };
  }

  Book copyWith({
    int? id,
    String? title,
    String? author,
    String? description,
    String? coverUrl,
    String? localCoverPath,
    String? category,
    String? pdfUrl,
    String? pdfPath,
    String? localPdfPath,
    int? pages,
    List<String>? tags,
    bool? isFavorite,
    double? rating,
    int? downloadProgress,
    int? lastReadPage,
    DateTime? lastReadDate,
  }) {
    return Book(
      id: id ?? this.id,
      title: title ?? this.title,
      author: author ?? this.author,
      description: description ?? this.description,
      coverUrl: coverUrl ?? this.coverUrl,
      localCoverPath: localCoverPath ?? this.localCoverPath,
      category: category ?? this.category,
      pdfUrl: pdfUrl ?? this.pdfUrl,
      pdfPath: pdfPath ?? this.pdfPath,
      localPdfPath: localPdfPath ?? this.localPdfPath,
      pages: pages ?? this.pages,
      tags: tags ?? this.tags,
      isFavorite: isFavorite ?? this.isFavorite,
      rating: rating ?? this.rating,
      downloadProgress: downloadProgress ?? this.downloadProgress,
      lastReadPage: lastReadPage ?? this.lastReadPage,
      lastReadDate: lastReadDate ?? this.lastReadDate,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Book && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // طريقة مساعدة للتحقق من اكتمال تنزيل الكتاب
  bool get isDownloaded =>
      downloadProgress >= 100 ||
      (localPdfPath != null && localPdfPath!.isNotEmpty) ||
      (pdfPath != null && pdfPath!.isNotEmpty);

  // طريقة مساعدة للتحقق من وجود ملف PDF محلي
  bool get hasLocalPdf =>
      (localPdfPath != null && localPdfPath!.isNotEmpty) ||
      (pdfPath != null && pdfPath!.isNotEmpty);

  // طريقة مساعدة للتحقق من إمكانية تنزيل الكتاب
  bool get canDownload => pdfUrl.isNotEmpty && !isDownloaded;

  // طريقة مساعدة للتحقق من إمكانية قراءة الكتاب
  bool get canRead => isDownloaded || pdfUrl.isNotEmpty;

  // طريقة مساعدة للحصول على مسار الملف المحلي (إن وجد)
  String? get effectiveLocalPath =>
      localPdfPath?.isNotEmpty == true ? localPdfPath : pdfPath;
}
