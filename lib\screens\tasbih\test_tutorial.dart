import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// دالة لإعادة تعيين حالة الشرح التوضيحي للمسبحة
/// تستخدم فقط للاختبار
Future<void> resetTasbihTutorialState(BuildContext context) async {
  final prefs = await SharedPreferences.getInstance();
  await prefs.setBool('tasbih_tutorial_shown', false);
  
  if (context.mounted) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إعادة تعيين حالة الشرح التوضيحي للمسبحة'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
