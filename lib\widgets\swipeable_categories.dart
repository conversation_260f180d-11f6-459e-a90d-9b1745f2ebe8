// استيراد الحزم الأساسية اللازمة
import 'package:flutter/material.dart'; // حزمة المواد الأساسية لبناء واجهات المستخدم
import 'package:flutter/services.dart'; // حزمة للوصول إلى خدمات النظام مثل الاهتزاز (HapticFeedback)
import 'dart:math' as math; // حزمة الرياضيات لاستخدامها في حسابات الأنيميشن (مثل قيمة باي 'pi')
import '../widgets/horizontal_category_card.dart'; // استيراد ويدجت بطاقة الفئة المخصصة

// تعريف ويدجت Stateful (قابل لتغيير الحالة) لعرض الفئات القابلة للتمرير
class SwipeableCategories extends StatefulWidget {
  // تعريف المتغيرات النهائية التي يتم تمريرها إلى الويدجت
  final List<CategoryItem> categories; // قائمة بعناصر الفئات التي سيتم عرضها
  final bool isDarkMode; // لتحديد ما إذا كان التطبيق في الوضع الداكن
  final Function(int) onPageChanged; // دالة تُستدعى عند تغيير الصفحة (لتحديث الواجهة الرئيسية مثلاً)

  // المُنشئ (Constructor) الخاص بالويدجت
  const SwipeableCategories({
    Key? key,
    required this.categories,
    required this.isDarkMode,
    required this.onPageChanged,
  }) : super(key: key);

  @override
  // إنشاء كلاس الحالة (State) المرتبط بهذا الويدجت
  State<SwipeableCategories> createState() => _SwipeableCategoriesState();
}

// كلاس الحالة (State) الخاص بويدجت SwipeableCategories
class _SwipeableCategoriesState extends State<SwipeableCategories>
    with SingleTickerProviderStateMixin { // استخدام Mixin لدعم الأنيميشن
  // تعريف متغيرات الحالة
  late PageController _pageController; // متحكم في PageView للتحكم في التمرير والصفحة الحالية
  late AnimationController _animationController; // متحكم في الأنيميشن لتأثيرات النبض
  int _currentPage = 0; // لتخزين فهرس (index) الصفحة المعروضة حالياً
  double _currentPageValue = 0.0; // لتخزين قيمة الصفحة الحالية بدقة (تتضمن الكسور أثناء التمرير)

  @override
  // دالة تُستدعى مرة واحدة عند إنشاء الويدجت
  void initState() {
    super.initState();
    // إعداد PageController
    _pageController = PageController(
      // viewportFraction يحدد عرض كل صفحة كنسبة من عرض الشاشة (هنا 85%)
      // هذا يجعل جزءاً من الصفحتين المجاورتين يظهر على الشاشة
      viewportFraction: 0.85,
      initialPage: _currentPage, // تحديد الصفحة الابتدائية
    );

    // إضافة مستمع (listener) لـ PageController لمراقبة التغييرات أثناء التمرير
    _pageController.addListener(() {
      setState(() {
        // تحديث قيمة الصفحة الحالية باستمرار أثناء التمرير
        _currentPageValue = _pageController.page!;
      });
    });

    // إعداد AnimationController لتأثيرات النبض في مؤشرات الصفحات
    _animationController = AnimationController(
      vsync: this, // مزامنة الأنيميشن مع معدل تحديث الشاشة
      duration: const Duration(milliseconds: 300), // مدة الأنيميشن
    );
  }

  @override
  // دالة تُستدعى عند إزالة الويدجت من الشاشة لتنظيف الموارد
  void dispose() {
    _pageController.dispose(); // التخلص من PageController
    _animationController.dispose(); // التخلص من AnimationController
    super.dispose();
  }

  // دالة تُستدعى عند استقرار PageView على صفحة جديدة
  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page; // تحديث فهرس الصفحة الحالية
    });
    widget.onPageChanged(page); // استدعاء الدالة الممررة من الويدجت الأب

    HapticFeedback.selectionClick(); // إضافة تأثير اهتزاز خفيف للتأكيد على التغيير
    _animationController.reset(); // إعادة تعيين الأنيميشن
    _animationController.forward(); // بدء تشغيل الأنيميشن من جديد (لتأثير النبض)
  }

  @override
  // دالة بناء واجهة المستخدم
  Widget build(BuildContext context) {
    // استخدام Column لترتيب العناصر بشكل عمودي (البطاقات ثم المؤشرات)
    return Column(
      children: [
        // حاوية لعرض البطاقات القابلة للتمرير
        SizedBox(
          height: MediaQuery.of(context).size.height * 0.22, // ارتفاع نسبي
          // استخدام PageView.builder لبناء الصفحات (البطاقات) بكفاءة
          child: PageView.builder(
            controller: _pageController,
            itemCount: widget.categories.length, // عدد الصفحات = عدد الفئات
            onPageChanged: _onPageChanged,
            physics: const BouncingScrollPhysics(), // تأثير ارتداد عند نهاية التمرير
            // دالة بناء كل صفحة (بطاقة) على حدة
            itemBuilder: (context, index) {
              // --- حسابات الأنيميشن الديناميكي للبطاقات ---
              // هذه الحسابات تعتمد على موقع الصفحة الحالية (_currentPageValue)
              // لإنشاء تأثيرات التصغير، التدوير، الشفافية، والإزاحة للبطاقات المجاورة
              double scale = 1.0;
              double rotate = 0.0;
              double opacity = 1.0;
              double translateX = 0.0;

              // إذا كانت هذه هي الصفحة الحالية أو التي تليها مباشرة
              if (_currentPageValue.floor() == index) {
                // الصفحة الحالية: تكون بحجمها الكامل وتتأثر قليلاً عند بدء سحبها
                scale = 1.0 - (_currentPageValue - index) * 0.2;
                rotate = (_currentPageValue - index) * 0.05;
                opacity = 1.0 - (_currentPageValue - index) * 0.5;
                translateX = (_currentPageValue - index) * 30;
              } else if (_currentPageValue.floor() + 1 == index) {
                // الصفحة التالية: تكون أصغر وتكبر تدريجياً كلما اقتربت من المركز
                scale = 0.8 + (_currentPageValue - _currentPageValue.floor()) * 0.2;
                rotate = -(_currentPageValue - _currentPageValue.floor()) * 0.05;
                opacity = 0.5 + (_currentPageValue - _currentPageValue.floor()) * 0.5;
                translateX = (1 - (_currentPageValue - _currentPageValue.floor())) * 30;
              } else if (_currentPageValue.floor() - 1 == index) {
                // الصفحة السابقة: نفس منطق الصفحة التالية ولكن في الاتجاه المعاكس
                scale = 0.8 + (1 - (_currentPageValue - _currentPageValue.floor() + 1)) * 0.2;
                rotate = (1 - (_currentPageValue - _currentPageValue.floor() + 1)) * 0.05;
                opacity = 0.5 + (1 - (_currentPageValue - _currentPageValue.floor() + 1)) * 0.5;
                translateX = -((1 - (_currentPageValue - _currentPageValue.floor() + 1))) * 30;
              } else {
                // الصفحات البعيدة: تكون بحجم ثابت وأقل شفافية
                scale = 0.8;
                opacity = 0.5;
              }

              final item = widget.categories[index]; // الحصول على بيانات الفئة الحالية

              // استخدام TweenAnimationBuilder لعمل أنيميشن ظهور البطاقات عند تحميل الشاشة
              return TweenAnimationBuilder<double>(
                tween: Tween<double>(begin: 0.0, end: 1.0),
                duration: const Duration(milliseconds: 600),
                curve: Curves.easeOutCubic,
                builder: (context, animValue, child) {
                  // تطبيق سلسلة من التحويلات (Transforms) لإنشاء التأثيرات البصرية
                  return Transform.translate(
                    offset: Offset(0, 30 * (1 - animValue)), // تأثير ظهور من الأسفل
                    child: Opacity(
                      opacity: animValue * opacity, // تأثير ظهور تدريجي مع شفافية التمرير
                      child: Transform.translate(
                        offset: Offset(translateX, 0), // إزاحة أفقية أثناء التمرير
                        child: Transform.scale(
                          scale: scale, // تصغير/تكبير البطاقة
                          child: Transform.rotate(
                            angle: rotate * math.pi, // تدوير البطاقة
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 5.0, vertical: 10.0),
                              // استخدام ويدجت البطاقة المخصص لعرض محتوى الفئة
                              child: HorizontalCategoryCard(
                                title: item.title,
                                subtitle: item.subtitle,
                                svgIcon: item.svgIcon,
                                color: item.color,
                                hasRippleEffect: true,
                                isComingSoon: item.isComingSoon,
                                // تحديد الإجراء عند النقر على البطاقة
                                onTap: () {
                                  if (item.isComingSoon) {
                                    // إذا كان القسم قيد التطوير، أظهر رسالة SnackBar
                                    ScaffoldMessenger.of(context).clearSnackBars();
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          "قسم ${item.title} قيد التطوير، سيتوفر قريباً",
                                          textAlign: TextAlign.center,
                                        ),
                                        duration: const Duration(seconds: 2),
                                        behavior: SnackBarBehavior.floating,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(10),
                                        ),
                                      ),
                                    );
                                  } else {
                                    // إذا كان القسم متاحاً، انتقل إلى المسار المحدد
                                    HapticFeedback.lightImpact(); // اهتزاز خفيف عند النقر
                                    Navigator.of(context).pushNamed(
                                      item.route!,
                                      arguments: {'animation': true},
                                    );
                                  }
                                },
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),

        // مؤشرات الصفحات (النقاط الصغيرة أسفل البطاقات)
        const SizedBox(height: 16), // مسافة فاصلة
        Row(
          mainAxisAlignment: MainAxisAlignment.center, // توسيط المؤشرات أفقياً
          // إنشاء قائمة من المؤشرات بناءً على عدد الفئات
          children: List.generate(
            widget.categories.length,
            (index) => _buildPageIndicator(index), // استدعاء دالة بناء كل مؤشر
          ),
        ),
      ],
    );
  }

  // دالة لبناء ويدجت مؤشر الصفحة الواحدة
  Widget _buildPageIndicator(int index) {
    final isCurrentPage = index == _currentPage; // التحقق مما إذا كان هذا هو مؤشر الصفحة الحالية
    final color = widget.categories[index].color; // الحصول على لون الفئة المقابلة

    // استخدام AnimatedBuilder لعمل أنيميشن النبض للمؤشر الحالي فقط
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        double size = isCurrentPage ? 10.0 : 8.0; // حجم أكبر للمؤشر الحالي

        // تطبيق تأثير النبض (تكبير وتصغير) على المؤشر الحالي
        if (isCurrentPage) {
          final pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
            CurvedAnimation(
              parent: _animationController,
              curve: Curves.easeInOut,
            ),
          );
          size *= pulseAnimation.value; // تغيير الحجم بناءً على قيمة الأنيميشن
        }

        // بناء حاوية المؤشر
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          width: size,
          height: size,
          decoration: BoxDecoration(
            // استخدام اللون الكامل للمؤشر الحالي، ولون باهت للبقية
            color: isCurrentPage ? color : color.withAlpha(77),
            shape: BoxShape.circle, // شكل دائري
            // إضافة ظل للمؤشر الحالي فقط لإبرازه
            boxShadow: isCurrentPage
                ? [
                    BoxShadow(
                      color: color.withAlpha(77),
                      blurRadius: 4,
                      spreadRadius: 1,
                    ),
                  ]
                : null,
          ),
        );
      },
    );
  }
}

// كلاس لنمذجة بيانات كل عنصر فئة (Category)
class CategoryItem {
  final String title;
  final String? subtitle;
  final String svgIcon; // تم التعديل ليقبل مسار SVG
  final Color color;
  final String? route;
  final bool isComingSoon;

  // المُنشئ الخاص بنموذج البيانات
  CategoryItem({
    required this.title,
    this.subtitle,
    required this.svgIcon, // تم التعديل هنا
    required this.color,
    this.route,
    this.isComingSoon = false,
  });
}
