import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;
import '../widgets/horizontal_category_card.dart';

class SwipeableCategories extends StatefulWidget {
  final List<CategoryItem> categories;
  final bool isDarkMode;
  final Function(int) onPageChanged;

  const SwipeableCategories({
    Key? key,
    required this.categories,
    required this.isDarkMode,
    required this.onPageChanged,
  }) : super(key: key);

  @override
  State<SwipeableCategories> createState() => _SwipeableCategoriesState();
}

class _SwipeableCategoriesState extends State<SwipeableCategories>
    with SingleTickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  int _currentPage = 0;
  double _currentPageValue = 0.0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(
      viewportFraction: 0.85,
      initialPage: _currentPage,
    );

    _pageController.addListener(() {
      setState(() {
        _currentPageValue = _pageController.page!;
      });
    });

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
    widget.onPageChanged(page);

    // تأثير اهتزاز خفيف عند تغيير الصفحة
    HapticFeedback.selectionClick();

    // تشغيل الأنيميشن
    _animationController.reset();
    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // عرض البطاقات القابلة للتمرير
        SizedBox(
          height:
              MediaQuery.of(context).size.height * 0.22, // ارتفاع نسبي للشاشة
          child: PageView.builder(
            controller: _pageController,
            itemCount: widget.categories.length,
            onPageChanged: _onPageChanged,
            physics: const BouncingScrollPhysics(),
            itemBuilder: (context, index) {
              // حساب مقدار التحويل والتكبير/التصغير
              double scale = 1.0;
              double rotate = 0.0;
              double opacity = 1.0;
              double translateX = 0.0;

              if (_currentPageValue.floor() == index) {
                // الصفحة الحالية
                scale = 1.0 - (_currentPageValue - index) * 0.2;
                rotate = (_currentPageValue - index) * 0.05;
                opacity = 1.0 - (_currentPageValue - index) * 0.5;
                translateX = (_currentPageValue - index) * 30;
              } else if (_currentPageValue.floor() + 1 == index) {
                // الصفحة التالية
                scale =
                    0.8 + (_currentPageValue - _currentPageValue.floor()) * 0.2;
                rotate =
                    -(_currentPageValue - _currentPageValue.floor()) * 0.05;
                opacity =
                    0.5 + (_currentPageValue - _currentPageValue.floor()) * 0.5;
                translateX =
                    (1 - (_currentPageValue - _currentPageValue.floor())) * 30;
              } else if (_currentPageValue.floor() - 1 == index) {
                // الصفحة السابقة
                scale = 0.8 +
                    (1 - (_currentPageValue - _currentPageValue.floor() + 1)) *
                        0.2;
                rotate =
                    (1 - (_currentPageValue - _currentPageValue.floor() + 1)) *
                        0.05;
                opacity = 0.5 +
                    (1 - (_currentPageValue - _currentPageValue.floor() + 1)) *
                        0.5;
                translateX = -((1 -
                        (_currentPageValue - _currentPageValue.floor() + 1))) *
                    30;
              } else {
                // الصفحات البعيدة
                scale = 0.8;
                opacity = 0.5;
              }

              final item = widget.categories[index];

              return TweenAnimationBuilder<double>(
                tween: Tween<double>(begin: 0.0, end: 1.0),
                duration: const Duration(milliseconds: 600),
                curve: Curves.easeOutCubic,
                builder: (context, animValue, child) {
                  return Transform.translate(
                    offset: Offset(0, 30 * (1 - animValue)),
                    child: Opacity(
                      opacity: animValue * opacity,
                      child: Transform.translate(
                        offset: Offset(translateX, 0),
                        child: Transform.scale(
                          scale: scale,
                          child: Transform.rotate(
                            angle: rotate * math.pi,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 5.0, vertical: 10.0),
                              child: HorizontalCategoryCard(
                                title: item.title,
                                subtitle: item.subtitle,
                                icon: item.icon,
                                color: item.color,
                                hasRippleEffect: true,
                                isComingSoon: item.isComingSoon,
                                onTap: () {
                                  if (item.isComingSoon) {
                                    ScaffoldMessenger.of(context)
                                        .clearSnackBars();
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          "قسم ${item.title} قيد التطوير، سيتوفر قريباً",
                                          textAlign: TextAlign.center,
                                        ),
                                        duration: const Duration(seconds: 2),
                                        behavior: SnackBarBehavior.floating,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                        ),
                                      ),
                                    );
                                  } else {
                                    // تأثير اهتزاز خفيف عند النقر
                                    HapticFeedback.lightImpact();

                                    // التنقل إلى الشاشة المطلوبة
                                    Navigator.of(context).pushNamed(
                                      item.route!,
                                      arguments: {
                                        'animation': true,
                                      },
                                    );
                                  }
                                },
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),

        // مؤشرات الصفحات
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            widget.categories.length,
            (index) => _buildPageIndicator(index),
          ),
        ),
      ],
    );
  }

  // بناء مؤشر الصفحة
  Widget _buildPageIndicator(int index) {
    final isCurrentPage = index == _currentPage;
    final color = widget.categories[index].color;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        double size = isCurrentPage ? 10.0 : 8.0;

        // تأثير نبض للمؤشر الحالي
        if (isCurrentPage) {
          final pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
            CurvedAnimation(
              parent: _animationController,
              curve: Curves.easeInOut,
            ),
          );
          size *= pulseAnimation.value;
        }

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          width: size,
          height: size,
          decoration: BoxDecoration(
            color:
                isCurrentPage ? color : color.withAlpha(77), // 0.3 * 255 = ~77
            shape: BoxShape.circle,
            boxShadow: isCurrentPage
                ? [
                    BoxShadow(
                      color: color.withAlpha(77), // 0.3 * 255 = ~77
                      blurRadius: 4,
                      spreadRadius: 1,
                    ),
                  ]
                : null,
          ),
        );
      },
    );
  }
}

// نموذج بيانات العنصر
class CategoryItem {
  final String title;
  final String? subtitle;
  final IconData icon;
  final Color color;
  final String? route;
  final bool isComingSoon;

  CategoryItem({
    required this.title,
    this.subtitle,
    required this.icon,
    required this.color,
    this.route,
    this.isComingSoon = false,
  });
}
