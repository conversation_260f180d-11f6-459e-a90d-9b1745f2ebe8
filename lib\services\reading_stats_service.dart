import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import '../models/book.dart';

class ReadingStatsService {
  static const String _totalReadTimeKey = 'total_read_time';
  static const String _dailyGoalKey = 'daily_goal_minutes';
  static const String _booksOpenedKey = 'books_opened';
  static const String _lastReadDateKey = 'last_read_date';
  static const String _currentStreakKey = 'current_streak';
  static const String _longestStreakKey = 'longest_streak';

  // إضافة وقت القراءة
  Future<void> addReadingTime(int minutes) async {
    final prefs = await SharedPreferences.getInstance();
    final int currentTotal = prefs.getInt(_totalReadTimeKey) ?? 0;
    await prefs.setInt(_totalReadTimeKey, currentTotal + minutes);

    // تحديث سجل القراءة اليومي
    await _updateReadingStreak();
  }

  // تحديث سلسلة أيام القراءة المتتالية
  Future<void> _updateReadingStreak() async {
    final prefs = await SharedPreferences.getInstance();
    final String today = DateTime.now().toIso8601String().split('T')[0];
    final String? lastReadDate = prefs.getString(_lastReadDateKey);

    if (lastReadDate == null) {
      // أول مرة قراءة
      await prefs.setString(_lastReadDateKey, today);
      await prefs.setInt(_currentStreakKey, 1);
      await prefs.setInt(_longestStreakKey, 1);
      return;
    }

    final DateTime lastDate = DateTime.parse(lastReadDate);
    final DateTime todayDate = DateTime.parse(today);
    final difference = todayDate.difference(lastDate).inDays;

    if (difference == 0) {
      // نفس اليوم، لا تغيير في السلسلة
      return;
    } else if (difference == 1) {
      // يوم متتالي
      final int currentStreak = prefs.getInt(_currentStreakKey) ?? 0;
      final int newStreak = currentStreak + 1;
      await prefs.setInt(_currentStreakKey, newStreak);

      // تحديث أطول سلسلة إذا لزم الأمر
      final int longestStreak = prefs.getInt(_longestStreakKey) ?? 0;
      if (newStreak > longestStreak) {
        await prefs.setInt(_longestStreakKey, newStreak);
      }
    } else {
      // انقطاع السلسلة، إعادة البدء
      await prefs.setInt(_currentStreakKey, 1);
    }

    await prefs.setString(_lastReadDateKey, today);
  }

  // تعيين هدف القراءة اليومي
  Future<void> setDailyGoal(int minutes) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_dailyGoalKey, minutes);
  }

  // تسجيل فتح كتاب
  Future<void> recordBookOpened(Book book) async {
    final prefs = await SharedPreferences.getInstance();
    final List<String> openedBooks = prefs.getStringList(_booksOpenedKey) ?? [];

    if (!openedBooks.contains(book.id.toString())) {
      openedBooks.add(book.id.toString());
      await prefs.setStringList(_booksOpenedKey, openedBooks);
    }
  }

  // الحصول على عدد الكتب المقروءة
  Future<int> getBooksOpenedCount() async {
    final prefs = await SharedPreferences.getInstance();
    final List<String> openedBooks = prefs.getStringList(_booksOpenedKey) ?? [];
    return openedBooks.length;
  }

  // الحصول على إجمالي وقت القراءة
  Future<int> getTotalReadingTime() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_totalReadTimeKey) ?? 0;
  }

  // الحصول على الهدف اليومي
  Future<int> getDailyGoal() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_dailyGoalKey) ?? 30; // 30 دقيقة افتراضية
  }

  // الحصول على السلسلة الحالية
  Future<int> getCurrentStreak() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_currentStreakKey) ?? 0;
  }

  // الحصول على أطول سلسلة
  Future<int> getLongestStreak() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_longestStreakKey) ?? 0;
  }

  // مشاركة إحصائيات القراءة
  Future<void> shareReadingStats() async {
    final totalTime = await getTotalReadingTime();
    final booksOpened = await getBooksOpenedCount();
    final currentStreak = await getCurrentStreak();
    final longestStreak = await getLongestStreak();

    final String statsText = '''
إحصائيات القراءة الخاصة بي من تطبيق وهج السالك:

📚 قرأت $booksOpened كتب
⏱ قضيت ${totalTime ~/ 60} ساعة و ${totalTime % 60} دقيقة في القراءة
🔥 سلسلة القراءة الحالية: $currentStreak يوم
🏆 أطول سلسلة قراءة: $longestStreak يوم

قم بتحميل تطبيق وهج السالك واستمتع بالقراءة!
''';

    await Share.share(statsText);
  }
}
