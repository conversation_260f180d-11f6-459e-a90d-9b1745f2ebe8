class Azkar {
  final int id;
  final String category;
  final String content;
  final int? count;

  Azkar({
    required this.id,
    required this.category,
    required this.content,
    this.count,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'category': category,
      'content': content,
      'count': count,
    };
  }

  factory Azkar.fromMap(Map<String, dynamic> map) {
    return Azkar(
      id: map['id'],
      category: map['category'],
      content: map['content'],
      count: map['count'],
    );
  }
}