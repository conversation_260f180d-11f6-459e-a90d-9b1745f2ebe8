import 'package:flutter/material.dart';
import 'dart:math' as math;

class HorizontalCategoryCard extends StatefulWidget {
  final String title;
  final IconData icon;
  final Color color;
  final VoidCallback? onTap;
  final bool hasRippleEffect;
  final bool isComingSoon;
  final String? subtitle;

  const HorizontalCategoryCard({
    Key? key,
    required this.title,
    required this.icon,
    required this.color,
    this.onTap,
    this.hasRippleEffect = false,
    this.isComingSoon = false,
    this.subtitle,
  }) : super(key: key);

  @override
  State<HorizontalCategoryCard> createState() => _HorizontalCategoryCardState();
}

class _HorizontalCategoryCardState extends State<HorizontalCategoryCard>
    with SingleTickerProviderStateMixin {
  bool _isPressed = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotateAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.97).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic,
      ),
    );

    _rotateAnimation = Tween<double>(begin: 0.0, end: 0.01).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTapDown: (_) {
        if (widget.onTap != null) {
          setState(() {
            _isPressed = true;
          });
          _animationController.forward();
        }
      },
      onTapUp: (_) {
        if (widget.onTap != null) {
          setState(() {
            _isPressed = false;
          });
          _animationController.reverse();

          // حفظ مرجع لدالة onTap
          final VoidCallback? onTapFunction = widget.onTap;

          // استخدام addPostFrameCallback لتجنب تحديث الحالة أثناء مرحلة البناء
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted && onTapFunction != null) {
              onTapFunction();
            }
          });
        }
      },
      onTapCancel: () {
        if (widget.onTap != null) {
          setState(() {
            _isPressed = false;
          });
          _animationController.reverse();
        }
      },
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Transform.rotate(
              angle: _rotateAnimation.value * math.pi,
              child: Container(
                height: MediaQuery.of(context).size.height *
                    0.15, // ارتفاع نسبي للشاشة
                margin: const EdgeInsets.symmetric(vertical: 8.0),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topRight,
                    end: Alignment.bottomLeft,
                    colors: [
                      widget.color
                          .withAlpha(isDarkMode ? 51 : 25), // 0.2/0.1 * 255
                      widget.color
                          .withAlpha(isDarkMode ? 77 : 38), // 0.3/0.15 * 255
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: widget.color
                        .withAlpha(isDarkMode ? 102 : 77), // 0.4/0.3 * 255
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: widget.color.withAlpha(25), // 0.1 * 255
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Stack(
                  children: [
                    // محتوى البطاقة
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          // أيقونة القسم
                          Container(
                            width: 70,
                            height: 70,
                            decoration: BoxDecoration(
                              color: widget.color.withAlpha(
                                  isDarkMode ? 51 : 26), // 0.2/0.1 * 255
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color:
                                      widget.color.withAlpha(38), // 0.15 * 255
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Icon(
                              widget.icon,
                              size: 36,
                              color: widget.color,
                            ),
                          ),
                          const SizedBox(width: 16),

                          // عنوان ووصف القسم
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  widget.title,
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 20,
                                    color: isDarkMode
                                        ? Colors.white
                                        : Colors.black87,
                                  ),
                                ),
                                if (widget.subtitle != null) ...[
                                  const SizedBox(height: 6),
                                  Text(
                                    widget.subtitle!,
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: isDarkMode
                                          ? Colors.white
                                              .withAlpha(179) // 0.7 * 255
                                          : Colors.black87
                                              .withAlpha(179), // 0.7 * 255
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ],
                            ),
                          ),

                          // سهم للانتقال
                          if (!widget.isComingSoon)
                            Icon(
                              Icons.arrow_forward_ios_rounded,
                              size: 18,
                              color: widget.color,
                            ),
                        ],
                      ),
                    ),

                    // شارة "قريباً" إذا كان القسم غير متاح
                    if (widget.isComingSoon)
                      Positioned(
                        top: 12,
                        left: 12,
                        child: TweenAnimationBuilder<double>(
                          tween: Tween<double>(begin: 0.9, end: 1.1),
                          duration: const Duration(seconds: 1),
                          curve: Curves.easeInOut,
                          builder: (context, scale, child) {
                            return Transform.scale(
                              scale: scale,
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: isDarkMode
                                      ? Colors.black
                                          .withAlpha(138) // 0.54 * 255
                                      : Colors.white
                                          .withAlpha(204), // 0.8 * 255
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black
                                          .withAlpha(26), // 0.1 * 255
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.access_time_rounded,
                                      color: Colors.amber,
                                      size: 12,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      'قريباً',
                                      style: TextStyle(
                                        fontSize: 11,
                                        fontWeight: FontWeight.bold,
                                        color: widget.color,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),

                    // تأثير التموج (إذا كان مفعلاً)
                    if (widget.hasRippleEffect && _isPressed)
                      Positioned.fill(
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(20),
                          child: CustomPaint(
                            painter: RipplePainter(
                              color: widget.color.withAlpha(77), // 0.3 * 255
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

// رسم تأثير التموج
class RipplePainter extends CustomPainter {
  final Color color;

  RipplePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2.5;

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
