// استيراد الحزم الأساسية اللازمة لبناء واجهات المستخدم في فلاتر
import 'package:flutter/material.dart';
// استيراد مكتبة الرياضيات لاستخدامها في عمليات حسابية مثل التدوير (pi)
import 'dart:math' as math;
// استيراد حزمة flutter_svg لعرض الصور بصيغة SVG
import 'package:flutter_svg/flutter_svg.dart';

// تعريف ويدجت (عنصر واجهة) مخصص لعرض بطاقة قسم بشكل أفقي
class HorizontalCategoryCard extends StatefulWidget {
  // تعريف المتغيرات النهائية (final) التي سيمررها المستخدم عند استدعاء الويدجت
  final String title;       // عنوان البطاقة
  final String svgIcon;     // مسار ملف أيقونة SVG
  final Color color;        // اللون الأساسي للبطاقة
  final VoidCallback? onTap; // دالة (وظيفة) تُنفذ عند النقر على البطاقة
  final bool hasRippleEffect; // لتحديد ما إذا كان سيتم عرض تأثير التموج عند الضغط
  final bool isComingSoon;  // لتحديد ما إذا كان القسم "قادم قريباً"
  final String? subtitle;   // العنوان الفرعي للبطاقة (اختياري)

  // المُنشئ (Constructor) الخاص بالويدجت، يتطلب تمرير المتغيرات الأساسية
  const HorizontalCategoryCard({
    Key? key,
    required this.title,
    required this.svgIcon,
    required this.color,
    this.onTap,
    this.hasRippleEffect = false,
    this.isComingSoon = false,
    this.subtitle,
  }) : super(key: key);

  @override
  // إنشاء حالة (State) للويدجت، مما يجعله قابلاً للتغيير
  State<HorizontalCategoryCard> createState() => _HorizontalCategoryCardState();
}

// تعريف كلاس الحالة (State) الخاص بالويدجت، مع استخدام SingleTickerProviderStateMixin لدعم الأنيميشن
class _HorizontalCategoryCardState extends State<HorizontalCategoryCard>
    with SingleTickerProviderStateMixin {
  // متغيرات الحالة (State Variables)
  bool _isPressed = false; // لتتبع ما إذا كانت البطاقة مضغوطة حالياً
  late AnimationController _animationController; // للتحكم في الأنيميشن
  late Animation<double> _scaleAnimation;      // أنيميشن لتغيير حجم البطاقة (تصغيرها)
  late Animation<double> _rotateAnimation;     // أنيميشن لتدوير البطاقة بشكل طفيف

  @override
  // دالة تُستدعى مرة واحدة عند إنشاء الويدجت لأول مرة
  void initState() {
    super.initState();
    // إعداد AnimationController وتحديد مدة الأنيميشن (300 ميلي ثانية)
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // إعداد أنيميشن التصغير: يبدأ من حجم 1.0 وينتهي عند 0.97
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.97).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic, // تأثير تسارع وتباطؤ للأنيميشن
      ),
    );

    // إعداد أنيميشن التدوير: يبدأ من زاوية 0.0 وينتهي عند 0.01 (جزء صغير من الدورة الكاملة)
    _rotateAnimation = Tween<double>(begin: 0.0, end: 0.01).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  // دالة تُستدعى عند إزالة الويدجت من الشاشة لتنظيف الموارد
  void dispose() {
    _animationController.dispose(); // التخلص من controller لمنع تسرب الذاكرة
    super.dispose();
  }

  @override
  // دالة بناء واجهة المستخدم (UI) الخاصة بالويدجت
  Widget build(BuildContext context) {
    // التحقق مما إذا كان التطبيق في الوضع الداكن أم الفاتح
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // استخدام GestureDetector لاكتشاف إيماءات المستخدم (النقر)
    return GestureDetector(
      // عند بدء الضغط على البطاقة
      onTapDown: (_) {
        if (widget.onTap != null) {
          setState(() {
            _isPressed = true; // تحديث الحالة لتفعيل تأثير الضغط
          });
          _animationController.forward(); // بدء تشغيل الأنيميشن
        }
      },
      // عند رفع الإصبع بعد الضغط
      onTapUp: (_) {
        if (widget.onTap != null) {
          setState(() {
            _isPressed = false; // إلغاء تفعيل تأثير الضغط
          });
          _animationController.reverse(); // عكس الأنيميشن للعودة للحالة الطبيعية
          final VoidCallback? onTapFunction = widget.onTap;
          // تنفيذ دالة onTap بعد انتهاء بناء الإطار الحالي لضمان سلاسة الأداء
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted && onTapFunction != null) {
              onTapFunction();
            }
          });
        }
      },
      // عند إلغاء الضغط (مثلاً بسحب الإصبع خارج البطاقة)
      onTapCancel: () {
        if (widget.onTap != null) {
          setState(() {
            _isPressed = false;
          });
          _animationController.reverse();
        }
      },
      // استخدام AnimatedBuilder لإعادة بناء الجزء المتحرك فقط من الواجهة
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          // تطبيق تأثير التصغير والتدوير على البطاقة
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Transform.rotate(
              angle: _rotateAnimation.value * math.pi,
              child: Container(
                // تحديد ارتفاع نسبي للبطاقة بناءً على ارتفاع الشاشة
                height: MediaQuery.of(context).size.height * 0.15,
                margin: const EdgeInsets.symmetric(vertical: 8.0),
                // تصميم وتزيين الحاوية (البطاقة)
                decoration: BoxDecoration(
                  // إضافة تدرج لوني للخلفية
                  gradient: LinearGradient(
                    begin: Alignment.topRight,
                    end: Alignment.bottomLeft,
                    colors: [
                      widget.color.withAlpha(isDarkMode ? 51 : 25),
                      widget.color.withAlpha(isDarkMode ? 77 : 38),
                    ],
                  ),
                  // تحديد حواف دائرية للبطاقة
                  borderRadius: BorderRadius.circular(20),
                  // إضافة إطار (حد) حول البطاقة
                  border: Border.all(
                    color: widget.color.withAlpha(isDarkMode ? 102 : 77),
                    width: 1.5,
                  ),
                  // إضافة ظل للبطاقة لإعطائها عمقاً
                  boxShadow: [
                    BoxShadow(
                      color: widget.color.withAlpha(25),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                // استخدام Stack لوضع العناصر فوق بعضها (مثل شارة "قريباً")
                child: Stack(
                  children: [
                    // المحتوى الرئيسي للبطاقة
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          // حاوية الأيقونة الدائرية
                          Container(
                            width: 70,
                            height: 70,
                            decoration: BoxDecoration(
                              color: widget.color.withAlpha(isDarkMode ? 51 : 26),
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: widget.color.withAlpha(38),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            // عرض أيقونة SVG
                            child: SvgPicture.asset(
                              widget.svgIcon, // مسار ملف SVG
                              width: 37,
                              height: 37,
                              // استخدام colorFilter لتلوين أيقونة SVG بنفس لون البطاقة
                              colorFilter: ColorFilter.mode(
                                widget.color,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                          const SizedBox(width: 16), // مسافة فاصلة

                          // عمود لعرض العنوان والعنوان الفرعي
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  widget.title,
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 20,
                                    color: isDarkMode ? Colors.white : Colors.black87,
                                  ),
                                ),
                                // عرض العنوان الفرعي فقط إذا كان موجوداً
                                if (widget.subtitle != null) ...[
                                  const SizedBox(height: 6),
                                  Text(
                                    widget.subtitle!,
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: isDarkMode
                                          ? Colors.white
                                              .withAlpha(179) // 0.7 * 255
                                          : Colors.black87
                                              .withAlpha(179), // 0.7 * 255
                                    ),                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ],
                            ),
                          ),

                          // عرض سهم الانتقال فقط إذا لم يكن القسم "قادم قريباً"
                          if (!widget.isComingSoon)
                            Icon(
                              Icons.arrow_forward_ios_rounded,
                              size: 18,
                              color: widget.color,
                            ),
                        ],
                      ),
                    ),

                    // عرض شارة "قريباً" إذا كان القسم غير متاح
                    if (widget.isComingSoon)
                      Positioned(
                        top: 12,
                        left: 12,
                        child: TweenAnimationBuilder<double>(
                          tween: Tween<double>(begin: 0.9, end: 1.1),
                          duration: const Duration(seconds: 1),
                          curve: Curves.easeInOut,
                          builder: (context, scale, child) {
                            return Transform.scale(
                              scale: scale,
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: isDarkMode
                                      ? Colors.black.withAlpha(138)
                                      : Colors.white.withAlpha(204),
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withAlpha(26),
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.access_time_rounded,
                                      color: Colors.amber,
                                      size: 12,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      'قريباً',
                                      style: TextStyle(
                                        fontSize: 11,
                                        fontWeight: FontWeight.bold,
                                        color: widget.color,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),

                    // عرض تأثير التموج (Ripple) إذا كان مفعلاً ومضغوطاً
                    if (widget.hasRippleEffect && _isPressed)
                      Positioned.fill(
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(20),
                          child: CustomPaint(
                            painter: RipplePainter(
                              color: widget.color.withAlpha(77),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

// كلاس مخصص لرسم تأثير التموج الدائري
class RipplePainter extends CustomPainter {
  final Color color;

  RipplePainter({required this.color});

  @override
  // دالة الرسم على الـ Canvas
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2.5;

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius, paint);
  }

  @override
  // تحديد ما إذا كان يجب إعادة الرسم (هنا دائماً يعيد الرسم)
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
