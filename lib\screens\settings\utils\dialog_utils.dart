// ملف للأدوات المساعدة (الحوارات)

import 'package:flutter/material.dart';

class DialogUtils {
  // عرض حوار مخصص
  static void showCustomDialog(
    BuildContext context, {
    required String title,
    required String content,
    List<Widget>? actions,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(content),
          actions: actions,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        );
      },
    );
  }
}