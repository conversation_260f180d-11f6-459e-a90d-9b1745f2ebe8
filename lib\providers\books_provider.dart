import 'package:flutter/material.dart';
import '../models/book.dart';
import '../database/database_helper.dart';

class BooksProvider extends ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  List<Book> _books = [];
  List<Book> _favorites = [];
  bool _isLoading = false;
  String _errorMessage = '';

  List<Book> get books => _books;
  List<Book> get favorites => _favorites;
  bool get isLoading => _isLoading;
  String get errorMessage => _errorMessage;

  // تحميل جميع الكتب
  Future<void> loadBooks() async {
    _isLoading = true;
    _errorMessage = '';
    notifyListeners();

    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> bookMaps = await db.query('books');

      _books = _convertMapsToBooks(bookMaps);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'فشل تحميل الكتب: $e';
      notifyListeners();
    }
  }

  // تحميل الكتب المفضلة
  Future<void> loadFavorites() async {
    try {
      final favoritesData = await _databaseHelper.getFavoriteBooks();

      // استرجاع الكتب المفضلة
      List<Book> favBooks = [];
      for (var fav in favoritesData) {
        final bookId = fav['item_id'];
        final bookData = await _databaseHelper.getBookById(bookId);

        if (bookData != null) {
          List<String> tags = [];
          if (bookData['tags'] != null &&
              bookData['tags'].toString().isNotEmpty) {
            tags = bookData['tags'].toString().split(', ');
          }

          favBooks.add(Book(
            id: bookId,
            title: bookData['title'] ?? '',
            author: bookData['author'] ?? '',
            description: bookData['description'] ?? '',
            coverUrl: bookData['cover_url'] ?? '',
            localCoverPath: bookData['local_cover_path'],
            category: bookData['category'] ?? '',
            pdfUrl: bookData['pdf_url'] ?? '',
            pdfPath: bookData['pdf_path'],
            localPdfPath: bookData['local_pdf_path'],
            pages: bookData['pages'] ?? 0,
            tags: tags,
          ));
        }
      }

      _favorites = favBooks;
      notifyListeners();
    } catch (e) {
      _errorMessage = 'فشل تحميل المفضلة: $e';
      notifyListeners();
    }
  }

  // إضافة إلى المفضلة
  Future<void> toggleFavorite(Book book) async {
    try {
      // التحقق إذا كان الكتاب مفضلاً بالفعل
      final isCurrentlyFavorite = _favorites.any((b) => b.id == book.id);

      if (isCurrentlyFavorite) {
        await _databaseHelper.removeFromFavorites(book.id);
        _favorites.removeWhere((b) => b.id == book.id);
      } else {
        await _databaseHelper.addToFavorites(book.id);
        _favorites.add(book);
      }

      notifyListeners();
    } catch (e) {
      _errorMessage = 'خطأ في تحديث المفضلة: $e';
      notifyListeners();
    }
  }

  // البحث عن الكتب
  Future<List<Book>> searchBooks(String query) async {
    if (query.isEmpty) return _books;

    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> results = await db.query(
        'books',
        where: 'title LIKE ? OR author LIKE ? OR description LIKE ?',
        whereArgs: ['%$query%', '%$query%', '%$query%'],
      );

      return _convertMapsToBooks(results);
    } catch (e) {
      _errorMessage = 'خطأ في البحث: $e';
      notifyListeners();
      return [];
    }
  }

  // تحويل نتائج قاعدة البيانات إلى كائنات Book
  List<Book> _convertMapsToBooks(List<Map<String, dynamic>> maps) {
    return maps.map((map) {
      List<String> tags = [];
      if (map['tags'] != null && map['tags'].toString().isNotEmpty) {
        tags = map['tags'].toString().split(', ');
      }

      return Book(
        id: int.tryParse(map['id'].toString().replaceAll('b', '')) ?? 0,
        title: map['title'] ?? '',
        author: map['author'] ?? '',
        description: map['description'] ?? '',
        coverUrl: map['cover_url'] ?? '',
        localCoverPath: map['local_cover_path'],
        category: map['category'] ?? '',
        pdfUrl: map['pdf_url'] ?? '',
        pdfPath: map['pdf_path'],
        localPdfPath: map['local_pdf_path'],
        pages: map['pages'] ?? 0,
        tags: tags,
      );
    }).toList();
  }
}
