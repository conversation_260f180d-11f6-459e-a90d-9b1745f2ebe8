import 'package:flutter/material.dart';

class AppTheme {
  // Light theme colors
  static const Color _lightPrimaryColor = Color(0xFF1F4E79); // Deep blue
  static const Color _lightPrimaryVariantColor =
      Color(0xFF2C5F8E); // Slightly lighter blue
  static const Color _lightSecondaryColor = Color(0xFFD4AF37); // Gold
  static const Color _lightSecondaryVariantColor =
      Color(0xFFE5C158); // Lighter gold
  static const Color _lightBackgroundColor = Color(0xFFF8F8F8); // Off-white
  static const Color _lightSurfaceColor = Color(0xFFFFFFFF); // Pure white
  static const Color _lightAccentColor = Color(0xFFE67E22); // Orange

  // Islamic pattern colors
  static const Color _patternLightColor =
      Color(0xFFEAE0C8); // Light beige for patterns
  static const Color _patternDarkColor =
      Color(0xFF3D3522); // Dark brown for patterns

  // Dark theme colors
  static const Color _darkPrimaryColor = Color(0xFF0D2B4B); // Darker blue
  static const Color _darkPrimaryVariantColor =
      Color(0xFF1A3A5A); // Slightly lighter dark blue
  static const Color _darkSecondaryColor = Color(0xFFBF9B30); // Darker gold
  static const Color _darkSecondaryVariantColor =
      Color(0xFFD4B04A); // Lighter dark gold
  static const Color _darkBackgroundColor = Color(0xFF121212); // Dark grey
  static const Color _darkSurfaceColor =
      Color(0xFF1E1E1E); // Slightly lighter dark grey
  static const Color _darkAccentColor = Color(0xFFD35400); // Darker orange

  // Light theme
  static ThemeData lightTheme(String fontFamily) {
    return ThemeData(
      useMaterial3: true,
      colorScheme: const ColorScheme.light(
        primary: _lightPrimaryColor,
        primaryContainer: _lightPrimaryVariantColor,
        secondary: _lightSecondaryColor,
        secondaryContainer: _lightSecondaryVariantColor,
        tertiary: _lightAccentColor,
        surface: _lightSurfaceColor,
        surfaceContainerHighest:
            _lightBackgroundColor, // استبدال surfaceVariant
        onPrimary: Colors.white,
        onSecondary: Colors.black,
        onSurface: Colors.black87, // استبدال onBackground
        onSurfaceVariant: Colors.black87,
      ),
      pageTransitionsTheme: const PageTransitionsTheme(
        builders: {
          TargetPlatform.android: CupertinoPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
          TargetPlatform.windows: CupertinoPageTransitionsBuilder(),
        },
      ),
      fontFamily: fontFamily,
      textTheme: const TextTheme(
        displayLarge: TextStyle(color: Colors.black87),
        displayMedium: TextStyle(color: Colors.black87),
        displaySmall: TextStyle(color: Colors.black87),
        headlineLarge: TextStyle(color: Colors.black87),
        headlineMedium: TextStyle(color: Colors.black87),
        headlineSmall: TextStyle(color: Colors.black87),
        titleLarge: TextStyle(color: Colors.black87),
        titleMedium: TextStyle(color: Colors.black87),
        titleSmall: TextStyle(color: Colors.black87),
        bodyLarge: TextStyle(color: Colors.black87),
        bodyMedium: TextStyle(color: Colors.black87),
        bodySmall: TextStyle(color: Colors.black87),
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: _lightPrimaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        shadowColor: _lightPrimaryColor.withAlpha(128), // 0.5 * 255 = 128
        centerTitle: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(bottom: Radius.circular(15)),
        ),
        titleTextStyle: TextStyle(
          fontFamily: fontFamily,
          fontSize: 22,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      cardTheme: CardTheme(
        color: Colors.white,
        elevation: 4,
        shadowColor: _lightPrimaryColor.withAlpha(77), // 0.3 * 255 = 77
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
          side: BorderSide(
              color: _lightSecondaryColor.withAlpha(26),
              width: 1), // 0.1 * 255 = 26
        ),
      ),
      scaffoldBackgroundColor: _lightBackgroundColor,
      dialogBackgroundColor: _lightSurfaceColor,
      highlightColor: _patternLightColor.withAlpha(77), // 0.3 * 255 = 77
      splashColor: _patternLightColor.withAlpha(26), // 0.1 * 255 = 26
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.resolveWith<Color>(
            (Set<WidgetState> states) {
              if (states.contains(WidgetState.pressed)) {
                return _lightPrimaryVariantColor;
              }
              return _lightPrimaryColor;
            },
          ),
          foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
          shape: WidgetStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
        ),
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: _lightSecondaryColor,
        foregroundColor: Colors.white,
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: Colors.white,
        selectedItemColor: _lightPrimaryColor,
        unselectedItemColor: Colors.grey,
      ),
      dividerTheme: DividerThemeData(
        color: Colors.grey[300],
        thickness: 1,
      ),
    );
  }

  // Dark theme
  static ThemeData darkTheme(String fontFamily) {
    return ThemeData(
      useMaterial3: true,
      colorScheme: const ColorScheme.dark(
        primary: _darkPrimaryColor,
        primaryContainer: _darkPrimaryVariantColor,
        secondary: _darkSecondaryColor,
        secondaryContainer: _darkSecondaryVariantColor,
        tertiary: _darkAccentColor,
        surface: _darkSurfaceColor,
        surfaceContainer: _darkBackgroundColor, // استبدال background
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: Colors.white, // استبدال onBackground
        onSurfaceVariant: Colors.white,
      ),
      pageTransitionsTheme: const PageTransitionsTheme(
        builders: {
          TargetPlatform.android: CupertinoPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
          TargetPlatform.windows: CupertinoPageTransitionsBuilder(),
        },
      ),
      fontFamily: fontFamily,
      textTheme: const TextTheme(
        displayLarge: TextStyle(color: Colors.white),
        displayMedium: TextStyle(color: Colors.white),
        displaySmall: TextStyle(color: Colors.white),
        headlineLarge: TextStyle(color: Colors.white),
        headlineMedium: TextStyle(color: Colors.white),
        headlineSmall: TextStyle(color: Colors.white),
        titleLarge: TextStyle(color: Colors.white),
        titleMedium: TextStyle(color: Colors.white),
        titleSmall: TextStyle(color: Colors.white),
        bodyLarge: TextStyle(color: Colors.white),
        bodyMedium: TextStyle(color: Colors.white),
        bodySmall: TextStyle(color: Colors.white),
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: _darkPrimaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        shadowColor: _darkPrimaryColor.withAlpha(128), // 0.5 * 255 = 128
        centerTitle: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(bottom: Radius.circular(15)),
        ),
        titleTextStyle: TextStyle(
          fontFamily: fontFamily,
          fontSize: 22,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      cardTheme: CardTheme(
        color: const Color(0xFF2C2C2C),
        elevation: 4,
        shadowColor: _darkPrimaryColor.withAlpha(128), // 0.5 * 255 = 128
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
          side: BorderSide(
              color: _darkSecondaryColor.withAlpha(51),
              width: 1), // 0.2 * 255 = 51
        ),
      ),
      scaffoldBackgroundColor: _darkBackgroundColor,
      dialogBackgroundColor: _darkSurfaceColor,
      highlightColor: _patternDarkColor.withAlpha(77), // 0.3 * 255 = 77
      splashColor: _patternDarkColor.withAlpha(26), // 0.1 * 255 = 26
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.resolveWith<Color>(
            (Set<WidgetState> states) {
              if (states.contains(WidgetState.pressed)) {
                return _darkPrimaryVariantColor;
              }
              return _darkPrimaryColor;
            },
          ),
          foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
          shape: WidgetStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
        ),
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: _darkSecondaryColor,
        foregroundColor: Colors.white,
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: Color(0xFF1E1E1E),
        selectedItemColor: _darkSecondaryColor,
        unselectedItemColor: Colors.grey,
      ),
      dividerTheme: DividerThemeData(
        color: Colors.grey[800],
        thickness: 1,
      ),
    );
  }
}
