//  الزخرفة الإسلامية

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class IslamicDecoration extends StatelessWidget {
  final double opacity;
  final Color color;
  final double size;
  final String pattern;

  const IslamicDecoration({
    Key? key,
    this.opacity = 0.08,
    required this.color,
    required this.size,
    this.pattern = 'assets/images/p2.svg',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: opacity,
      child: SvgPicture.asset(
        pattern,
        width: size,
        height: size,
        colorFilter: ColorFilter.mode(
          color,
          BlendMode.srcIn,
        ),
      ),
    );
  }
}