//  إنشاء ملف لقسم التطبيق

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
//import 'package:url_launcher/url_launcher.dart';
import '../../../utils/constants.dart';
import '../widgets/setting_item.dart';
import '../utils/url_utils.dart';

class AppSection extends StatelessWidget {
  final Color settingsColor;
  final Function(Widget, {double delay}) buildSlideAnimation;
  final Function(BuildContext, String, IconData) buildSectionTitle;

  const AppSection({
    super.key,
    required this.settingsColor,
    required this.buildSlideAnimation,
    required this.buildSectionTitle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildSectionTitle(context, 'التطبيق', Icons.app_settings_alt_outlined),

        // حول التطبيق
        buildSlideAnimation(
          SettingItem(
            title: 'حول التطبيق',
            icon: Icons.info_outline,
            iconColor: settingsColor,
            subtitle: 'معلومات عن التطبيق ورسالة من المطور',
            onTap: () => _navigateToAboutScreen(context),
            trailing: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: settingsColor.withAlpha(30),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'جديد',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: settingsColor,
                ),
              ),
            ),
          ),
          delay: 0.3,
        ),

        // مشاركة التطبيق
        buildSlideAnimation(
          SettingItem(
            title: 'مشاركة التطبيق',
            icon: Icons.share_outlined,
            iconColor: settingsColor,
            subtitle: 'شارك التطبيق مع الأصدقاء والعائلة',
            onTap: _shareApp,
          ),
          delay: 0.4,
        ),

        // تقييم التطبيق - معطل مؤقتاً
        // buildSlideAnimation(
        //   SettingItem(
        //     title: 'تقييم التطبيق',
        //     icon: Icons.star_outline,
        //     iconColor: settingsColor,
        //     subtitle: 'قيّم التطبيق على متجر التطبيقات',
        //     onTap: _rateApp,
        //   ),
        //   delay: 0.5,
        // ),

        // التواصل مع المطور
        buildSlideAnimation(
          SettingItem(
            title: 'تواصل معنا',
            icon: Icons.email_outlined,
            iconColor: settingsColor,
            subtitle: 'أرسل ملاحظاتك أو اقتراحاتك',
            onTap: _contactDeveloper,
          ),
          delay: 0.6,
        ),
      ],
    );
  }

  // الانتقال إلى شاشة حول التطبيق
  void _navigateToAboutScreen(BuildContext context) {
    Navigator.pushNamed(context, AppConstants.aboutRoute);
    // تأثير اهتزاز خفيف عند النقر
    HapticFeedback.lightImpact();
  }

  // مشاركة التطبيق
  void _shareApp() {
    Share.share(
      'تطبيق وهج السالك: ينابيع الحكمة وأنوار المعرفة. حمله الآن من متجر جوجل بلاي: https://play.google.com/store/apps/details?id=com.wahaj.alsalik',
      subject: 'تطبيق وهج السالك',
    );
  }

  // تقييم التطبيق
  // Future<void> _rateApp() async {
  //   final url = Uri.parse(
  //       'https://play.google.com/store/apps/details?id=com.wahaj.alsalik');
  //   await UrlUtils.launchUrlSafely(url);
  // }

  // التواصل مع المطور
  Future<void> _contactDeveloper() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      query: UrlUtils.encodeQueryParameters({
        'subject': 'تواصل من تطبيق وهج السالك',
        'body': 'مرحباً،\n\nأرسلت هذا البريد لـ:\n\n',
      }),
    );

    await UrlUtils.launchUrlSafely(emailUri);
  }
}
