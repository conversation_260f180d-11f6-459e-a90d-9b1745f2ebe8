// شاشة الأدعية الرئيسية

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'dart:ui'; // يُستخدم لـ ImageFilter
import '../providers/duas_provider.dart'; // افتراض وجود هذا الملف
import '../models/dua.dart'; // افتراض وجود هذا الملف
import '../utils/app_colors.dart'; // افتراض وجود هذا الملف
import '../widgets/dua_card_new.dart'; // استخدام بطاقة الدعاء المحسنة
import 'duas_details_screen.dart'; // افتراض وجود هذه الشاشة
import 'package:share_plus/share_plus.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:math' as math; // لإستخدام math.pi

class DuasScreen extends StatefulWidget {
  const DuasScreen({Key? key}) : super(key: key); // استخدام const للمنشئ

  @override
  State<DuasScreen> createState() => _DuasScreenState();
}

class _DuasScreenState extends State<DuasScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late ScrollController _scrollController;
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();
  List<DuaCategory> _filteredCategories = [];
  bool _showScrollToTop = false;
  // تعريف FocusNode لحقل البحث لإدارة التركيز بشكل صريح
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // تهيئة وحدة التحكم بالرسوم المتحركة
    _animationController = AnimationController(
      duration:
          const Duration(milliseconds: 1200), // مدة للرسوم المتحركة المتدرجة
      vsync: this,
    );

    // تهيئة وحدة التحكم بالتمرير
    _scrollController = ScrollController();
    _scrollController.addListener(_scrollListener);

    // تهيئة مزود الأدعية بشكل أكثر كفاءة بعد اكتمال الإطار الأول
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _initializeData();
      }
    });
  }

  // دالة منفصلة لتهيئة البيانات لتحسين قراءة الكود - محسنة للأداء
  Future<void> _initializeData() async {
    try {
      final duasProvider = Provider.of<DuasProvider>(context, listen: false);

      // التحقق من حالة البيانات
      if (duasProvider.categories.isEmpty) {
        // إذا لم تكن البيانات محملة بعد، قم بتهيئتها
        await duasProvider.initialize();

        // طباعة معلومات تشخيصية
        debugPrint('تم تحميل ${duasProvider.categories.length} فئة من الأدعية');
        debugPrint('تم تحميل ${duasProvider.featuredDuas.length} دعاء مميز');
      }

      // التأكد من أن الشاشة لا تزال مثبتة قبل تحديث الحالة
      if (mounted) {
        setState(() {
          _filteredCategories = duasProvider.categories;
        });

        // بدء الرسوم المتحركة بعد تحميل البيانات
        _animationController.forward();
      }
    } catch (e) {
      // معالجة الأخطاء بشكل أفضل
      debugPrint('خطأ في تهيئة بيانات الأدعية: $e');
      if (mounted) {
        // إعادة المحاولة بعد فترة قصيرة في حالة حدوث خطأ
        Future.delayed(const Duration(seconds: 2), () {
          _initializeData();
        });
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _searchFocusNode.dispose(); // التخلص من FocusNode
    super.dispose();
  }

  // مراقبة التمرير - محسن للأداء
  void _scrollListener() {
    // استخدام متغير مؤقت لتقليل استدعاءات setState
    final bool shouldShow = _scrollController.offset > 300;

    // تحديث الحالة فقط عند تغير قيمة الزر وعندما تكون الشاشة مثبتة
    if (shouldShow != _showScrollToTop && mounted) {
      setState(() {
        _showScrollToTop = shouldShow;
      });
    }
  }

  void _scrollToTop() {
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeOutCubic,
    );
  }

  // الانتقال إلى فئة
  void _navigateToCategory(DuaCategory category) {
    HapticFeedback.mediumImpact();
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            DuasDetailsScreen(category: category),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOutCubic;
          var tween =
              Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
          var offsetAnimation = animation.drive(tween);
          return SlideTransition(position: offsetAnimation, child: child);
        },
        transitionDuration: const Duration(milliseconds: 400),
      ),
    );
  }

  // الحصول على رسوم متحركة متدرجة
  Animation<double> _getAnimation(int index) {
    final double normalizedIndex = index /
        (_filteredCategories.isNotEmpty ? _filteredCategories.length : 15.0);
    final double startDelay = (normalizedIndex * 0.4).clamp(0.0, 0.5);
    final double endDelay = (startDelay + 0.5).clamp(startDelay, 1.0);

    return Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Interval(
          startDelay,
          endDelay,
          curve: Curves.easeOutCubic,
        ),
      ),
    );
  }

  // تحديث نتائج البحث - محسن للأداء
  void _updateSearchResults(String query) {
    final currentQuery = query.trim();
    final duasProvider = Provider.of<DuasProvider>(context, listen: false);

    Future.delayed(const Duration(milliseconds: 250), () {
      if (!mounted || _searchController.text.trim() != currentQuery) {
        return;
      }

      if (mounted) {
        setState(() {
          if (currentQuery.isEmpty) {
            _filteredCategories = duasProvider.categories;
          } else {
            _filteredCategories = duasProvider.searchDuas(currentQuery);
          }
        });
      }
    });
  }

  // إظهار/إخفاء حقل البحث
  void _toggleSearch() {
    HapticFeedback.mediumImpact();
    if (mounted) {
      setState(() {
        _isSearching = !_isSearching;
        if (!_isSearching) {
          _searchController.clear();
          final duasProvider =
              Provider.of<DuasProvider>(context, listen: false);
          _filteredCategories = duasProvider.categories;
          _searchFocusNode.unfocus();
        }
      });
      if (_isSearching) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) _searchFocusNode.requestFocus();
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final duasColor = AppColors.getDuasColor(isDarkMode);
    final duasProvider = Provider.of<DuasProvider>(context);
    final List<Dua> featuredDuas = duasProvider.featuredDuas;

    return Scaffold(
      body: duasProvider.isLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: duasColor,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'جاري تحميل الأدعية...',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            )
          : CustomScrollView(
              controller: _scrollController,
              physics: const BouncingScrollPhysics(),
              slivers: [
                // شريط التطبيق المحسن
                SliverAppBar(
                  expandedHeight: 250,
                  floating: false,
                  pinned: true,
                  elevation: 0,
                  backgroundColor: duasColor,
                  stretch: true,
                  flexibleSpace: FlexibleSpaceBar(
                    titlePadding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
                    title: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 300),
                      transitionBuilder:
                          (Widget child, Animation<double> animation) {
                        return FadeTransition(opacity: animation, child: child);
                      },
                      child: _isSearching
                          ? Container(
                              key: const ValueKey('search_field_key'),
                              width: double.infinity,
                              height: 40,
                              margin:
                                  const EdgeInsets.only(left: 16, right: 56),
                              decoration: BoxDecoration(
                                color: isDarkMode
                                    ? Colors.grey[800]
                                    : Colors.white,
                                borderRadius: BorderRadius.circular(20),
                                boxShadow: const [
                                  BoxShadow(
                                    color: Color.fromRGBO(0, 0, 0, 0.1),
                                    blurRadius: 10,
                                    spreadRadius: 1,
                                  ),
                                ],
                              ),
                              child: TextField(
                                controller: _searchController,
                                focusNode: _searchFocusNode,
                                onChanged: _updateSearchResults,
                                textDirection: TextDirection.rtl,
                                textAlign: TextAlign.right,
                                decoration: InputDecoration(
                                  hintText: 'ابحث عن دعاء...',
                                  hintStyle: TextStyle(
                                    color: isDarkMode
                                        ? Colors.grey[400]
                                        : Colors.grey[600],
                                    fontSize: 14,
                                  ),
                                  hintTextDirection: TextDirection.rtl,
                                  prefixIcon: Icon(
                                    Icons.search,
                                    color: isDarkMode
                                        ? Colors.grey[400]
                                        : Colors.grey[600],
                                  ),
                                  border: InputBorder.none,
                                  contentPadding: const EdgeInsets.symmetric(
                                      vertical: 12.0, horizontal: 8.0),
                                ),
                                style: TextStyle(
                                  color: isDarkMode
                                      ? Colors.white
                                      : Colors.black87,
                                ),
                                textInputAction: TextInputAction.search,
                              ),
                            )
                          : ShaderMask(
                              key: const ValueKey('title_key'),
                              shaderCallback: (bounds) {
                                return const LinearGradient(
                                  colors: [
                                    Colors.white,
                                    Color.fromRGBO(
                                        255, 255, 255, 0.78), // 200 alpha
                                  ],
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                ).createShader(bounds);
                              },
                              child: const Text(
                                'الأدعية',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 24,
                                ),
                                textDirection: TextDirection.rtl,
                                textAlign: TextAlign.right,
                              ),
                            ),
                    ),
                    stretchModes: const [
                      StretchMode.zoomBackground,
                      StretchMode.blurBackground,
                      StretchMode.fadeTitle,
                    ],
                    background: Stack(
                      fit: StackFit.expand,
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                duasColor,
                                duasColor.withAlpha(204),
                              ],
                            ),
                          ),
                        ),
                        RepaintBoundary(
                          child: TweenAnimationBuilder<double>(
                            tween: Tween<double>(begin: 0.0, end: 1.0),
                            duration: const Duration(seconds: 45),
                            curve: Curves.linear,
                            child: Opacity(
                              opacity: 0.04,
                              child: SvgPicture.asset(
                                'assets/images/p2.svg',
                                fit: BoxFit.cover,
                              ),
                            ),
                            builder: (context, value, child) {
                              return Transform.rotate(
                                angle: value * 2 * math.pi,
                                child: child!,
                              );
                            },
                          ),
                        ),
                        Opacity(
                          opacity: 0.06,
                          child: SvgPicture.asset(
                            'assets/images/p2.svg',
                            fit: BoxFit.cover,
                          ),
                        ),
                        const DecoratedBox(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Color.fromRGBO(0, 0, 0, 0.54),
                                Color.fromRGBO(0, 0, 0, 0.87),
                              ],
                              stops: [0.4, 0.8, 1.0],
                            ),
                          ),
                        ),
                        const Positioned(
                          top: 50,
                          right: 20,
                          child: Opacity(
                            opacity: 0.15,
                            child: Icon(
                              Icons.auto_awesome,
                              size: 100,
                              color: Color.fromRGBO(255, 255, 255, 0.39),
                            ),
                          ),
                        ),
                        Positioned(
                          left: 16,
                          right: 16,
                          bottom: 70,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            textDirection: TextDirection.rtl,
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color:
                                      const Color.fromRGBO(255, 255, 255, 0.11),
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                      color: const Color.fromRGBO(
                                          255, 255, 255, 0.19),
                                      width: 1),
                                ),
                                child: const Row(
                                  mainAxisSize: MainAxisSize.min,
                                  textDirection: TextDirection.rtl,
                                  children: [
                                    Icon(
                                      Icons.auto_awesome,
                                      size: 16,
                                      color:
                                          Color.fromRGBO(255, 255, 255, 0.90),
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      'أدعية مختارة',
                                      style: TextStyle(
                                        color:
                                            Color.fromRGBO(255, 255, 255, 0.90),
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      textDirection: TextDirection.rtl,
                                      textAlign: TextAlign.right,
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 12),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 4, vertical: 4),
                                child: const Text(
                                  'من القرآن الكريم والسنة النبوية',
                                  style: TextStyle(
                                    color: Color.fromRGBO(255, 255, 255, 0.70),
                                    fontSize: 14,
                                    shadows: [
                                      BoxShadow(
                                        color: Color.fromRGBO(0, 0, 0, 0.39),
                                        offset: Offset(0, 1),
                                        blurRadius: 3,
                                      )
                                    ],
                                  ),
                                  textDirection: TextDirection.rtl,
                                  textAlign: TextAlign.right,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  actions: [
                    TweenAnimationBuilder<double>(
                      tween: Tween<double>(
                          begin: 1.0, end: _isSearching ? 1.0 : 1.1),
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeOut,
                      child: Icon(
                        _isSearching ? Icons.close : Icons.search,
                        color: Colors.white,
                      ),
                      builder: (context, value, child) {
                        return AnimatedSwitcher(
                          duration: const Duration(milliseconds: 300),
                          transitionBuilder:
                              (Widget child, Animation<double> animation) {
                            return ScaleTransition(
                                scale: animation, child: child);
                          },
                          child: IconButton(
                            key: ValueKey(_isSearching),
                            icon: Transform.scale(
                              scale: _isSearching ? 1.0 : value,
                              child: child,
                            ),
                            onPressed: _toggleSearch,
                            tooltip: _isSearching ? 'إغلاق البحث' : 'بحث',
                          ),
                        );
                      },
                    ),
                  ],
                ),

                // قسم الأدعية المميزة المحسن
                if (featuredDuas.isNotEmpty && _searchController.text.isEmpty)
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TweenAnimationBuilder<double>(
                            tween: Tween<double>(begin: 0.0, end: 1.0),
                            duration: const Duration(milliseconds: 600),
                            curve: Curves.easeOutCubic,
                            child: Row(
                              textDirection: TextDirection.rtl,
                              children: [
                                TweenAnimationBuilder<double>(
                                  tween: Tween<double>(begin: 0.0, end: 1.0),
                                  duration: const Duration(milliseconds: 1200),
                                  curve: Curves.elasticOut,
                                  child: Icon(
                                    Icons.star,
                                    color: duasColor,
                                    size: 22,
                                  ),
                                  builder: (context, animValue, iconChild) {
                                    return Transform.rotate(
                                      angle: animValue * 2 * math.pi * 0.1,
                                      child: Container(
                                        padding: const EdgeInsets.all(10),
                                        decoration: BoxDecoration(
                                          color: duasColor.withAlpha(51),
                                          shape: BoxShape.circle,
                                          boxShadow: [
                                            BoxShadow(
                                              color: duasColor.withAlpha(30),
                                              blurRadius: 8 * animValue,
                                              spreadRadius: 2 * animValue,
                                            ),
                                          ],
                                        ),
                                        child: iconChild,
                                      ),
                                    );
                                  },
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: ShaderMask(
                                    shaderCallback: (bounds) {
                                      return LinearGradient(
                                        colors: [
                                          isDarkMode
                                              ? Colors.white
                                              : Colors.black87,
                                          duasColor,
                                        ],
                                        stops: const [0.3, 1.0],
                                        begin: Alignment.centerRight,
                                        end: Alignment.centerLeft,
                                      ).createShader(bounds);
                                    },
                                    child: Text(
                                      'أدعية مميزة',
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                        color: isDarkMode
                                            ? Colors.white
                                            : Colors.black87,
                                      ),
                                      textDirection: TextDirection.rtl,
                                      textAlign: TextAlign.right,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            builder: (context, value, child) {
                              return Transform.translate(
                                offset: Offset(0, 20 * (1 - value)),
                                child: Opacity(
                                  opacity: value,
                                  child: child,
                                ),
                              );
                            },
                          ),
                          const SizedBox(height: 20),
                          SizedBox(
                            height: 200,
                            child: TweenAnimationBuilder<double>(
                              tween: Tween<double>(begin: 0.0, end: 1.0),
                              duration: const Duration(milliseconds: 700),
                              curve: Curves.easeOutCubic,
                              child: RepaintBoundary(
                                child: ListView.builder(
                                  scrollDirection: Axis.horizontal,
                                  physics: const BouncingScrollPhysics(),
                                  itemCount: featuredDuas.length,
                                  itemBuilder: (context, index) {
                                    final dua = featuredDuas[index];
                                    final Animation<double> itemAnimation =
                                        Tween<double>(begin: 0.0, end: 1.0)
                                            .animate(
                                      CurvedAnimation(
                                        parent: _animationController,
                                        curve: Interval(
                                          (0.1 * index).clamp(0.0, 0.5),
                                          (0.1 * index + 0.5).clamp(0.1, 1.0),
                                          curve: Curves.easeOutCubic,
                                        ),
                                      ),
                                    );

                                    return AnimatedBuilder(
                                      animation: itemAnimation,
                                      child: Padding(
                                        padding:
                                            const EdgeInsets.only(right: 16),
                                        child: _buildEnhancedFeaturedDuaCard(
                                          dua,
                                          duasColor,
                                          isDarkMode,
                                          index,
                                        ),
                                      ),
                                      builder: (context, childWidget) {
                                        final animValue = itemAnimation.value;
                                        return Opacity(
                                          opacity: animValue,
                                          child: Transform.translate(
                                            offset:
                                                Offset(50 * (1 - animValue), 0),
                                            child: childWidget,
                                          ),
                                        );
                                      },
                                    );
                                  },
                                ),
                              ),
                              builder: (context, value, child) {
                                return Opacity(
                                  opacity: value,
                                  child: Transform.translate(
                                    offset: Offset(0, 30 * (1 - value)),
                                    child: child!,
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                // قسم جميع الأدعية
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
                    child: Row(
                      textDirection: TextDirection.rtl,
                      children: [
                        Icon(
                          Icons.menu_book,
                          color: duasColor,
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'جميع الأدعية',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: isDarkMode ? Colors.white : Colors.black87,
                          ),
                          textDirection: TextDirection.rtl,
                          textAlign: TextAlign.right,
                        ),
                      ],
                    ),
                  ),
                ),

                // قائمة الأدعية - بتصميم فاخر وسلس
                if (_filteredCategories.isNotEmpty)
                  SliverPadding(
                    padding: EdgeInsets.symmetric(
                      horizontal: MediaQuery.of(context).size.width * 0.04,
                      vertical: MediaQuery.of(context).size.width * 0.02,
                    ),
                    sliver: SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          if (index >= _filteredCategories.length) {
                            return null; // ضمان عدم تجاوز الحدود
                          }

                          final category = _filteredCategories[index];
                          final Animation<double> itemAnimation =
                              _getAnimation(index);

                          // إضافة مسافة بين العناصر
                          return Padding(
                            padding: EdgeInsets.only(
                              bottom: MediaQuery.of(context).size.width * 0.03,
                            ),
                            child: RepaintBoundary(
                              key: ValueKey(
                                  'dua_category_${category.id}_${category.name.hashCode}'),
                              child: DuaCard(
                                category: category,
                                onTap: () => _navigateToCategory(category),
                                animation: itemAnimation,
                                // تحديد نوع العرض كقائمة
                                displayMode: DuaCardDisplayMode.list,
                              ),
                            ),
                          );
                        },
                        childCount: _filteredCategories.length,
                        addAutomaticKeepAlives: true,
                        addRepaintBoundaries: false,
                      ),
                    ),
                  ),

                // رسالة عند عدم وجود نتائج
                if (_filteredCategories.isEmpty && _isSearching)
                  SliverFillRemaining(
                    hasScrollBody: false,
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.search_off,
                                size: 64, color: Colors.grey[400]),
                            const SizedBox(height: 16),
                            Text('لا توجد نتائج مطابقة',
                                style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.grey[600])),
                            const SizedBox(height: 8),
                            Text('حاول البحث بكلمات أخرى',
                                style: TextStyle(
                                    fontSize: 14, color: Colors.grey[500]),
                                textAlign: TextAlign.center),
                          ],
                        ),
                      ),
                    ),
                  ),
                const SliverToBoxAdapter(
                  child: SizedBox(height: 80),
                ),
              ],
            ),
      floatingActionButtonLocation: FloatingActionButtonLocation.startFloat,
      floatingActionButton: AnimatedOpacity(
        duration: const Duration(milliseconds: 300),
        opacity: _showScrollToTop ? 1.0 : 0.0,
        child: AnimatedScale(
          duration: const Duration(milliseconds: 300),
          scale: _showScrollToTop ? 1.0 : 0.0,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  duasColor,
                  duasColor.withAlpha(230),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: duasColor.withAlpha(50),
                  blurRadius: 8,
                  spreadRadius: 0,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(16),
              clipBehavior: Clip.antiAlias,
              child: InkWell(
                onTap: () {
                  _scrollToTop();
                  HapticFeedback.mediumImpact();
                },
                child: Container(
                  padding: const EdgeInsets.all(12),
                  child: const Icon(
                    Icons.arrow_upward,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // بناء بطاقة الدعاء المميز المحسنة
  Widget _buildEnhancedFeaturedDuaCard(
      Dua dua, Color duasColor, bool isDarkMode, int index) {
    const cardBorderRadiusValue = 24.0;
    const cardBorderRadius =
        BorderRadius.all(Radius.circular(cardBorderRadiusValue));

    return GestureDetector(
      onTap: () {
        HapticFeedback.mediumImpact();
        _showDuaDialog(dua);
      },
      child: Container(
        width: 300,
        decoration: BoxDecoration(
          borderRadius: cardBorderRadius,
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [duasColor.withAlpha(25), duasColor.withAlpha(51)],
            stops: const [0.3, 1.0],
          ),
          boxShadow: [
            BoxShadow(
                color: duasColor.withAlpha(25),
                blurRadius: 15,
                spreadRadius: 1,
                offset: const Offset(0, 5))
          ],
          border: Border.all(color: duasColor.withAlpha(76), width: 1.5),
        ),
        child: ClipRRect(
          borderRadius: cardBorderRadius,
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
            child: Stack(
              children: [
                Positioned.fill(
                  child: RepaintBoundary(
                    child: TweenAnimationBuilder<double>(
                      tween: Tween<double>(begin: 0.0, end: 1.0),
                      duration: const Duration(seconds: 60),
                      curve: Curves.linear,
                      child: Opacity(
                        opacity: 0.03,
                        child: SvgPicture.asset('assets/images/p2.svg',
                            fit: BoxFit.cover),
                      ),
                      builder: (context, value, child) {
                        return Transform.rotate(
                            angle: value * 2 * math.pi, child: child!);
                      },
                    ),
                  ),
                ),
                Positioned(
                  right: -20,
                  bottom: -20,
                  child: TweenAnimationBuilder<double>(
                    tween: Tween<double>(begin: 0.0, end: 1.0),
                    duration: const Duration(seconds: 2),
                    curve: Curves.easeOutCubic,
                    child: Icon(Icons.format_quote,
                        size: 120, color: duasColor.withAlpha(50)),
                    builder: (context, value, child) {
                      return Transform.rotate(
                          angle: -0.2 * value,
                          child: Opacity(opacity: value, child: child));
                    },
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    textDirection: TextDirection.rtl,
                    children: [
                      if (dua.source != null)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 5),
                          decoration: BoxDecoration(
                            color: duasColor.withAlpha(51),
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                  color: duasColor.withAlpha(25), blurRadius: 8)
                            ],
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            textDirection: TextDirection.rtl,
                            children: [
                              Icon(Icons.auto_awesome,
                                  size: 14, color: duasColor),
                              const SizedBox(width: 6),
                              Flexible(
                                child: Text(
                                  dua.source!,
                                  style: TextStyle(
                                      fontSize: 12,
                                      color: duasColor,
                                      fontWeight: FontWeight.bold),
                                  textDirection: TextDirection.rtl,
                                  textAlign: TextAlign.right,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                      const Spacer(),
                      ShaderMask(
                        shaderCallback: (bounds) {
                          return LinearGradient(
                            colors: [
                              isDarkMode ? Colors.white : Colors.black87,
                              isDarkMode ? Colors.white70 : Colors.black54
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            stops: const [0.7, 1.0],
                          ).createShader(bounds);
                        },
                        blendMode: BlendMode.srcIn,
                        child: Text(
                          dua.text,
                          style: TextStyle(
                            fontSize: 15,
                            height: 1.5,
                            color: isDarkMode ? Colors.white : Colors.black87,
                            shadows: [
                              Shadow(
                                  color: duasColor.withAlpha(25),
                                  offset: const Offset(0, 1),
                                  blurRadius: 2)
                            ],
                          ),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.right,
                        ),
                      ),
                      const Spacer(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        textDirection: TextDirection.rtl,
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                                color: duasColor.withAlpha(25),
                                borderRadius: BorderRadius.circular(12)),
                            child: Text(
                              '${index + 1}',
                              style: TextStyle(
                                  fontSize: 12,
                                  color: duasColor,
                                  fontWeight: FontWeight.bold),
                              textDirection: TextDirection.rtl,
                              textAlign: TextAlign.right,
                            ),
                          ),
                          TweenAnimationBuilder<double>(
                            tween: Tween<double>(begin: 0.0, end: 1.0),
                            duration: const Duration(milliseconds: 1500),
                            curve: Curves.elasticOut,
                            child: Icon(Icons.arrow_back_ios,
                                size: 14, color: duasColor),
                            builder: (context, value, iconChild) {
                              return Transform.scale(
                                scale: 0.8 + (0.2 * value),
                                child: Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: duasColor.withAlpha(51),
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                          color: duasColor.withAlpha(25),
                                          blurRadius: 8 * value,
                                          spreadRadius: 1 * value)
                                    ],
                                  ),
                                  child: iconChild,
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // عرض حوار الدعاء المحسن
  void _showDuaDialog(Dua dua) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final duasColor = AppColors.getDuasColor(isDarkMode);

    const dialogBorderRadiusValue = 28.0;
    const dialogBorderRadius =
        BorderRadius.all(Radius.circular(dialogBorderRadiusValue));
    const dialogBorderWidth = 1.5;
    const dialogAlphaValue = 230;

    showDialog(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.black.withAlpha(120),
      builder: (context) => Dialog(
        shape: const RoundedRectangleBorder(borderRadius: dialogBorderRadius),
        elevation: 16,
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
        child: ClipRRect(
          borderRadius: dialogBorderRadius,
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    (isDarkMode ? Colors.grey[850]! : Colors.white)
                        .withAlpha(dialogAlphaValue),
                    (isDarkMode ? Colors.grey[800]! : Colors.grey[100]!)
                        .withAlpha(dialogAlphaValue),
                  ],
                ),
                borderRadius: dialogBorderRadius,
                border: Border.all(
                    color: duasColor.withAlpha(76), width: dialogBorderWidth),
              ),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.8),
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildDialogSectionTitle(
                          dua.source ?? 'دعاء', duasColor, isDarkMode),
                      const SizedBox(height: 16),
                      _buildAnimatedDivider(duasColor),
                      const SizedBox(height: 16),
                      _buildDuaTextContent(dua, duasColor, isDarkMode),
                      const SizedBox(height: 16),
                      if (dua.translation != null) ...[
                        _buildDuaTranslation(dua, isDarkMode),
                        const SizedBox(height: 16),
                      ],
                      if (dua.virtue != null) ...[
                        _buildDuaVirtue(dua, duasColor, isDarkMode),
                        const SizedBox(height: 16),
                      ],
                      _buildDialogActionButtons(dua, duasColor),
                      const SizedBox(height: 8),
                      _buildDialogCloseButton(isDarkMode),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDialogSectionTitle(
      String title, Color duasColor, bool isDarkMode) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeOutCubic,
      child: Row(
        textDirection: TextDirection.rtl,
        children: [
          TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: 0.0, end: 1.0),
            duration: const Duration(seconds: 1),
            curve: Curves.elasticOut,
            child: Icon(Icons.format_quote, color: duasColor, size: 20),
            builder: (context, value, iconChild) {
              return Transform.rotate(
                angle: value * 2 * math.pi * 0.1,
                child: Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: duasColor.withAlpha(51),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                          color: duasColor.withAlpha(30),
                          blurRadius: 8 * value,
                          spreadRadius: 2 * value)
                    ],
                  ),
                  child: iconChild,
                ),
              );
            },
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ShaderMask(
              shaderCallback: (bounds) {
                return LinearGradient(
                  colors: [
                    isDarkMode ? Colors.white : Colors.black87,
                    duasColor
                  ],
                  stops: const [0.7, 1.0],
                  begin: Alignment.centerRight,
                  end: Alignment.centerLeft,
                ).createShader(bounds);
              },
              child: Text(
                title,
                style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black87),
                textDirection: TextDirection.rtl,
                textAlign: TextAlign.right,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ],
      ),
      builder: (context, value, rowChild) {
        return Opacity(
            opacity: value,
            child: Transform.translate(
                offset: Offset(0, 20 * (1 - value)), child: rowChild));
      },
    );
  }

  Widget _buildAnimatedDivider(Color duasColor) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 600),
      curve: Curves.easeOutCubic,
      child: Container(
        height: 1.5,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.transparent,
              duasColor.withAlpha(100),
              Colors.transparent
            ],
            stops: const [0.1, 0.5, 0.9],
          ),
        ),
      ),
      builder: (context, value, dividerChild) =>
          Opacity(opacity: value, child: dividerChild),
    );
  }

  Widget _buildDuaTextContent(Dua dua, Color duasColor, bool isDarkMode) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 700),
      curve: Curves.easeOutCubic,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: duasColor.withAlpha(20),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: duasColor.withAlpha(60), width: 1),
        ),
        child: Stack(children: [
          Positioned(
              right: -20,
              bottom: -20,
              child: Opacity(
                  opacity: 0.04,
                  child:
                      Icon(Icons.format_quote, size: 100, color: duasColor))),
          ConstrainedBox(
            constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.25),
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Column(mainAxisSize: MainAxisSize.min, children: [
                SelectableText(
                  dua.text,
                  style: TextStyle(
                      fontSize: 17,
                      height: 1.7,
                      color: isDarkMode ? Colors.white : Colors.black87,
                      shadows: [
                        Shadow(
                            color: duasColor.withAlpha(25),
                            offset: const Offset(0, 1),
                            blurRadius: 2)
                      ]),
                  textDirection: TextDirection.rtl,
                  textAlign: TextAlign.right,
                  key: ValueKey('dua_text_dialog_${dua.id}'),
                ),
                if (dua.text.length > 150) ...[
                  const SizedBox(height: 8),
                  Icon(Icons.keyboard_double_arrow_down,
                      size: 16, color: duasColor.withAlpha(128)),
                ],
              ]),
            ),
          ),
        ]),
      ),
      builder: (context, value, textContentChild) => Opacity(
          opacity: value,
          child: Transform.translate(
              offset: Offset(0, 25 * (1 - value)), child: textContentChild)),
    );
  }

  Widget _buildDuaTranslation(Dua dua, bool isDarkMode) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 800),
      curve: Curves.easeOutCubic,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: (isDarkMode ? Colors.grey[800]! : Colors.grey[100]!)
              .withAlpha(120),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
              color: (isDarkMode ? Colors.grey[700]! : Colors.grey[300]!)
                  .withAlpha(150),
              width: 1),
        ),
        constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.12),
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Column(mainAxisSize: MainAxisSize.min, children: [
            SelectableText(
              dua.translation!,
              style: TextStyle(
                  fontSize: 13,
                  fontStyle: FontStyle.italic,
                  height: 1.4,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[600]),
              textDirection: TextDirection.rtl,
              textAlign: TextAlign.right,
              key: ValueKey('dua_translation_dialog_${dua.id}'),
            ),
            if (dua.translation!.length > 100) ...[
              const SizedBox(height: 8),
              Icon(Icons.keyboard_double_arrow_down,
                  size: 14, color: Colors.grey.withAlpha(128)),
            ],
          ]),
        ),
      ),
      builder: (context, value, translationChild) =>
          Opacity(opacity: value, child: translationChild),
    );
  }

  Widget _buildDuaVirtue(Dua dua, Color duasColor, bool isDarkMode) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 900),
      curve: Curves.easeOutCubic,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: duasColor.withAlpha(10),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: duasColor.withAlpha(40), width: 1),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          textDirection: TextDirection.rtl,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                  color: duasColor.withAlpha(25), shape: BoxShape.circle),
              child: Icon(Icons.star, color: duasColor, size: 16),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.12),
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      textDirection: TextDirection.rtl,
                      children: [
                        SelectableText(
                          dua.virtue!,
                          style: TextStyle(
                              fontSize: 13,
                              height: 1.4,
                              color: isDarkMode
                                  ? Colors.grey[300]
                                  : Colors.grey[700]),
                          textDirection: TextDirection.rtl,
                          textAlign: TextAlign.right,
                          key: ValueKey('dua_virtue_dialog_${dua.id}'),
                        ),
                        if (dua.virtue!.length > 100) ...[
                          const SizedBox(height: 8),
                          Center(
                              child: Icon(Icons.keyboard_double_arrow_down,
                                  size: 14, color: duasColor.withAlpha(128))),
                        ],
                      ]),
                ),
              ),
            ),
          ],
        ),
      ),
      builder: (context, value, virtueChild) => Opacity(
          opacity: value,
          child: Transform.translate(
              offset: Offset(0, 15 * (1 - value)), child: virtueChild)),
    );
  }

  Widget _buildDialogActionButtons(Dua dua, Color duasColor) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 1000),
      curve: Curves.easeOutCubic,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        textDirection: TextDirection.rtl,
        children: [
          _buildDialogActionButton(
              icon: Icons.share,
              text: 'مشاركة',
              duasColor: duasColor,
              onTap: () {
                HapticFeedback.mediumImpact();
                if (mounted) Navigator.pop(context);
                _shareDua(dua);
              }),
          _buildDialogActionButton(
              icon: Icons.copy,
              text: 'نسخ',
              duasColor: duasColor,
              onTap: () {
                HapticFeedback.mediumImpact();
                Clipboard.setData(ClipboardData(text: dua.text));
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Row(
                          textDirection: TextDirection.rtl,
                          children: [
                            Icon(Icons.check_circle,
                                color: Colors.white, size: 16),
                            SizedBox(width: 8),
                            Text('تم نسخ الدعاء')
                          ]),
                      backgroundColor: duasColor,
                      duration: const Duration(seconds: 2),
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10)),
                      margin: const EdgeInsets.all(16),
                    ),
                  );
                }
              }),
        ],
      ),
      builder: (context, value, actionsChild) =>
          Opacity(opacity: value, child: actionsChild),
    );
  }

  Widget _buildDialogActionButton(
      {required IconData icon,
      required String text,
      required Color duasColor,
      required VoidCallback onTap}) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4.0),
        child: Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(30),
          clipBehavior: Clip.antiAlias,
          child: InkWell(
            onTap: onTap,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              decoration: BoxDecoration(
                color: duasColor.withAlpha(20),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(color: duasColor.withAlpha(40), width: 1),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                textDirection: TextDirection.rtl,
                children: [
                  Icon(icon, color: duasColor, size: 18),
                  const SizedBox(width: 8),
                  Text(text,
                      style: TextStyle(
                          color: duasColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 13)),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDialogCloseButton(bool isDarkMode) {
    return Align(
      alignment: Alignment.bottomLeft,
      child: IconButton(
        onPressed: () {
          HapticFeedback.mediumImpact();
          if (mounted) Navigator.pop(context);
        },
        icon: Icon(Icons.close,
            color: (isDarkMode ? Colors.grey[400] : Colors.grey[600])
                ?.withAlpha(204), // 0.8 * 255 = 204
            size: 22),
        tooltip: 'إغلاق',
        padding: const EdgeInsets.all(4),
      ),
    );
  }

  // مشاركة الدعاء
  void _shareDua(Dua dua) {
    String shareText = dua.text;
    if (dua.translation != null) shareText += '\n\nالترجمة: ${dua.translation}';
    if (dua.source != null) shareText += '\n\nالمصدر: ${dua.source}';
    if (dua.virtue != null) shareText += '\n\nالفضل: ${dua.virtue}';
    shareText += '\n\nمشاركة من تطبيق وهج السالك'; // تم تحديث اسم التطبيق
    Share.share(shareText);
  }
}
