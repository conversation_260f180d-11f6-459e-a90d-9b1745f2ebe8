/// هذا الملف يحدد بنية ملفات JSON المستخدمة في التطبيق
/// ويوفر توثيقاً شاملاً لكل حقل

/// بنية ملف الأذكار (azkar.json)
/// 
/// {
///   "categories": [                  // مصفوفة تحتوي على فئات الأذكار
///     {
///       "id": "string",              // معرف فريد للفئة (مطلوب)
///       "name": "string",            // اسم الفئة (مطلوب)
///       "description": "string",     // وصف الفئة (مطلوب)
///       "count": number,             // عدد الأذكار في الفئة (اختياري، يتم حسابه تلقائياً إذا لم يتم تحديده)
///       "iconName": "string",        // اسم الأيقونة المستخدمة للفئة (اختياري)
///       "hasSubcategories": boolean, // هل تحتوي الفئة على فئات فرعية (اختياري، افتراضي: false)
///       
///       // إما items أو subcategories يجب أن يكون موجوداً
///       "items": [                   // مصفوفة تحتوي على عناصر الأذكار (إذا لم تكن هناك فئات فرعية)
///         {
///           "id": "string",          // معرف فريد للذكر (مطلوب)
///           "text": "string",        // نص الذكر (مطلوب)
///           "count": number,         // عدد مرات تكرار الذكر (اختياري، افتراضي: 1)
///           "source": "string",      // مصدر الذكر (اختياري)
///           "fadl": "string"         // فضل الذكر (اختياري)
///         }
///       ],
///       
///       "subcategories": [           // مصفوفة تحتوي على الفئات الفرعية (إذا كانت hasSubcategories = true)
///         {
///           // نفس بنية الفئة الرئيسية
///           "id": "string",
///           "name": "string",
///           "description": "string",
///           "count": number,
///           "iconName": "string",
///           "items": [ ... ]
///         }
///       ]
///     }
///   ]
/// }

/// قواعد وإرشادات لإضافة أو تعديل الأذكار:
/// 
/// 1. يجب أن يكون لكل فئة وذكر معرف فريد
/// 2. معرفات الفئات الرئيسية يجب أن تكون قصيرة ووصفية (مثل: morning, evening, prayer)
/// 3. معرفات الأذكار يجب أن تبدأ بمعرف الفئة متبوعاً بشرطة سفلية ورقم (مثل: morning_1, evening_2)
/// 4. معرفات الفئات الفرعية يجب أن تبدأ بمعرف الفئة الرئيسية متبوعاً بشرطة سفلية واسم الفئة الفرعية (مثل: prayer_fajr)
/// 5. يجب استخدام الترميز UTF-8 لضمان عرض النصوص العربية بشكل صحيح
/// 6. يجب أن يكون حقل count متوافقاً مع العدد الفعلي للعناصر (إذا كان مختلفاً، سيتم استخدام العدد الفعلي)
/// 7. يفضل إضافة حقول source و fadl لكل ذكر لتوفير معلومات إضافية للمستخدم

/// مثال على فئة رئيسية بدون فئات فرعية:
/// 
/// {
///   "id": "morning",
///   "name": "أذكار الصباح",
///   "description": "أذكار الصباح التي يستحب قراءتها بعد صلاة الفجر إلى طلوع الشمس",
///   "count": 14,
///   "iconName": "wb_sunny_outlined",
///   "items": [
///     {
///       "id": "morning_1",
///       "text": "أَعُوذُ بِاللَّهِ مِنَ الشَّيْطَانِ الرَّجِيمِ...",
///       "count": 1,
///       "source": "صحيح البخاري",
///       "fadl": "من قالها حين يصبح أجير من الجن حتى يمسي"
///     }
///   ]
/// }
/// 
/// مثال على فئة رئيسية مع فئات فرعية:
/// 
/// {
///   "id": "prayer",
///   "name": "أذكار الصلاة",
///   "description": "الأذكار المتعلقة بالصلوات المفروضة",
///   "count": 10,
///   "iconName": "mosque_outlined",
///   "hasSubcategories": true,
///   "subcategories": [
///     {
///       "id": "prayer_fajr",
///       "name": "أذكار صلاة الفجر",
///       "description": "الأذكار المتعلقة بصلاة الفجر",
///       "count": 5,
///       "iconName": "brightness_2_outlined",
///       "items": [ ... ]
///     }
///   ]
/// }
