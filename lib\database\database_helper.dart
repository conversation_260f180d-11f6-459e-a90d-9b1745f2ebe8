import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:wahaj_alsaalik/models/zikr.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:convert';
import 'package:wahaj_alsaalik/models/book.dart'; // إضافة استيراد لنموذج الكتاب
import 'package:wahaj_alsaalik/models/poem.dart'; // إضافة استيراد لنموذج القصيدة

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  DatabaseHelper._internal();

  static Database? _database;

  // الحصول على مثيل قاعدة البيانات
  Future<Database> get database async {
    if (_database != null) return _database!;

    _database = await _initDatabase();
    return _database!;
  }

  // إعادة تعيين قاعدة البيانات بالكامل
  Future<void> resetDatabase() async {
    try {
      _database = null;
      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, 'wahaj_alsaalik.db');

      // حذف قاعدة البيانات الحالية إذا كانت موجودة
      if (await databaseExists(path)) {
        debugPrint('حذف قاعدة البيانات الحالية');
        await deleteDatabase(path);
      }

      // إعادة تهيئة قاعدة البيانات
      _database = await _initDatabase();
      debugPrint('تم إعادة تعيين قاعدة البيانات بنجاح');
    } catch (e) {
      debugPrint('خطأ في إعادة تعيين قاعدة البيانات: $e');
      rethrow;
    }
  }

  // تهيئة قاعدة البيانات
  Future<Database> _initDatabase() async {
    try {
      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, 'wahaj_alsaalik.db');

      debugPrint('فتح قاعدة البيانات في: $path');

      // الفتح بطريقة أكثر أماناً مع التعامل مع خطأ القراءة فقط
      return await openDatabase(
        path,
        version: 5, // تم زيادة رقم الإصدار لضمان تنفيذ الترقية
        onCreate: _createDatabase,
        onUpgrade: _upgradeDatabase,
        // السماح بالكتابة بشكل صريح
        readOnly: false,
      );
    } catch (e) {
      debugPrint('خطأ في فتح قاعدة البيانات: $e');

      // محاولة إنشاء قاعدة بيانات جديدة في مسار بديل
      try {
        final appDocDir = await getApplicationDocumentsDirectory();
        final path = join(appDocDir.path, 'wahaj_alsaalik_alt.db');

        // حذف قاعدة البيانات القديمة إذا كانت موجودة
        if (await databaseExists(path)) {
          await deleteDatabase(path);
        }

        return await openDatabase(
          path,
          version: 5, // تم تحديث رقم الإصدار ليتوافق مع المسار الرئيسي
          onCreate: _createDatabase,
          onUpgrade: _upgradeDatabase,
        );
      } catch (e2) {
        debugPrint('فشل أيضاً في إنشاء قاعدة بيانات بديلة: $e2');

        // إذا فشلت كل المحاولات، استخدم قاعدة بيانات في الذاكرة فقط
        return await openDatabase(
          ':memory:',
          version: 5, // تم تحديث رقم الإصدار ليتوافق مع المسار الرئيسي
          onCreate: _createDatabase,
          onUpgrade: _upgradeDatabase,
        );
      }
    }
  }

  // إنشاء جداول قاعدة البيانات
  Future<void> _createDatabase(Database db, int version) async {
    debugPrint('إنشاء قاعدة البيانات الإصدار $version');

    // جدول الكتب - تم تحديثه ليشمل جميع الأعمدة المطلوبة
    await db.execute('''
      CREATE TABLE IF NOT EXISTS books(
        id TEXT PRIMARY KEY,
        title TEXT,
        author TEXT,
        description TEXT,
        cover_url TEXT,
        local_cover_path TEXT,
        category TEXT,
        pdf_url TEXT,
        pdf_path TEXT,
        local_pdf_path TEXT,
        pages INTEGER DEFAULT 0,
        date_added INTEGER,
        tags TEXT,
        is_favorite INTEGER DEFAULT 0,
        rating REAL DEFAULT 0.0,
        download_progress INTEGER DEFAULT -1,
        last_read_page INTEGER DEFAULT 0,
        last_read_date INTEGER
      )
    ''');

    // جدول القصائد - تم تحديثه ليشمل جميع الأعمدة المطلوبة
    await db.execute('''
      CREATE TABLE IF NOT EXISTS poems(
        id TEXT PRIMARY KEY,
        title TEXT,
        poet TEXT,
        category TEXT,
        content TEXT,
        era TEXT,
        is_favorite INTEGER DEFAULT 0,
        date_added INTEGER DEFAULT (strftime('%s','now') * 1000)
      )
    ''');

    // جدول الإشارات المرجعية
    await db.execute('''
      CREATE TABLE bookmarks(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        book_id INTEGER,
        page INTEGER,
        note TEXT,
        timestamp INTEGER,
        FOREIGN KEY (book_id) REFERENCES books (id) ON DELETE CASCADE
      )
    ''');

    // جدول المفضلة - تم تحديثه ليتوافق مع جميع الاستخدامات
    await db.execute('''
      CREATE TABLE IF NOT EXISTS favorites(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        item_id TEXT NOT NULL,
        item_type TEXT NOT NULL,
        timestamp INTEGER DEFAULT (strftime('%s','now') * 1000),
        UNIQUE(item_id, item_type)
      )
    ''');

    // جدول آخر مواضع القراءة
    await db.execute('''
      CREATE TABLE IF NOT EXISTS reading_positions(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        book_id INTEGER UNIQUE,
        page INTEGER,
        timestamp INTEGER
      )
    ''');

    // جدول الفئات الرئيسية للأذكار
    await db.execute('''
      CREATE TABLE IF NOT EXISTS zikr_categories(
        id TEXT PRIMARY KEY,
        title TEXT,
        description TEXT,
        image_url TEXT,
        category TEXT,
        iconName TEXT
      )
    ''');

    // جدول الفئات الفرعية للأذكار
    await db.execute('''
      CREATE TABLE IF NOT EXISTS zikr_subcategories(
        id INTEGER PRIMARY KEY,
        parent_id TEXT,
        title TEXT,
        description TEXT,
        image_url TEXT,
        category TEXT,
        iconName TEXT,
        FOREIGN KEY (parent_id) REFERENCES zikr_categories(id)
      )
    ''');

    // جدول عناصر الأذكار
    await db.execute('''
      CREATE TABLE IF NOT EXISTS zikr_items(
        id INTEGER PRIMARY KEY,
        category_id INTEGER,
        subcategory_id INTEGER,
        text TEXT,
        count INTEGER,
        reference TEXT,
        FOREIGN KEY (category_id) REFERENCES zikr_categories(id),
        FOREIGN KEY (subcategory_id) REFERENCES zikr_subcategories(id)
      )
    ''');

    // جدول الإنجازات
    await db.execute('''
      CREATE TABLE IF NOT EXISTS zikr_completions(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        zikr_id INTEGER,
        zikr_type TEXT,
        timestamp INTEGER
      )
    ''');
  }

  // ترقية قاعدة البيانات
  Future<void> _upgradeDatabase(
      Database db, int oldVersion, int newVersion) async {
    debugPrint('ترقية قاعدة البيانات من الإصدار $oldVersion إلى $newVersion');

    // الترقية من الإصدار 1 إلى الإصدار 2
    if (oldVersion < 2) {
      // تحديث جدول الكتب لإضافة الأعمدة المفقودة
      try {
        // التحقق من وجود الأعمدة
        var columns = await db.rawQuery('PRAGMA table_info(books)');
        var columnNames = columns.map((c) => c['name'].toString()).toList();

        // إضافة الأعمدة المفقودة
        if (!columnNames.contains('local_cover_path')) {
          await db
              .execute('ALTER TABLE books ADD COLUMN local_cover_path TEXT');
        }
        if (!columnNames.contains('local_pdf_path')) {
          await db.execute('ALTER TABLE books ADD COLUMN local_pdf_path TEXT');
        }
        if (!columnNames.contains('pages')) {
          await db
              .execute('ALTER TABLE books ADD COLUMN pages INTEGER DEFAULT 0');
        }
        if (!columnNames.contains('date_added')) {
          await db.execute('ALTER TABLE books ADD COLUMN date_added INTEGER');
        }
        if (!columnNames.contains('tags')) {
          await db.execute('ALTER TABLE books ADD COLUMN tags TEXT');
        }
        if (!columnNames.contains('is_favorite')) {
          await db.execute(
              'ALTER TABLE books ADD COLUMN is_favorite INTEGER DEFAULT 0');
        }
        if (!columnNames.contains('rating')) {
          await db
              .execute('ALTER TABLE books ADD COLUMN rating REAL DEFAULT 0.0');
        }
        if (!columnNames.contains('download_progress')) {
          await db.execute(
              'ALTER TABLE books ADD COLUMN download_progress INTEGER DEFAULT -1');
        }
        if (!columnNames.contains('last_read_page')) {
          await db.execute(
              'ALTER TABLE books ADD COLUMN last_read_page INTEGER DEFAULT 0');
        }
        if (!columnNames.contains('last_read_date')) {
          await db
              .execute('ALTER TABLE books ADD COLUMN last_read_date INTEGER');
        }
      } catch (e) {
        debugPrint('خطأ في تحديث جدول الكتب: $e');
      }

      // إنشاء جداول الأذكار إذا لم تكن موجودة
      await db.execute('''
        CREATE TABLE IF NOT EXISTS zikr_categories(
          id TEXT PRIMARY KEY,
          title TEXT,
          description TEXT,
          image_url TEXT,
          category TEXT,
          iconName TEXT
        )
      ''');

      await db.execute('''
        CREATE TABLE IF NOT EXISTS zikr_subcategories(
          id INTEGER PRIMARY KEY,
          parent_id TEXT,
          title TEXT,
          description TEXT,
          image_url TEXT,
          category TEXT,
          iconName TEXT,
          FOREIGN KEY (parent_id) REFERENCES zikr_categories(id)
        )
      ''');

      await db.execute('''
        CREATE TABLE IF NOT EXISTS zikr_items(
          id INTEGER PRIMARY KEY,
          category_id INTEGER,
          subcategory_id INTEGER,
          text TEXT,
          count INTEGER,
          reference TEXT,
          FOREIGN KEY (category_id) REFERENCES zikr_categories(id),
          FOREIGN KEY (subcategory_id) REFERENCES zikr_subcategories(id)
        )
      ''');
    }

    // الترقية من الإصدار 2 إلى الإصدار 3
    if (oldVersion < 3) {
      debugPrint('تنفيذ ترقية من الإصدار 2 إلى الإصدار 3');

      // التحقق من وجود جدول الكتب
      try {
        var tableCheck = await db.rawQuery(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='books'");

        if (tableCheck.isNotEmpty) {
          // التحقق من وجود الأعمدة
          var columns = await db.rawQuery('PRAGMA table_info(books)');
          var columnNames = columns.map((c) => c['name'].toString()).toList();

          // إضافة الأعمدة المفقودة
          if (!columnNames.contains('local_cover_path')) {
            await db
                .execute('ALTER TABLE books ADD COLUMN local_cover_path TEXT');
            debugPrint('تمت إضافة عمود local_cover_path');
          }
          if (!columnNames.contains('local_pdf_path')) {
            await db
                .execute('ALTER TABLE books ADD COLUMN local_pdf_path TEXT');
            debugPrint('تمت إضافة عمود local_pdf_path');
          }
          if (!columnNames.contains('pages')) {
            await db.execute(
                'ALTER TABLE books ADD COLUMN pages INTEGER DEFAULT 0');
            debugPrint('تمت إضافة عمود pages');
          }
          if (!columnNames.contains('tags')) {
            await db.execute('ALTER TABLE books ADD COLUMN tags TEXT');
            debugPrint('تمت إضافة عمود tags');
          }
        } else {
          // إنشاء جدول الكتب إذا لم يكن موجودًا
          await db.execute('''
            CREATE TABLE IF NOT EXISTS books(
              id TEXT PRIMARY KEY,
              title TEXT,
              author TEXT,
              description TEXT,
              cover_url TEXT,
              local_cover_path TEXT,
              category TEXT,
              pdf_url TEXT,
              pdf_path TEXT,
              local_pdf_path TEXT,
              pages INTEGER DEFAULT 0,
              date_added INTEGER,
              tags TEXT,
              is_favorite INTEGER DEFAULT 0,
              rating REAL DEFAULT 0.0,
              download_progress INTEGER DEFAULT -1,
              last_read_page INTEGER DEFAULT 0,
              last_read_date INTEGER
            )
          ''');
          debugPrint('تم إنشاء جدول الكتب من جديد');
        }
      } catch (e) {
        debugPrint('خطأ في ترقية قاعدة البيانات إلى الإصدار 3: $e');
      }
    }

    // الترقية من الإصدار 3 إلى الإصدار 4
    if (oldVersion < 4) {
      debugPrint('تنفيذ ترقية من الإصدار 3 إلى الإصدار 4');

      try {
        // إعادة إنشاء جدول الكتب بالكامل
        // هذا سيحل مشكلة الأعمدة المفقودة

        // أولاً، نحفظ البيانات الحالية
        List<Map<String, dynamic>> existingBooks = [];
        try {
          existingBooks = await db.query('books');
          debugPrint(
              'تم حفظ ${existingBooks.length} كتاب من قاعدة البيانات الحالية');
        } catch (e) {
          debugPrint('خطأ في استرجاع الكتب الحالية: $e');
        }

        // ثانياً، نحذف الجدول الحالي
        try {
          await db.execute('DROP TABLE IF EXISTS books');
          debugPrint('تم حذف جدول الكتب الحالي');
        } catch (e) {
          debugPrint('خطأ في حذف جدول الكتب: $e');
        }

        // ثالثاً، نعيد إنشاء الجدول بالهيكل الصحيح
        await db.execute('''
          CREATE TABLE IF NOT EXISTS books(
            id TEXT PRIMARY KEY,
            title TEXT,
            author TEXT,
            description TEXT,
            cover_url TEXT,
            local_cover_path TEXT,
            category TEXT,
            pdf_url TEXT,
            pdf_path TEXT,
            local_pdf_path TEXT,
            pages INTEGER DEFAULT 0,
            date_added INTEGER,
            tags TEXT,
            is_favorite INTEGER DEFAULT 0,
            rating REAL DEFAULT 0.0,
            download_progress INTEGER DEFAULT -1,
            last_read_page INTEGER DEFAULT 0,
            last_read_date INTEGER
          )
        ''');
        debugPrint('تم إعادة إنشاء جدول الكتب بالهيكل الصحيح');

        // رابعاً، نعيد إدخال البيانات
        if (existingBooks.isNotEmpty) {
          for (var book in existingBooks) {
            try {
              // إنشاء سجل جديد مع التأكد من وجود جميع الأعمدة المطلوبة
              Map<String, dynamic> newBook = {
                'id': book['id'],
                'title': book['title'] ?? '',
                'author': book['author'] ?? '',
                'description': book['description'] ?? '',
                'cover_url': book['cover_url'] ?? '',
                'local_cover_path': book['local_cover_path'] ?? '',
                'category': book['category'] ?? '',
                'pdf_url': book['pdf_url'] ?? '',
                'pdf_path': book['pdf_path'] ?? '',
                'local_pdf_path': book['local_pdf_path'] ?? '',
                'pages': book['pages'] ?? 0,
                'date_added':
                    book['date_added'] ?? DateTime.now().millisecondsSinceEpoch,
                'tags': book['tags'] ?? '',
                'is_favorite': book['is_favorite'] ?? 0,
                'rating': book['rating'] ?? 0.0,
                'download_progress': book['download_progress'] ?? -1,
                'last_read_page': book['last_read_page'] ?? 0,
                'last_read_date': book['last_read_date'] ?? 0,
              };

              await db.insert('books', newBook,
                  conflictAlgorithm: ConflictAlgorithm.replace);
            } catch (e) {
              debugPrint('خطأ في إعادة إدخال الكتاب: $e');
            }
          }
          debugPrint('تمت إعادة إدخال ${existingBooks.length} كتاب');
        }

        // خامساً، نتحقق من جدول المفضلة ونعيد إنشائه إذا لزم الأمر
        try {
          // حفظ البيانات الحالية من جدول المفضلة
          List<Map<String, dynamic>> existingFavorites = [];
          try {
            existingFavorites = await db.query('favorites');
            debugPrint(
                'تم حفظ ${existingFavorites.length} عنصر مفضل من قاعدة البيانات');
          } catch (e) {
            debugPrint('خطأ في استرجاع المفضلة الحالية: $e');
          }

          // حذف جدول المفضلة الحالي
          await db.execute('DROP TABLE IF EXISTS favorites');
          debugPrint('تم حذف جدول المفضلة الحالي');

          // إعادة إنشاء جدول المفضلة بالهيكل الصحيح
          await db.execute('''
            CREATE TABLE IF NOT EXISTS favorites(
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              item_id TEXT NOT NULL,
              item_type TEXT NOT NULL,
              timestamp INTEGER DEFAULT (strftime('%s','now') * 1000),
              UNIQUE(item_id, item_type)
            )
          ''');
          debugPrint('تم إعادة إنشاء جدول المفضلة بالهيكل الصحيح');

          // إعادة إدخال البيانات المفضلة
          if (existingFavorites.isNotEmpty) {
            for (var favorite in existingFavorites) {
              try {
                // تحويل البيانات القديمة إلى الهيكل الجديد
                Map<String, dynamic> newFavorite = {
                  'item_id': favorite['item_id'] ??
                      favorite['book_id']?.toString() ??
                      '',
                  'item_type':
                      favorite['item_type'] ?? favorite['type'] ?? 'book',
                  'timestamp': favorite['timestamp'] ??
                      DateTime.now().millisecondsSinceEpoch,
                };

                await db.insert('favorites', newFavorite,
                    conflictAlgorithm: ConflictAlgorithm.replace);
              } catch (e) {
                debugPrint('خطأ في إعادة إدخال المفضلة: $e');
              }
            }
            debugPrint('تمت إعادة إدخال ${existingFavorites.length} عنصر مفضل');
          }
        } catch (e) {
          debugPrint('خطأ في تحديث جدول المفضلة: $e');
        }
      } catch (e) {
        debugPrint('خطأ في ترقية قاعدة البيانات إلى الإصدار 4: $e');
      }
    }

    // الترقية من الإصدار 4 إلى الإصدار 5
    if (oldVersion < 5) {
      debugPrint('تنفيذ ترقية من الإصدار 4 إلى الإصدار 5');

      try {
        // التحقق من هيكل جدول المفضلة
        var favoritesTableCheck = await db.rawQuery(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='favorites'");

        if (favoritesTableCheck.isNotEmpty) {
          // التحقق من هيكل الجدول
          var columns = await db.rawQuery('PRAGMA table_info(favorites)');
          var columnNames = columns.map((c) => c['name'].toString()).toList();

          debugPrint('أعمدة جدول المفضلة: $columnNames');

          // التحقق من وجود الأعمدة المطلوبة
          bool hasItemId = columnNames.contains('item_id');
          bool hasItemType = columnNames.contains('item_type');
          bool hasType = columnNames.contains('type');

          // إذا كان الجدول يستخدم 'type' بدلاً من 'item_type' أو لا يحتوي على 'item_id'
          if (!hasItemId || (hasType && !hasItemType)) {
            debugPrint('تحديث هيكل جدول المفضلة لاستخدام item_id و item_type');

            // حفظ البيانات الحالية
            List<Map<String, dynamic>> existingFavorites = [];
            try {
              existingFavorites = await db.query('favorites');
              debugPrint(
                  'تم حفظ ${existingFavorites.length} عنصر مفضل من قاعدة البيانات الحالية');
            } catch (e) {
              debugPrint('خطأ في استرجاع العناصر المفضلة الحالية: $e');
            }

            // حذف الجدول الحالي
            await db.execute('DROP TABLE IF EXISTS favorites');
            debugPrint('تم حذف جدول المفضلة الحالي');

            // إنشاء الجدول بالهيكل الجديد
            await db.execute('''
              CREATE TABLE IF NOT EXISTS favorites(
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                item_id TEXT NOT NULL,
                item_type TEXT NOT NULL,
                timestamp INTEGER DEFAULT (strftime('%s','now') * 1000),
                UNIQUE(item_id, item_type)
              )
            ''');
            debugPrint('تم إنشاء جدول المفضلة بالهيكل الجديد');

            // إعادة إدخال البيانات
            for (var favorite in existingFavorites) {
              try {
                await db.insert(
                  'favorites',
                  {
                    'item_id': favorite['item_id'] ??
                        favorite['book_id']?.toString() ??
                        '',
                    'item_type':
                        favorite['item_type'] ?? favorite['type'] ?? 'book',
                    'timestamp': favorite['timestamp'] ??
                        DateTime.now().millisecondsSinceEpoch,
                  },
                  conflictAlgorithm: ConflictAlgorithm.replace,
                );
              } catch (e) {
                debugPrint('خطأ في إعادة إدخال عنصر مفضل: $e');
              }
            }
            debugPrint('تمت إعادة إدخال ${existingFavorites.length} عنصر مفضل');
          } else {
            debugPrint('جدول المفضلة لديه الهيكل الصحيح بالفعل');
          }
        } else {
          // إنشاء جدول المفضلة إذا لم يكن موجوداً
          await db.execute('''
            CREATE TABLE IF NOT EXISTS favorites(
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              item_id TEXT NOT NULL,
              item_type TEXT NOT NULL,
              timestamp INTEGER DEFAULT (strftime('%s','now') * 1000),
              UNIQUE(item_id, item_type)
            )
          ''');
          debugPrint('تم إنشاء جدول المفضلة لأنه لم يكن موجوداً');
        }
      } catch (e) {
        debugPrint('خطأ في ترقية قاعدة البيانات إلى الإصدار 5: $e');
      }
    }
  }

  // إدخال ذكر جديد
  Future<int> insertDhikr(String name, int count) async {
    // إنشاء معرف فريد
    final id = DateTime.now().millisecondsSinceEpoch;

    // إنشاء الذكر الجديد
    final Map<String, dynamic> newDhikr = {
      'id': id,
      'name': name,
      'count': count,
    };

    // استرجاع الأذكار الحالية
    final dhikrs = await getDhikrs();

    // إضافة الذكر الجديد
    dhikrs.add(newDhikr);

    // حفظ القائمة المحدثة
    await saveDhikrs(dhikrs);

    return id;
  }

  // تحديث هدف ذكر
  Future<void> updateDhikrTarget(int id, int newTarget) async {
    // استرجاع الأذكار الحالية
    final dhikrs = await getDhikrs();

    // تحديث هدف الذكر المطلوب
    for (var i = 0; i < dhikrs.length; i++) {
      if (dhikrs[i]['id'] == id) {
        dhikrs[i]['count'] = newTarget;
        break;
      }
    }

    // حفظ القائمة المحدثة
    await saveDhikrs(dhikrs);
  }

  // استرجاع قائمة الأذكار
  Future<List<Map<String, dynamic>>> getDhikrs() async {
    final prefs = await SharedPreferences.getInstance();

    // استرجاع الأذكار المخزنة
    final List<String>? savedDhikrsJson = prefs.getStringList('dhikrs');

    debugPrint('استرجاع الأذكار من SharedPreferences');
    if (savedDhikrsJson != null) {
      debugPrint('تم العثور على ${savedDhikrsJson.length} ذكر مخزن');
    } else {
      debugPrint('لم يتم العثور على أذكار مخزنة');
    }

    if (savedDhikrsJson == null || savedDhikrsJson.isEmpty) {
      debugPrint('إنشاء قائمة الأذكار الافتراضية');
      // إنشاء قائمة افتراضية
      final defaultDhikrs = [
        {
          'id': 1,
          'name': 'سبحان الله',
          'count': 33,
          'isDefault': true,
          'arabicText': 'سُبْحَانَ اللهِ',
          'transliteration': 'Subhan Allah',
          'translation': 'Glory be to Allah'
        },
        {
          'id': 2,
          'name': 'الحمد لله',
          'count': 33,
          'isDefault': true,
          'arabicText': 'الْحَمْدُ لِلَّهِ',
          'transliteration': 'Alhamdulillah',
          'translation': 'Praise be to Allah'
        },
        {
          'id': 3,
          'name': 'الله أكبر',
          'count': 33,
          'isDefault': true,
          'arabicText': 'اللهُ أَكْبَرُ',
          'transliteration': 'Allahu Akbar',
          'translation': 'Allah is the Greatest'
        },
        {
          'id': 4,
          'name': 'لا إله إلا الله',
          'count': 33,
          'isDefault': true,
          'arabicText': 'لَا إِلَهَ إِلَّا اللهُ',
          'transliteration': 'La ilaha illallah',
          'translation': 'There is no god but Allah'
        },
        {
          'id': 5,
          'name': 'استغفر الله',
          'count': 33,
          'isDefault': true,
          'arabicText': 'أَسْتَغْفِرُ اللهَ',
          'transliteration': 'Astaghfirullah',
          'translation': 'I seek forgiveness from Allah'
        },
        {
          'id': 6,
          'name': 'سبحان الله وبحمده',
          'count': 33,
          'isDefault': true,
          'arabicText': 'سُبْحَانَ اللهِ وَبِحَمْدِهِ',
          'transliteration': 'Subhan Allah wa bihamdihi',
          'translation': 'Glory be to Allah and praise Him'
        },
        {
          'id': 7,
          'name': 'لا حول ولا قوة إلا بالله',
          'count': 33,
          'isDefault': true,
          'arabicText': 'لَا حَوْلَ وَلَا قُوَّةَ إِلَّا بِاللهِ',
          'transliteration': 'La hawla wala quwwata illa billah',
          'translation': 'There is no might nor power except with Allah'
        },
        {
          'id': 8,
          'name': 'اللهم صل على محمد',
          'count': 33,
          'isDefault': true,
          'arabicText':
              'اللَّهُمَّ صَلِّ عَلَى مُحَمَّدٍ وَعَلَى آلِ مُحَمَّدٍ',
          'transliteration':
              'Allahumma salli ala Muhammad wa ala aali Muhammad',
          'translation':
              'O Allah, send blessings upon Muhammad and the family of Muhammad'
        },
      ];

      // طباعة معلومات عن الأذكار الافتراضية
      for (var dhikr in defaultDhikrs) {
        debugPrint(
            'ذكر افتراضي: ${dhikr['name']}, المعرف: ${dhikr['id']}, العدد: ${dhikr['count']}');
      }

      // حفظ الأذكار الافتراضية
      await saveDhikrs(defaultDhikrs);

      return defaultDhikrs;
    }

    // تحويل JSON إلى كائنات
    List<Map<String, dynamic>> dhikrs = [];
    for (final jsonStr in savedDhikrsJson) {
      try {
        final map = json.decode(jsonStr) as Map<String, dynamic>;

        // التأكد من وجود خاصية isDefault
        // إذا كان المعرف أقل من أو يساوي 8، فهو ذكر افتراضي
        if (!map.containsKey('isDefault')) {
          map['isDefault'] = (map['id'] as int) <= 8;
        }

        dhikrs.add(map);
        debugPrint(
            'تم تحويل ذكر: ${map['name']}, المعرف: ${map['id']}, العدد: ${map['count']}');
      } catch (e) {
        debugPrint('خطأ في تحويل JSON للذكر: $e');
        debugPrint('النص المسبب للخطأ: $jsonStr');
      }
    }

    // إذا كانت القائمة فارغة بعد التحويل (بسبب أخطاء التحويل)، استخدم القائمة الافتراضية
    if (dhikrs.isEmpty) {
      debugPrint('القائمة فارغة بعد التحويل، استخدام القائمة الافتراضية');
      final defaultDhikrs = [
        {
          'id': 1,
          'name': 'سبحان الله',
          'count': 33,
          'isDefault': true,
          'arabicText': 'سُبْحَانَ اللهِ',
          'transliteration': 'Subhan Allah',
          'translation': 'Glory be to Allah'
        },
        {
          'id': 2,
          'name': 'الحمد لله',
          'count': 33,
          'isDefault': true,
          'arabicText': 'الْحَمْدُ لِلَّهِ',
          'transliteration': 'Alhamdulillah',
          'translation': 'Praise be to Allah'
        },
        {
          'id': 3,
          'name': 'الله أكبر',
          'count': 33,
          'isDefault': true,
          'arabicText': 'اللهُ أَكْبَرُ',
          'transliteration': 'Allahu Akbar',
          'translation': 'Allah is the Greatest'
        },
        {
          'id': 4,
          'name': 'لا إله إلا الله',
          'count': 33,
          'isDefault': true,
          'arabicText': 'لَا إِلَهَ إِلَّا اللهُ',
          'transliteration': 'La ilaha illallah',
          'translation': 'There is no god but Allah'
        },
        {
          'id': 5,
          'name': 'استغفر الله',
          'count': 33,
          'isDefault': true,
          'arabicText': 'أَسْتَغْفِرُ اللهَ',
          'transliteration': 'Astaghfirullah',
          'translation': 'I seek forgiveness from Allah'
        },
        {
          'id': 6,
          'name': 'سبحان الله وبحمده',
          'count': 33,
          'isDefault': true,
          'arabicText': 'سُبْحَانَ اللهِ وَبِحَمْدِهِ',
          'transliteration': 'Subhan Allah wa bihamdihi',
          'translation': 'Glory be to Allah and praise Him'
        },
        {
          'id': 7,
          'name': 'لا حول ولا قوة إلا بالله',
          'count': 33,
          'isDefault': true,
          'arabicText': 'لَا حَوْلَ وَلَا قُوَّةَ إِلَّا بِاللهِ',
          'transliteration': 'La hawla wala quwwata illa billah',
          'translation': 'There is no might nor power except with Allah'
        },
        {
          'id': 8,
          'name': 'اللهم صل على محمد',
          'count': 33,
          'isDefault': true,
          'arabicText':
              'اللَّهُمَّ صَلِّ عَلَى مُحَمَّدٍ وَعَلَى آلِ مُحَمَّدٍ',
          'transliteration':
              'Allahumma salli ala Muhammad wa ala aali Muhammad',
          'translation':
              'O Allah, send blessings upon Muhammad and the family of Muhammad'
        },
      ];

      // حفظ الأذكار الافتراضية
      await saveDhikrs(defaultDhikrs);

      return defaultDhikrs;
    }

    return dhikrs;
  }

  // إضافة دالة جديدة لحفظ الأذكار
  Future<void> saveDhikrs(List<Map<String, dynamic>> dhikrs) async {
    final prefs = await SharedPreferences.getInstance();

    // تحويل الخرائط إلى سلاسل JSON
    final List<String> dhikrsJson =
        dhikrs.map((dhikr) => json.encode(dhikr)).toList();

    debugPrint('حفظ ${dhikrs.length} ذكر في SharedPreferences');
    for (var i = 0; i < dhikrs.length; i++) {
      debugPrint(
          'حفظ الذكر $i: ${dhikrs[i]['name']}, المعرف: ${dhikrs[i]['id']}');
    }

    // حفظ القائمة
    await prefs.setStringList('dhikrs', dhikrsJson);

    // التحقق من الحفظ
    final savedList = prefs.getStringList('dhikrs');
    if (savedList != null) {
      debugPrint('تم التحقق من الحفظ: ${savedList.length} ذكر');
    } else {
      debugPrint('فشل التحقق من الحفظ: لم يتم العثور على القائمة');
    }
  }

  // تحديث مسار ملف PDF في قاعدة البيانات
  Future<void> updateBookPdfPath(int bookId, String pdfPath) async {
    final db = await database;
    await db.update(
      'books',
      {
        'pdf_path': pdfPath,
        'download_progress': 100, // اكتمل التنزيل
        'last_read_page': 0, // إعادة تعيين صفحة القراءة
      },
      where: 'id = ?',
      whereArgs: [bookId],
    );
  }

  // تحديث حالة تنزيل الكتاب
  Future<void> updateBookDownloadProgress(int bookId, int progress) async {
    final db = await database;
    await db.update(
      'books',
      {'download_progress': progress},
      where: 'id = ?',
      whereArgs: [bookId],
    );
  }

  // جلب كتب مع معلومات PDF
  Future<List<Map<String, dynamic>>> getBooksWithPdf() async {
    final db = await database;
    return await db.query(
      'books',
      columns: [
        'id',
        'title',
        'author',
        'description',
        'cover_url',
        'category',
        'pdf_url',
        'pdf_path'
      ],
    );
  }

  // حفظ إشارة مرجعية باسم الصفحة وملاحظة
  Future<int> saveBookmark(int bookId, int page, String? note) async {
    final db = await database;
    return await db.insert('bookmarks', {
      'book_id': bookId,
      'page': page,
      'note': note,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  // جلب الإشارات المرجعية لكتاب
  Future<List<Map<String, dynamic>>> getBookmarks(int bookId) async {
    final db = await database;
    return await db.query(
      'bookmarks',
      where: 'book_id = ?',
      whereArgs: [bookId],
      orderBy: 'timestamp DESC',
    );
  }

  // حذف إشارة مرجعية
  Future<int> deleteBookmark(int id) async {
    final db = await database;
    return await db.delete(
      'bookmarks',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // حفظ آخر موضع قراءة
  Future<int?> getLastReadPosition(int bookId) async {
    final db = await database;
    final result = await db.query(
      'reading_positions',
      columns: ['page'],
      where: 'book_id = ?',
      whereArgs: [bookId],
    );

    if (result.isNotEmpty) {
      return result.first['page'] as int;
    }
    return null;
  }

  // إدراج كتاب جديد
  Future<int> insertBook(Map<String, dynamic> bookData) async {
    final db = await database;
    return await db.insert('books', {
      'id': bookData['id'] is String
          ? int.parse(bookData['id'].replaceAll('b', ''))
          : bookData['id'],
      'title': bookData['title'],
      'author': bookData['author'] ?? '',
      'description': bookData['description'] ?? '',
      'cover_url': bookData['coverUrl'] ?? '',
      'category': bookData['category'] ?? '',
      'pdf_url': bookData['pdfUrl'] ?? '',
      'pdf_path': bookData['pdfPath'] ?? '',
      'local_pdf_path': bookData['localPdfPath'] ?? '',
      'pages': bookData['pages'] ?? 0,
      'date_added': DateTime.now().millisecondsSinceEpoch,
    });
  }

  // استيراد الكتب من ملف JSON - تم تحسينه للتعامل مع الأعمدة الجديدة
  Future<void> importBooksFromJson(List<dynamic> booksData) async {
    try {
      // إعادة تعيين قاعدة البيانات لحل مشكلة الأعمدة المفقودة
      try {
        await resetDatabase();
        debugPrint('تم إعادة تعيين قاعدة البيانات قبل استيراد الكتب');
      } catch (e) {
        debugPrint('خطأ في إعادة تعيين قاعدة البيانات: $e');
      }

      final db = await database;

      // استيراد البيانات
      for (var bookData in booksData) {
        try {
          // معالجة معرف الكتاب
          String bookId = bookData['id'].toString();

          // تخزين الوسوم كنص مفصول بفواصل إذا كانت موجودة
          final List<dynamic> tags = bookData['tags'] ?? [];
          final String tagsString = tags.join(', ');

          // إعداد بيانات الكتاب للإدخال مع جميع الأعمدة المطلوبة
          Map<String, dynamic> bookMap = {
            'id': bookId,
            'title': bookData['title'] ?? '',
            'author': bookData['author'] ?? '',
            'description': bookData['description'] ?? '',
            'cover_url': bookData['coverUrl'] ?? '',
            'local_cover_path': bookData['localCoverPath'] ?? '',
            'category': bookData['category'] ?? '',
            'pdf_url': bookData['pdfUrl'] ?? '',
            'pdf_path': bookData['pdfPath'] ?? '',
            'local_pdf_path': bookData['localPdfPath'] ?? '',
            'pages': bookData['pages'] ?? 0,
            'date_added': DateTime.now().millisecondsSinceEpoch,
            'tags': tagsString,
            'is_favorite': 0,
            'rating': 0.0,
            'download_progress': -1,
            'last_read_page': 0,
            'last_read_date': 0,
          };

          await db.insert(
            'books',
            bookMap,
            conflictAlgorithm: ConflictAlgorithm.replace,
          );

          debugPrint('تم إدخال الكتاب: ${bookData['title']}');
        } catch (e) {
          debugPrint('خطأ في إدخال كتاب: $e');
        }
      }

      debugPrint('تم استيراد ${booksData.length} كتاب بنجاح');
    } catch (e) {
      debugPrint('خطأ في استيراد الكتب: $e');
      rethrow; // إعادة رمي الخطأ للتعامل معه في المستوى الأعلى
    }
  }

  // الحصول على الكتب المفضلة
  Future<List<Map<String, dynamic>>> getFavoriteBooks() async {
    final db = await database;
    return await db.query(
      'favorites',
      where: 'item_type = ?',
      whereArgs: ['book'],
    );
  }

  // التحقق مما إذا كان العنصر مفضلاً
  Future<bool> isFavorite(dynamic itemId, String itemType) async {
    try {
      final db = await database;

      // تحويل المعرف إلى نص بغض النظر عن نوعه
      String itemIdStr = itemId.toString();

      // التحقق من وجود جدول المفضلة
      var tableCheck = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='favorites'");

      if (tableCheck.isEmpty) {
        return false; // الجدول غير موجود
      }

      // البحث عن العنصر في المفضلة
      final List<Map<String, dynamic>> result = await db.query(
        'favorites',
        where: 'item_id = ? AND item_type = ?',
        whereArgs: [itemIdStr, itemType],
      );

      return result.isNotEmpty;
    } catch (e) {
      debugPrint('خطأ في التحقق من المفضلة: $e');
      return false;
    }
  }

  // الحصول على كتاب بواسطة المعرف - تم تحسينه للتعامل مع المعرفات النصية
  Future<Map<String, dynamic>?> getBookById(dynamic id) async {
    try {
      final db = await database;
      debugPrint(
          'محاولة الحصول على الكتاب بالمعرف: $id (النوع: ${id.runtimeType})');

      // تحويل المعرف إلى نص للتوافق مع الأنواع المختلفة
      String idStr = id.toString();

      // البحث عن الكتاب باستخدام المعرف كنص
      List<Map<String, dynamic>> results = await db.query(
        'books',
        where: 'id = ?',
        whereArgs: [idStr],
      );

      if (results.isNotEmpty) {
        debugPrint('تم العثور على الكتاب بالمعرف النصي: $idStr');
        return results.first;
      }

      // إذا لم يتم العثور على الكتاب، حاول تحويل المعرف إلى رقم والبحث مرة أخرى
      try {
        int numericId = int.parse(idStr);
        results = await db.query(
          'books',
          where: 'id = ?',
          whereArgs: [numericId],
        );

        if (results.isNotEmpty) {
          debugPrint('تم العثور على الكتاب بالمعرف الرقمي: $numericId');
          return results.first;
        }
      } catch (e) {
        debugPrint('خطأ في تحويل المعرف إلى رقم: $e');
      }

      // محاولة البحث باستخدام المعرف مع إضافة 'b' في البداية
      if (!idStr.startsWith('b') && idStr.isNotEmpty) {
        String bIdStr = 'b$idStr';
        results = await db.query(
          'books',
          where: 'id = ?',
          whereArgs: [bIdStr],
        );

        if (results.isNotEmpty) {
          debugPrint('تم العثور على الكتاب بالمعرف مع إضافة b: $bIdStr');
          return results.first;
        }
      }

      // محاولة البحث باستخدام المعرف بدون 'b' إذا كان يبدأ بها
      if (idStr.startsWith('b')) {
        String numericIdStr = idStr.substring(1);
        results = await db.query(
          'books',
          where: 'id = ?',
          whereArgs: [numericIdStr],
        );

        if (results.isNotEmpty) {
          debugPrint('تم العثور على الكتاب بالمعرف بدون b: $numericIdStr');
          return results.first;
        }

        // محاولة تحويل المعرف بدون 'b' إلى رقم
        try {
          int numericId = int.parse(numericIdStr);
          results = await db.query(
            'books',
            where: 'id = ?',
            whereArgs: [numericId],
          );

          if (results.isNotEmpty) {
            debugPrint(
                'تم العثور على الكتاب بالمعرف الرقمي بدون b: $numericId');
            return results.first;
          }
        } catch (e) {
          debugPrint('خطأ في تحويل المعرف بدون b إلى رقم: $e');
        }
      }

      // إذا لم يتم العثور على الكتاب، قم بالبحث عن جميع الكتب والمقارنة
      final List<Map<String, dynamic>> allBooks = await db.query('books');
      debugPrint('تم الحصول على ${allBooks.length} كتاب للبحث');

      for (var book in allBooks) {
        String bookId = book['id'].toString();
        // مقارنة بعدة طرق
        if (bookId == idStr ||
            bookId == 'b$idStr' ||
            (idStr.startsWith('b') && bookId == idStr.substring(1)) ||
            (bookId.startsWith('b') && bookId.substring(1) == idStr)) {
          debugPrint(
              'تم العثور على الكتاب بالمقارنة المباشرة: ${book['title']}');
          return book;
        }

        // محاولة المقارنة بالأرقام
        try {
          int numericBookId = int.parse(bookId.replaceAll('b', ''));
          int numericSearchId = int.parse(idStr.replaceAll('b', ''));
          if (numericBookId == numericSearchId) {
            debugPrint(
                'تم العثور على الكتاب بالمقارنة الرقمية: ${book['title']}');
            return book;
          }
        } catch (e) {
          // تجاهل أخطاء التحويل
        }
      }

      debugPrint('لم يتم العثور على الكتاب بالمعرف: $id');
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على الكتاب بالمعرف: $e');
      return null;
    }
  }

  // استيراد القصائد من ملف JSON
  Future<void> importPoemsFromJson(Database db) async {
    try {
      debugPrint('بدء استيراد القصائد من ملف JSON');

      // قراءة ملف البيانات
      final String jsonString =
          await rootBundle.loadString('assets/data/poems.json');
      final Map<String, dynamic> data = json.decode(jsonString);
      final List<dynamic> poemsData = data['poems'];

      debugPrint('تم العثور على ${poemsData.length} قصيدة في ملف JSON');

      // حفظ البيانات في قاعدة البيانات
      for (var poemData in poemsData) {
        try {
          // تخزين القصيدة في قاعدة البيانات
          await db.insert(
            'poems',
            {
              'id': poemData['id'].toString(),
              'title': poemData['title'] ?? '',
              'poet': poemData['poet'] ?? '',
              'category': poemData['category'] ?? '',
              'content': poemData['content'] ?? '',
              'era': poemData['era'] ?? '',
              'is_favorite': 0,
              'date_added': DateTime.now().millisecondsSinceEpoch,
            },
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        } catch (e) {
          debugPrint('خطأ في إدخال قصيدة: $e');
        }
      }

      debugPrint('تم استيراد القصائد بنجاح');
    } catch (e) {
      debugPrint('خطأ في استيراد القصائد: $e');
    }
  }

  // الحصول على قصيدة بواسطة المعرف
  Future<Map<String, dynamic>?> getPoemById(dynamic id) async {
    try {
      final db = await database;
      debugPrint(
          'محاولة الحصول على القصيدة بالمعرف: $id (النوع: ${id.runtimeType})');

      // تحويل المعرف إلى نص للتوافق مع الأنواع المختلفة
      String idStr = id.toString();

      // البحث عن القصيدة باستخدام المعرف كنص
      List<Map<String, dynamic>> results = await db.query(
        'poems',
        where: 'id = ?',
        whereArgs: [idStr],
      );

      if (results.isNotEmpty) {
        debugPrint('تم العثور على القصيدة بالمعرف النصي: $idStr');
        return results.first;
      }

      // محاولة البحث باستخدام hashCode
      try {
        int hashCode = int.parse(idStr);
        results = await db.rawQuery(
          'SELECT * FROM poems WHERE id = ? OR id = ?',
          [idStr, hashCode.toString()],
        );

        if (results.isNotEmpty) {
          debugPrint('تم العثور على القصيدة باستخدام hashCode: $hashCode');
          return results.first;
        }
      } catch (e) {
        debugPrint('خطأ في تحويل المعرف إلى hashCode: $e');
      }

      // إذا لم يتم العثور على القصيدة، قم بالبحث عن جميع القصائد والمقارنة
      final List<Map<String, dynamic>> allPoems = await db.query('poems');
      debugPrint('تم الحصول على ${allPoems.length} قصيدة للبحث');

      for (var poem in allPoems) {
        String poemId = poem['id'].toString();
        // مقارنة بعدة طرق
        if (poemId == idStr ||
            poemId.hashCode.toString() == idStr ||
            idStr.hashCode.toString() == poemId) {
          debugPrint(
              'تم العثور على القصيدة بالمقارنة المباشرة: ${poem['title']}');
          return poem;
        }
      }

      debugPrint('لم يتم العثور على القصيدة بالمعرف: $id');
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على القصيدة بالمعرف: $e');
      return null;
    }
  }

  // الحصول على القصائد
  Future<List<Poem>> getPoems() async {
    try {
      final db = await database;

      // التحقق من وجود جدول القصائد
      var tableCheck = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='poems'");

      // إذا لم يكن الجدول موجوداً، قم بإنشائه
      if (tableCheck.isEmpty) {
        await db.execute('''
          CREATE TABLE IF NOT EXISTS poems(
            id TEXT PRIMARY KEY,
            title TEXT,
            poet TEXT,
            category TEXT,
            content TEXT,
            era TEXT,
            is_favorite INTEGER DEFAULT 0,
            date_added INTEGER DEFAULT (strftime('%s','now') * 1000)
          )
        ''');

        // استيراد القصائد من ملف JSON
        await importPoemsFromJson(db);
      }

      // التحقق من وجود بيانات في الجدول
      final poemCount = Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM poems'));
      if (poemCount == 0) {
        // لا توجد بيانات، قم باستيراد القصائد
        await importPoemsFromJson(db);
      }

      // الحصول على القصائد من قاعدة البيانات
      final List<Map<String, dynamic>> dbPoems = await db.query('poems');

      // إذا كانت قاعدة البيانات تحتوي على القصائد
      if (dbPoems.isNotEmpty) {
        // الحصول على حالة المفضلة من جدول المفضلة
        // التحقق من وجود الأعمدة المطلوبة
        final columns = await db.rawQuery('PRAGMA table_info(favorites)');
        final columnNames = columns.map((c) => c['name'].toString()).toList();

        // تحديد أي عمود يجب استخدامه
        final typeColumnName =
            columnNames.contains('item_type') ? 'item_type' : 'type';

        final List<Map<String, dynamic>> favorites = await db.query('favorites',
            where: '$typeColumnName = ?', whereArgs: ['poem']);
        final Set<String> favoriteIds =
            favorites.map((f) => f['item_id'].toString()).toSet();

        return dbPoems.map((map) {
          final poem = Poem.fromMap(map);
          // تحديث حالة المفضلة
          return poem.copyWith(isFavorite: favoriteIds.contains(poem.id));
        }).toList();
      }

      // إذا لم يتم العثور على قصائد، حاول استيرادها مرة أخرى
      await importPoemsFromJson(db);
      final List<Map<String, dynamic>> importedPoems = await db.query('poems');

      if (importedPoems.isNotEmpty) {
        final columns = await db.rawQuery('PRAGMA table_info(favorites)');
        final columnNames = columns.map((c) => c['name'].toString()).toList();
        final typeColumnName =
            columnNames.contains('item_type') ? 'item_type' : 'type';

        final List<Map<String, dynamic>> favorites = await db.query('favorites',
            where: '$typeColumnName = ?', whereArgs: ['poem']);
        final Set<String> favoriteIds =
            favorites.map((f) => f['item_id'].toString()).toSet();

        return importedPoems.map((map) {
          final poem = Poem.fromMap(map);
          return poem.copyWith(isFavorite: favoriteIds.contains(poem.id));
        }).toList();
      }

      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على القصائد: $e');
      return [];
    }
  }

  // الحصول على جميع الكتب
  Future<List<Book>> getBooks() async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query('books');

      // التحقق من حالة المفضلة من جدول المفضلة
      final List<Map<String, dynamic>> favorites = await db.query(
        'favorites',
        where: 'item_type = ?',
        whereArgs: ['book'],
      );

      // إنشاء مجموعة من معرفات الكتب المفضلة
      final Set<String> favoriteIds =
          favorites.map((f) => f['item_id'].toString()).toSet();

      // تحويل البيانات إلى قائمة من الكتب
      return List.generate(maps.length, (i) {
        final map = maps[i];

        // استخراج الوسوم إذا كانت موجودة
        List<String> tags = [];
        if (map['tags'] != null && map['tags'].toString().isNotEmpty) {
          tags = map['tags'].toString().split(', ');
        }

        // التحقق من حالة المفضلة
        final bool isFavorite = favoriteIds.contains(map['id'].toString()) ||
            (map['is_favorite'] == 1);

        return Book(
          id: map['id'],
          title: map['title'] ?? '',
          author: map['author'] ?? '',
          description: map['description'] ?? '',
          coverUrl: map['cover_url'] ?? '',
          localCoverPath: map['local_cover_path'],
          category: map['category'] ?? '',
          pdfUrl: map['pdf_url'] ?? '',
          pdfPath: map['pdf_path'],
          localPdfPath: map['local_pdf_path'],
          pages: map['pages'] ?? 0,
          tags: tags,
          isFavorite: isFavorite,
        );
      });
    } catch (e) {
      debugPrint('خطأ في الحصول على الكتب: $e');
      return [];
    }
  }

  // إضافة عنصر إلى المفضلة
  Future<void> addToFavorites(dynamic itemId,
      {String itemType = 'book'}) async {
    try {
      final db = await database;

      // تحويل المعرف إلى نص بغض النظر عن نوعه
      String itemIdStr = itemId.toString();

      // التحقق من وجود جدول المفضلة
      var tableCheck = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='favorites'");

      if (tableCheck.isEmpty) {
        // إنشاء جدول المفضلة إذا لم يكن موجوداً
        await db.execute('''
          CREATE TABLE IF NOT EXISTS favorites(
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            item_id TEXT NOT NULL,
            item_type TEXT NOT NULL,
            timestamp INTEGER DEFAULT (strftime('%s','now') * 1000),
            UNIQUE(item_id, item_type)
          )
        ''');
        debugPrint('تم إنشاء جدول المفضلة');
      }

      // التحقق من وجود العنصر في المفضلة قبل الإضافة
      final List<Map<String, dynamic>> existingItems = await db.query(
        'favorites',
        where: 'item_id = ? AND item_type = ?',
        whereArgs: [itemIdStr, itemType],
      );

      // إذا كان العنصر موجودًا بالفعل، لا تفعل شيئًا
      if (existingItems.isNotEmpty) {
        debugPrint('العنصر موجود بالفعل في المفضلة: $itemIdStr, $itemType');
        return;
      }

      // إضافة إلى جدول المفضلة
      await db.insert(
        'favorites',
        {
          'item_id': itemIdStr,
          'item_type': itemType,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
        conflictAlgorithm: ConflictAlgorithm.ignore,
      );

      debugPrint('تمت إضافة العنصر إلى المفضلة: $itemIdStr, $itemType');

      // تحديث حالة المفضلة في جدول الكتب إذا كان النوع كتاب
      if (itemType == 'book') {
        try {
          await db.update(
            'books',
            {'is_favorite': 1},
            where: 'id = ?',
            whereArgs: [itemId],
          );
        } catch (e) {
          debugPrint('خطأ في تحديث حالة المفضلة في جدول الكتب: $e');
          // لا تنشر هذا الخطأ، لأن الإضافة الرئيسية تمت بالفعل
        }
      }

      // تحديث حالة المفضلة في جدول القصائد إذا كان النوع قصيدة
      if (itemType == 'poem') {
        try {
          await db.update(
            'poems',
            {'is_favorite': 1},
            where: 'id = ?',
            whereArgs: [itemIdStr],
          );
          debugPrint('تم تحديث حالة المفضلة في جدول القصائد');
        } catch (e) {
          debugPrint('خطأ في تحديث حالة المفضلة في جدول القصائد: $e');
          // لا تنشر هذا الخطأ، لأن الإضافة الرئيسية تمت بالفعل
        }
      }
    } catch (e) {
      debugPrint('خطأ في إضافة عنصر إلى المفضلة: $e');
      rethrow;
    }
  }

  // منهج إضافة مفضلة للتوافق مع الكود القديم
  Future<void> addFavorite(dynamic itemId, String itemType) async {
    return addToFavorites(itemId, itemType: itemType);
  }

  // منهج إزالة مفضلة للتوافق مع الكود القديم
  Future<void> removeFavorite(dynamic itemId, String itemType) async {
    return removeFromFavorites(itemId, itemType: itemType);
  }

  // إزالة عنصر من المفضلة - تم تحسينه لمعالجة مشكلة عدم الحذف
  Future<void> removeFromFavorites(dynamic itemId,
      {String? itemType = 'book'}) async {
    try {
      final db = await database;

      // تحويل المعرف إلى نص بغض النظر عن نوعه
      String itemIdStr = itemId.toString();

      debugPrint(
          'محاولة إزالة العنصر من المفضلة: $itemIdStr (النوع: $itemType)');

      // التحقق من وجود جدول المفضلة
      var tableCheck = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='favorites'");

      if (tableCheck.isEmpty) {
        debugPrint('جدول المفضلة غير موجود');
        return;
      }

      // محاولة الحذف بالمعرف الأصلي أولاً
      int deletedCount = 0;
      if (itemType != null) {
        deletedCount = await db.delete(
          'favorites',
          where: 'item_id = ? AND item_type = ?',
          whereArgs: [itemIdStr, itemType],
        );
      } else {
        deletedCount = await db.delete(
          'favorites',
          where: 'item_id = ?',
          whereArgs: [itemIdStr],
        );
      }

      debugPrint(
          'تم حذف $deletedCount عنصر باستخدام المعرف الأصلي: $itemIdStr');

      // محاولة الحذف باستخدام hashCode
      int hashId = itemIdStr.hashCode;
      String hashIdStr = hashId.toString();

      int hashDeletedCount = 0;
      if (itemType != null) {
        hashDeletedCount = await db.delete(
          'favorites',
          where: 'item_id = ? AND item_type = ?',
          whereArgs: [hashIdStr, itemType],
        );
      } else {
        hashDeletedCount = await db.delete(
          'favorites',
          where: 'item_id = ?',
          whereArgs: [hashIdStr],
        );
      }

      debugPrint('تم حذف $hashDeletedCount عنصر باستخدام hashCode: $hashIdStr');

      // محاولة الحذف باستخدام المعرف الرقمي إذا أمكن
      try {
        int numericId = int.parse(itemIdStr);
        String numericIdStr = numericId.toString();

        int numericDeletedCount = 0;
        if (itemType != null) {
          numericDeletedCount = await db.delete(
            'favorites',
            where: 'item_id = ? AND item_type = ?',
            whereArgs: [numericIdStr, itemType],
          );
        } else {
          numericDeletedCount = await db.delete(
            'favorites',
            where: 'item_id = ?',
            whereArgs: [numericIdStr],
          );
        }

        debugPrint(
            'تم حذف $numericDeletedCount عنصر باستخدام المعرف الرقمي: $numericIdStr');
      } catch (e) {
        // تجاهل الخطأ إذا لم يكن المعرف رقمًا
        debugPrint('المعرف ليس رقمًا، تجاهل محاولة الحذف الرقمية');
      }

      // محاولة الحذف باستخدام استعلام أكثر مرونة
      try {
        // استخدام LIKE للبحث عن المعرف بشكل أكثر مرونة
        int likeDeletedCount = 0;
        if (itemType != null) {
          likeDeletedCount = await db.rawDelete(
            'DELETE FROM favorites WHERE item_id LIKE ? AND item_type = ?',
            ['%$itemIdStr%', itemType],
          );
        } else {
          likeDeletedCount = await db.rawDelete(
            'DELETE FROM favorites WHERE item_id LIKE ?',
            ['%$itemIdStr%'],
          );
        }

        debugPrint(
            'تم حذف $likeDeletedCount عنصر باستخدام استعلام LIKE: %$itemIdStr%');
      } catch (e) {
        debugPrint('خطأ في استعلام LIKE: $e');
      }

      // تحديث حالة المفضلة في جدول القصائد إذا كان النوع قصيدة
      if (itemType == 'poem') {
        try {
          await db.update(
            'poems',
            {'is_favorite': 0},
            where: 'id = ?',
            whereArgs: [itemIdStr],
          );

          // محاولة التحديث باستخدام hashCode أيضًا
          await db.update(
            'poems',
            {'is_favorite': 0},
            where: 'id = ?',
            whereArgs: [hashIdStr],
          );

          debugPrint('تم تحديث حالة المفضلة في جدول القصائد');
        } catch (e) {
          debugPrint('خطأ في تحديث حالة المفضلة في جدول القصائد: $e');
        }
      }

      // تحديث حالة المفضلة في جدول الكتب إذا كان النوع كتاب
      if (itemType == 'book' || itemType == null) {
        try {
          await db.update(
            'books',
            {'is_favorite': 0},
            where: 'id = ?',
            whereArgs: [itemIdStr],
          );

          // محاولة التحديث باستخدام hashCode أيضًا
          await db.update(
            'books',
            {'is_favorite': 0},
            where: 'id = ?',
            whereArgs: [hashIdStr],
          );

          debugPrint('تم تحديث حالة المفضلة في جدول الكتب');
        } catch (e) {
          debugPrint('خطأ في تحديث حالة المفضلة في جدول الكتب: $e');
        }
      }

      // إذا كان النوع أذكار، قم بتحديث SharedPreferences أيضًا
      if (itemType == 'azkar') {
        try {
          final prefs = await SharedPreferences.getInstance();
          final favorites = prefs.getStringList('favorites') ?? [];

          debugPrint(
              'قائمة المفضلة في SharedPreferences قبل الحذف: $favorites');

          // إزالة العنصر بجميع الطرق المحتملة
          final updatedFavorites = favorites
              .where((id) => id != itemIdStr && id != hashIdStr)
              .toList();

          // محاولة إزالة المعرف الرقمي أيضًا
          try {
            int numericId = int.parse(itemIdStr);
            String numericIdStr = numericId.toString();
            updatedFavorites.removeWhere((id) => id == numericIdStr);
          } catch (e) {
            // تجاهل الخطأ إذا لم يكن المعرف رقمًا
          }

          await prefs.setStringList('favorites', updatedFavorites);
          debugPrint(
              'تم تحديث SharedPreferences: ${updatedFavorites.length} عنصر');
        } catch (e) {
          debugPrint('خطأ في تحديث SharedPreferences: $e');
        }
      }

      debugPrint('تمت محاولة إزالة العنصر من المفضلة بجميع الطرق الممكنة');
    } catch (e) {
      debugPrint('خطأ في إزالة العنصر من المفضلة: $e');
      // لا نعيد رمي الخطأ لتحسين تجربة المستخدم
    }
  }

  // إضافة موضع القراءة الجديد
  Future<void> saveLastReadPosition(int bookId, int page) async {
    final db = await database;
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    // تحديث في جدول مواضع القراءة
    await db.delete(
      'reading_positions',
      where: 'book_id = ?',
      whereArgs: [bookId],
    );

    await db.insert(
      'reading_positions',
      {
        'book_id': bookId,
        'page': page,
        'timestamp': timestamp,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    // تحديث في جدول الكتب أيضاً
    await db.update(
      'books',
      {
        'last_read_page': page,
        'last_read_date': timestamp,
      },
      where: 'id = ?',
      whereArgs: [bookId],
    );
  }

  // تقييم كتاب
  Future<void> rateBook(int bookId, double rating) async {
    final db = await database;
    await db.update(
      'books',
      {'rating': rating},
      where: 'id = ?',
      whereArgs: [bookId],
    );
  }

  // الحصول على الكتب المنزلة
  Future<List<Map<String, dynamic>>> getDownloadedBooks() async {
    final db = await database;
    return await db.query(
      'books',
      where: 'download_progress >= 100 OR pdf_path IS NOT NULL',
    );
  }

  // استرجاع فئات الأذكار الرئيسية
  Future<List<Zikr>> getZikrCategories() async {
    final db = await database;
    final List<Map<String, dynamic>> categories =
        await db.query('zikr_categories');

    return Future.wait(categories.map((category) async {
      final List<Map<String, dynamic>> subcategories = await db.query(
        'zikr_subcategories',
        where: 'parent_id = ?',
        whereArgs: [category['id']],
      );

      if (subcategories.isNotEmpty) {
        category['subcategories'] =
            await Future.wait(subcategories.map((subcat) async {
          final List<Map<String, dynamic>> items = await db.query(
            'zikr_items',
            where: 'subcategory_id = ?',
            whereArgs: [subcat['id']],
          );

          subcat['azkar'] = items;
          return subcat;
        }).toList());
      } else {
        final List<Map<String, dynamic>> items = await db.query(
          'zikr_items',
          where: 'category_id = ?',
          whereArgs: [category['id']],
        );

        category['azkar'] = items;
      }

      return Zikr.fromJson(category);
    }).toList());
  }

  // استرجاع فئة فرعية من الأذكار
  Future<Zikr?> getZikrSubcategory(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> result = await db.query(
      'zikr_subcategories',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (result.isEmpty) return null;

    final subcategory = result.first;
    final List<Map<String, dynamic>> items = await db.query(
      'zikr_items',
      where: 'subcategory_id = ?',
      whereArgs: [id],
    );

    subcategory['azkar'] = items;
    return Zikr.fromJson(subcategory);
  }

  // استرجاع فئة رئيسية من الأذكار
  Future<Zikr?> getZikrCategory(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> result = await db.query(
      'zikr_categories',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (result.isEmpty) return null;

    final category = result.first;
    final List<Map<String, dynamic>> subcategories = await db.query(
      'zikr_subcategories',
      where: 'parent_id = ?',
      whereArgs: [id],
    );

    if (subcategories.isNotEmpty) {
      category['subcategories'] =
          await Future.wait(subcategories.map((subcat) async {
        final List<Map<String, dynamic>> items = await db.query(
          'zikr_items',
          where: 'subcategory_id = ?',
          whereArgs: [subcat['id']],
        );

        subcat['azkar'] = items;
        return subcat;
      }).toList());
    } else {
      final List<Map<String, dynamic>> items = await db.query(
        'zikr_items',
        where: 'category_id = ?',
        whereArgs: [id],
      );

      category['azkar'] = items;
    }

    return Zikr.fromJson(category);
  }

  Future<void> saveZikrCompletion(String id, String type, int timestamp) async {
    final db = await database;

    int numericId;
    try {
      // محاولة تحويل المعرف إلى رقم إذا كان رقمًا
      numericId = int.parse(id);
    } catch (e) {
      // استخدام قيمة فريدة مستندة إلى الـ hashCode إذا كان المعرف نصًا
      numericId = id.hashCode;
    }

    await db.insert(
      'zikr_completions',
      {
        'zikr_id': numericId,
        'zikr_type': type, // 'category' أو 'subcategory'
        'timestamp': timestamp,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<void> importZikrFromJson(Map<String, dynamic> jsonData) async {
    final db = await database;
    final batch = db.batch();

    // تصحيح أسماء الأيقونات
    Map<String, String> correctIconNames = {
      'wb_twighlight': 'wb_twilight',
    };

    if (jsonData['categories'] != null) {
      for (var category in jsonData['categories']) {
        String categoryId = category['id'].toString();

        // تصحيح اسم الأيقونة إذا كان خاطئاً
        String iconName = category['iconName'] ?? '';
        iconName = correctIconNames[iconName] ?? iconName;

        batch.insert(
          'zikr_categories',
          {
            'id': categoryId,
            'title': category['name'],
            'description': category['description'],
            'image_url': 'assets/images/${category['id']}.jpg',
            'category': category['name'],
            'iconName': iconName, // استخدام الاسم المصحح
          },
          conflictAlgorithm: ConflictAlgorithm.replace,
        );

        // إدخال عناصر الفئة
        if (category['items'] != null) {
          for (var item in category['items']) {
            batch.insert(
              'zikr_items',
              {
                'id': item['id'].hashCode,
                'category_id': categoryId,
                'subcategory_id': null,
                'text': item['text'],
                'count': item['count'] ?? 1,
                'reference': item['source'],
              },
              conflictAlgorithm: ConflictAlgorithm.replace,
            );
          }
        }

        // إدخال الفئات الفرعية
        if (category['subcategories'] != null) {
          for (var subcategory in category['subcategories']) {
            // تصحيح اسم الأيقونة للفئة الفرعية
            String subIconName = subcategory['iconName'] ?? '';
            subIconName = correctIconNames[subIconName] ?? subIconName;

            int subcategoryId = subcategory['id'].hashCode;

            batch.insert(
              'zikr_subcategories',
              {
                'id': subcategoryId,
                'parent_id': categoryId,
                'title': subcategory['name'],
                'description': subcategory['description'],
                'image_url': 'assets/images/${subcategory['id']}.jpg',
                'category': subcategory['name'],
                'iconName': subIconName, // استخدام الاسم المصحح
              },
              conflictAlgorithm: ConflictAlgorithm.replace,
            );

            // إدخال عناصر الفئة الفرعية
            if (subcategory['items'] != null) {
              for (var item in subcategory['items']) {
                batch.insert(
                  'zikr_items',
                  {
                    'id': item['id'].hashCode,
                    'category_id': null,
                    'subcategory_id': subcategoryId,
                    'text': item['text'],
                    'count': item['count'] ?? 1,
                    'reference': item['source'],
                  },
                  conflictAlgorithm: ConflictAlgorithm.replace,
                );
              }
            }
          }
        }
      }

      await batch.commit(noResult: true);
    }
  }

  // منهج إضافة مفضلة للتوافق مع الكود القديم - تم تحديثه لاستخدام المنهج الجديد

  // تعديل استرجاع المفضلة - تم تحسينه للتعامل مع الأخطاء
  Future<List<Map<String, dynamic>>> getFavorites() async {
    try {
      final db = await database;

      // التحقق من وجود جدول المفضلة
      var tableCheck = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='favorites'");

      if (tableCheck.isEmpty) {
        // إنشاء جدول المفضلة إذا لم يكن موجوداً
        await db.execute('''
          CREATE TABLE IF NOT EXISTS favorites(
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            item_id TEXT NOT NULL,
            item_type TEXT NOT NULL,
            timestamp INTEGER DEFAULT (strftime('%s','now') * 1000),
            UNIQUE(item_id, item_type)
          )
        ''');
        debugPrint('تم إنشاء جدول المفضلة');
        return []; // إرجاع قائمة فارغة لأن الجدول جديد
      }

      // الحصول على جميع العناصر المفضلة
      final List<Map<String, dynamic>> maps = await db.query('favorites');
      debugPrint('تم العثور على ${maps.length} عنصر مفضل');
      return maps;
    } catch (e) {
      debugPrint('خطأ في الحصول على المفضلة: $e');
      return []; // إرجاع قائمة فارغة في حالة حدوث خطأ
    }
  }

  // دالة للتأكد من وجود جداول الأذكار
  Future<void> ensureTablesExist() async {
    final db = await database;

    // التحقق من وجود جدول zikr_categories
    final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='zikr_categories'");

    if (tables.isEmpty) {
      // إنشاء الجداول إذا لم تكن موجودة
      await db.execute('''
        CREATE TABLE IF NOT EXISTS zikr_categories(
          id TEXT PRIMARY KEY,
          title TEXT,
          description TEXT,
          image_url TEXT,
          category TEXT,
          iconName TEXT
        )
      ''');

      await db.execute('''
        CREATE TABLE IF NOT EXISTS zikr_subcategories(
          id INTEGER PRIMARY KEY,
          parent_id TEXT,
          title TEXT,
          description TEXT,
          image_url TEXT,
          category TEXT,
          iconName TEXT,
          FOREIGN KEY (parent_id) REFERENCES zikr_categories(id)
        )
      ''');

      await db.execute('''
        CREATE TABLE IF NOT EXISTS zikr_items(
          id INTEGER PRIMARY KEY,
          category_id INTEGER,
          subcategory_id INTEGER,
          text TEXT,
          count INTEGER,
          reference TEXT,
          FOREIGN KEY (category_id) REFERENCES zikr_categories(id),
          FOREIGN KEY (subcategory_id) REFERENCES zikr_subcategories(id)
        )
      ''');

      await db.execute('''
        CREATE TABLE IF NOT EXISTS zikr_completions(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          zikr_id INTEGER,
          zikr_type TEXT,
          timestamp INTEGER
        )
      ''');

      debugPrint('تم إنشاء جداول الأذكار بنجاح');
    }
  }

  // استرجاع الأذكار بطريقة آمنة للقراءة فقط
  Future<List<Zikr>> getZikrCategoriesSafe(String jsonData) async {
    try {
      final data = json.decode(jsonData);
      return List<Zikr>.from(
          data['categories'].map((category) => Zikr.fromJson(category)));
    } catch (e) {
      debugPrint('خطأ في تحويل بيانات JSON: $e');
      return [];
    }
  }

  // التحقق إذا كان الكتاب مفضلاً - تم تحديثه لاستخدام المنهج الجديد
  Future<bool> isBookFavorite(int bookId) async {
    return isFavorite(bookId, 'book');
  }

  // الحصول على تفاصيل الكتاب
  Future<Map<String, dynamic>> getBookDetails(int bookId) async {
    final db = await database;
    final results = await db.query(
      'books',
      where: 'id = ?',
      whereArgs: [bookId],
    );
    return results.isNotEmpty ? results.first : {};
  }

  // إضافة كتاب للمفضلة - تم تحديثه لاستخدام المنهج الجديد
  Future<int> addBookToFavorites(int bookId) async {
    await addToFavorites(bookId, itemType: 'book');
    return 1; // للتوافق مع المنهج القديم الذي يتوقع رقمًا
  }

  // إزالة كتاب من المفضلة - تم تحديثه لاستخدام المنهج الجديد
  Future<int> removeBookFromFavorites(int bookId) async {
    await removeFromFavorites(bookId, itemType: 'book');
    return 1; // للتوافق مع المنهج القديم الذي يتوقع رقمًا
  }

  // تحديث مسار PDF المحلي للكتاب
  Future<int> updateBookLocalPdfPath(int bookId, String path) async {
    final db = await database;
    return await db.update(
      'books',
      {'local_pdf_path': path},
      where: 'id = ?',
      whereArgs: [bookId],
    );
  }

  // تحديث عدد صفحات الكتاب
  Future<int> updateBookPages(int bookId, int pageCount) async {
    final db = await database;
    return await db.update(
      'books',
      {'pages': pageCount},
      where: 'id = ?',
      whereArgs: [bookId],
    );
  }

  // تحديث آخر صفحة تمت قراءتها
  Future<int> updateLastReadPage(int bookId, int page) async {
    final db = await database;
    return await db.update(
      'books',
      {
        'last_read_page': page,
        'last_read_time': DateTime.now().toIso8601String()
      },
      where: 'id = ?',
      whereArgs: [bookId],
    );
  }

  // تحديث صفحة الإشارة المرجعية
  Future<int> updateBookmarkPage(int bookId, int page) async {
    final db = await database;
    return await db.update(
      'books',
      {'bookmark_page': page},
      where: 'id = ?',
      whereArgs: [bookId],
    );
  }

  // تحديث وقت آخر قراءة
  Future<int> updateLastReadTime(int bookId) async {
    final db = await database;
    return await db.update(
      'books',
      {'last_read_time': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [bookId],
    );
  }
}
