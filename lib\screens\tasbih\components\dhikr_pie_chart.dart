// رسم بياني دائري لتوزيع الأذكار

import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/tasbih_stats_model.dart';
import '../utils/tasbih_colors.dart';

class DhikrPieChart extends StatefulWidget {
  final List<DhikrStats> dhikrStats;
  final double size;

  const Dhikr<PERSON>ieChart({
    Key? key,
    required this.dhikrStats,
    this.size = 200,
  }) : super(key: key);

  @override
  State<DhikrPieChart> createState() => _DhikrPieChartState();
}

class _DhikrPieChartState extends State<DhikrPieChart> {
  int touchedIndex = -1;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // الحصول على حجم الشاشة للتصميم المتجاوب بشكل أكثر دقة
    final size = MediaQuery.of(context).size;
    final isVerySmallScreen = size.width < 320; // للشاشات الصغيرة جداً
    final isSmallScreen =
        size.width >= 320 && size.width < 360; // للشاشات الصغيرة
    final isMediumScreen =
        size.width >= 360 && size.width < 600; // للهواتف العادية
    final isLargeScreen = size.width >= 600; // للأجهزة اللوحية والشاشات الكبيرة

    // إذا كانت القائمة فارغة، عرض رسالة
    if (widget.dhikrStats.isEmpty) {
      return SizedBox(
        height: widget.size,
        child: Center(
          child: Text(
            'لا توجد بيانات للعرض',
            style: TextStyle(
              fontSize: isVerySmallScreen
                  ? 12.0
                  : (isSmallScreen ? 14.0 : (isMediumScreen ? 16.0 : 18.0)),
              color: isDarkMode ? Colors.white70 : Colors.black54,
            ),
            textDirection:
                TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
            textAlign: TextAlign.center, // محاذاة النص إلى الوسط
          ),
        ),
      );
    }

    // إنشاء قائمة الألوان
    final List<Color> colors = [
      TasbihColors.primary,
      TasbihColors.secondary,
      TasbihColors.tertiary,
      Colors.amber,
      Colors.teal,
      Colors.indigo,
      Colors.purple,
      Colors.orange,
    ];

    return SizedBox(
      height: widget.size,
      child: Stack(
        children: [
          // الرسم البياني الدائري
          PieChart(
            PieChartData(
              pieTouchData: PieTouchData(
                touchCallback: (FlTouchEvent event, pieTouchResponse) {
                  setState(() {
                    if (!event.isInterestedForInteractions ||
                        pieTouchResponse == null ||
                        pieTouchResponse.touchedSection == null) {
                      touchedIndex = -1;
                      return;
                    }
                    touchedIndex =
                        pieTouchResponse.touchedSection!.touchedSectionIndex;
                  });
                },
              ),
              borderData: FlBorderData(show: false),
              sectionsSpace: isVerySmallScreen
                  ? 0.5
                  : (isSmallScreen ? 1 : (isMediumScreen ? 2 : 3)),
              centerSpaceRadius: isVerySmallScreen
                  ? widget.size / 7
                  : (isSmallScreen
                      ? widget.size / 6
                      : (isMediumScreen ? widget.size / 5 : widget.size / 4)),
              sections: _buildSections(colors, isVerySmallScreen, isSmallScreen,
                  isMediumScreen, isLargeScreen),
            ),
          ),

          // النص في المنتصف
          Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه العمود من اليمين إلى اليسار
              children: [
                Text(
                  'المجموع',
                  style: TextStyle(
                    fontSize: isVerySmallScreen
                        ? 10.0
                        : (isSmallScreen
                            ? 12.0
                            : (isMediumScreen ? 14.0 : 18.0)),
                    color: isDarkMode ? Colors.white70 : Colors.black54,
                  ),
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                  textAlign: TextAlign.center, // محاذاة النص إلى الوسط
                ),
                SizedBox(
                    height: isVerySmallScreen
                        ? 1
                        : (isSmallScreen ? 2 : (isMediumScreen ? 3 : 4))),
                Text(
                  _calculateTotal().toString(),
                  style: TextStyle(
                    fontSize: isVerySmallScreen
                        ? 16.0
                        : (isSmallScreen
                            ? 18.0
                            : (isMediumScreen ? 22.0 : 28.0)),
                    fontWeight: FontWeight.bold,
                    color: TasbihColors.primary,
                  ),
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                  textAlign: TextAlign.center, // محاذاة النص إلى الوسط
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء أقسام الرسم البياني
  List<PieChartSectionData> _buildSections(
      List<Color> colors,
      bool isVerySmallScreen,
      bool isSmallScreen,
      bool isMediumScreen,
      bool isLargeScreen) {
    return List.generate(widget.dhikrStats.length, (i) {
      final isTouched = i == touchedIndex;

      // تحديد أحجام العناصر بناءً على حجم الشاشة بشكل أكثر دقة
      final double fontSize = isTouched
          ? (isVerySmallScreen
              ? 12
              : (isSmallScreen ? 14 : (isMediumScreen ? 18 : 22)))
          : (isVerySmallScreen
              ? 8
              : (isSmallScreen ? 10 : (isMediumScreen ? 14 : 18)));

      final double radius = isTouched
          ? (isVerySmallScreen
              ? widget.size / 4.5
              : (isSmallScreen
                  ? widget.size / 4
                  : (isMediumScreen ? widget.size / 3.5 : widget.size / 3)))
          : (isVerySmallScreen
              ? widget.size / 5
              : (isSmallScreen
                  ? widget.size / 4.5
                  : (isMediumScreen ? widget.size / 4 : widget.size / 3.5)));

      final double widgetSize = isTouched
          ? (isVerySmallScreen
              ? 30
              : (isSmallScreen ? 40 : (isMediumScreen ? 55 : 70)))
          : (isVerySmallScreen
              ? 20
              : (isSmallScreen ? 30 : (isMediumScreen ? 40 : 50)));

      // تحديد اللون
      final color = i < colors.length ? colors[i] : colors[i % colors.length];

      final dhikr = widget.dhikrStats[i];

      return PieChartSectionData(
        color: color,
        value: dhikr.count.toDouble(),
        title: '${dhikr.percentage.toInt()}%',
        radius: radius,
        titleStyle: TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
          color: Colors.white,
          shadows: const [
            Shadow(
              color: Colors.black26,
              blurRadius: 2,
              offset: Offset(0, 1),
            ),
          ],
        ),
        badgeWidget: _Badge(
          size: widgetSize,
          borderColor: color,
          dhikrName: dhikr.name,
        ),
        badgePositionPercentageOffset: 1.1,
      );
    });
  }

  // حساب المجموع الكلي
  int _calculateTotal() {
    return widget.dhikrStats.fold(0, (sum, dhikr) => sum + dhikr.count);
  }
}

// مكون شارة الذكر
class _Badge extends StatelessWidget {
  final double size;
  final Color borderColor;
  final String dhikrName;

  const _Badge({
    required this.size,
    required this.borderColor,
    required this.dhikrName,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: PieChart.defaultDuration,
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
        border: Border.all(
          color: borderColor,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(20),
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      padding: EdgeInsets.all(size * 0.15),
      child: Center(
        child: FittedBox(
          child: Text(
            _getShortName(dhikrName),
            style: TextStyle(
              color: borderColor,
              fontWeight: FontWeight.bold,
              fontSize: size * 0.3,
            ),
            textDirection:
                TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  // الحصول على اسم مختصر للذكر
  String _getShortName(String name) {
    if (name.length <= 5) return name;

    final words = name.split(' ');
    if (words.length > 1) {
      return words.map((word) => word.isNotEmpty ? word[0] : '').join('');
    }

    return name.substring(0, 5);
  }
}
