// lib/screens/home/<USER>/categories_section.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;
import '../controllers/home_controller.dart';
import '../../../utils/app_colors.dart';

class CategoriesSection extends StatefulWidget {
  const CategoriesSection({super.key});

  @override
  State<CategoriesSection> createState() => _CategoriesSectionState();
}

class _CategoriesSectionState extends State<CategoriesSection>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1800),
    )..forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final controller = Provider.of<HomeController>(context);

    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.1,
      ),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount:
          5, // الأقسام الخمسة: الكتب، القصائد، الأذكار، المفضلة، الصلاة على النبي
      itemBuilder: (context, index) {
        // تحديد خصائص كل قسم
        final CategoryInfo categoryInfo = _getCategoryInfo(index);

        // تطبيق رسوم متحركة متدرجة لظهور العناصر
        final animation = _getStaggeredAnimation(index);

        return AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(
                0,
                20 * (1 - animation.value),
              ),
              child: Opacity(
                opacity: animation.value,
                child: _buildCategoryCard(
                  context,
                  categoryInfo,
                  controller.isSectionAvailable(categoryInfo.key),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildCategoryCard(
      BuildContext context, CategoryInfo categoryInfo, bool isAvailable) {
    return GestureDetector(
      onTap: () {
        if (isAvailable) {
          HapticFeedback.lightImpact();
          Navigator.pushNamed(context, categoryInfo.route);
        } else {
          _showComingSoonSnackbar(context, categoryInfo.title);
        }
      },
      child: Stack(
        children: [
          // البطاقة الأساسية
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topRight,
                end: Alignment.bottomLeft,
                colors: [
                  categoryInfo.color.withAlpha(178), // 0.7 * 255 ≈ 178
                  categoryInfo.color,
                  categoryInfo.color.withBlue(
                    (categoryInfo.color.b + 20).clamp(0, 255).toInt(),
                  ),
                ],
                stops: const [0.1, 0.5, 0.9],
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: categoryInfo.color.withAlpha(77), // 0.3 * 255 ≈ 77
                  blurRadius: 10,
                  spreadRadius: 0,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Stack(
                children: [
                  // الزخرفة الإسلامية في الخلفية
                  Positioned(
                    right: -25,
                    bottom: -25,
                    child: Opacity(
                      opacity: 0.15,
                      child: SvgPicture.asset(
                        'assets/images/p2.svg',
                        width: 110,
                        height: 110,
                        colorFilter: const ColorFilter.mode(
                          Colors.white,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),

                  // الوهج المتحرك
                  AnimatedBuilder(
                    animation: _animationController,
                    builder: (context, child) {
                      return CustomPaint(
                        painter: CategoryGlowPainter(
                          progress: _animationController.value,
                          color: Colors.white,
                        ),
                        size: const Size(double.infinity, double.infinity),
                      );
                    },
                  ),

                  // محتوى البطاقة
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // أيقونة القسم
                        Container(
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(51), // 0.2 * 255 ≈ 51
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black
                                    .withAlpha(13), // 0.05 * 255 ≈ 13
                                blurRadius: 5,
                                spreadRadius: 0,
                              ),
                            ],
                            border: Border.all(
                              color:
                                  Colors.white.withAlpha(77), // 0.3 * 255 ≈ 77
                              width: 1,
                            ),
                          ),
                          child: Icon(
                            categoryInfo.icon,
                            color: Colors.white,
                            size: 28,
                          ),
                        ),

                        // اسم القسم
                        Text(
                          categoryInfo.title,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                            shadows: [
                              Shadow(
                                color: Colors.black26,
                                blurRadius: 2,
                                offset: Offset(0, 1),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // طبقة للقسم غير المتاح
                  if (!isAvailable) _buildComingSoonOverlay(context),

                  // تأثير الضغط (ripple)
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(20),
                      splashColor: Colors.white.withAlpha(26), // 0.1 * 255 ≈ 26
                      highlightColor:
                          Colors.white.withAlpha(13), // 0.05 * 255 ≈ 13
                      onTap: () {
                        if (isAvailable) {
                          HapticFeedback.lightImpact();
                          Navigator.pushNamed(context, categoryInfo.route);
                        } else {
                          _showComingSoonSnackbar(context, categoryInfo.title);
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComingSoonOverlay(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: Colors.black.withAlpha(128), // 0.5 * 255 ≈ 128
      ),
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.white.withAlpha(204), // 0.8 * 255 ≈ 204
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(51), // 0.2 * 255 ≈ 51
                blurRadius: 5,
                spreadRadius: 0,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: const Text(
            'قريباً',
            style: TextStyle(
              color: Colors.black87,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
        ),
      ),
    );
  }

  void _showComingSoonSnackbar(BuildContext context, String title) {
    ScaffoldMessenger.of(context).clearSnackBars();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'قسم "$title" قيد التطوير وسيتوفر قريباً',
          textAlign: TextAlign.center,
          style: const TextStyle(fontFamily: 'Tajawal'),
        ),
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).size.height * 0.7,
          left: 20,
          right: 20,
        ),
      ),
    );
  }

  Animation<double> _getStaggeredAnimation(int index) {
    // يتم تأخير ظهور كل عنصر عن العنصر السابق
    final startInterval = 0.1 + (index * 0.1).clamp(0.0, 0.6);
    final endInterval = startInterval + 0.4;

    return Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Interval(
          startInterval,
          endInterval,
          curve: Curves.easeOutQuart,
        ),
      ),
    );
  }

  CategoryInfo _getCategoryInfo(int index) {
    switch (index) {
      case 0:
        return CategoryInfo(
          key: 'books',
          title: 'الكتب',
          icon: Icons.book,
          color: AppColors.booksColor,
          route: '/books',
        );
      case 1:
        return CategoryInfo(
          key: 'poems',
          title: 'القصائد',
          icon: Icons.music_note,
          color: AppColors.poemsColor,
          route: '/poems',
        );
      case 2:
        return CategoryInfo(
          key: 'azkar',
          title: 'الأذكار',
          icon: Icons.favorite,
          color: AppColors.azkarColor,
          route: '/azkar',
        );
      case 3:
        return CategoryInfo(
          key: 'favorites',
          title: 'المفضلة',
          icon: Icons.bookmark,
          color: AppColors.favoritesColor,
          route: '/favorites',
        );
      case 4:
        return CategoryInfo(
          key: 'prophetPrayers',
          title: 'الصلاة على النبي',
          icon: Icons.auto_awesome,
          color: AppColors.prophetPrayersColor,
          route: '/prophet-prayers',
        );
      default:
        // مكتبة احتياطية للحالة الاستثنائية
        return CategoryInfo(
          key: 'default',
          title: 'القسم',
          icon: Icons.category,
          color: Colors.grey,
          route: '/',
        );
    }
  }
}

// فئة لحفظ معلومات كل قسم
class CategoryInfo {
  final String key;
  final String title;
  final IconData icon;
  final Color color;
  final String route;

  CategoryInfo({
    required this.key,
    required this.title,
    required this.icon,
    required this.color,
    required this.route,
  });
}

// رسام مخصص لتأثير الوهج المتحرك
class CategoryGlowPainter extends CustomPainter {
  final double progress;
  final Color color;

  CategoryGlowPainter({
    required this.progress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);

    // الوهج الأول - من الزاوية العلوية اليمنى
    final topGlow = RadialGradient(
      center: Alignment(
        0.8 + (0.2 * math.sin(progress * math.pi * 1.5)),
        -0.8 + (0.2 * math.cos(progress * math.pi * 1.5)),
      ),
      radius: 0.8 + (0.2 * math.sin(progress * math.pi * 2)),
      colors: [
        color.withAlpha((0.12 * progress * 255).toInt()), // Valor dinámico
        color.withAlpha((0.04 * progress * 255).toInt()), // Valor dinámico
        Colors.transparent,
      ],
      stops: const [0.0, 0.3, 0.6],
    ).createShader(rect);

    canvas.drawRect(rect, Paint()..shader = topGlow);

    // الوهج الثاني - من الزاوية السفلية اليسرى
    final bottomGlow = RadialGradient(
      center: Alignment(
        -0.6 + (0.1 * math.cos(progress * math.pi * 2)),
        0.6 + (0.1 * math.sin(progress * math.pi * 2)),
      ),
      radius: 0.6 + (0.1 * math.sin(progress * math.pi * 1.5)),
      colors: [
        color.withAlpha((0.08 * progress * 255).toInt()), // Valor dinámico
        color.withAlpha((0.02 * progress * 255).toInt()), // Valor dinámico
        Colors.transparent,
      ],
      stops: const [0.0, 0.4, 0.7],
    ).createShader(rect);

    canvas.drawRect(rect, Paint()..shader = bottomGlow);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
