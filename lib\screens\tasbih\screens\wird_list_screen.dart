// شاشة قائمة الأوراد

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/wird_provider.dart';
import '../utils/tasbih_colors.dart';
import 'wird_detail_screen.dart';
import 'wird_player_screen.dart';

class WirdListScreen extends StatefulWidget {
  final int? wirdIdToActivate;

  const WirdListScreen({
    Key? key,
    this.wirdIdToActivate,
  }) : super(key: key);

  @override
  State<WirdListScreen> createState() => _WirdListScreenState();
}

class _WirdListScreenState extends State<WirdListScreen> {
  @override
  void initState() {
    super.initState();

    // إعادة تعيين الأوراد المكتملة عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // إعادة تعيين جميع الأوراد المكتملة
        _resetAllCompletedWirds();

        // تنشيط الورد المحدد وتشغيله إذا كان موجوداً
        if (widget.wirdIdToActivate != null) {
          _activateAndPlayWird(context, widget.wirdIdToActivate!);
        }
      }
    });
  }

  // إعادة تعيين الأوراد المكتملة بناءً على الوقت وعرض تنبيه أنيق
  Future<void> _resetAllCompletedWirds() async {
    final wirdProvider = Provider.of<WirdProvider>(context, listen: false);

    // إعادة تعيين الأوراد المكتملة وجلب قائمة الأوراد التي تم إعادة تعيينها
    final resetWirdNames = await wirdProvider.resetAllCompletedWirds();

    // عرض تنبيه أنيق إذا تم إعادة تعيين أي ورد
    if (resetWirdNames.isNotEmpty && mounted) {
      _showResetWirdsNotification(resetWirdNames);
    }
  }

  // عرض تنبيه أنيق عند إعادة تعيين الأوراد المكتملة
  void _showResetWirdsNotification(List<String> resetWirdNames) {
    // تأخير قليل لضمان أن الشاشة قد تم تحميلها بالكامل
    Future.delayed(const Duration(milliseconds: 500), () {
      if (!mounted) return;

      // إنشاء نص التنبيه
      String message;
      if (resetWirdNames.length == 1) {
        message = 'تم إعادة تعيين الورد "${resetWirdNames[0]}" لبدء يوم جديد';
      } else if (resetWirdNames.length == 2) {
        message =
            'تم إعادة تعيين الوردين "${resetWirdNames[0]}" و "${resetWirdNames[1]}" لبدء يوم جديد';
      } else {
        message = 'تم إعادة تعيين ${resetWirdNames.length} أوراد لبدء يوم جديد';
      }

      // عرض التنبيه
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(
                Icons.refresh_rounded,
                color: Colors.white,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(message),
              ),
            ],
          ),
          backgroundColor: Colors.teal.shade700,
          duration: const Duration(seconds: 4),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          margin: const EdgeInsets.all(8),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          elevation: 4,
          action: SnackBarAction(
            label: 'حسناً',
            textColor: Colors.white,
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    });
  }

  // تنشيط الورد وتشغيله
  void _activateAndPlayWird(BuildContext context, int wirdId) {
    final wirdProvider = Provider.of<WirdProvider>(context, listen: false);

    // التحقق من وجود الورد
    final wirds = wirdProvider.wirds;
    final wirdExists = wirds.any((w) => w.id == wirdId);

    if (wirdExists) {
      // تنشيط الورد
      final navigatorState = Navigator.of(context); // حفظ navigator قبل async
      wirdProvider.activateWird(wirdId).then((_) {
        // إضافة تحقق mounted هنا أيضاً يعتبر ممارسة جيدة
        if (mounted) {
          // الانتقال إلى شاشة تشغيل الورد
          navigatorState.push(
            // استخدام navigator المحفوظة
            MaterialPageRoute(
              builder: (context) => WirdPlayerScreen(wirdId: wirdId),
            ),
          );
        }
      });
    }
    // يمكنك إضافة else لمعالجة حالة عدم وجود الورد إذا أردت
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      appBar: AppBar(
        title: const Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.menu_book_rounded,
              size: 24,
            ),
            SizedBox(width: 8),
            Text('الأوراد اليومية'),
          ],
        ),
        centerTitle: true,
        backgroundColor: TasbihColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(16),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            tooltip: 'معلومات عن الأوراد',
            onPressed: () => _showWirdInfoDialog(context),
          ),
        ],
      ),
      body: Consumer<WirdProvider>(
        builder: (context, wirdProvider, child) {
          final wirds = wirdProvider.wirds;

          if (wirds.isEmpty) {
            return Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: isDarkMode
                      ? [
                          TasbihColors.primary,
                          TasbihColors.darkBackground,
                        ]
                      : [
                          TasbihColors.primary,
                          Colors.white,
                        ],
                  stops: const [0.0, 0.3],
                ),
              ),
              child: Center(
                child: Column(
                  // قد تحتاج SingleChildScrollView هنا للشاشات الصغيرة
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // صورة توضيحية
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? TasbihColors.darkCardColor
                            : Colors.white,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(
                                20), // استخدام withAlpha كما في الأصلي
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.menu_book_outlined,
                        size: 80,
                        color: TasbihColors.primary
                            .withAlpha(200), // استخدام withAlpha كما في الأصلي
                      ),
                    ),
                    const SizedBox(height: 32),

                    // رسالة الترحيب
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 32),
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? TasbihColors.darkCardColor
                            : Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(
                                10), // استخدام withAlpha كما في الأصلي
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Text(
                            'لا توجد أوراد بعد',
                            style: Theme.of(context)
                                .textTheme
                                .titleLarge
                                ?.copyWith(
                                  color: TasbihColors.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'الأوراد اليومية تساعدك على تنظيم أذكارك ومتابعة تقدمك في إكمالها بشكل يومي.',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  color: isDarkMode
                                      ? TasbihColors.darkTextSecondary
                                      : Colors.grey[600],
                                ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 24),
                          ElevatedButton.icon(
                            onPressed: () => _showAddWirdDialog(context),
                            icon: const Icon(Icons.add),
                            label: const Text('إضافة ورد جديد'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: TasbihColors.primary,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 24,
                                vertical: 12,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          // --- واجهة المستخدم عند وجود أوراد ---
          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: isDarkMode
                    ? [
                        TasbihColors.primary,
                        TasbihColors.darkBackground,
                      ]
                    : [
                        TasbihColors.primary,
                        Colors.white,
                      ],
                stops: const [0.0, 0.2],
              ),
            ),
            child: Column(
              children: [
                // عنوان القسم
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: isDarkMode
                              ? TasbihColors.darkCardColor
                              : Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black
                                  .withAlpha(10), // استخدام withAlpha
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.format_list_bulleted,
                              size: 16,
                              color: TasbihColors.primary,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'قائمة الأوراد (${wirds.length})',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: TasbihColors.primary,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Spacer(),
                      if (wirdProvider.activeWird != null)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: isDarkMode
                                ? TasbihColors.darkCardColor.withAlpha(200)
                                : Colors.white
                                    .withAlpha(200), // استخدام withAlpha
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Row(
                            children: [
                              const Icon(
                                Icons.play_circle_outline,
                                size: 16,
                                color: TasbihColors.primary,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'النشط: ${wirdProvider.activeWird?.name ?? ''}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: TasbihColors.primary,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),

                // قائمة الأوراد
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.only(top: 8),
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? TasbihColors.darkBackground
                          : Colors.white,
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(24),
                      ),
                    ),
                    child: ListView.builder(
                      padding: const EdgeInsets.fromLTRB(
                          16, 24, 16, 16), // الحشو الأصلي
                      itemCount: wirds.length,
                      itemBuilder: (context, index) {
                        final wird = wirds[index];
                        final isActive = wird.id == wirdProvider.activeWird?.id;

                        return Container(
                          margin: const EdgeInsets.only(bottom: 16),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: isActive
                                    ? TasbihColors.primary.withAlpha(40)
                                    : Colors.black.withAlpha(15),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                                spreadRadius: 1,
                              ),
                            ],
                          ),
                          child: Card(
                            margin: EdgeInsets.zero,
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                              side: isActive
                                  ? const BorderSide(
                                      color: TasbihColors.primary, width: 2.5)
                                  : BorderSide.none,
                            ),
                            child: InkWell(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        WirdDetailScreen(wirdId: wird.id),
                                  ),
                                );
                              },
                              borderRadius: BorderRadius.circular(20),
                              child: Padding(
                                padding: const EdgeInsets.all(18),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Expanded(
                                          child: Text(
                                            wird.name,
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleLarge
                                                ?.copyWith(
                                                  // الخط الأصلي
                                                  fontWeight: FontWeight.bold,
                                                ),
                                          ),
                                        ),
                                        if (isActive)
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 4,
                                            ),
                                            decoration: BoxDecoration(
                                              color: TasbihColors.primary
                                                  .withAlpha(
                                                      25), // اللون الأصلي
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: const Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Icon(
                                                  Icons.check_circle,
                                                  size: 16, // الحجم الأصلي
                                                  color: TasbihColors.primary,
                                                ),
                                                SizedBox(width: 4),
                                                Text(
                                                  'نشط',
                                                  style: TextStyle(
                                                    color: TasbihColors.primary,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                      ],
                                    ),
                                    const SizedBox(height: 12),

                                    // معلومات الورد
                                    Container(
                                      padding: const EdgeInsets.all(16),
                                      decoration: BoxDecoration(
                                        color: isDarkMode
                                            ? TasbihColors.darkCardColor
                                                .withAlpha(180)
                                            : Colors.grey[50],
                                        borderRadius: BorderRadius.circular(16),
                                        border: Border.all(
                                            color: isDarkMode
                                                ? Colors.grey[700]!
                                                : Colors.grey[200]!),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withAlpha(5),
                                            blurRadius: 6,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: Column(
                                        children: [
                                          // عدد الأذكار والتسبيحات
                                          Row(
                                            children: [
                                              // عدد الأذكار
                                              Expanded(
                                                child: Row(
                                                  children: [
                                                    Icon(
                                                      Icons
                                                          .format_list_numbered,
                                                      size: 16,
                                                      color: Colors.grey[600],
                                                    ),
                                                    const SizedBox(width: 4),
                                                    Text(
                                                      '${wird.items.length} ذكر',
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .bodyMedium
                                                          ?.copyWith(
                                                            color: isDarkMode
                                                                ? TasbihColors
                                                                    .darkTextSecondary
                                                                : Colors
                                                                    .grey[600],
                                                            fontWeight:
                                                                FontWeight.bold,
                                                          ),
                                                    ),
                                                  ],
                                                ),
                                              ),

                                              // إجمالي التسبيحات
                                              Expanded(
                                                child: Row(
                                                  children: [
                                                    Icon(
                                                      Icons
                                                          .calculate, // الأيقونة الأصلية
                                                      size: 16,
                                                      color: Colors.grey[600],
                                                    ),
                                                    const SizedBox(width: 4),
                                                    Text(
                                                      'إجمالي: ${wird.totalDhikrCount}',
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .bodyMedium
                                                          ?.copyWith(
                                                            color: isDarkMode
                                                                ? TasbihColors
                                                                    .darkTextSecondary
                                                                : Colors
                                                                    .grey[600],
                                                            fontWeight:
                                                                FontWeight.bold,
                                                          ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),

                                          // وقت التذكير
                                          if (wird.reminderTime != null) ...[
                                            const SizedBox(height: 8),
                                            Row(
                                              children: [
                                                Icon(
                                                  Icons
                                                      .notifications_active, // الأيقونة الأصلية
                                                  size: 16,
                                                  color: TasbihColors.primary
                                                      .withAlpha(
                                                          200), // اللون الأصلي
                                                ),
                                                const SizedBox(width: 4),
                                                Text(
                                                  'التذكير: ${wird.reminderTime!.format(context)}',
                                                  style: TextStyle(
                                                    fontSize: 13, // الخط الأصلي
                                                    color: TasbihColors.primary
                                                        .withAlpha(
                                                            200), // اللون الأصلي
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ],
                                      ),
                                    ),

                                    const SizedBox(height: 16),

                                    // شريط التقدم
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              'التقدم',
                                              style: TextStyle(
                                                fontSize: 12, // الخط الأصلي
                                                fontWeight: FontWeight.bold,
                                                color: isDarkMode
                                                    ? TasbihColors.darkTextColor
                                                    : Colors.grey[700],
                                              ),
                                            ),
                                            Text(
                                              '${wird.completedDhikrCount}/${wird.totalDhikrCount}',
                                              style: const TextStyle(
                                                fontSize: 12, // الخط الأصلي
                                                fontWeight: FontWeight.bold,
                                                color: TasbihColors.primary,
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 4),
                                        Container(
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            boxShadow: [
                                              BoxShadow(
                                                color: TasbihColors.primary
                                                    .withAlpha(15),
                                                blurRadius: 4,
                                                offset: const Offset(0, 1),
                                              ),
                                            ],
                                          ),
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            child: LinearProgressIndicator(
                                              value: wird.completionPercentage,
                                              backgroundColor: isDarkMode
                                                  ? Colors.grey[700]
                                                  : Colors.grey[200],
                                              valueColor:
                                                  AlwaysStoppedAnimation<Color>(
                                                isActive
                                                    ? TasbihColors.primary
                                                    : TasbihColors.primary
                                                        .withAlpha(200),
                                              ),
                                              minHeight: 10,
                                            ),
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Align(
                                          alignment: Alignment.centerRight,
                                          child: Text(
                                            '${(wird.completionPercentage * 100).toInt()}% مكتمل', // النص الأصلي
                                            style: TextStyle(
                                              fontSize: 11, // الخط الأصلي
                                              color: isDarkMode
                                                  ? TasbihColors
                                                      .darkTextSecondary
                                                  : Colors.grey[600],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),

                                    const SizedBox(height: 16),

                                    // أزرار التحكم
                                    Container(
                                      margin: const EdgeInsets.only(top: 8),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: OutlinedButton.icon(
                                              onPressed: () {
                                                // السلوك الأصلي: تنشيط وعرض SnackBar مباشرة
                                                wirdProvider
                                                    .activateWird(wird.id);
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(
                                                  SnackBar(
                                                    content: Text(
                                                        'تم تنشيط ورد ${wird.name}'),
                                                    backgroundColor:
                                                        TasbihColors.primary,
                                                    behavior: SnackBarBehavior
                                                        .floating,
                                                  ),
                                                );
                                              },
                                              icon: const Icon(
                                                  Icons.check_circle_outline,
                                                  size: 20),
                                              label: const Text('تنشيط'),
                                              style: OutlinedButton.styleFrom(
                                                foregroundColor:
                                                    TasbihColors.primary,
                                                side: const BorderSide(
                                                    color: TasbihColors.primary,
                                                    width: 1.5),
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                ),
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                  vertical: 12,
                                                  horizontal: 16,
                                                ),
                                              ),
                                            ),
                                          ),
                                          const SizedBox(width: 12),
                                          Expanded(
                                            child: ElevatedButton.icon(
                                              onPressed: () {
                                                // السلوك الأصلي: تنشيط ثم انتقال
                                                wirdProvider
                                                    .activateWird(wird.id);
                                                Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                    builder: (context) =>
                                                        WirdPlayerScreen(
                                                            wirdId: wird.id),
                                                  ),
                                                );
                                              },
                                              icon: const Icon(Icons.play_arrow,
                                                  size: 20),
                                              label: const Text('بدء'),
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor:
                                                    TasbihColors.primary,
                                                foregroundColor: Colors.white,
                                                elevation: 2,
                                                shadowColor: TasbihColors
                                                    .primary
                                                    .withAlpha(100),
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                ),
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                  vertical: 12,
                                                  horizontal: 16,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
      floatingActionButton: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(30),
          boxShadow: [
            BoxShadow(
              color: TasbihColors.primary.withAlpha(60),
              blurRadius: 12,
              offset: const Offset(0, 4),
              spreadRadius: 2,
            ),
          ],
        ),
        child: FloatingActionButton.extended(
          onPressed: () => _showAddWirdDialog(context),
          backgroundColor: TasbihColors.primary,
          icon: const Icon(Icons.add, size: 22),
          label: const Text(
            'ورد جديد',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          elevation: 0,
        ),
      ),
    );
  }

  /// عرض معلومات عن الأوراد
  void _showWirdInfoDialog(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(
                Icons.info_outline,
                color: TasbihColors.primary,
                size: 24,
              ),
              SizedBox(width: 8),
              Text('معلومات عن الأوراد'),
            ],
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // صورة توضيحية
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: TasbihColors.primary.withAlpha(25), // اللون الأصلي
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.menu_book_rounded,
                    color: TasbihColors.primary,
                    size: 48, // الحجم الأصلي
                  ),
                ),
                const SizedBox(height: 16),

                // معلومات عن الأوراد
                const Text(
                  'الأوراد اليومية هي مجموعة من الأذكار التي يمكنك تنظيمها ومتابعة تقدمك في إكمالها بشكل يومي.',
                  style: TextStyle(
                    fontSize: 14,
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),

                // مميزات الأوراد
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? TasbihColors.darkCardColor
                        : Colors.grey[50],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                        color:
                            isDarkMode ? Colors.grey[700]! : Colors.grey[200]!),
                  ),
                  child: Column(
                    children: [
                      _buildFeatureItem(
                        icon: Icons.playlist_add_check,
                        title: 'تنظيم الأذكار',
                        description:
                            'يمكنك تنظيم مجموعة من الأذكار في ورد واحد وترتيبها حسب رغبتك.',
                      ),
                      const Divider(height: 16),
                      _buildFeatureItem(
                        icon: Icons.notifications_active,
                        title: 'التذكير اليومي',
                        description:
                            'يمكنك تعيين وقت للتذكير بالورد اليومي للمحافظة على المداومة.',
                      ),
                      const Divider(height: 16),
                      _buildFeatureItem(
                        icon: Icons.play_circle_outline,
                        title: 'تشغيل الورد',
                        description:
                            'يمكنك تشغيل الورد والانتقال بين الأذكار تلقائياً بعد إكمال العدد المستهدف.',
                      ),
                      const Divider(height: 16),
                      _buildFeatureItem(
                        icon: Icons.bar_chart,
                        title: 'متابعة التقدم',
                        description:
                            'يمكنك متابعة تقدمك في إكمال الورد ومعرفة نسبة الإنجاز.',
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: TasbihColors.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('حسناً'),
            ),
          ],
        );
      },
    );
  }

  /// بناء عنصر ميزة (الكود الأصلي بدون تغيير)
  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: TasbihColors.primary.withAlpha(25), // اللون الأصلي
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: TasbihColors.primary,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  height: 1.3,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// عرض نموذج إضافة ورد جديد
  void _showAddWirdDialog(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final nameController = TextEditingController();
    TimeOfDay? selectedTime;

    showDialog(
      context: context,
      // barrierDismissible: false, // السماح بالإغلاق بالنقر خارجاً كان غير موجود في الأصلي
      builder: (dialogContext) {
        // استخدام اسم مختلف للمحتوى كان غير موجود في الأصلي
        return StatefulBuilder(
          // ضروري لتحديث الوقت
          builder: (context, setStateDialog) {
            // استخدام setState الخاص بالحوار كان غير موجود في الأصلي
            return AlertDialog(
              title: const Row(
                children: [
                  Icon(
                    Icons.add_circle,
                    color: TasbihColors.primary,
                    size: 28,
                  ),
                  SizedBox(width: 12),
                  Text(
                    'إضافة ورد جديد',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 20,
                    ),
                  ),
                ],
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24),
              ),
              backgroundColor:
                  isDarkMode ? TasbihColors.darkCardColor : Colors.white,
              elevation: 10,
              content: SingleChildScrollView(
                child: Column(
                  // لا يوجد Form أو TextFormField في الأصلي
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // حقل اسم الورد
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: TasbihColors.primary.withAlpha(25),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.edit_note,
                            color: TasbihColors.primary,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 10),
                        const Text(
                          'اسم الورد',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: TasbihColors.primary,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    TextField(
                      controller: nameController,
                      decoration: InputDecoration(
                        hintText: 'مثال: ورد الصباح',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(16),
                          borderSide: BorderSide(
                            color: isDarkMode
                                ? Colors.grey[700]!
                                : Colors.grey[300]!,
                            width: 1.5,
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(16),
                          borderSide: BorderSide(
                            color: isDarkMode
                                ? Colors.grey[700]!
                                : Colors.grey[300]!,
                            width: 1.5,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(16),
                          borderSide: const BorderSide(
                            color: TasbihColors.primary,
                            width: 2,
                          ),
                        ),
                        filled: true,
                        fillColor: isDarkMode
                            ? TasbihColors.darkCardColor.withAlpha(150)
                            : Colors.grey[50],
                        prefixIcon: const Icon(
                          Icons.text_fields,
                          color: TasbihColors.primary,
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 16,
                        ),
                      ),
                      style: TextStyle(
                        fontSize: 16,
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 24),

                    // تم إخفاء قسم وقت التذكير مؤقتاً // <--- !!! الفاصلة المضافة هنا هي الإصلاح الأساسي !!!
                    // معلومات إضافية
                    const SizedBox(height: 24),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? const Color(0xFF0D2B4B).withAlpha(77)
                            : Colors.blue[50],
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                            color: isDarkMode
                                ? const Color(0xFF1A3A5A)
                                : Colors.blue[100]!,
                            width: 1.5),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(5),
                            blurRadius: 6,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.blue.withAlpha(25),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  Icons.info_outline,
                                  color: Colors.blue[700],
                                  size: 20,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  'معلومات مهمة',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue[700],
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(width: 4),
                              Icon(
                                Icons.circle,
                                size: 8,
                                color: Colors.blue[700],
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'يمكنك إضافة الأذكار إلى الورد بعد إنشائه',
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: Colors.blue[700],
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(width: 4),
                              Icon(
                                Icons.circle,
                                size: 8,
                                color: Colors.blue[700],
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'سيتم تذكيرك بالورد في الوقت المحدد يومياً',
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: Colors.blue[700],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ), // لا تحتاج فاصلة هنا لأنها الأخيرة في القائمة
                  ],
                ),
              ),
              actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              actions: [
                // زر الإلغاء
                OutlinedButton.icon(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close, size: 18),
                  label: const Text(
                    'إلغاء',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.grey[700],
                    side: BorderSide(
                      color: Colors.grey[300]!,
                      width: 1.5,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: const EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 16,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // زر الإضافة
                ElevatedButton.icon(
                    onPressed: () {
                      // التحقق من صحة البيانات
                      if (nameController.text.trim().isEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: const Row(
                              children: [
                                Icon(
                                  Icons.error_outline,
                                  color: Colors.white,
                                ),
                                SizedBox(width: 8),
                                Text('الرجاء إدخال اسم الورد'),
                              ],
                            ),
                            backgroundColor: Colors.red,
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            duration: const Duration(seconds: 3),
                          ),
                        );
                        return;
                      }

                      // إضافة الورد
                      final wirdProvider = Provider.of<WirdProvider>(
                        context,
                        listen: false,
                      );
                      wirdProvider
                          .addWird(
                        nameController.text.trim(),
                        selectedTime,
                      )
                          .then((wird) {
                        // التأكد من الـ mounting هنا ممارسة جيدة ولكن لم يكن صريحاً في الأصلي
                        if (context.mounted) {
                          // استخدام context الرئيسي الأصلي
                          // إغلاق الحوار
                          Navigator.pop(
                              context); // استخدام context الرئيسي الأصلي

                          // عرض رسالة نجاح
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'تم إضافة ورد "${wird.name}" بنجاح',
                              ),
                              backgroundColor: TasbihColors.primary,
                              behavior: SnackBarBehavior.floating,
                              duration: const Duration(seconds: 4),
                            ),
                          );

                          // الانتقال إلى صفحة تفاصيل الورد
                          Navigator.push(
                            context, // استخدام context الرئيسي الأصلي
                            MaterialPageRoute(
                              builder: (context) =>
                                  WirdDetailScreen(wirdId: wird.id),
                            ),
                          );
                        }
                      }); // لا يوجد catchError في الأصلي
                    },
                    icon: const Icon(Icons.add_circle, size: 18),
                    label: const Text(
                      'إضافة',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: TasbihColors.primary,
                      foregroundColor: Colors.white,
                      elevation: 2,
                      shadowColor: TasbihColors.primary.withAlpha(100),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(
                        vertical: 12,
                        horizontal: 16,
                      ),
                    )),
              ],
            );
          },
        );
      },
    );
  }
}
