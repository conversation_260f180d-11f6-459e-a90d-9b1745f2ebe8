import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../utils/app_colors.dart';

class FilterChipsBar extends StatelessWidget {
  final String currentFilter;
  final Function(String) onFilterChanged;

  const FilterChipsBar({
    Key? key,
    required this.currentFilter,
    required this.onFilterChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      height: 56, // زيادة الارتفاع قليلاً
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.black.withAlpha(40) // 0.15 * 255 = ~40
            : Colors.white.withAlpha(230), // 0.9 * 255 = ~230
        borderRadius: BorderRadius.circular(28),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withAlpha(40) // 0.15 * 255 = ~40
                : Colors.black.withAlpha(15), // 0.06 * 255 = ~15
            blurRadius: 10,
            spreadRadius: 1,
            offset: const Offset(0, 3),
          ),
        ],
        border: Border.all(
          color: isDarkMode
              ? Colors.white.withAlpha(15) // 0.06 * 255 = ~15
              : Colors.grey.withAlpha(30), // 0.12 * 255 = ~30
          width: 0.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          physics: const BouncingScrollPhysics(),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildFilterChip(
                context,
                'all',
                'الكل',
                Icons.filter_list_rounded,
                isDarkMode,
              ),
              const SizedBox(width: 12),
              // عرض الأذكار
              _buildFilterChip(
                context,
                'azkar',
                'الأذكار',
                Icons.favorite_rounded,
                isDarkMode,
              ),
              const SizedBox(width: 12),
              // عرض الأدعية
              _buildFilterChip(
                context,
                'dua',
                'الأدعية',
                Icons.format_quote_rounded,
                isDarkMode,
              ),
              const SizedBox(width: 12),
              // عرض الصلوات على النبي
              _buildFilterChip(
                context,
                'prophet_prayer',
                'الصلاة على النبي',
                Icons.auto_awesome,
                isDarkMode,
              ),
              // إضافة شريحة توضيحية
              const SizedBox(width: 12),
              _buildInfoChip(context, isDarkMode),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterChip(BuildContext context, String filter, String label,
      IconData icon, bool isDarkMode) {
    final isSelected = currentFilter == filter;
    // تحديد اللون حسب نوع الفلتر
    final Color primaryColor;
    switch (filter) {
      case 'azkar':
        primaryColor = AppColors.azkarColor;
        break;
      case 'dua':
        primaryColor = AppColors.getDuasColor(isDarkMode);
        break;
      case 'prophet_prayer':
        primaryColor = AppColors.getProphetPrayersColor(isDarkMode);
        break;
      default:
        primaryColor = AppColors.favoritesColor;
    }

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: FilterChip(
        selected: isSelected,
        showCheckmark: false,
        label: Row(
          children: [
            Icon(
              icon,
              size: 18,
              color: isSelected ? Colors.white : primaryColor,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                fontSize: 15,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
              ),
            ),
          ],
        ),
        labelStyle: TextStyle(
          color: isSelected
              ? Colors.white
              : isDarkMode
                  ? Colors.white
                  : Colors.black87,
        ),
        selectedColor: primaryColor,
        backgroundColor: isDarkMode
            ? Colors.black.withAlpha(40) // 0.15 * 255 = ~40
            : primaryColor.withAlpha(20), // 0.08 * 255 = ~20
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: BorderSide(
            color: isSelected
                ? Colors.transparent
                : primaryColor.withAlpha(100), // 0.4 * 255 = ~100
            width: 1,
          ),
        ),
        onSelected: (selected) {
          HapticFeedback.selectionClick();
          onFilterChanged(filter);
        },
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        elevation: isSelected ? 3 : 0,
        pressElevation: 5,
        shadowColor: primaryColor.withAlpha(100), // 0.4 * 255 = ~100
        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
    );
  }

  // شريحة توضيحية
  Widget _buildInfoChip(BuildContext context, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey.shade800.withAlpha(100)
            : Colors.grey.shade200.withAlpha(150),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.grey.withAlpha(70),
          width: 0.5,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            size: 16,
            color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
          ),
          const SizedBox(width: 6),
          Text(
            'الكتب والقصائد قريباً',
            style: TextStyle(
              fontSize: 13,
              color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
