// رسم بياني للنشاط اليومي

import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/tasbih_stats_model.dart';
import '../utils/tasbih_colors.dart';

class ActivityChart extends StatelessWidget {
  final List<DailyStats> dailyStats;
  final double height;
  final int daysToShow;
  final bool showLabels;

  const ActivityChart({
    Key? key,
    required this.dailyStats,
    this.height = 200,
    this.daysToShow = 7,
    this.showLabels = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // الحصول على حجم الشاشة للتصميم المتجاوب بشكل أكثر دقة
    final size = MediaQuery.of(context).size;
    final isVerySmallScreen = size.width < 320; // للشاشات الصغيرة جداً
    final isSmallScreen =
        size.width >= 320 && size.width < 360; // للشاشات الصغيرة
    final isMediumScreen =
        size.width >= 360 && size.width < 600; // للهواتف العادية
    final isLargeScreen = size.width >= 600; // للأجهزة اللوحية والشاشات الكبيرة

    // تحديد أحجام الهوامش بناءً على حجم الشاشة بشكل أكثر دقة
    final rightPadding = isVerySmallScreen
        ? 8.0
        : (isSmallScreen ? 12.0 : (isMediumScreen ? 16.0 : 20.0));

    final leftPadding = isVerySmallScreen
        ? 2.0
        : (isSmallScreen ? 4.0 : (isMediumScreen ? 6.0 : 8.0));

    final topPadding = isVerySmallScreen
        ? 8.0
        : (isSmallScreen ? 12.0 : (isMediumScreen ? 16.0 : 20.0));

    final bottomPadding = isVerySmallScreen
        ? 4.0
        : (isSmallScreen ? 8.0 : (isMediumScreen ? 12.0 : 16.0));

    // ترتيب الإحصائيات حسب التاريخ
    final sortedStats = List<DailyStats>.from(dailyStats)
      ..sort((a, b) => a.date.compareTo(b.date));

    // أخذ آخر X يوم
    final recentStats = sortedStats.length > daysToShow
        ? sortedStats.sublist(sortedStats.length - daysToShow)
        : sortedStats;

    // إذا كانت القائمة فارغة، عرض رسالة
    if (recentStats.isEmpty) {
      return SizedBox(
        height: height,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.bar_chart_outlined,
                size: isVerySmallScreen
                    ? 32
                    : (isSmallScreen ? 40 : (isMediumScreen ? 48 : 56)),
                color: isDarkMode ? Colors.white30 : Colors.black12,
              ),
              SizedBox(height: isVerySmallScreen ? 8 : 12),
              Text(
                'لا توجد بيانات للعرض',
                style: TextStyle(
                  fontSize: isVerySmallScreen
                      ? 12.0
                      : (isSmallScreen ? 14.0 : (isMediumScreen ? 16.0 : 18.0)),
                  color: isDarkMode ? Colors.white70 : Colors.black54,
                ),
                textDirection:
                    TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                textAlign: TextAlign.center, // محاذاة النص إلى الوسط
              ),
            ],
          ),
        ),
      );
    }

    // الحصول على أقصى قيمة للمقياس
    final maxY = _getMaxCount(recentStats) * 1.2;

    return SizedBox(
      height: height,
      child: Padding(
        padding: EdgeInsets.only(
          right: rightPadding,
          left: leftPadding,
          top: topPadding,
          bottom: bottomPadding,
        ),
        child: LineChart(
          LineChartData(
            gridData: FlGridData(
              show: true,
              drawVerticalLine: false,
              horizontalInterval: maxY / 5,
              getDrawingHorizontalLine: (value) {
                return FlLine(
                  color: isDarkMode
                      ? Colors.white.withAlpha(15)
                      : Colors.black.withAlpha(15),
                  strokeWidth: 1,
                  dashArray: [5, 5],
                );
              },
            ),
            titlesData: FlTitlesData(
              show: true,
              rightTitles: const AxisTitles(
                sideTitles: SideTitles(showTitles: false),
              ),
              topTitles: const AxisTitles(
                sideTitles: SideTitles(showTitles: false),
              ),
              bottomTitles: AxisTitles(
                sideTitles: SideTitles(
                  showTitles: showLabels,
                  reservedSize: 30,
                  interval: 1,
                  getTitlesWidget: (value, meta) {
                    if (value.toInt() >= recentStats.length || value < 0) {
                      return const SizedBox.shrink();
                    }

                    final date = recentStats[value.toInt()].date;
                    return Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        '${date.day}/${date.month}',
                        style: TextStyle(
                          color: isDarkMode ? Colors.white70 : Colors.black54,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    );
                  },
                ),
              ),
              leftTitles: AxisTitles(
                sideTitles: SideTitles(
                  showTitles: showLabels,
                  interval: maxY / 5,
                  getTitlesWidget: (value, meta) {
                    if (value == 0) {
                      return const SizedBox.shrink();
                    }
                    return Text(
                      value.toInt().toString(),
                      style: TextStyle(
                        color: isDarkMode ? Colors.white70 : Colors.black54,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.left,
                    );
                  },
                  reservedSize: 40,
                ),
              ),
            ),
            borderData: FlBorderData(
              show: false,
            ),
            minX: 0,
            maxX: recentStats.length - 1.0,
            minY: 0,
            maxY: maxY,
            lineBarsData: [
              // خط النشاط
              LineChartBarData(
                spots: List.generate(recentStats.length, (index) {
                  return FlSpot(
                    index.toDouble(),
                    recentStats[index].count.toDouble(),
                  );
                }),
                isCurved: true,
                gradient: LinearGradient(
                  colors: [
                    TasbihColors.primary.withAlpha(180),
                    TasbihColors.primary,
                  ],
                ),
                barWidth: isVerySmallScreen
                    ? 2
                    : (isSmallScreen ? 2.5 : (isMediumScreen ? 3 : 4)),
                isStrokeCapRound: true,
                dotData: FlDotData(
                  show: true,
                  getDotPainter: (spot, percent, barData, index) {
                    return FlDotCirclePainter(
                      radius: isVerySmallScreen
                          ? 3
                          : (isSmallScreen ? 3.5 : (isMediumScreen ? 4 : 5)),
                      color: TasbihColors.primary,
                      strokeWidth: isVerySmallScreen
                          ? 1
                          : (isSmallScreen ? 1.5 : (isMediumScreen ? 2 : 2.5)),
                      strokeColor: Colors.white,
                    );
                  },
                ),
                belowBarData: BarAreaData(
                  show: true,
                  gradient: LinearGradient(
                    colors: [
                      TasbihColors.primary.withAlpha(100),
                      TasbihColors.primary.withAlpha(10),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
              ),
            ],
            lineTouchData: LineTouchData(
              touchTooltipData: LineTouchTooltipData(
                tooltipBgColor: isDarkMode ? Colors.grey[800]! : Colors.white,
                tooltipRoundedRadius: 8,
                getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                  return touchedBarSpots.map((barSpot) {
                    final index = barSpot.x.toInt();
                    if (index >= 0 && index < recentStats.length) {
                      final stats = recentStats[index];
                      final date = stats.date;
                      final formattedDate =
                          '${date.day}/${date.month}/${date.year}';

                      return LineTooltipItem(
                        '$formattedDate\n',
                        TextStyle(
                          color: isDarkMode ? Colors.white : Colors.black87,
                          fontWeight: FontWeight.bold,
                        ),
                        children: [
                          TextSpan(
                            text: 'العدد: ${stats.count}',
                            style: const TextStyle(
                              color: TasbihColors.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      );
                    }
                    return null;
                  }).toList();
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  // الحصول على أقصى عدد في الإحصائيات
  double _getMaxCount(List<DailyStats> stats) {
    if (stats.isEmpty) return 100;

    double maxCount = 0;
    for (final stat in stats) {
      if (stat.count > maxCount) {
        maxCount = stat.count.toDouble();
      }
    }

    return maxCount > 0 ? maxCount : 100;
  }
}
