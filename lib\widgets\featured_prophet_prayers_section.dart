// قسم الصلوات المميزة على النبي بتصميم فاخر

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import 'dart:math' as math;
import 'package:flutter_svg/flutter_svg.dart';
import '../models/dua.dart';
import '../utils/app_colors.dart';

class FeaturedProphetPrayersSection extends StatefulWidget {
  final List<Dua> featuredPrayers;
  final Function(Dua) onPrayerTap;
  final Animation<double> animation;

  const FeaturedProphetPrayersSection({
    Key? key,
    required this.featuredPrayers,
    required this.onPrayerTap,
    required this.animation,
  }) : super(key: key);

  @override
  State<FeaturedProphetPrayersSection> createState() =>
      _FeaturedProphetPrayersSectionState();
}

class _FeaturedProphetPrayersSectionState
    extends State<FeaturedProphetPrayersSection>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late PageController _pageController;
  int _currentPage = 0;

  // متغيرات للتحكم في تأثيرات الحركة
  final double _viewportFraction = 0.85;

  @override
  void initState() {
    super.initState();
    // تقليل مدة الرسوم المتحركة من 30 ثانية إلى 15 ثانية لتحسين الأداء
    _animationController = AnimationController(
      duration: const Duration(seconds: 15),
      vsync: this,
    )..repeat(reverse: false);

    _pageController = PageController(
      initialPage: 0,
      viewportFraction: _viewportFraction,
    );

    // الاستماع لتغييرات الصفحة - تحسين الأداء بتقليل استدعاءات setState
    _pageController.addListener(() {
      if (!_pageController.hasClients) return;

      final int next = _pageController.page!.round();
      if (_currentPage != next && mounted) {
        setState(() {
          _currentPage = next;
        });
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final prophetPrayersColor = AppColors.getProphetPrayersColor(isDarkMode);

    // استخدام RepaintBoundary للقسم بأكمله لتحسين أداء الرسم
    return RepaintBoundary(
      child: AnimatedBuilder(
        animation: widget.animation,
        builder: (context, child) {
          return Opacity(
            opacity: widget.animation.value,
            child: Transform.translate(
              // تقليل مسافة الحركة لتحسين الأداء
              offset: Offset(0, 20 * (1 - widget.animation.value)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // عنوان القسم مع تأثيرات فاخرة
                  Padding(
                    padding: const EdgeInsets.fromLTRB(16, 30, 16, 16),
                    child: Stack(
                      children: [
                        // زخرفة خلفية للعنوان - ثابتة بدون رسوم متحركة
                        Positioned(
                          left: -10,
                          top: -15,
                          child: Opacity(
                            opacity: 0.07,
                            child: Icon(
                              Icons.auto_awesome_mosaic,
                              size: 60,
                              color: prophetPrayersColor,
                            ),
                          ),
                        ),

                        // محتوى العنوان
                        Row(
                          children: [
                            // أيقونة متحركة فاخرة
                            _buildLuxuryIcon(prophetPrayersColor),
                            const SizedBox(width: 16),

                            // عنوان مع تأثيرات متقدمة - تم تبسيطها
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // عنوان رئيسي - تم تبسيط التأثيرات
                                  Text(
                                    'صيغ مميزة للصلاة على النبي',
                                    style: TextStyle(
                                      fontSize: 22,
                                      fontWeight: FontWeight.bold,
                                      color: isDarkMode
                                          ? Colors.white
                                          : Colors.black87,
                                    ),
                                  ),

                                  // وصف إضافي للقسم
                                  Padding(
                                    padding:
                                        const EdgeInsets.only(top: 6, right: 4),
                                    child: Text(
                                      'صلوات مختارة بعناية للصلاة على النبي الكريم',
                                      style: TextStyle(
                                        fontSize: 13,
                                        color: isDarkMode
                                            ? Colors.grey[400]
                                            : Colors.grey[600],
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // خط فاصل مزخرف
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: _buildDecorativeDivider(
                        prophetPrayersColor, isDarkMode),
                  ),

                  const SizedBox(height: 20),

                  // قائمة الصلوات المميزة بتصميم فاخر
                  SizedBox(
                    height: 240,
                    child: widget.featuredPrayers.isEmpty
                        ? _buildEmptyState(prophetPrayersColor, isDarkMode)
                        : _buildPageView(prophetPrayersColor, isDarkMode),
                  ),

                  // مؤشرات الصفحات
                  if (widget.featuredPrayers.length > 1)
                    Padding(
                      padding: const EdgeInsets.only(top: 16),
                      child: Center(
                        child: _buildPageIndicator(prophetPrayersColor),
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // بناء أيقونة فاخرة متحركة - تم تحسينها للأداء
  Widget _buildLuxuryIcon(Color prophetPrayersColor) {
    // استخدام RepaintBoundary لتحسين أداء الرسم
    return RepaintBoundary(
      child: Stack(
        alignment: Alignment.center,
        children: [
          // طبقة توهج خارجية - تم تقليل مدة الرسوم المتحركة
          TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: 0.0, end: 1.0),
            // تقليل المدة من 3 ثوانٍ إلى 1.5 ثانية
            duration: const Duration(milliseconds: 1500),
            curve: Curves.easeInOut,
            builder: (context, value, _) {
              // تبسيط حساب الألفا لتحسين الأداء
              return Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      prophetPrayersColor
                          .withAlpha((30 * value).toInt()), // تبسيط الحساب
                      Colors.transparent,
                    ],
                    stops: const [0.1, 1.0],
                  ),
                ),
              );
            },
          ),

          // حلقة خارجية متحركة - تم تقليل مدة الرسوم المتحركة
          TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: 0.0, end: 1.0),
            // تقليل المدة من 6 ثوانٍ إلى 3 ثوانٍ
            duration: const Duration(seconds: 3),
            curve: Curves.easeInOut,
            builder: (context, value, _) {
              return Transform.rotate(
                angle: value * math.pi * 2,
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: prophetPrayersColor.withAlpha(30),
                      width: 1.5, // تقليل سمك الحدود
                    ),
                  ),
                ),
              );
            },
          ),

          // تم إزالة الحلقة الداخلية المتحركة لتحسين الأداء

          // الأيقونة المركزية - تم تبسيطها
          Container(
            width: 46,
            height: 46,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  prophetPrayersColor.withAlpha(70),
                  prophetPrayersColor.withAlpha(120),
                ],
              ),
              shape: BoxShape.circle,
              // تقليل تأثير الظل
              boxShadow: [
                BoxShadow(
                  color: prophetPrayersColor.withAlpha(30),
                  blurRadius: 6, // تقليل قوة البلور
                  spreadRadius: 0,
                  offset: const Offset(0, 2), // تقليل الإزاحة
                ),
              ],
            ),
            child: TweenAnimationBuilder<double>(
              tween: Tween<double>(begin: 0.0, end: 1.0),
              // تقليل المدة من 2 ثوانٍ إلى 1 ثانية
              duration: const Duration(seconds: 1),
              curve: Curves.easeOutBack, // تغيير المنحنى لتحسين الأداء
              builder: (context, value, _) {
                return Transform.rotate(
                  angle: value * 2 * math.pi * 0.05, // تقليل زاوية الدوران
                  child: const Icon(
                    Icons.auto_awesome,
                    color: Colors.white,
                    size: 24,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // بناء خط فاصل مزخرف
  Widget _buildDecorativeDivider(Color prophetPrayersColor, bool isDarkMode) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Container(
            height: 2,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.centerRight,
                end: Alignment.centerLeft,
                colors: [
                  prophetPrayersColor,
                  prophetPrayersColor.withAlpha(0),
                ],
              ),
            ),
          ),
        ),
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 8),
          width: 6,
          height: 6,
          decoration: BoxDecoration(
            color: prophetPrayersColor,
            shape: BoxShape.circle,
          ),
        ),
        Expanded(
          flex: 3,
          child: Container(
            height: 2,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.centerRight,
                end: Alignment.centerLeft,
                colors: [
                  prophetPrayersColor.withAlpha(180),
                  prophetPrayersColor.withAlpha(0),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  // بناء عرض الصفحات - تم تحسينه للأداء
  Widget _buildPageView(Color prophetPrayersColor, bool isDarkMode) {
    return RepaintBoundary(
      // إضافة RepaintBoundary لتحسين أداء الرسم
      child: PageView.builder(
        controller: _pageController,
        physics: const BouncingScrollPhysics(),
        itemCount: widget.featuredPrayers.length,
        onPageChanged: (index) {
          HapticFeedback.lightImpact();
        },
        itemBuilder: (context, index) {
          // حساب معامل التكبير بناءً على المسافة من الصفحة الحالية - تحسين الأداء
          double pageOffset = 0;
          if (_pageController.position.haveDimensions) {
            pageOffset = index - _pageController.page!;
            // تقييد قيمة pageOffset لتجنب الحسابات المكلفة
            pageOffset = pageOffset.clamp(-1.0, 1.0);
          }

          final isCurrentPage = index == _currentPage;
          final prayer = widget.featuredPrayers[index];

          return AnimatedContainer(
            // تقليل مدة الرسوم المتحركة لتحسين الأداء
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeOut, // تبسيط منحنى الحركة
            margin: EdgeInsets.only(
              right: 10,
              left: 10,
              // تقليل الفرق في الحجم لتحسين الأداء
              top: isCurrentPage ? 0 : 10,
              bottom: isCurrentPage ? 0 : 10,
            ),
            child: _buildLuxuryPrayerCard(
              prayer,
              prophetPrayersColor,
              isDarkMode,
              index,
              pageOffset,
            ),
          );
        },
      ),
    );
  }

  // بناء بطاقة الصلاة المميزة بتصميم فاخر - تم تحسينها للأداء
  Widget _buildLuxuryPrayerCard(
    Dua prayer,
    Color prophetPrayersColor,
    bool isDarkMode,
    int index,
    double pageOffset,
  ) {
    // تأثير التحويل 3D - تم تقليل قوة التأثير
    final transform = Matrix4.identity()
      ..setEntry(3, 2, 0.0005) // تقليل قوة المنظور لتحسين الأداء
      ..rotateY(pageOffset * 0.05); // تقليل زاوية الدوران لتحسين الأداء

    return RepaintBoundary(
      // إضافة RepaintBoundary لتحسين أداء الرسم
      child: Transform(
        transform: transform,
        alignment: Alignment.center,
        child: GestureDetector(
          onTap: () {
            HapticFeedback.mediumImpact();
            widget.onPrayerTap(prayer);
          },
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(28),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  prophetPrayersColor.withAlpha(35),
                  prophetPrayersColor.withAlpha(65),
                ],
                stops: const [0.3, 1.0],
              ),
              // تقليل قوة الظل
              boxShadow: [
                BoxShadow(
                  color: prophetPrayersColor.withAlpha(25),
                  blurRadius: 12, // تقليل قوة البلور
                  spreadRadius: 0, // تقليل انتشار الظل
                  offset: const Offset(0, 5), // تقليل الإزاحة
                ),
              ],
              border: Border.all(
                color: prophetPrayersColor.withAlpha(90),
                width: 1.5, // تقليل سمك الحدود
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(28),
              // تقليل قوة تأثير BackdropFilter لتحسين الأداء
              child: BackdropFilter(
                filter:
                    ImageFilter.blur(sigmaX: 3, sigmaY: 3), // تقليل قوة البلور
                child: Stack(
                  children: [
                    // نمط زخرفي ثابت بدلاً من متحرك لتحسين الأداء
                    Positioned.fill(
                      child: Opacity(
                        opacity: 0.04,
                        child: SvgPicture.asset(
                          'assets/images/p2.svg',
                          fit: BoxFit.cover,
                          colorFilter: ColorFilter.mode(
                              prophetPrayersColor, BlendMode.srcIn),
                        ),
                      ),
                    ),

                    // زخرفة خلفية - تم تبسيطها
                    Positioned(
                      right: -30,
                      bottom: -30,
                      child: Opacity(
                        opacity: 0.12,
                        child: Icon(
                          Icons.format_quote,
                          size: 140,
                          color: prophetPrayersColor,
                        ),
                      ),
                    ),

                    // تأثير توهج في الزاوية - تم الإبقاء عليه لأنه ثابت
                    Positioned(
                      left: -20,
                      top: -20,
                      child: Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(
                            colors: [
                              prophetPrayersColor.withAlpha(30),
                              Colors.transparent,
                            ],
                            stops: const [0.1, 1.0],
                          ),
                        ),
                      ),
                    ),

                    // محتوى البطاقة
                    Padding(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // الجزء العلوي: مصدر الصلاة ورقم الصلاة
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // مصدر الصلاة - تم تبسيط التأثيرات
                              if (prayer.source != null)
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 14,
                                    vertical: 8,
                                  ),
                                  decoration: BoxDecoration(
                                    color: prophetPrayersColor.withAlpha(60),
                                    borderRadius: BorderRadius.circular(16),
                                    // إزالة الظل لتحسين الأداء
                                    border: Border.all(
                                      color: prophetPrayersColor.withAlpha(80),
                                      width: 1,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.auto_awesome,
                                        size: 14,
                                        color: Colors.white.withAlpha(220),
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        prayer.source!,
                                        style: TextStyle(
                                          fontSize: 13,
                                          color: Colors.white.withAlpha(220),
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),

                              // رقم الصلاة
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 10,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: prophetPrayersColor.withAlpha(40),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: prophetPrayersColor.withAlpha(60),
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  '${index + 1}',
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: prophetPrayersColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 20),

                          // نص الصلاة - تم تبسيط التأثيرات
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: prophetPrayersColor.withAlpha(15),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: prophetPrayersColor.withAlpha(40),
                                  width: 1,
                                ),
                              ),
                              // إزالة ShaderMask لتحسين الأداء
                              child: Center(
                                child: SingleChildScrollView(
                                  physics: const BouncingScrollPhysics(),
                                  child: Text(
                                    prayer.text,
                                    style: TextStyle(
                                      fontSize: 16,
                                      height: 1.6,
                                      color: isDarkMode
                                          ? Colors.white
                                          : Colors.black87,
                                      // إزالة الظل لتحسين الأداء
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                            ),
                          ),

                          const SizedBox(height: 16),

                          // زر التفاصيل - تم تبسيط الرسوم المتحركة
                          Center(
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    prophetPrayersColor.withAlpha(120),
                                    prophetPrayersColor.withAlpha(180),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(16),
                                // تقليل قوة الظل
                                boxShadow: [
                                  BoxShadow(
                                    color: prophetPrayersColor.withAlpha(30),
                                    blurRadius: 4,
                                    spreadRadius: 0,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: const Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    'عرض',
                                    style: TextStyle(
                                      fontSize: 13,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                  SizedBox(width: 6),
                                  Icon(
                                    Icons.arrow_forward_ios,
                                    size: 12,
                                    color: Colors.white,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // بناء مؤشرات الصفحات - تم تحسينها للأداء
  Widget _buildPageIndicator(Color prophetPrayersColor) {
    return RepaintBoundary(
      // إضافة RepaintBoundary لتحسين أداء الرسم
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(
          widget.featuredPrayers.length,
          (index) {
            final isCurrentPage = index == _currentPage;
            return AnimatedContainer(
              duration: const Duration(
                  milliseconds: 200), // تقليل مدة الرسوم المتحركة
              margin: const EdgeInsets.symmetric(horizontal: 4),
              height: 8,
              width: isCurrentPage ? 24 : 8,
              decoration: BoxDecoration(
                color: isCurrentPage
                    ? prophetPrayersColor
                    : prophetPrayersColor.withAlpha(70),
                borderRadius: BorderRadius.circular(12),
                // إزالة الظلال لتحسين الأداء
              ),
            );
          },
        ),
      ),
    );
  }

  // بناء حالة فارغة
  Widget _buildEmptyState(Color prophetPrayersColor, bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.auto_awesome,
            size: 60,
            color: prophetPrayersColor.withAlpha(100),
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد صلوات مميزة حالياً',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white70 : Colors.black54,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'سيتم إضافة صلوات مميزة قريباً',
            style: TextStyle(
              fontSize: 14,
              color: isDarkMode ? Colors.white60 : Colors.black45,
            ),
          ),
        ],
      ),
    );
  }
}
