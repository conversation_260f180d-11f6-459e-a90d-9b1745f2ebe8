import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/book.dart';
import '../utils/app_colors.dart';
import 'book_cover.dart';

class BookListItem extends StatefulWidget {
  final Book book;
  final VoidCallback onTap;
  final bool animate;
  final Animation<double>? animation;
  final bool isLast;

  const BookListItem({
    Key? key,
    required this.book,
    required this.onTap,
    this.animate = false,
    this.animation,
    this.isLast = false,
  }) : super(key: key);

  @override
  State<BookListItem> createState() => _BookListItemState();
}

class _BookListItemState extends State<BookListItem>
    with SingleTickerProviderStateMixin {
  bool _isPressed = false;
  late AnimationController _pressController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _pressController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.97).animate(
      CurvedAnimation(
        parent: _pressController,
        curve: Curves.easeOut,
      ),
    );
  }

  @override
  void dispose() {
    _pressController.dispose();
    super.dispose();
  }

  void _onPressChanged(bool isPressed) {
    if (_isPressed != isPressed) {
      setState(() {
        _isPressed = isPressed;
      });

      if (isPressed) {
        _pressController.forward();
      } else {
        _pressController.reverse();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final Widget child = AnimatedBuilder(
      animation: _pressController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: child,
        );
      },
      child: _buildCard(),
    );

    // تطبيق الرسوم المتحركة إذا كانت مطلوبة
    if (widget.animate && widget.animation != null) {
      return FadeTransition(
        opacity: widget.animation!,
        child: SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.05, 0),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: widget.animation!,
            curve: Curves.easeOutCubic,
          )),
          child: child,
        ),
      );
    }

    return child;
  }

  Widget _buildCard() {
    final categoryColor = AppColors.getCategoryColor(widget.book.category);
    final textTheme = Theme.of(context).textTheme;

    return GestureDetector(
      onTap: () {
        HapticFeedback.selectionClick();
        widget.onTap();
      },
      onTapDown: (_) => _onPressChanged(true),
      onTapUp: (_) => _onPressChanged(false),
      onTapCancel: () => _onPressChanged(false),
      child: Container(
        margin: EdgeInsets.only(
            bottom: widget.isLast ? 0 : 12, left: 16, right: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.all(Radius.circular(16)),
          boxShadow: [
            BoxShadow(
              color: _isPressed
                  ? categoryColor.withValues(alpha: 26) // 0.1 * 255 = ~26
                  : Colors.grey.withValues(alpha: 26), // 0.1 * 255 = ~26
              blurRadius: _isPressed ? 4 : 6,
              offset: _isPressed ? const Offset(0, 1) : const Offset(0, 3),
              spreadRadius: _isPressed ? 0 : 1,
            ),
          ],
          border: Border.all(
            color: Colors.grey.withValues(alpha: 26), // 0.1 * 255 = ~26
            width: 0.5,
          ),
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.all(Radius.circular(16)),
          child: IntrinsicHeight(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // غلاف الكتاب (جانبي)
                Hero(
                  tag: 'book_cover_${widget.book.id}',
                  child: SizedBox(
                    width: 100,
                    child: BookCover(
                      key: ValueKey('book_cover_widget_${widget.book.id}'),
                      book: widget.book,
                      borderRadius: 16,
                    ),
                  ),
                ),

                // معلومات الكتاب
                Expanded(
                  child: AnimatedContainer(
                    key: ValueKey('book_info_${widget.book.id}'),
                    duration: const Duration(milliseconds: 150),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topRight,
                        end: Alignment.bottomLeft,
                        colors: [
                          Colors.white,
                          _isPressed
                              ? categoryColor.withValues(
                                  alpha: 13) // 0.05 * 255 = ~13
                              : Colors.white,
                        ],
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // فئة الكتاب
                        _buildCategoryChip(categoryColor),

                        const SizedBox(height: 10),

                        // عنوان الكتاب
                        Text(
                          widget.book.title,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            height: 1.3,
                          ),
                        ),

                        const SizedBox(height: 6),

                        // المؤلف
                        if (widget.book.author.isNotEmpty) ...[
                          Row(
                            children: [
                              Icon(
                                Icons.person_outline,
                                size: 14,
                                color: Colors.grey[600],
                              ),
                              const SizedBox(width: 6),
                              Expanded(
                                child: Text(
                                  widget.book.author,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: textTheme.bodyMedium?.copyWith(
                                    color: Colors.grey[700],
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                        ],

                        // إضافة مؤشر تقدم القراءة
                        _buildReadingProgress(),
                        const SizedBox(height: 10),

                        // الصف السفلي: عدد الصفحات + زر
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // عدد الصفحات
                            if (widget.book.pages > 0) _buildPagesCounter(),

                            // زر التفاصيل
                            _buildActionButton(categoryColor),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryChip(Color categoryColor) {
    return Container(
      key: ValueKey('category_chip_${widget.book.category}'),
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        color: categoryColor.withValues(alpha: 26), // 0.1 * 255 = ~26
        borderRadius: const BorderRadius.all(Radius.circular(8)),
        border: Border.all(
          color: categoryColor.withValues(alpha: 51), // 0.2 * 255 = ~51
          width: 0.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getCategoryIcon(widget.book.category),
            color: categoryColor,
            size: 12,
          ),
          const SizedBox(width: 4),
          Text(
            widget.book.category,
            style: TextStyle(
              color: categoryColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPagesCounter() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.menu_book,
          size: 14,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 4),
        Text(
          '${widget.book.pages} صفحة',
          style: TextStyle(
            color: Colors.grey[700],
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton(Color categoryColor) {
    return Material(
      color: Colors.transparent,
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: widget.onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: categoryColor,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: categoryColor.withValues(alpha: 77), // 0.3 * 255 = ~77
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: const Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.book_outlined,
                size: 14,
                color: Colors.white,
              ),
              SizedBox(width: 4),
              Text(
                'قراءة',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    // تمت المواءمة مع وظيفة GetCategoryIcon في BookGridItem
    switch (category.toLowerCase()) {
      case 'رواية':
        return Icons.auto_stories;
      case 'ديني':
        return Icons.mosque;
      case 'تاريخ':
        return Icons.history_edu;
      case 'فلسفة':
        return Icons.psychology;
      case 'علمي':
        return Icons.science;
      case 'أدب':
        return Icons.menu_book;
      default:
        return Icons.category;
    }
  }

  Widget _buildReadingProgress() {
    // افتراض أن هناك نسبة قراءة من 0 إلى 100
    final progress = widget.book.id.hashCode % 100 / 100; // للاختبار فقط

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'تقدم القراءة:',
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 4),
        Stack(
          children: [
            // خلفية شريط التقدم
            Container(
              height: 4,
              width: 100,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // شريط التقدم الفعلي
            Container(
              height: 4,
              width: 100 * progress,
              decoration: BoxDecoration(
                color: AppColors.getCategoryColor(widget.book.category),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
