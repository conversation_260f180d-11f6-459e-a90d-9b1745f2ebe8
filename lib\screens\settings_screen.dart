import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../utils/app_colors.dart';
import '../services/database_helper.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
// import '../services/notification_service.dart'; // تم استبداله بـ notification_manager.dart
// import 'package:awesome_notifications/awesome_notifications.dart'; // تم تعطيل النظام القديم
import '../services/notification_manager.dart'; // استيراد مدير الإشعارات الجديد

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;

  String _appVersion = '';

  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final NotificationManager _notificationManager = NotificationManager();
  TimeOfDay? _morningTime;
  TimeOfDay? _eveningTime;
  bool _morningEnabled = false;
  bool _eveningEnabled = false;
  int _snoozeMinutes = 10;

  @override
  void initState() {
    super.initState();

    // إعداد التحريكات
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
      ),
    );

    _slideAnimation = Tween<double>(begin: 0.2, end: 0.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
      ),
    );

    _animationController.forward();

    _getAppVersion();
    _loadSettings();
  }

  // الحصول على إصدار التطبيق
  Future<void> _getAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _appVersion = packageInfo.version;
      });
    } catch (e) {
      // استخدم إصدار افتراضي إذا حدث خطأ
      setState(() {
        _appVersion = '1.0.0';
      });
    }
  }

  // تحميل إعدادات الإشعارات
  Future<void> _loadSettings() async {
    try {
      // Cargar configuración de notificaciones usando el nuevo sistema
      final morningEnabled = await _notificationManager.isMorningAzkarEnabled();
      final morningTime = await _notificationManager.getMorningAzkarTime();
      final eveningEnabled = await _notificationManager.isEveningAzkarEnabled();
      final eveningTime = await _notificationManager.getEveningAzkarTime();
      final snoozeMinutes = await _notificationManager.getSnoozeTime();

      // Actualizar directamente los valores en el estado
      setState(() {
        _morningTime = morningTime;
        _morningEnabled = morningEnabled;
        _eveningTime = eveningTime;
        _eveningEnabled = eveningEnabled;
        _snoozeMinutes = snoozeMinutes;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل الإعدادات: $e');
    }
  }

  // دالة اختيار الخط
  void _selectFont(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار نوع الخط'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: ThemeProvider.availableFonts.length,
            itemBuilder: (context, index) {
              final fontName = ThemeProvider.availableFonts[index];
              return RadioListTile<int>(
                title: Text(
                  fontName,
                  style: _getFontStyle(index),
                ),
                value: index,
                groupValue: themeProvider.fontIndex,
                onChanged: (value) {
                  Navigator.pop(context);
                  themeProvider.setFontType(value!);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  // الحصول على نمط الخط للعرض
  TextStyle _getFontStyle(int index) {
    if (index == 0) {
      return const TextStyle(); // خط النظام
    }

    final fontName = ThemeProvider.availableFonts[index];
    String fontFamily;

    switch (fontName) {
      case 'Cairo':
        fontFamily = 'Cairo';
        break;
      case 'Amiri':
        fontFamily = 'Amiri';
        break;
      case 'Almarai':
        fontFamily = 'Almarai';
        break;
      case 'Tajawal':
        fontFamily = 'Tajawal';
        break;
      case 'Scheherazade New':
        fontFamily = 'ScheherazadeNew';
        break;
      case 'Noto Kufi Arabic':
        fontFamily = 'NotoKufiArabic';
        break;
      case 'Noto Naskh Arabic':
        fontFamily = 'NotoNaskhArabic';
        break;
      case 'Changa':
        fontFamily = 'Changa';
        break;
      case 'El Messiri':
        fontFamily = 'ElMessiri';
        break;
      default:
        fontFamily = 'Tajawal';
    }

    return TextStyle(fontFamily: fontFamily);
  }

  // مشاركة التطبيق
  void _shareApp() {
    Share.share(
      'تطبيق وهج السالك: ينابيع الحكمة وأنوار المعرفة. حمله الآن من متجر جوجل بلاي: https://play.google.com/store/apps/details?id=com.wahaj.alsalik',
      subject: 'تطبيق وهج السالك',
    );
  }

  // تقييم التطبيق - تم تعطيله مؤقتاً
  // Future<void> _rateApp() async {
  //   final url = Uri.parse(
  //       'https://play.google.com/store/apps/details?id=com.wahaj.alsalik');
  //   try {
  //     await launchUrl(url, mode: LaunchMode.externalApplication);
  //   } catch (e) {
  //     if (mounted) {
  //       ScaffoldMessenger.of(context).showSnackBar(
  //         const SnackBar(content: Text('لا يمكن فتح متجر التطبيقات')),
  //       );
  //     }
  //   }
  // }

  // التواصل مع المطور
  Future<void> _contactDeveloper() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      query: encodeQueryParameters({
        'subject': 'تواصل من تطبيق وهج السالك',
        'body': 'مرحباً،\n\nأرسلت هذا البريد لـ:\n\n',
      }),
    );

    try {
      await launchUrl(emailUri);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('لا يمكن فتح تطبيق البريد الإلكتروني')),
        );
      }
    }
  }

  // مسح بيانات التطبيق
  Future<void> _clearAppData() async {
    _showCustomDialog(
      context,
      'مسح البيانات',
      'هل أنت متأكد من رغبتك في مسح جميع البيانات المحفوظة؟ لا يمكن التراجع عن هذا الإجراء.',
      [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        TextButton(
          onPressed: () async {
            Navigator.pop(context);
            await _databaseHelper.clearAllData();
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم مسح البيانات بنجاح')),
              );
            }
          },
          child: const Text('مسح', style: TextStyle(color: Colors.red)),
        ),
      ],
    );
  }

  // تشفير معلمات الاستعلام
  String encodeQueryParameters(Map<String, String> params) {
    return params.entries
        .map((e) =>
            '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    const settingsColor = AppColors.azkarColor;

    return Scaffold(
      body: CustomScrollView(
        physics: const BouncingScrollPhysics(),
        slivers: [
          // شريط العنوان
          SliverAppBar(
            pinned: true,
            expandedHeight: 150.0,
            elevation: 0,
            backgroundColor: Theme.of(context).scaffoldBackgroundColor,
            flexibleSpace: FlexibleSpaceBar(
              titlePadding:
                  const EdgeInsets.only(bottom: 16, right: 16, left: 16),
              title: const Text(
                'الإعدادات',
                style: TextStyle(
                  color: settingsColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              background: Stack(
                children: [
                  // خلفية متدرجة
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          settingsColor.withAlpha(38),
                          Theme.of(context).scaffoldBackgroundColor,
                        ],
                      ),
                    ),
                  ),
                  // زخرفة إسلامية
                  Positioned(
                    top: -20,
                    right: -20,
                    child: Opacity(
                      opacity: 0.1,
                      child: SvgPicture.asset(
                        'assets/images/islamic_pattern1.svg',
                        width: 150,
                        height: 150,
                        colorFilter: const ColorFilter.mode(
                          settingsColor,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // قائمة الإعدادات
          SliverList(
            delegate: SliverChildListDelegate(
              [
                // قسم المظهر
                _buildSectionTitle(context, 'المظهر', Icons.palette_outlined),

                // إعداد السمة الداكنة
                _buildSlideAnimation(
                  _buildSettingItem(
                    context,
                    'الوضع الداكن',
                    Icons.dark_mode_outlined,
                    settingsColor,
                    trailing: Switch(
                      value: themeProvider.isDarkMode,
                      onChanged: (value) => themeProvider.toggleDarkMode(),
                      activeColor: settingsColor,
                    ),
                  ),
                  delay: 0.1,
                ),

                // إعداد نوع الخط
                _buildSlideAnimation(
                  _buildSettingItem(
                    context,
                    'نوع الخط',
                    Icons.font_download_outlined,
                    settingsColor,
                    subtitle: 'الخط الحالي: ${themeProvider.currentFontName}',
                    onTap: () => _selectFont(context),
                  ),
                  delay: 0.2,
                ),

                const Divider(),

                // قسم التطبيق
                _buildSectionTitle(
                    context, 'التطبيق', Icons.app_settings_alt_outlined),

                // مشاركة التطبيق
                _buildSlideAnimation(
                  _buildSettingItem(
                    context,
                    'مشاركة التطبيق',
                    Icons.share_outlined,
                    settingsColor,
                    subtitle: 'شارك التطبيق مع الأصدقاء والعائلة',
                    onTap: _shareApp,
                  ),
                  delay: 0.4,
                ),

                // تقييم التطبيق - معطل مؤقتاً
                // _buildSlideAnimation(
                //   _buildSettingItem(
                //     context,
                //     'تقييم التطبيق',
                //     Icons.star_outline,
                //     settingsColor,
                //     subtitle: 'قيّم التطبيق على متجر التطبيقات',
                //     onTap: _rateApp,
                //   ),
                //   delay: 0.5,
                // ),

                // التواصل مع المطور
                _buildSlideAnimation(
                  _buildSettingItem(
                    context,
                    'تواصل معنا',
                    Icons.email_outlined,
                    settingsColor,
                    subtitle: 'أرسل ملاحظاتك أو اقتراحاتك',
                    onTap: _contactDeveloper,
                  ),
                  delay: 0.6,
                ),

                const Divider(),

                // قسم البيانات
                _buildSectionTitle(context, 'البيانات', Icons.storage_outlined),

                // مسح البيانات
                _buildSlideAnimation(
                  _buildSettingItem(
                    context,
                    'مسح البيانات',
                    Icons.delete_outline,
                    Colors.red,
                    subtitle: 'حذف جميع المفضلات والإعدادات',
                    onTap: _clearAppData,
                  ),
                  delay: 0.7,
                ),

                const SizedBox(height: 16),

                // معلومات التطبيق
                _buildSlideAnimation(
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        Image.asset(
                          'assets/images/app_logo.png',
                          width: 60,
                          height: 60,
                          color: settingsColor.withAlpha(180),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'وهج السالك',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: settingsColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'الإصدار $_appVersion',
                          style: TextStyle(
                            fontSize: 14,
                            color: Theme.of(context).textTheme.bodySmall?.color,
                          ),
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'تم التطوير بواسطة فريق وهج السالك',
                          style: TextStyle(
                            fontSize: 12,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                  delay: 0.8,
                ),

                const SizedBox(height: 40),

                // دالة لعرض قسم إشعارات الأذكار في الإعدادات
                _buildNotificationsSettings(),

                // إضافة زر للتحقق من عمل الإشعارات
                Padding(
                  padding: const EdgeInsets.only(top: 8.0, bottom: 8.0),
                  child: OutlinedButton.icon(
                    onPressed: () async {
                      // إرسال إشعار اختبار باستخدام النظام الجديد
                      final bool notificationSent = await _notificationManager
                          .sendTestMorningAzkarNotification();

                      // التحقق من الإشعارات المجدولة
                      final bool permissionsGranted = await _notificationManager
                          .checkNotificationPermissions();

                      // حفظ المتغيرات المحلية لاستخدامها في الرسالة
                      final String message = notificationSent
                          ? 'تم إرسال إشعار اختباري. ${permissionsGranted ? 'الأذونات ممنوحة بشكل صحيح' : 'يرجى التحقق من أذونات الإشعارات'}'
                          : 'فشل إرسال الإشعار الاختباري. يرجى التحقق من الإعدادات';

                      // التأكد من أن الحالة لا تزال مثبتة قبل عرض الرسالة
                      // استخدام BuildContext المحلي لتجنب مشكلة استخدام السياق عبر فجوات غير متزامنة
                      if (mounted) {
                        // استخدام متغير محلي للسياق
                        final BuildContext currentContext = context;
                        ScaffoldMessenger.of(currentContext).showSnackBar(
                          SnackBar(
                            content: Text(message),
                            duration: const Duration(seconds: 3),
                          ),
                        );
                      }
                    },
                    icon: const Icon(Icons.notifications_active, size: 18),
                    label: const Text('اختبار وفحص الإشعارات'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.azkarColor,
                      side: BorderSide(
                          color: AppColors.azkarColor
                              .withAlpha(128)), // 0.5 * 255 = 128
                      padding: const EdgeInsets.symmetric(
                          vertical: 8, horizontal: 16),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء عنوان قسم
  Widget _buildSectionTitle(BuildContext context, String title, IconData icon) {
    return _buildSlideAnimation(
      Padding(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
        child: Row(
          children: [
            Icon(
              icon,
              size: 20,
              color: AppColors.azkarColor,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.azkarColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء عنصر إعداد
  Widget _buildSettingItem(
    BuildContext context,
    String title,
    IconData icon,
    Color iconColor, {
    String? subtitle,
    Widget? content,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: iconColor.withAlpha(25),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    icon,
                    size: 22,
                    color: iconColor,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                      ),
                      if (subtitle != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 4.0),
                          child: Text(
                            subtitle,
                            style: TextStyle(
                              fontSize: 12,
                              color:
                                  Theme.of(context).textTheme.bodySmall?.color,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                if (trailing != null) trailing,
              ],
            ),
            if (content != null) content,
          ],
        ),
      ),
    );
  }

  // حركة انزلاق للعناصر
  Widget _buildSlideAnimation(Widget child, {double delay = 0.0}) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        final double delayedValue = delay > 0.0
            ? _animationController.value < delay
                ? 0.0
                : (_animationController.value - delay) / (1.0 - delay)
            : _animationController.value;

        // استخدام _fadeAnimation هنا
        final double opacity = _fadeAnimation.value * delayedValue;
        final double slideOffset = _slideAnimation.value * (1.0 - delayedValue);

        return Opacity(
          opacity: opacity,
          child: Transform.translate(
            offset: Offset(0, 20 * slideOffset),
            child: child,
          ),
        );
      },
      child: child,
    );
  }

  // إضافة دالة _showCustomDialog داخل الصف بدلاً من استخدام الدالة من ملف خارجي
  void _showCustomDialog(
    BuildContext context,
    String title,
    String content,
    List<Widget> actions,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(content),
          actions: actions,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        );
      },
    );
  }

  // واجهة إعدادات الإشعارات
  Widget _buildNotificationsSettings() {
    final theme = Theme.of(context);
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDark = themeProvider.isDarkMode;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم
        _buildSectionTitle(
            context, 'إشعارات الأذكار', Icons.notifications_outlined),

        // بطاقة إشعارات أذكار الصباح
        _buildSlideAnimation(
          Card(
            elevation: 2,
            margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: InkWell(
              onTap: () => _selectTime(context, true),
              borderRadius: BorderRadius.circular(12),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Container(
                      width: 44,
                      height: 44,
                      decoration: BoxDecoration(
                        color: Colors.amber.withAlpha(25), // 0.1 * 255 = ~25
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.wb_sunny_outlined,
                        color: Colors.amber,
                        size: 22,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'أذكار الصباح',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: theme.textTheme.titleLarge?.color,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _morningTime != null
                                ? 'يومياً الساعة ${_morningTime!.format(context)}'
                                : 'غير مفعّل',
                            style: TextStyle(
                              fontSize: 13,
                              color: theme.textTheme.bodySmall?.color,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Transform.scale(
                      scale: 0.8,
                      child: Switch(
                        value: _morningEnabled,
                        activeColor: Colors.amber,
                        onChanged: (value) async {
                          setState(() {
                            _morningEnabled = value;
                          });

                          if (value) {
                            // Si está habilitado, programar la notificación
                            await _notificationManager
                                .scheduleMorningAzkarNotification(
                              _morningTime ??
                                  const TimeOfDay(hour: 6, minute: 0),
                            );
                          } else {
                            // Si está deshabilitado, cancelar la notificación
                            await _notificationManager
                                .cancelMorningAzkarNotification();
                          }

                          // Actualizar la configuración
                          await _notificationManager.updateNotificationSettings(
                            morningEnabled: value,
                            morningTime: _morningTime ??
                                const TimeOfDay(hour: 6, minute: 0),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          delay: 0.6,
        ),

        // بطاقة إشعارات أذكار المساء
        _buildSlideAnimation(
          Card(
            elevation: 2,
            margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: InkWell(
              onTap: () => _selectTime(context, false),
              borderRadius: BorderRadius.circular(12),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Container(
                      width: 44,
                      height: 44,
                      decoration: BoxDecoration(
                        color: Colors.indigo.withAlpha(25), // 0.1 * 255 = ~25
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.nightlight_round,
                        color: Colors.indigo,
                        size: 22,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'أذكار المساء',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: theme.textTheme.titleLarge?.color,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _eveningTime != null
                                ? 'يومياً الساعة ${_eveningTime!.format(context)}'
                                : 'غير مفعّل',
                            style: TextStyle(
                              fontSize: 13,
                              color: theme.textTheme.bodySmall?.color,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Transform.scale(
                      scale: 0.8,
                      child: Switch(
                        value: _eveningEnabled,
                        activeColor: Colors.indigo,
                        onChanged: (value) async {
                          setState(() {
                            _eveningEnabled = value;
                          });

                          if (value) {
                            // Si está habilitado, programar la notificación
                            await _notificationManager
                                .scheduleEveningAzkarNotification(
                              _eveningTime ??
                                  const TimeOfDay(hour: 17, minute: 0),
                            );
                          } else {
                            // Si está deshabilitado, cancelar la notificación
                            await _notificationManager
                                .cancelEveningAzkarNotification();
                          }

                          // Actualizar la configuración
                          await _notificationManager.updateNotificationSettings(
                            eveningEnabled: value,
                            eveningTime: _eveningTime ??
                                const TimeOfDay(hour: 17, minute: 0),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          delay: 0.7,
        ),

        // إعدادات تأجيل الإشعارات
        _buildSlideAnimation(
          Card(
            elevation: 2,
            margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: InkWell(
              onTap: _showSnoozeDurationPicker,
              borderRadius: BorderRadius.circular(12),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Container(
                      width: 44,
                      height: 44,
                      decoration: BoxDecoration(
                        color: Colors.purple.withAlpha(25), // 0.1 * 255 = ~25
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.snooze_outlined,
                        color: Colors.purple,
                        size: 22,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'مدة تأجيل الإشعارات',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: theme.textTheme.titleLarge?.color,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '$_snoozeMinutes دقيقة',
                            style: TextStyle(
                              fontSize: 13,
                              color: theme.textTheme.bodySmall?.color,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: isDark ? Colors.white60 : Colors.black45,
                    ),
                  ],
                ),
              ),
            ),
          ),
          delay: 0.8,
        ),

        // إعادة ضبط الإشعارات
        if (_morningEnabled || _eveningEnabled)
          _buildSlideAnimation(
            Padding(
              padding: const EdgeInsets.only(top: 16.0, bottom: 24.0),
              child: ElevatedButton.icon(
                onPressed: () async {
                  await _notificationManager.init();

                  if (_morningEnabled) {
                    await _notificationManager
                        .scheduleMorningAzkarNotification(_morningTime!);
                  }

                  if (_eveningEnabled) {
                    await _notificationManager
                        .scheduleEveningAzkarNotification(_eveningTime!);
                  }

                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('تم إعادة ضبط الإشعارات')),
                    );
                  }
                },
                icon: const Icon(Icons.refresh_rounded, size: 18),
                label: const Text('إعادة ضبط الإشعارات'),
                style: ElevatedButton.styleFrom(
                  foregroundColor: Colors.white,
                  backgroundColor: AppColors.azkarColor,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                ),
              ),
            ),
            delay: 0.9,
          ),

        // إضافة زر للتحقق من الإشعارات
        _buildSlideAnimation(
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: TextButton.icon(
              onPressed: () async {
                // Verificar si hay notificaciones programadas
                final morningEnabled =
                    await _notificationManager.isMorningAzkarEnabled();
                final eveningEnabled =
                    await _notificationManager.isEveningAzkarEnabled();
                final hasScheduled = morningEnabled || eveningEnabled;
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(hasScheduled
                          ? 'توجد إشعارات مجدولة بنجاح'
                          : 'لا توجد إشعارات مجدولة حالياً'),
                    ),
                  );
                }
              },
              icon: const Icon(Icons.check_circle_outline, size: 18),
              label: const Text('فحص الإشعارات المجدولة'),
            ),
          ),
          delay: 1.0,
        ),
      ],
    );
  }

  // تحسين حوار اختيار وقت التأجيل
  void _showSnoozeDurationPicker() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.4,
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.grey.withAlpha(77), // 0.3 * 255 = ~77
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const Text(
                'مدة تأجيل الإشعارات',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.azkarColor,
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView(
                  children: [5, 10, 15, 20, 30, 45, 60].map((minutes) {
                    final isSelected = _snoozeMinutes == minutes;
                    return ListTile(
                      title: Text('$minutes دقيقة'),
                      leading: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: isSelected
                              ? AppColors.azkarColor
                                  .withAlpha(25) // 0.1 * 255 = ~25
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Icon(
                          isSelected
                              ? Icons.check_circle
                              : Icons.circle_outlined,
                          color:
                              isSelected ? AppColors.azkarColor : Colors.grey,
                        ),
                      ),
                      onTap: () {
                        // Guardar el valor seleccionado y cerrar el diálogo
                        setState(() {
                          _snoozeMinutes = minutes;
                        });
                        Navigator.pop(context);

                        // Guardar el valor en el sistema de notificaciones
                        _notificationManager.updateSnoozeTime(minutes);
                      },
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _selectTime(BuildContext context, bool isMorning) async {
    final TimeOfDay? currentTime = isMorning ? _morningTime : _eveningTime;

    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: currentTime ??
          (isMorning
              ? const TimeOfDay(hour: 6, minute: 0)
              : const TimeOfDay(hour: 17, minute: 0)),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: Theme.of(context).primaryColor,
                ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).primaryColor,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedTime != null) {
      setState(() {
        if (isMorning) {
          _morningTime = pickedTime;
        } else {
          _eveningTime = pickedTime;
        }
      });

      // Actualizar la configuración de notificaciones
      await _notificationManager.updateNotificationSettings(
        morningEnabled: isMorning ? _morningEnabled : null,
        morningTime: isMorning ? pickedTime : null,
        eveningEnabled: !isMorning ? _eveningEnabled : null,
        eveningTime: !isMorning ? pickedTime : null,
      );

      // Programar o cancelar notificaciones según corresponda
      if (isMorning && _morningEnabled) {
        await _notificationManager.scheduleMorningAzkarNotification(pickedTime);
      } else if (!isMorning && _eveningEnabled) {
        await _notificationManager.scheduleEveningAzkarNotification(pickedTime);
      }
    }
  }
}
