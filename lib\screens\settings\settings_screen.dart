// lib/screens/settings/settings_screen.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/theme_provider.dart';

import '../../utils/app_colors.dart';
import 'sections/appearance_section.dart';
import 'sections/app_section.dart';
import 'sections/data_section.dart';
// تم تعليق استيراد قسم الإشعارات مؤقتاً
// import 'sections/notification_section.dart';
import 'widgets/settings_header.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  // Estas animaciones no se utilizan actualmente
  // late Animation<double> _fadeAnimation;
  // late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    // Estas animaciones se inicializaban pero no se utilizan
    // _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
    //   CurvedAnimation(
    //     parent: _animationController,
    //     curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
    //   ),
    // );

    // _slideAnimation = Tween<double>(begin: 0.2, end: 0.0).animate(
    //   CurvedAnimation(
    //     parent: _animationController,
    //     curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
    //   ),
    // );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    const settingsColor = AppColors.azkarColor;

    return Scaffold(
      body: CustomScrollView(
        physics: const BouncingScrollPhysics(),
        slivers: [
          // شريط العنوان
          SettingsHeader(settingsColor: settingsColor),

          // قائمة الإعدادات
          SliverList(
            delegate: SliverChildListDelegate(
              [
                // قسم المظهر
                AppearanceSection(
                  themeProvider: themeProvider,
                  settingsColor: settingsColor,
                  buildSlideAnimation: _buildSlideAnimation,
                  buildSectionTitle: _buildSectionTitle,
                ),

                const Divider(),

                // قسم التطبيق
                AppSection(
                  settingsColor: settingsColor,
                  buildSlideAnimation: _buildSlideAnimation,
                  buildSectionTitle: _buildSectionTitle,
                ),

                const Divider(),

                // تم تعليق قسم إعدادات الإشعارات مؤقتاً
                /*
                // إعدادات الإشعارات
                NotificationSection(
                  buildSlideAnimation: _buildSlideAnimation,
                  buildSectionTitle: _buildSectionTitle,
                ),
                */

                const SizedBox(height: 40),

                // قسم البيانات
                DataSection(
                  settingsColor: settingsColor,
                  buildSlideAnimation: _buildSlideAnimation,
                  buildSectionTitle: _buildSectionTitle,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء عنوان قسم - متوافق مع اتجاه اللغة العربية
  Widget _buildSectionTitle(BuildContext context, String title, IconData icon) {
    return _buildSlideAnimation(
      Padding(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
        child: Row(
          textDirection:
              TextDirection.rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
          children: [
            Icon(
              icon,
              size: 20,
              color: AppColors.azkarColor,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.azkarColor,
              ),
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
              textAlign: TextAlign.right, // محاذاة النص إلى اليمين
            ),
          ],
        ),
      ),
    );
  }

  // دالة تضمن أن قيم التأخير صحيحة دائمًا
  double _safeDelay(double value) {
    // تأكد من أن القيمة بين 0.0 و 1.0
    return value.clamp(0.0, 1.0);
  }

  // تعديل الدالة للتأكد من أن قيم الشفافية في الحدود المسموح بها
  Widget _buildSlideAnimation(Widget child, {double delay = 0}) {
    // تطبيق قيود على قيمة التأخير
    final safeDelay = _safeDelay(delay);

    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0, 0.2), // تقليل المسافة للتحريك
        end: Offset.zero,
      ).animate(
        CurvedAnimation(
          parent: _animationController,
          curve: Interval(
            safeDelay,
            1.0,
            curve: Curves.easeOutQuart,
          ),
        ),
      ),
      child: FadeTransition(
        opacity: Tween<double>(
          begin: 0.0,
          end: 1.0, // تأكد من أن النهاية لا تتجاوز 1.0
        ).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Interval(
              safeDelay,
              1.0,
              curve: Curves.easeOut,
            ),
          ),
        ),
        child: child,
      ),
    );
  }
}
