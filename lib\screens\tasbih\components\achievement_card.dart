// بطاقة الإنجازات

import 'package:flutter/material.dart';
import '../models/achievement_model.dart';
import '../utils/tasbih_colors.dart';
import '../../../utils/icon_mapping.dart';

class AchievementCard extends StatelessWidget {
  final Achievement achievement;
  final VoidCallback? onTap;

  const AchievementCard({
    Key? key,
    required this.achievement,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // تحديد الألوان بناءً على حالة الإنجاز
    final Color cardColor = isDarkMode
        ? achievement.isCompleted
            ? TasbihColors.primary.withAlpha(50)
            : TasbihColors.darkCardColor
        : achievement.isCompleted
            ? TasbihColors.primary.withAlpha(20)
            : Colors.white;

    final Color iconColor = achievement.isCompleted
        ? TasbihColors.primary
        : isDarkMode
            ? Colors.grey[400]!
            : Colors.grey[600]!;

    final Color textColor = achievement.isCompleted
        ? TasbihColors.primary
        : isDarkMode
            ? Colors.white
            : Colors.black87;

    return Card(
      elevation: achievement.isCompleted ? 2 : 1,
      shadowColor: achievement.isCompleted
          ? TasbihColors.primary.withAlpha(50)
          : Colors.black12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: achievement.isCompleted
              ? TasbihColors.primary.withAlpha(100)
              : Colors.grey.withAlpha(30),
          width: achievement.isCompleted ? 1 : 0.5,
        ),
      ),
      color: cardColor,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // أيقونة الإنجاز
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: achievement.isCompleted
                          ? TasbihColors.primary.withAlpha(30)
                          : Colors.grey.withAlpha(20),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      AppIcons.getIconFromCode(achievement.icon),
                      color: iconColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),

                  // عنوان الإنجاز
                  Expanded(
                    child: Text(
                      achievement.title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: textColor,
                      ),
                    ),
                  ),

                  // شارة الإكمال
                  if (achievement.isCompleted)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: TasbihColors.primary,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'مكتمل',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),

              const SizedBox(height: 12),

              // وصف الإنجاز
              Text(
                achievement.description,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: textColor.withAlpha(200),
                ),
              ),

              const SizedBox(height: 12),

              // شريط التقدم
              LinearProgressIndicator(
                value: achievement.progress,
                backgroundColor: Colors.grey.withAlpha(30),
                valueColor: AlwaysStoppedAnimation<Color>(
                  achievement.isCompleted
                      ? TasbihColors.primary
                      : TasbihColors.secondary,
                ),
                borderRadius: BorderRadius.circular(4),
              ),

              const SizedBox(height: 8),

              // نسبة الإكمال
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    '${(achievement.progress * 100).toInt()}%',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: textColor.withAlpha(180),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),

              // تاريخ الإكمال
              if (achievement.isCompleted && achievement.completedDate != null)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Icon(
                        Icons.calendar_today,
                        size: 12,
                        color: textColor.withAlpha(150),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _formatDate(achievement.completedDate!),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: textColor.withAlpha(150),
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  // تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day}';
  }
}
