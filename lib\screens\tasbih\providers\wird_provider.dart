// مزود بيانات الورد اليومي

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/wird_model.dart';
import '../models/dhikr_model.dart';
// import '../services/awesome_notification_service.dart'; // تم تعطيل النظام القديم
import '../../../services/notification_manager.dart'; // النظام الجديد
import 'tasbih_provider.dart';

class WirdProvider with ChangeNotifier {
  final TasbihProvider _tasbihProvider;

  // النظام الجديد للإشعارات
  final NotificationManager _notificationManager = NotificationManager();

  // قائمة الأوراد
  List<WirdModel> _wirds = [];

  // الورد النشط حالياً
  WirdModel? _activeWird;

  // مؤشر العنصر الحالي في الورد النشط
  int _currentItemIndex = 0;

  // حالة تشغيل الورد
  bool _isPlaying = false;

  // مفاتيح التخزين
  static const String _wirdsKey = 'wirds';
  static const String _wirdItemsKey = 'wird_items';
  static const String _activeWirdKey = 'active_wird';
  static const String _wirdRemindersDisabledKey =
      'wird_reminders_temp_disabled';

  // الحصول على قائمة الأوراد
  List<WirdModel> get wirds => _wirds;

  // الحصول على الورد النشط
  WirdModel? get activeWird => _activeWird;

  // الحصول على حالة تشغيل الورد
  bool get isPlaying => _isPlaying;

  // الحصول على العنصر الحالي في الورد النشط
  WirdItemModel? get currentItem {
    if (_activeWird == null || _activeWird!.items.isEmpty) {
      return null;
    }

    if (_currentItemIndex >= _activeWird!.items.length) {
      _currentItemIndex = 0;
    }

    return _activeWird!.items[_currentItemIndex];
  }

  // الحصول على نسبة إكمال الورد النشط
  double get activeWirdCompletionPercentage {
    if (_activeWird == null) return 0;
    return _activeWird!.completionPercentage;
  }

  // البناء
  WirdProvider(this._tasbihProvider) {
    _loadWirds();
    // تعطيل تذكيرات الأوراد تلقائياً عند بدء التطبيق
    _disableWirdRemindersAutomatically();
  }

  // تعطيل تذكيرات الأوراد تلقائياً
  Future<void> _disableWirdRemindersAutomatically() async {
    try {
      // تعطيل تذكيرات الأوراد
      await toggleWirdRemindersTemporarilyDisabled(true);
      debugPrint('تم تعطيل تذكيرات الأوراد تلقائياً عند بدء التطبيق');
    } catch (e) {
      debugPrint('خطأ في تعطيل تذكيرات الأوراد تلقائياً: $e');
    }
  }

  // تحميل الأوراد من التخزين المحلي
  Future<void> _loadWirds() async {
    debugPrint('جاري تحميل الأوراد من التخزين المحلي...');
    try {
      final prefs = await SharedPreferences.getInstance();

      // تحميل الأوراد
      final wirdsJson = prefs.getString(_wirdsKey);
      if (wirdsJson != null) {
        final List<dynamic> wirdsData = jsonDecode(wirdsJson);

        // تحميل عناصر الأوراد
        final wirdItemsJson = prefs.getString(_wirdItemsKey);
        final Map<int, List<WirdItemModel>> wirdItems = {};

        if (wirdItemsJson != null) {
          final Map<String, dynamic> wirdItemsData = jsonDecode(wirdItemsJson);

          // تحويل البيانات إلى نماذج
          for (var entry in wirdItemsData.entries) {
            final wirdId = int.parse(entry.key);
            final List<dynamic> items = entry.value;

            wirdItems[wirdId] = items.map((item) {
              final dhikrId = item['dhikrId'] as int;
              final dhikr = _tasbihProvider.getDhikrById(dhikrId);

              // استخدام الدالة المحسنة التي تتعامل مع الذكر الغير موجود
              // ستستخدم المعلومات المحفوظة في الخريطة إذا كان الذكر غير موجود
              if (dhikr == null) {
                debugPrint(
                    'لم يتم العثور على الذكر بالمعرف: $dhikrId، سيتم استخدام المعلومات المحفوظة');
              }

              return WirdItemModel.fromMap(item, dhikr);
            }).toList();
          }
        }

        // إنشاء نماذج الأوراد
        _wirds = wirdsData.map<WirdModel>((wird) {
          final wirdId = wird['id'] as int;
          return WirdModel.fromMap(
            wird,
            wirdItems[wirdId] ?? [],
          );
        }).toList();

        // تحميل الورد النشط
        final activeWirdId = prefs.getInt(_activeWirdKey);
        if (activeWirdId != null) {
          try {
            _activeWird = _wirds.firstWhere(
              (wird) => wird.id == activeWirdId,
            );
          } catch (e) {
            // إذا لم يتم العثور على الورد النشط، استخدم الورد الأول إذا كان متاحاً
            if (_wirds.isNotEmpty) {
              _activeWird = _wirds.first;
            }
          }
        } else if (_wirds.isNotEmpty) {
          _activeWird = _wirds.first;
        }

        // التحقق مما إذا كانت تذكيرات الأوراد معطلة مؤقتاً
        final isDisabled = await areWirdRemindersTemporarilyDisabled();
        if (isDisabled) {
          debugPrint(
              'تذكيرات الأوراد معطلة مؤقتاً، لن يتم جدولة إشعارات الأوراد');
        } else {
          // جدولة إشعارات الأوراد التي لها أوقات تذكير
          // التحقق من أذونات الإشعارات أولاً
          final hasPermission =
              await _notificationManager.requestNotificationPermissions();
          if (hasPermission) {
            int successCount = 0;
            int failCount = 0;

            for (final wird in _wirds) {
              if (wird.reminderTime != null) {
                final success = await _scheduleWirdReminder(wird);
                if (success) {
                  successCount++;
                } else {
                  failCount++;
                }
              }
            }

            debugPrint(
                'تم جدولة إشعارات $successCount ورد بنجاح، وفشل جدولة $failCount ورد');
          } else {
            debugPrint(
                'لا توجد أذونات للإشعارات، لن يتم جدولة إشعارات الأوراد');
          }
        }

        notifyListeners();
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الأوراد: $e');
    }
  }

  // حفظ الأوراد في التخزين المحلي
  Future<void> _saveWirds() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // حفظ الأوراد
      final wirdsData = _wirds.map((wird) => wird.toMap()).toList();
      await prefs.setString(_wirdsKey, jsonEncode(wirdsData));

      // حفظ عناصر الأوراد
      final Map<String, List<Map<String, dynamic>>> wirdItemsData = {};
      for (var wird in _wirds) {
        wirdItemsData[wird.id.toString()] =
            wird.items.map((item) => item.toMap()).toList();
      }
      await prefs.setString(_wirdItemsKey, jsonEncode(wirdItemsData));

      // حفظ الورد النشط
      if (_activeWird != null) {
        await prefs.setInt(_activeWirdKey, _activeWird!.id);
      } else {
        await prefs.remove(_activeWirdKey);
      }
    } catch (e) {
      debugPrint('خطأ في حفظ الأوراد: $e');
    }
  }

  // إضافة ورد جديد
  Future<WirdModel> addWird(String name, TimeOfDay? reminderTime) async {
    try {
      // إنشاء معرف جديد
      final newId = _wirds.isEmpty
          ? 1
          : _wirds.map((w) => w.id).reduce((a, b) => a > b ? a : b) + 1;

      // إنشاء ورد جديد
      final newWird = WirdModel(
        id: newId,
        name: name,
        items: [],
        reminderTime: reminderTime,
      );

      // إضافة الورد إلى القائمة
      _wirds.add(newWird);

      // إذا كان هذا هو الورد الأول، اجعله نشطاً
      if (_wirds.length == 1) {
        _activeWird = newWird;
      }

      // جدولة إشعار التذكير إذا كان محدداً
      bool notificationScheduled = false;
      if (reminderTime != null) {
        notificationScheduled = await _scheduleWirdReminder(newWird);
        if (!notificationScheduled) {
          debugPrint('تم إنشاء الورد ولكن فشلت جدولة الإشعار: ${newWird.name}');
        }
      }

      // حفظ التغييرات
      await _saveWirds();
      notifyListeners();

      return newWird;
    } catch (e) {
      debugPrint('خطأ في إضافة ورد جديد: $e');
      // إنشاء ورد بدون إشعار في حالة حدوث خطأ
      final fallbackWird = WirdModel(
        id: DateTime.now().millisecondsSinceEpoch % 10000,
        name: name,
        items: [],
        reminderTime: null, // لا نحاول جدولة إشعار في حالة الخطأ
      );

      // إضافة الورد إلى القائمة
      _wirds.add(fallbackWird);

      // حفظ التغييرات
      await _saveWirds();
      notifyListeners();

      return fallbackWird;
    }
  }

  // تحديث ورد
  Future<bool> updateWird(WirdModel wird) async {
    try {
      final index = _wirds.indexWhere((w) => w.id == wird.id);
      if (index != -1) {
        // الحصول على الورد القديم قبل التحديث
        final oldWird = _wirds[index];

        // تحديث الورد
        _wirds[index] = wird;

        // إذا كان هذا هو الورد النشط، قم بتحديثه
        if (_activeWird?.id == wird.id) {
          _activeWird = wird;
        }

        // إذا تغير وقت التذكير، قم بتحديث الإشعار
        bool notificationSuccess = true;
        if (oldWird.reminderTime != wird.reminderTime) {
          if (wird.reminderTime != null) {
            // إلغاء الإشعار القديم أولاً
            await _cancelWirdReminder(wird.id);

            // جدولة إشعار جديد
            notificationSuccess = await _scheduleWirdReminder(wird);
            if (!notificationSuccess) {
              debugPrint(
                  'تم تحديث الورد ولكن فشلت جدولة الإشعار: ${wird.name}');
            }
          } else {
            // إلغاء الإشعار القديم
            await _cancelWirdReminder(wird.id);
          }
        }

        await _saveWirds();
        notifyListeners();

        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في تحديث الورد: $e');
      return false;
    }
  }

  // جدولة إشعار التذكير بالورد - تم تحسين آلية الجدولة
  Future<bool> _scheduleWirdReminder(WirdModel wird) async {
    if (wird.reminderTime != null) {
      try {
        // التحقق مما إذا كانت تذكيرات الأوراد معطلة مؤقتاً
        final isDisabled = await areWirdRemindersTemporarilyDisabled();
        if (isDisabled) {
          debugPrint(
              'تذكيرات الأوراد معطلة مؤقتاً، لن يتم جدولة إشعار للورد: ${wird.name}');
          return false;
        }

        // طباعة معلومات التشخيص
        debugPrint('محاولة جدولة إشعار للورد: ${wird.name}');
        debugPrint(
            'وقت التذكير: ${wird.reminderTime!.hour}:${wird.reminderTime!.minute}');

        // التحقق من أذونات الإشعارات أولاً
        final hasPermission =
            await _notificationManager.requestNotificationPermissions();
        if (!hasPermission) {
          debugPrint(
              'لا توجد أذونات للإشعارات، لن يتم جدولة إشعار تذكير بالورد');
          return false;
        }

        // إلغاء أي إشعارات سابقة لهذا الورد
        await _cancelWirdReminder(wird.id);

        // تأخير قصير قبل جدولة الإشعار الجديد
        await Future.delayed(const Duration(milliseconds: 300));

        // استخدام النظام الجديد للإشعارات مع محاولات متعددة
        bool success = false;
        int attempts = 0;
        const maxAttempts = 3;

        while (!success && attempts < maxAttempts) {
          attempts++;
          debugPrint('محاولة جدولة إشعار الورد رقم $attempts من $maxAttempts');

          success = await _notificationManager.scheduleWirdReminderNotification(
            wird.id,
            wird.name,
            wird.reminderTime!,
          );

          if (success) {
            debugPrint(
                'نجحت جدولة إشعار تذكير بالورد: ${wird.name} في المحاولة رقم $attempts');
            debugPrint(
                'الساعة: ${wird.reminderTime!.hour}:${wird.reminderTime!.minute}');

            // تحقق من الجدولة بعد فترة قصيرة
            _verifyWirdReminderScheduling(wird);

            return true;
          } else {
            debugPrint(
                'فشلت المحاولة رقم $attempts لجدولة إشعار الورد: ${wird.name}');

            // تأخير قبل المحاولة التالية
            if (attempts < maxAttempts) {
              await Future.delayed(const Duration(seconds: 1) * attempts);
            }
          }
        }

        debugPrint('فشلت جميع محاولات جدولة إشعار تذكير بالورد: ${wird.name}');
        return false;
      } catch (e) {
        debugPrint('خطأ في جدولة إشعار تذكير بالورد: $e');
        return false;
      }
    }
    return false;
  }

  // التحقق من جدولة إشعار الورد بعد فترة
  void _verifyWirdReminderScheduling(WirdModel wird) {
    Future.delayed(const Duration(seconds: 10), () async {
      try {
        debugPrint('التحقق من جدولة إشعار الورد: ${wird.name}');
        // إعادة جدولة الإشعار إذا لزم الأمر
        final success =
            await _notificationManager.verifyWirdReminderNotification(
          wird.id,
          wird.name,
          wird.reminderTime!,
        );
        debugPrint('نتيجة التحقق من إشعار الورد: ${success ? "ناجح" : "فاشل"}');
      } catch (e) {
        debugPrint('خطأ في التحقق من جدولة إشعار الورد: $e');
      }
    });
  }

  // إلغاء إشعار التذكير بالورد
  Future<void> _cancelWirdReminder(int wirdId) async {
    // إلغاء الإشعار في النظام الجديد
    await _notificationManager.cancelWirdReminderNotification(wirdId);
  }

  // حذف ورد
  Future<void> deleteWird(int wirdId) async {
    // التحقق من وجود الورد قبل الحذف
    final wirdToDelete = _wirds.firstWhere(
      (w) => w.id == wirdId,
      orElse: () => WirdModel(id: -1, name: '', items: []),
    );

    if (wirdToDelete.id != -1) {
      // إلغاء إشعار التذكير إذا كان موجوداً
      if (wirdToDelete.reminderTime != null) {
        await _cancelWirdReminder(wirdId);
      }
    }

    // حذف الورد من القائمة
    _wirds.removeWhere((w) => w.id == wirdId);

    // إذا كان هذا هو الورد النشط، قم بإلغاء تنشيطه
    if (_activeWird?.id == wirdId) {
      _activeWird = _wirds.isNotEmpty ? _wirds.first : null;
      _currentItemIndex = 0;
      _isPlaying = false;
    }

    await _saveWirds();
    notifyListeners();
  }

  // إضافة عنصر إلى ورد
  Future<void> addWirdItem(
      int wirdId, DhikrModel dhikr, int targetCount) async {
    // طباعة معلومات عن الذكر المضاف
    debugPrint(
        'إضافة ذكر إلى الورد: ${dhikr.name}, المعرف: ${dhikr.id}, النص العربي: ${dhikr.arabicText}');
    final wirdIndex = _wirds.indexWhere((w) => w.id == wirdId);
    if (wirdIndex != -1) {
      // إنشاء معرف جديد للعنصر
      final items = _wirds[wirdIndex].items;
      final newItemId = items.isEmpty
          ? 1
          : items.map((i) => i.id).reduce((a, b) => a > b ? a : b) + 1;

      // إنشاء عنصر جديد
      final newItem = WirdItemModel(
        id: newItemId,
        dhikr: dhikr,
        targetCount: targetCount,
        order: items.length + 1,
      );

      // إضافة العنصر إلى الورد
      final updatedItems = List<WirdItemModel>.from(items)..add(newItem);
      _wirds[wirdIndex] = _wirds[wirdIndex].copyWith(items: updatedItems);

      // إذا كان هذا هو الورد النشط، قم بتحديثه
      if (_activeWird?.id == wirdId) {
        _activeWird = _wirds[wirdIndex];
      }

      await _saveWirds();
      notifyListeners();
    }
  }

  // تحديث عنصر في ورد
  Future<void> updateWirdItem(int wirdId, WirdItemModel item) async {
    final wirdIndex = _wirds.indexWhere((w) => w.id == wirdId);
    if (wirdIndex != -1) {
      final items = _wirds[wirdIndex].items;
      final itemIndex = items.indexWhere((i) => i.id == item.id);

      if (itemIndex != -1) {
        final updatedItems = List<WirdItemModel>.from(items);
        updatedItems[itemIndex] = item;

        _wirds[wirdIndex] = _wirds[wirdIndex].copyWith(items: updatedItems);

        // إذا كان هذا هو الورد النشط، قم بتحديثه
        if (_activeWird?.id == wirdId) {
          _activeWird = _wirds[wirdIndex];
        }

        await _saveWirds();
        notifyListeners();
      }
    }
  }

  // حذف عنصر من ورد
  Future<void> deleteWirdItem(int wirdId, int itemId) async {
    final wirdIndex = _wirds.indexWhere((w) => w.id == wirdId);
    if (wirdIndex != -1) {
      final items = _wirds[wirdIndex].items;
      final updatedItems = items.where((i) => i.id != itemId).toList();

      // إعادة ترتيب العناصر
      for (var i = 0; i < updatedItems.length; i++) {
        updatedItems[i] = updatedItems[i].copyWith(order: i + 1);
      }

      _wirds[wirdIndex] = _wirds[wirdIndex].copyWith(items: updatedItems);

      // إذا كان هذا هو الورد النشط، قم بتحديثه
      if (_activeWird?.id == wirdId) {
        _activeWird = _wirds[wirdIndex];
        if (_currentItemIndex >= updatedItems.length) {
          _currentItemIndex =
              updatedItems.isEmpty ? 0 : updatedItems.length - 1;
        }
      }

      await _saveWirds();
      notifyListeners();
    }
  }

  // تغيير ترتيب العناصر في ورد
  Future<void> reorderWirdItems(int wirdId, int oldIndex, int newIndex) async {
    final wirdIndex = _wirds.indexWhere((w) => w.id == wirdId);
    if (wirdIndex != -1) {
      final items = List<WirdItemModel>.from(_wirds[wirdIndex].items);

      // تحريك العنصر
      final item = items.removeAt(oldIndex);
      items.insert(newIndex, item);

      // إعادة ترتيب العناصر
      for (var i = 0; i < items.length; i++) {
        items[i] = items[i].copyWith(order: i + 1);
      }

      _wirds[wirdIndex] = _wirds[wirdIndex].copyWith(items: items);

      // إذا كان هذا هو الورد النشط، قم بتحديثه
      if (_activeWird?.id == wirdId) {
        _activeWird = _wirds[wirdIndex];
        if (_currentItemIndex == oldIndex) {
          _currentItemIndex = newIndex;
        } else if (_currentItemIndex > oldIndex &&
            _currentItemIndex <= newIndex) {
          _currentItemIndex--;
        } else if (_currentItemIndex < oldIndex &&
            _currentItemIndex >= newIndex) {
          _currentItemIndex++;
        }
      }

      await _saveWirds();
      notifyListeners();
    }
  }

  // تنشيط ورد
  Future<void> activateWird(int wirdId) async {
    try {
      final wird = _wirds.firstWhere((w) => w.id == wirdId);
      _activeWird = wird;
      _currentItemIndex = 0;
      _isPlaying = false;

      await _saveWirds();
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تنشيط الورد: $e');
    }
  }

  // بدء تشغيل الورد
  void startWird() {
    if (_activeWird != null && _activeWird!.items.isNotEmpty) {
      _isPlaying = true;

      // البدء من أول عنصر غير مكتمل
      for (var i = 0; i < _activeWird!.items.length; i++) {
        if (!_activeWird!.items[i].isCompleted) {
          _currentItemIndex = i;
          break;
        }
      }

      notifyListeners();
    }
  }

  // إيقاف تشغيل الورد
  void stopWird() {
    _isPlaying = false;
    notifyListeners();
  }

  // زيادة عداد العنصر الحالي
  Future<void> incrementCurrentItem() async {
    if (_activeWird != null && _currentItemIndex < _activeWird!.items.length) {
      final item = _activeWird!.items[_currentItemIndex];

      if (item.currentCount < item.targetCount) {
        // زيادة العداد
        var newItem = item.copyWith(currentCount: item.currentCount + 1);

        // التحقق مما إذا تم إكمال العنصر
        if (newItem.currentCount >= newItem.targetCount) {
          newItem = newItem.copyWith(isCompleted: true);

          // الانتقال إلى العنصر التالي إذا كان هناك عناصر غير مكتملة
          if (_currentItemIndex < _activeWird!.items.length - 1) {
            _currentItemIndex++;
          } else {
            // إذا كان هذا هو العنصر الأخير، قم بإيقاف تشغيل الورد
            _isPlaying = false;
          }
        }

        // تحديث العنصر
        await updateWirdItem(_activeWird!.id, newItem);
      }
    }
  }

  // إعادة تعيين الورد
  Future<void> resetWird(int wirdId) async {
    final wirdIndex = _wirds.indexWhere((w) => w.id == wirdId);
    if (wirdIndex != -1) {
      final items = _wirds[wirdIndex].items;
      final updatedItems = items
          .map((item) => item.copyWith(
                currentCount: 0,
                isCompleted: false,
              ))
          .toList();

      _wirds[wirdIndex] = _wirds[wirdIndex].copyWith(
        items: updatedItems,
        lastUsedDate: DateTime.now(),
      );

      // إذا كان هذا هو الورد النشط، قم بتحديثه
      if (_activeWird?.id == wirdId) {
        _activeWird = _wirds[wirdIndex];
        _currentItemIndex = 0;
        _isPlaying = false;
      }

      await _saveWirds();
      notifyListeners();
    }
  }

  /// اختبار إشعار الورد
  Future<bool> testWirdNotification(int wirdId) async {
    try {
      // البحث عن الورد
      final wird = _wirds.firstWhere(
        (w) => w.id == wirdId,
        orElse: () => WirdModel(id: -1, name: '', items: []),
      );

      if (wird.id != -1) {
        // التحقق من أذونات الإشعارات أولاً
        final hasPermission =
            await _notificationManager.requestNotificationPermissions();
        if (!hasPermission) {
          debugPrint(
              'لا توجد أذونات للإشعارات، لن يتم إرسال إشعار تجريبي للورد');
          return false;
        }

        // إرسال إشعار تجريبي فوري باستخدام النظام الجديد
        final success =
            await _notificationManager.sendTestWirdReminderNotification(
          wird.id,
          wird.name,
        );

        if (success) {
          debugPrint('تم إرسال إشعار تجريبي فوري للورد: ${wird.name}');
          return true;
        } else {
          debugPrint('فشل إرسال إشعار تجريبي فوري للورد: ${wird.name}');

          // محاولة إرسال الإشعار مرة أخرى
          await Future.delayed(const Duration(milliseconds: 500));
          final secondAttempt =
              await _notificationManager.sendTestWirdReminderNotification(
            wird.id,
            wird.name,
          );

          if (secondAttempt) {
            debugPrint(
                'نجحت المحاولة الثانية لإرسال إشعار تجريبي للورد: ${wird.name}');
            return true;
          } else {
            // محاولة ثالثة بعد فترة أطول
            await Future.delayed(const Duration(seconds: 1));
            final thirdAttempt =
                await _notificationManager.sendTestWirdReminderNotification(
              wird.id,
              wird.name,
            );

            if (thirdAttempt) {
              debugPrint(
                  'نجحت المحاولة الثالثة لإرسال إشعار تجريبي للورد: ${wird.name}');
              return true;
            } else {
              debugPrint(
                  'فشلت جميع محاولات إرسال إشعار تجريبي للورد: ${wird.name}');
              return false;
            }
          }
        }
      } else {
        debugPrint('لم يتم العثور على الورد بالمعرف: $wirdId');
        return false;
      }
    } catch (e) {
      debugPrint('خطأ في إرسال إشعار تجريبي للورد: $e');
      return false;
    }
  }

  // إكمال الورد وتخزين تاريخ الإكمال
  Future<void> completeWird(int wirdId) async {
    final wirdIndex = _wirds.indexWhere((w) => w.id == wirdId);
    if (wirdIndex != -1) {
      final wird = _wirds[wirdIndex];
      final now = DateTime.now();

      // تحديث الورد بتاريخ الإكمال الحالي وزيادة عداد الإكمال
      _wirds[wirdIndex] = wird.copyWith(
        lastUsedDate: now,
        completedCount: wird.completedCount + 1,
      );

      // إذا كان هذا هو الورد النشط، قم بتحديثه
      if (_activeWird?.id == wirdId) {
        _activeWird = _wirds[wirdIndex];
        _isPlaying = false;
      }

      debugPrint('تم إكمال الورد: ${wird.name} (المعرف: ${wird.id})');
      debugPrint('تاريخ الإكمال: $now');
      debugPrint('عدد مرات الإكمال: ${wird.completedCount + 1}');

      await _saveWirds();
      notifyListeners();
    }
  }

  // تحديث العدد المستهدف لعنصر في ورد
  Future<void> updateWirdItemTargetCount(
      int wirdId, int itemId, int targetCount) async {
    final wirdIndex = _wirds.indexWhere((w) => w.id == wirdId);
    if (wirdIndex != -1) {
      final items = _wirds[wirdIndex].items;
      final itemIndex = items.indexWhere((i) => i.id == itemId);

      if (itemIndex != -1) {
        // الحصول على العنصر الحالي
        final currentItem = items[itemIndex];

        // إنشاء نسخة محدثة من العنصر مع العدد المستهدف الجديد
        final updatedItem = currentItem.copyWith(targetCount: targetCount);

        // تحديث العنصر في القائمة
        final updatedItems = List<WirdItemModel>.from(items);
        updatedItems[itemIndex] = updatedItem;

        // تحديث الورد
        _wirds[wirdIndex] = _wirds[wirdIndex].copyWith(items: updatedItems);

        // إذا كان هذا هو الورد النشط، قم بتحديثه
        if (_activeWird?.id == wirdId) {
          _activeWird = _wirds[wirdIndex];
        }

        await _saveWirds();
        notifyListeners();
      }
    }
  }

  // إعادة تحميل الأوراد من التخزين المحلي
  Future<void> reloadWirds() async {
    debugPrint('إعادة تحميل الأوراد من التخزين المحلي...');
    await _loadWirds();
    debugPrint('تم إعادة تحميل ${_wirds.length} ورد');

    // طباعة معلومات عن الأوراد المحملة
    for (var i = 0; i < _wirds.length; i++) {
      final wird = _wirds[i];
      debugPrint(
          'الورد $i: ${wird.name}, المعرف: ${wird.id}, عدد العناصر: ${wird.items.length}');

      // طباعة معلومات عن عناصر الورد
      for (var j = 0; j < wird.items.length; j++) {
        final item = wird.items[j];
        debugPrint('العنصر $j: ${item.dhikr.name}, المعرف: ${item.dhikr.id}');
      }
    }
  }

  /// إعادة تعيين الأوراد المكتملة بناءً على الوقت
  /// يتم إعادة تعيين الأوراد المكتملة فقط إذا مر يوم كامل على إكمالها
  Future<List<String>> resetAllCompletedWirds() async {
    debugPrint(
        'جاري التحقق من الأوراد المكتملة لإعادة تعيينها بناءً على الوقت...');

    bool hasChanges = false;
    List<String> resetWirdNames =
        []; // قائمة بأسماء الأوراد التي تم إعادة تعيينها
    final now = DateTime.now();

    // التحقق من كل ورد
    for (int i = 0; i < _wirds.length; i++) {
      final wird = _wirds[i];

      // التحقق مما إذا كان الورد مكتملاً (جميع عناصره مكتملة)
      bool isWirdCompleted = wird.isCompleted;

      if (isWirdCompleted && wird.lastUsedDate != null) {
        // حساب الفرق بين التاريخ الحالي وتاريخ آخر استخدام
        final difference = now.difference(wird.lastUsedDate!);

        // إعادة تعيين الورد فقط إذا مر يوم كامل (24 ساعة) على إكماله
        // أو إذا كان التاريخ الحالي يوم مختلف عن تاريخ آخر استخدام
        bool shouldReset = difference.inHours >= 24 ||
            (now.day != wird.lastUsedDate!.day ||
                now.month != wird.lastUsedDate!.month ||
                now.year != wird.lastUsedDate!.year);

        if (shouldReset) {
          debugPrint(
              'إعادة تعيين الورد المكتمل: ${wird.name} (المعرف: ${wird.id})');
          debugPrint(
              'آخر استخدام: ${wird.lastUsedDate}, الفرق: ${difference.inHours} ساعة');

          // إعادة تعيين عناصر الورد
          final updatedItems = wird.items
              .map((item) => item.copyWith(
                    currentCount: 0,
                    isCompleted: false,
                  ))
              .toList();

          // تحديث الورد
          _wirds[i] = wird.copyWith(
            items: updatedItems,
            lastUsedDate: now,
          );

          // إضافة اسم الورد إلى قائمة الأوراد التي تم إعادة تعيينها
          resetWirdNames.add(wird.name);

          hasChanges = true;
        } else {
          debugPrint(
              'الورد ${wird.name} مكتمل ولكن لم يمر يوم كامل على إكماله (${difference.inHours} ساعة)');
        }
      }
    }

    // إذا كان هناك ورد نشط، قم بتحديثه
    if (_activeWird != null) {
      final activeWirdIndex = _wirds.indexWhere((w) => w.id == _activeWird!.id);
      if (activeWirdIndex != -1) {
        _activeWird = _wirds[activeWirdIndex];
        _currentItemIndex = 0;
        _isPlaying = false;
      }
    }

    // حفظ التغييرات إذا كان هناك أي تغييرات
    if (hasChanges) {
      await _saveWirds();
      debugPrint(
          'تم إعادة تعيين ${resetWirdNames.length} ورد مكتمل وحفظ التغييرات');
      debugPrint('الأوراد التي تم إعادة تعيينها: ${resetWirdNames.join(', ')}');

      // إعلام المستمعين بالتغييرات
      notifyListeners();
    } else {
      debugPrint('لا توجد أوراد مكتملة تحتاج إلى إعادة تعيين');
    }

    // إعادة قائمة أسماء الأوراد التي تم إعادة تعيينها
    return resetWirdNames;
  }

  /// التحقق مما إذا كانت تذكيرات الأوراد معطلة مؤقتاً
  Future<bool> areWirdRemindersTemporarilyDisabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_wirdRemindersDisabledKey) ?? false;
  }

  /// تفعيل أو تعطيل تذكيرات الأوراد مؤقتاً
  Future<void> toggleWirdRemindersTemporarilyDisabled(bool disabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_wirdRemindersDisabledKey, disabled);

    if (disabled) {
      // إلغاء جميع إشعارات الأوراد الحالية
      for (final wird in _wirds) {
        if (wird.reminderTime != null) {
          await _cancelWirdReminder(wird.id);
        }
      }
      debugPrint('تم تعطيل تذكيرات الأوراد مؤقتاً وإلغاء الإشعارات الحالية');
    } else {
      // إعادة جدولة إشعارات الأوراد
      int successCount = 0;
      int failCount = 0;

      for (final wird in _wirds) {
        if (wird.reminderTime != null) {
          final success = await _scheduleWirdReminder(wird);
          if (success) {
            successCount++;
          } else {
            failCount++;
          }
        }
      }

      debugPrint(
          'تم إعادة تفعيل تذكيرات الأوراد وجدولة $successCount ورد بنجاح، وفشل جدولة $failCount ورد');
    }

    notifyListeners();
  }
}
