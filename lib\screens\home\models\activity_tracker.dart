// نموذج لتتبع نشاط المستخدم في التطبيق
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../utils/app_colors.dart';
import '../../../utils/constants.dart';

/// نموذج لتخزين معلومات النشاط
class ActivityData {
  final String id; // معرف النشاط
  final String title; // عنوان النشاط
  final String section; // القسم (الأذكار، المسبحة، الورد)
  final String sectionType; // نوع القسم الفرعي (ورد الصباح، ورد المساء، إلخ)
  final IconData icon; // أيقونة النشاط
  final Color color; // لون النشاط
  final DateTime timestamp; // وقت النشاط
  final String route; // المسار للانتقال إليه
  final double? progress; // نسبة التقدم (0.0 - 1.0) إذا كان متاحًا
  final String? count; // العدد الحالي / الهدف إذا كان متاحًا
  final bool completed; // هل تم إكمال النشاط

  ActivityData({
    required this.id,
    required this.title,
    required this.section,
    required this.sectionType,
    required this.icon,
    required this.color,
    required this.timestamp,
    required this.route,
    this.progress,
    this.count,
    this.completed = false,
  });

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'section': section,
      'sectionType': sectionType,
      'icon': ActivityTracker._getIconName(icon),
      'color': color.value,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'route': route,
      'progress': progress,
      'count': count,
      'completed': completed,
    };
  }

  /// إنشاء نموذج من Map
  factory ActivityData.fromMap(Map<String, dynamic> map) {
    return ActivityData(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      section: map['section'] ?? '',
      sectionType: map['sectionType'] ?? '',
      icon: ActivityTracker._getIconFromCodePoint(map['icon']),
      color: map['color'] != null ? Color(map['color']) : AppColors.azkarColor,
      timestamp: DateTime.fromMillisecondsSinceEpoch(
          map['timestamp'] ?? DateTime.now().millisecondsSinceEpoch),
      route: map['route'] ?? '/',
      progress: map['progress'],
      count: map['count'],
      completed: map['completed'] ?? false,
    );
  }
}

/// مدير تتبع النشاط
class ActivityTracker {
  // Map لربط أسماء الأيقونات بكائناتها لتسهيل التحويل
  static final Map<String, IconData> _iconMap = {
    'wb_sunny_rounded': Icons.wb_sunny_rounded,
    'nights_stay_rounded': Icons.nights_stay_rounded,
    'bedtime_rounded': Icons.bedtime_rounded,
    'favorite_rounded': Icons.favorite_rounded,
    'panorama_fish_eye': Icons.panorama_fish_eye,
    'auto_awesome_rounded': Icons.auto_awesome_rounded,
    'circle': Icons.circle,
  };
  static const String _activityKey = 'user_activities';
  static const int _maxActivities = 10; // الحد الأقصى للأنشطة المخزنة

  /// الحصول على قائمة الأنشطة الأخيرة
  static Future<List<ActivityData>> getRecentActivities({int limit = 3}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final activitiesJson = prefs.getString(_activityKey);

      if (activitiesJson != null) {
        final List<dynamic> activitiesList = jsonDecode(activitiesJson);
        final activities =
            activitiesList.map((e) => ActivityData.fromMap(e)).toList();

        // ترتيب الأنشطة حسب الوقت (الأحدث أولاً)
        activities.sort((a, b) => b.timestamp.compareTo(a.timestamp));

        // تصفية الأنشطة لتشمل فقط الأذكار والمسبحة والورد
        final filteredActivities = activities.where((activity) {
          return activity.section == 'الأذكار' ||
              activity.section == 'المسبحة' ||
              activity.section == 'ورد' ||
              activity.section == 'تسبيح';
        }).toList();

        // إرجاع العدد المطلوب من الأنشطة
        return filteredActivities.take(limit).toList();
      }
    } catch (e) {
      debugPrint('خطأ في قراءة بيانات النشاط: $e');
    }

    // إرجاع قائمة فارغة في حالة عدم وجود أنشطة
    return [];
  }

  /// تسجيل نشاط جديد
  static Future<void> trackActivity({
    required String title,
    required String section,
    required String sectionType,
    required IconData icon,
    required Color color,
    required String route,
    double? progress,
    String? count,
    bool completed = false,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // إنشاء معرف فريد للنشاط
      final id =
          '${section}_${sectionType}_${DateTime.now().millisecondsSinceEpoch}';

      // إنشاء نشاط جديد
      final newActivity = ActivityData(
        id: id,
        title: title,
        section: section,
        sectionType: sectionType,
        icon: icon,
        color: color,
        timestamp: DateTime.now(),
        route: route,
        progress: progress,
        count: count,
        completed: completed,
      );

      // الحصول على الأنشطة الحالية
      List<ActivityData> activities = [];
      final activitiesJson = prefs.getString(_activityKey);

      if (activitiesJson != null) {
        final List<dynamic> activitiesList = jsonDecode(activitiesJson);
        activities =
            activitiesList.map((e) => ActivityData.fromMap(e)).toList();
      }

      // التحقق مما إذا كان النشاط موجودًا بالفعل (نفس القسم ونوع القسم)
      final existingIndex = activities.indexWhere(
          (a) => a.section == section && a.sectionType == sectionType);

      if (existingIndex != -1) {
        // تحديث النشاط الموجود
        activities[existingIndex] = newActivity;
      } else {
        // إضافة النشاط الجديد
        activities.add(newActivity);
      }

      // التأكد من عدم تجاوز الحد الأقصى للأنشطة
      if (activities.length > _maxActivities) {
        // ترتيب الأنشطة حسب الوقت (الأقدم أولاً)
        activities.sort((a, b) => a.timestamp.compareTo(b.timestamp));

        // إزالة الأنشطة الأقدم
        activities = activities.sublist(activities.length - _maxActivities);
      }

      // حفظ الأنشطة المحدثة
      final updatedActivitiesJson =
          jsonEncode(activities.map((e) => e.toMap()).toList());
      await prefs.setString(_activityKey, updatedActivitiesJson);

      debugPrint('تم تسجيل النشاط بنجاح: $title');
    } catch (e) {
      debugPrint('خطأ في تسجيل النشاط: $e');
    }
  }

  /// الحصول على وقت منسق للعرض
  static String getFormattedTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inSeconds < 60) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      // تنسيق التاريخ للأنشطة القديمة
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  /// تحديد الأيقونة المناسبة للقسم
  static IconData getSectionIcon(String section, String sectionType) {
    if (section == 'الأذكار') {
      if (sectionType.contains('صباح')) {
        return Icons.wb_sunny_rounded;
      } else if (sectionType.contains('مساء')) {
        return Icons.nights_stay_rounded;
      } else if (sectionType.contains('نوم')) {
        return Icons.bedtime_rounded;
      } else {
        return Icons.favorite_rounded;
      }
    } else if (section == 'المسبحة' || section == 'تسبيح') {
      return Icons.panorama_fish_eye;
    } else if (section == 'ورد') {
      return Icons.auto_awesome_rounded;
    } else {
      return Icons.circle;
    }
  }

  /// تحويل اسم الأيقونة إلى IconData ثابت
  static IconData _getIconFromCodePoint(dynamic iconData) {
    if (iconData == null) return Icons.circle;

    // إذا كان النوع string، فهو اسم الأيقونة
    if (iconData is String) {
      return _iconMap[iconData] ?? Icons.circle;
    }

    // إذا كان رقم، فهو codePoint قديم - نحوله إلى الأيقونة الافتراضية
    return Icons.circle;
  }

  /// الحصول على اسم الأيقونة من IconData
  static String _getIconName(IconData icon) {
    // البحث في الـ Map عن الاسم المقابل للأيقونة
    return _iconMap.entries
        .firstWhere((entry) => entry.value == icon, orElse: () => _iconMap.entries.last)
        .key;
  }

  /// تحديد اللون المناسب للقسم
  static Color getSectionColor(String section) {
    switch (section) {
      case 'الأذكار':
        return AppColors.azkarColor;
      case 'المسبحة':
      case 'تسبيح':
        return AppColors.tasbihColor;
      case 'ورد':
        return AppColors.tasbihColor; // استخدام نفس لون المسبحة للورد
      default:
        return Colors.grey;
    }
  }

  /// تحديد المسار المناسب للقسم
  static String getSectionRoute(String section, String sectionType) {
    switch (section) {
      case 'الأذكار':
        return '/azkar';
      case 'المسبحة':
      case 'تسبيح':
        return AppConstants.tasbihRoute;
      case 'ورد':
        return AppConstants.wirdListRoute; // استخدام مسار قائمة الورد
      default:
        return '/';
    }
  }
}
