// قسم حكمة اليوم
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;
import '../controllers/home_controller.dart';

class WisdomSection extends StatefulWidget {
  const WisdomSection({super.key});

  @override
  State<WisdomSection> createState() => _WisdomSectionState();
}

class _WisdomSectionState extends State<WisdomSection>
    with SingleTickerProviderStateMixin {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2200),
    )..forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final controller = Provider.of<HomeController>(context);
    final brightness = Theme.of(context).brightness;
    final isDarkMode = brightness == Brightness.dark;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
            0,
            20 * (1 - _animationController.value),
          ),
          child: Opacity(
            opacity: _animationController.value,
            child: _buildWisdomCard(context, controller, isDarkMode),
          ),
        );
      },
    );
  }

  Widget _buildWisdomCard(
      BuildContext context, HomeController controller, bool isDarkMode) {
    final colorScheme = Theme.of(context).colorScheme;

    // تحديد ألوان مناسبة للوضع الفاتح والداكن
    final cardBackground =
        isDarkMode ? colorScheme.surface.withOpacity(0.9) : colorScheme.surface;

    final borderColor = isDarkMode
        ? colorScheme.primary.withOpacity(0.3)
        : colorScheme.secondary.withOpacity(0.2);

    final shadowColor = isDarkMode
        ? colorScheme.primary.withOpacity(0.3)
        : colorScheme.primary.withOpacity(0.4);

    final quoteBackgroundColor = isDarkMode
        ? colorScheme.primary.withOpacity(0.08)
        : colorScheme.primary.withOpacity(0.05);

    return Card(
      elevation: isDarkMode ? 4 : 6,
      shadowColor: shadowColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24),
        side: BorderSide(
          color: borderColor,
          width: 1.2,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24),
          gradient: LinearGradient(
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            colors: [
              cardBackground,
              cardBackground.withOpacity(isDarkMode ? 0.85 : 0.9),
            ],
          ),
        ),
        child: Stack(
          children: [
            // زخرفة إسلامية في الخلفية
            _buildIslamicPatterns(context, isDarkMode),

            // تأثير الوهج المتحرك
            AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return CustomPaint(
                  painter: WisdomGlowPainter(
                    progress: _animationController.value,
                    baseColor: colorScheme.primary,
                    isDarkMode: isDarkMode,
                  ),
                  child: const SizedBox.expand(),
                );
              },
            ),

            // محتوى القسم
            Padding(
              padding: const EdgeInsets.all(18.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // العنوان مع الأيقونة
                  _buildSectionHeader(context, isDarkMode),

                  const SizedBox(height: 16),

                  // عرض الحكمة
                  _buildWisdomContent(
                      context, controller, isDarkMode, quoteBackgroundColor),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIslamicPatterns(BuildContext context, bool isDarkMode) {
    final colorScheme = Theme.of(context).colorScheme;

    return Stack(
      children: [
        // النمط الأول - في الأعلى
        Positioned(
          right: -40,
          top: -40,
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              final rotationValue = 0.05 *
                  math.pi *
                  math.sin(_animationController.value * math.pi);
              return Transform.rotate(
                angle: rotationValue,
                child: Opacity(
                  opacity: isDarkMode ? 0.05 : 0.07,
                  child: SvgPicture.asset(
                    'assets/images/p2.svg',
                    width: 150,
                    height: 150,
                    colorFilter: ColorFilter.mode(
                      colorScheme.primary,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              );
            },
          ),
        ),

        // النمط الثاني - في الأسفل
        Positioned(
          left: -50,
          bottom: -50,
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              final rotationValue = -0.04 *
                  math.pi *
                  math.sin(_animationController.value * math.pi * 0.8);
              return Transform.rotate(
                angle: rotationValue,
                child: Opacity(
                  opacity: isDarkMode ? 0.04 : 0.06,
                  child: SvgPicture.asset(
                    'assets/images/p2.svg',
                    width: 120,
                    height: 120,
                    colorFilter: ColorFilter.mode(
                      colorScheme.primary,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSectionHeader(BuildContext context, bool isDarkMode) {
    final colorScheme = Theme.of(context).colorScheme;

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: colorScheme.secondary.withAlpha(isDarkMode ? 30 : 51),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color:
                    colorScheme.secondary.withOpacity(isDarkMode ? 0.15 : 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
            border: Border.all(
              color:
                  colorScheme.secondary.withOpacity(isDarkMode ? 0.25 : 0.15),
              width: 1,
            ),
          ),
          child: Icon(
            Icons.format_quote,
            color: colorScheme.secondary,
            size: 22,
            shadows: [
              Shadow(
                color: colorScheme.secondary.withOpacity(0.3),
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
        ),
        const SizedBox(width: 12),
        Text(
          'حكمة اليوم',
          style: Theme.of(context).textTheme.titleMedium!.copyWith(
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                color: colorScheme.secondary.withOpacity(0.3),
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
        ),
        const Spacer(),

        // زر التحديث
        IconButton(
          icon: Icon(
            Icons.refresh_rounded,
            color: colorScheme.secondary,
            size: 20,
          ),
          onPressed: () {
            // تحديث الحكم
            HapticFeedback.lightImpact();
            Provider.of<HomeController>(context, listen: false).refresh();
          },
          tooltip: 'تحديث الحكمة',
          visualDensity: VisualDensity.compact,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
      ],
    );
  }

  Widget _buildWisdomContent(BuildContext context, HomeController controller,
      bool isDarkMode, Color backgroundColor) {
    final colorScheme = Theme.of(context).colorScheme;

    if (controller.isLoading) {
      return _buildLoadingIndicator(context, isDarkMode);
    }

    if (controller.wisdomQuotes.isEmpty) {
      return _buildEmptyState(context, isDarkMode);
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: colorScheme.primary.withOpacity(isDarkMode ? 0.2 : 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: colorScheme.primary.withOpacity(isDarkMode ? 0.05 : 0.03),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        children: [
          // عارض الحكم
          SizedBox(
            height: 150,
            child: PageView.builder(
              controller: _pageController,
              itemCount: controller.wisdomQuotes.length,
              onPageChanged: (index) {
                setState(() {
                  _currentPage = index;
                });
                HapticFeedback.selectionClick();
              },
              physics: const ClampingScrollPhysics(),
              itemBuilder: (context, index) {
                final wisdom = controller.wisdomQuotes[index];
                return InkWell(
                  onTap: () {
                    // عرض اسم الكاتب عند النقر
                    ScaffoldMessenger.of(context).clearSnackBars();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          "حكمة من ${wisdom.author}",
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontFamily: 'Tajawal'),
                        ),
                        duration: const Duration(seconds: 1),
                        behavior: SnackBarBehavior.floating,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        backgroundColor: isDarkMode
                            ? colorScheme.primary.withOpacity(0.8)
                            : null,
                      ),
                    );
                  },
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // نص الحكمة مع تأثيرات
                      TweenAnimationBuilder<double>(
                        tween: Tween<double>(begin: 0.8, end: 1.0),
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                        builder: (context, value, child) {
                          return Opacity(
                            opacity: value,
                            child: Transform.scale(
                              scale: value,
                              child: Text(
                                wisdom.text,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 19,
                                  fontWeight: FontWeight.w500,
                                  height: 1.5,
                                  color: isDarkMode
                                      ? Colors.white
                                      : colorScheme.onSurface,
                                  shadows: [
                                    Shadow(
                                      color:
                                          colorScheme.primary.withOpacity(0.1),
                                      blurRadius: 2,
                                      offset: const Offset(0, 1),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),

                      const SizedBox(height: 12),

                      // اسم الكاتب في كبسولة
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: colorScheme.secondary
                                .withOpacity(isDarkMode ? 0.15 : 0.1),
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: colorScheme.secondary.withOpacity(0.1),
                                blurRadius: 3,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                          child: Text(
                            '- ${wisdom.author}',
                            style: TextStyle(
                              color: colorScheme.secondary,
                              fontStyle: FontStyle.italic,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),

          const SizedBox(height: 14),

          // مؤشر الصفحة المحسن
          _buildPageIndicator(
              context, controller.wisdomQuotes.length, isDarkMode),
        ],
      ),
    );
  }

  Widget _buildPageIndicator(
      BuildContext context, int totalPages, bool isDarkMode) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      margin: const EdgeInsets.only(top: 5),
      height: 20,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          totalPages.clamp(0, 10),
          (index) => GestureDetector(
            onTap: () {
              // الانتقال للحكمة عند النقر
              _pageController.animateToPage(
                index,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
              HapticFeedback.selectionClick();
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              width: _currentPage == index ? 24 : 8,
              height: 8,
              margin: const EdgeInsets.symmetric(horizontal: 3),
              decoration: BoxDecoration(
                color: _currentPage == index
                    ? colorScheme.secondary
                    : colorScheme.secondary.withOpacity(isDarkMode ? 0.4 : 0.3),
                borderRadius: BorderRadius.circular(4),
                boxShadow: _currentPage == index
                    ? [
                        BoxShadow(
                          color: colorScheme.secondary.withOpacity(0.3),
                          blurRadius: 3,
                          offset: const Offset(0, 1),
                        ),
                      ]
                    : null,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator(BuildContext context, bool isDarkMode) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 40),
      decoration: BoxDecoration(
        color: colorScheme.primary.withOpacity(isDarkMode ? 0.08 : 0.05),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          SizedBox(
            width: 30,
            height: 30,
            child: CircularProgressIndicator(
              strokeWidth: 2.5,
              valueColor: AlwaysStoppedAnimation<Color>(colorScheme.secondary),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل الحكمة...',
            style: TextStyle(
              fontSize: 14,
              color: isDarkMode
                  ? Colors.white70
                  : colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, bool isDarkMode) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 20),
      decoration: BoxDecoration(
        color: colorScheme.primary.withOpacity(isDarkMode ? 0.08 : 0.05),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Icon(
            Icons.not_interested_rounded,
            size: 36,
            color: colorScheme.secondary.withOpacity(0.5),
          ),
          const SizedBox(height: 12),
          Text(
            'لا توجد حكم متاحة',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isDarkMode
                  ? Colors.white70
                  : colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 8),
          TextButton.icon(
            onPressed: () {
              Provider.of<HomeController>(context, listen: false).refresh();
              HapticFeedback.mediumImpact();
            },
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
            style: TextButton.styleFrom(
              foregroundColor: colorScheme.secondary,
            ),
          ),
        ],
      ),
    );
  }
}

// رسام مخصص لتأثير الوهج المتحرك
class WisdomGlowPainter extends CustomPainter {
  final double progress;
  final Color baseColor;
  final bool isDarkMode;

  WisdomGlowPainter({
    required this.progress,
    required this.baseColor,
    required this.isDarkMode,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);

    // الوهج الأول - تأثير متموج من الأعلى
    final topGlow = RadialGradient(
      center: Alignment(
        0.7 + (0.2 * math.sin(progress * math.pi * 1.3)),
        -0.6 + (0.1 * math.cos(progress * math.pi * 1.2)),
      ),
      radius: 0.8 + (0.2 * math.sin(progress * math.pi * 1.5)),
      colors: [
        baseColor.withOpacity(isDarkMode ? 0.08 : 0.05),
        baseColor.withOpacity(isDarkMode ? 0.03 : 0.02),
        Colors.transparent,
      ],
      stops: const [0.0, 0.3, 0.6],
    ).createShader(rect);

    canvas.drawRect(rect, Paint()..shader = topGlow);

    // الوهج الثاني - تأثير متموج من الأسفل
    final bottomGlow = RadialGradient(
      center: Alignment(
        -0.6 + (0.15 * math.cos(progress * math.pi * 1.7)),
        0.7 + (0.1 * math.sin(progress * math.pi * 1.5)),
      ),
      radius: 0.7 + (0.15 * math.sin(progress * math.pi * 1.2)),
      colors: [
        baseColor.withOpacity(isDarkMode ? 0.07 : 0.04),
        baseColor.withOpacity(isDarkMode ? 0.02 : 0.015),
        Colors.transparent,
      ],
      stops: const [0.0, 0.4, 0.7],
    ).createShader(rect);

    canvas.drawRect(rect, Paint()..shader = bottomGlow);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
