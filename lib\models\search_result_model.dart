// lib/models/search_result_model.dart

// استيراد الحزم الأساسية اللازمة
import 'package:flutter/material.dart'; // حزمة المواد الأساسية التي توفر عناصر مثل Color
import '../utils/app_colors.dart'; // استيراد ملف الألوان المخصص للتطبيق

/// يمثل هذا الكلاس (النموذج) بنية بيانات نتيجة البحث الواحدة.
/// يتم استخدامه لتوحيد شكل البيانات القادمة من مصادر مختلفة (أذكار، أدعية، إلخ)
/// لعرضها بشكل متناسق في قائمة نتائج البحث.
class SearchResult {
  // --- خصائص النموذج ---

  /// معرّف فريد لكل نتيجة بحث (مثل id الذكر أو الدعاء).
  final String id;

  /// العنوان الرئيسي لنتيجة البحث الذي سيظهر بخط كبير.
  final String title;

  /// العنوان الفرعي أو الوصف الذي يظهر تحت العنوان الرئيسي.
  final String subtitle;

  /// اسم القسم الذي تنتمي إليه هذه النتيجة (مثال: "الأذكار").
  final String section;

  /// **(تم التعديل هنا)**
  /// مسار ملف أيقونة SVG. تم تغييره من `IconData` إلى `String`
  /// لاستقبال مسار الأيقونة مثل 'assets/icons/favorite.svg'.
  final String svgIcon;

  /// اللون المميز للقسم الذي تنتمي إليه النتيجة.
  final Color color;

  /// المسار (Route) الذي سيتم الانتقال إليه عند النقر على هذه النتيجة.
  final String route;

  /// بيانات إضافية يمكن تمريرها مع النتيجة (مثل كائن الذكر كاملاً).
  /// النوع `dynamic` يعني أنه يمكن أن يكون أي نوع من البيانات.
  final dynamic data;

  /// يحدد ما إذا كانت النتيجة متاحة وقابلة للنقر أم لا (للقسام قيد التطوير).
  final bool isAvailable;

  // --- المُنشئ (Constructor) الأساسي ---

  /// المُنشئ الرئيسي للكلاس. يتطلب تمرير جميع الخصائص الأساسية.
  const SearchResult({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.section,
    required this.svgIcon, // تم التعديل هنا من icon إلى svgIcon
    required this.color,
    required this.route,
    this.data,
    this.isAvailable = true,
  });

  // --- المُنشئات المُسماة (Named Constructors) أو الـ Factory Methods ---
  // هذه الدوال تسهل إنشاء كائنات SearchResult لأنواع محددة
  // مع تعبئة بعض الحقول تلقائياً (مثل اسم القسم، اللون، والأيقونة).

  /// دالة لإنشاء نتيجة بحث خاصة بقسم "الأذكار".
  static SearchResult azkar({
    required String id,
    required String title,
    required String subtitle,
    dynamic data,
  }) {
    return SearchResult(
      id: id,
      title: title,
      subtitle: subtitle,
      section: 'الأذكار',
      svgIcon: 'assets/icons/azkar/azkar.svg', // تم التعديل هنا
      color: AppColors.azkarColor,
      route: '/azkar',
      data: data,
      isAvailable: true,
    );
  }

  /// دالة لإنشاء نتيجة بحث خاصة بقسم "المسبحة".
  static SearchResult tasbih({
    required String id,
    required String title,
    required String subtitle,
    dynamic data,
  }) {
    return SearchResult(
      id: id,
      title: title,
      subtitle: subtitle,
      section: 'المسبحة',
      svgIcon: 'assets/icons/azkar/tasbih.svg', // تم التعديل هنا
      color: AppColors.tasbihColor,
      route: '/tasbih',
      data: data,
      isAvailable: true,
    );
  }

  /// دالة لإنشاء نتيجة بحث خاصة بقسم "الأدعية".
  static SearchResult dua({
    required String id,
    required String title,
    required String subtitle,
    dynamic data,
  }) {
    return SearchResult(
      id: id,
      title: title,
      subtitle: subtitle,
      section: 'الأدعية',
      svgIcon: 'assets/icons/azkar/duas.svg', // تم التعديل هنا
      color: AppColors.duasColor,
      route: '/duas',
      data: data,
      isAvailable: true,
    );
  }

  /// دالة لإنشاء نتيجة بحث خاصة بقسم "الصلاة على النبي".
  static SearchResult prophetPrayer({
    required String id,
    required String title,
    required String subtitle,
    dynamic data,
  }) {
    return SearchResult(
      id: id,
      title: title,
      subtitle: subtitle,
      section: 'الصلاة على النبي',
      svgIcon: 'assets/icons/prophet_prayers/prophetPrayers.svg', // تم التعديل هنا
      color: AppColors.prophetPrayersColor,
      route: '/prophet-prayers',
      data: data,
      isAvailable: true,
    );
  }

  /// دالة لإنشاء نتيجة بحث خاصة بقسم "الكتب" (غير متاح حالياً).
  static SearchResult book({
    required String id,
    required String title,
    required String subtitle,
    dynamic data,
  }) {
    return SearchResult(
      id: id,
      title: title,
      subtitle: subtitle,
      section: 'الكتب',
      svgIcon: 'assets/icons/azkar/books.svg', // تم التعديل هنا
      color: AppColors.booksColor,
      route: '/books',
      data: data,
      isAvailable: false, // تحديد أن القسم غير متاح
    );
  }

  /// دالة لإنشاء نتيجة بحث خاصة بقسم "القصائد" (غير متاح حالياً).
  static SearchResult poem({
    required String id,
    required String title,
    required String subtitle,
    dynamic data,
  }) {
    return SearchResult(
      id: id,
      title: title,
      subtitle: subtitle,
      section: 'القصائد',
      svgIcon: 'assets/icons/azkar/poems.svg', // تم التعديل هنا
      color: AppColors.poemsColor,
      route: '/poems',
      data: data,
      isAvailable: false, // تحديد أن القسم غير متاح
    );
  }

  /// دالة لإنشاء نتيجة بحث خاصة بقسم "المفضلة".
  static SearchResult favorite({
    required String id,
    required String title,
    required String subtitle,
    dynamic data,
  }) {
    return SearchResult(
      id: id,
      title: title,
      subtitle: subtitle,
      section: 'المفضلة',
      svgIcon: 'assets/icons/azkar/favorites.svg', // تم التعديل هنا
      color: AppColors.favoritesColor,
      route: '/favorites',
      data: data,
      isAvailable: true,
    );
  }
}
