// lib/models/search_result_model.dart
import 'package:flutter/material.dart';
import '../utils/app_colors.dart';

/// نموذج نتيجة البحث
class SearchResult {
  final String id;
  final String title;
  final String subtitle;
  final String section;
  final IconData icon;
  final Color color;
  final String route;
  final dynamic data;
  final bool isAvailable;

  const SearchResult({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.section,
    required this.icon,
    required this.color,
    required this.route,
    this.data,
    this.isAvailable = true,
  });

  /// إنشاء نتيجة بحث للأذكار
  static SearchResult azkar({
    required String id,
    required String title,
    required String subtitle,
    dynamic data,
  }) {
    return SearchResult(
      id: id,
      title: title,
      subtitle: subtitle,
      section: 'الأذكار',
      icon: Icons.favorite_rounded,
      color: AppColors.azkarColor,
      route: '/azkar',
      data: data,
      isAvailable: true,
    );
  }

  /// إنشاء نتيجة بحث للمسبحة
  static SearchResult tasbih({
    required String id,
    required String title,
    required String subtitle,
    dynamic data,
  }) {
    return SearchResult(
      id: id,
      title: title,
      subtitle: subtitle,
      section: 'المسبحة',
      icon: Icons.panorama_fish_eye,
      color: AppColors.tasbihColor,
      route: '/tasbih',
      data: data,
      isAvailable: true,
    );
  }

  /// إنشاء نتيجة بحث للأدعية
  static SearchResult dua({
    required String id,
    required String title,
    required String subtitle,
    dynamic data,
  }) {
    return SearchResult(
      id: id,
      title: title,
      subtitle: subtitle,
      section: 'الأدعية',
      icon: Icons.volunteer_activism_rounded,
      color: AppColors.duasColor,
      route: '/duas',
      data: data,
      isAvailable: true,
    );
  }

  /// إنشاء نتيجة بحث للصلاة على النبي
  static SearchResult prophetPrayer({
    required String id,
    required String title,
    required String subtitle,
    dynamic data,
  }) {
    return SearchResult(
      id: id,
      title: title,
      subtitle: subtitle,
      section: 'الصلاة على النبي',
      icon: Icons.auto_awesome_rounded,
      color: AppColors.prophetPrayersColor,
      route: '/prophet-prayers',
      data: data,
      isAvailable: true,
    );
  }

  /// إنشاء نتيجة بحث للكتب (غير متاحة حالياً)
  static SearchResult book({
    required String id,
    required String title,
    required String subtitle,
    dynamic data,
  }) {
    return SearchResult(
      id: id,
      title: title,
      subtitle: subtitle,
      section: 'الكتب',
      icon: Icons.book_rounded,
      color: AppColors.booksColor,
      route: '/books',
      data: data,
      isAvailable: false, // غير متاح حالياً
    );
  }

  /// إنشاء نتيجة بحث للقصائد (غير متاحة حالياً)
  static SearchResult poem({
    required String id,
    required String title,
    required String subtitle,
    dynamic data,
  }) {
    return SearchResult(
      id: id,
      title: title,
      subtitle: subtitle,
      section: 'القصائد',
      icon: Icons.music_note_rounded,
      color: AppColors.poemsColor,
      route: '/poems',
      data: data,
      isAvailable: false, // غير متاح حالياً
    );
  }

  /// إنشاء نتيجة بحث للمفضلة
  static SearchResult favorite({
    required String id,
    required String title,
    required String subtitle,
    dynamic data,
  }) {
    return SearchResult(
      id: id,
      title: title,
      subtitle: subtitle,
      section: 'المفضلة',
      icon: Icons.bookmark_rounded,
      color: AppColors.favoritesColor,
      route: '/favorites',
      data: data,
      isAvailable: true,
    );
  }
}
