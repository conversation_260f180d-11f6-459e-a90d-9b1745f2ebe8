part of '../azkar_details_screen.dart';

Widget _buildProgressBar({
  required int total,
  required int completed,
  required BuildContext context,
  bool isHighlighted = false,
}) {
  final double progress = total > 0 ? completed / total : 0.0;
  final screenSize = MediaQuery.of(context).size;
  final progressBarWidth = screenSize.width - (screenSize.width * 0.08);

  return Column(
    crossAxisAlignment: CrossAxisAlignment.end, // تغيير المحاذاة إلى اليمين
    children: [
      Row(
        textDirection:
            TextDirection.rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
        children: [
          Icon(
            Icons.bar_chart,
            size: screenSize.width * 0.04,
            color: isHighlighted
                ? Colors.green
                : AppColors.getAzkarColor(
                    Theme.of(context).brightness == Brightness.dark),
          ),
          SizedBox(width: screenSize.width * 0.02),
          Text(
            'تقدم الإنجاز',
            style: TextStyle(
              fontSize: screenSize.width * 0.035,
              fontWeight: FontWeight.w500,
              color: isHighlighted
                  ? Colors.green
                  : AppColors.getAzkarColor(
                      Theme.of(context).brightness == Brightness.dark),
            ),
          ),
          const Spacer(),
          AnimatedDefaultTextStyle(
            duration: const Duration(milliseconds: 300),
            style: TextStyle(
              fontSize: screenSize.width * 0.035,
              fontWeight: FontWeight.bold,
              color: isHighlighted
                  ? Colors.green
                  : AppColors.getAzkarColor(
                      Theme.of(context).brightness == Brightness.dark),
            ),
            child: Text('$completed/$total'),
          ),
        ],
      ),
      SizedBox(height: screenSize.height * 0.01),
      Stack(
        children: [
          // قاعدة شريط التقدم
          Container(
            height: screenSize.height * 0.01,
            width: progressBarWidth,
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(
                      0xFF16213E) // TasbihColors.darkBackgroundSecondary
                  : Colors.grey[200],
              borderRadius: BorderRadius.circular(screenSize.width * 0.01),
            ),
          ),
          // شريط التقدم الفعلي - مُحسّن للأداء
          RepaintBoundary(
            child: TweenAnimationBuilder<double>(
              tween: Tween<double>(begin: 0, end: progress),
              // تقليل مدة الرسوم المتحركة لتحسين الأداء
              duration: const Duration(milliseconds: 400),
              // استخدام منحنى أبسط لتحسين الأداء
              curve: Curves.easeOut,
              builder: (context, animatedProgress, child) {
                return AnimatedContainer(
                  // تقليل مدة الرسوم المتحركة لتحسين الأداء
                  duration: const Duration(milliseconds: 200),
                  height: screenSize.height * 0.01,
                  width: math.max(0, animatedProgress * progressBarWidth),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: isHighlighted
                          ? [
                              Colors.green.withAlpha(179),
                              Colors.green
                            ] // 0.7 * 255 = 179
                          : [
                              AppColors.getAzkarColor(
                                      Theme.of(context).brightness ==
                                          Brightness.dark)
                                  .withAlpha(179), // 0.7 * 255 = 179
                              AppColors.getAzkarColor(
                                  Theme.of(context).brightness ==
                                      Brightness.dark)
                            ],
                    ),
                    borderRadius:
                        BorderRadius.circular(screenSize.width * 0.01),
                    // تبسيط الظلال لتحسين الأداء
                    boxShadow: [
                      BoxShadow(
                        color: (isHighlighted
                                ? Colors.green
                                : AppColors.getAzkarColor(
                                    Theme.of(context).brightness ==
                                        Brightness.dark))
                            .withAlpha(51), // 0.2 * 255 = 51
                        // تقليل قيمة التمويه لتحسين الأداء
                        blurRadius: isHighlighted ? 3 : 1.5,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      const SizedBox(height: 4),

      // رسالة تشجيعية مع تأثير تغيير عند الإنجاز والتعامل مع النصوص الطويلة - مُحسّنة للأداء
      RepaintBoundary(
        child: AnimatedDefaultTextStyle(
          // تقليل مدة الرسوم المتحركة لتحسين الأداء
          duration: const Duration(milliseconds: 200),
          style: TextStyle(
            fontSize: 12,
            color: isHighlighted
                ? Colors.green.shade700
                : Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFFAAAAAA) // TasbihColors.darkTextSecondary
                    : Colors.grey[600],
            fontStyle: FontStyle.italic,
            fontWeight: isHighlighted ? FontWeight.bold : FontWeight.normal,
          ),
          child: SizedBox(
            // استخدام عرض نسبي للحاوية
            width: MediaQuery.of(context).size.width - 40,
            child: Text(
              _getProgressMessage(progress),
              maxLines: 2, // السماح بسطرين للنصوص الطويلة
              overflow:
                  TextOverflow.ellipsis, // إظهار علامة القطع عند تجاوز الحد
              textAlign: TextAlign.right, // محاذاة النص إلى اليمين
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
            ),
          ),
        ),
      ),
    ],
  );
}

String _getProgressMessage(double progress) {
  if (progress >= 1.0) {
    return 'أحسنت! لقد أكملت جميع الأذكار، تقبل الله منك';
  } else if (progress >= 0.75) {
    return 'أنت قريب من الانتهاء، استمر في العمل الصالح';
  } else if (progress >= 0.5) {
    return 'أكملت أكثر من النصف، واصل التقدم';
  } else if (progress >= 0.25) {
    return 'بداية جيدة، استمر في قراءة الأذكار';
  } else if (progress > 0) {
    return 'بدأت القراءة، واصل التقدم';
  } else {
    return 'لم تبدأ بعد - ابدأ بقراءة الأذكار';
  }
}
