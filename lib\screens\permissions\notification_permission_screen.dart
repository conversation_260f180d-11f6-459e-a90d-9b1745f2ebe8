// شاشة طلب أذونات الإشعارات

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../services/notification_manager.dart';
import '../../utils/app_colors.dart';
import 'package:lottie/lottie.dart';

/// شاشة طلب أذونات الإشعارات بتصميم فاخر
class NotificationPermissionScreen extends StatefulWidget {
  final VoidCallback onPermissionGranted;
  final VoidCallback onPermissionDenied;

  const NotificationPermissionScreen({
    Key? key,
    required this.onPermissionGranted,
    required this.onPermissionDenied,
  }) : super(key: key);

  @override
  State<NotificationPermissionScreen> createState() =>
      _NotificationPermissionScreenState();
}

class _NotificationPermissionScreenState
    extends State<NotificationPermissionScreen>
    with SingleTickerProviderStateMixin {
  // استخدام مدير الإشعارات الجديد بدلاً من خدمة الأذونات القديمة
  final NotificationManager _notificationManager = NotificationManager();

  // مفتاح تخزين حالة طلب الأذونات
  static const String _permissionRequestedKey =
      'notification_permission_requested';

  late AnimationController _animationController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// طلب أذونات الإشعارات
  Future<void> _requestPermission() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // طلب أذونات الإشعارات باستخدام مدير الإشعارات الجديد
      final result =
          await _notificationManager.requestNotificationPermissions();

      // تسجيل أنه تم طلب الأذونات
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_permissionRequestedKey, true);

      if (result) {
        // تم منح الأذونات
        widget.onPermissionGranted();
      } else {
        // تم رفض الأذونات
        widget.onPermissionDenied();
      }
    } catch (e) {
      debugPrint('خطأ في طلب أذونات الإشعارات: $e');
      widget.onPermissionDenied();
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// تخطي طلب الأذونات
  void _skipPermission() async {
    // تسجيل أنه تم طلب الأذونات (حتى لا يتم طلبها مرة أخرى)
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_permissionRequestedKey, true);

    // استدعاء دالة رفض الأذونات
    widget.onPermissionDenied();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final textColor = isDarkMode ? Colors.white : Colors.black87;
    final backgroundColor =
        isDarkMode ? AppColors.darkBackground : Colors.white;

    return Scaffold(
      backgroundColor: backgroundColor,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 40),

              // رسوم متحركة للإشعارات
              Lottie.asset(
                'assets/animations/notification_animation.json',
                height: 200,
                width: 200,
                controller: _animationController,
                fit: BoxFit.contain,
              ),

              const SizedBox(height: 32),

              // عنوان الشاشة
              Text(
                'تفعيل الإشعارات',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: textColor,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 16),

              // وصف الإشعارات
              Text(
                'تساعدك الإشعارات على البقاء على اتصال مع أذكارك وأورادك اليومية، وتذكرك بالعودة إلى المسبحة بعد فترة من الغياب.',
                style: TextStyle(
                  fontSize: 16,
                  color: textColor.withAlpha(204), // 0.8 * 255 = 204
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 8),

              // فوائد الإشعارات
              _buildBenefitItem(
                context,
                'تذكير بأذكار الصباح والمساء',
                Icons.wb_sunny_outlined,
              ),

              _buildBenefitItem(
                context,
                'تنبيهات للأوراد اليومية',
                Icons.access_time_outlined,
              ),

              _buildBenefitItem(
                context,
                'رسائل تحفيزية للعودة إلى المسبحة',
                Icons.favorite_outline,
              ),

              const Spacer(),

              // زر تفعيل الإشعارات
              _isLoading
                  ? const CircularProgressIndicator(
                      color: AppColors.primary,
                    )
                  : Column(
                      children: [
                        ElevatedButton(
                          onPressed: _requestPermission,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 32,
                              vertical: 16,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            elevation: 0,
                          ),
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.notifications_active),
                              SizedBox(width: 8),
                              Text(
                                'تفعيل الإشعارات',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 16),

                        // زر تخطي
                        TextButton(
                          onPressed: _skipPermission,
                          child: Text(
                            'تخطي',
                            style: TextStyle(
                              color:
                                  textColor.withAlpha(153), // 0.6 * 255 = 153
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ],
                    ),

              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء عنصر فائدة
  Widget _buildBenefitItem(BuildContext context, String text, IconData icon) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final textColor = isDarkMode ? Colors.white : Colors.black87;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primary.withAlpha(26), // 0.1 * 255 = 25.5 ≈ 26
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: AppColors.primary,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 15,
                color: textColor.withAlpha(230), // 0.9 * 255 = 229.5 ≈ 230
              ),
            ),
          ),
        ],
      ),
    );
  }
}
