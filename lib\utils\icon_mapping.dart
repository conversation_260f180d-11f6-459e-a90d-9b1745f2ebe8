// خريطة الأيقونات الثابتة للتطبيق

import 'package:flutter/material.dart';

/// خريطة تحتوي على الأيقونات المستخدمة في التطبيق
/// تستخدم للحصول على أيقونات ثابتة بدلاً من إنشاء IconData في وقت التشغيل
/// مما يسمح بعملية tree shake لخطوط الأيقونات
class AppIcons {
  // منع إنشاء نسخة من الكلاس
  AppIcons._();

  // الأيقونة الافتراضية
  static const IconData defaultIcon = Icons.emoji_events;

  // خريطة الأيقونات المستخدمة في الإنجازات
  static const Map<String, IconData> achievementIcons = {
    // أيقونات الإنجازات الأساسية
    '0xe24e': Icons.emoji_events, // emoji_events
    '0xe157': Icons.star, // star
    '0xe838': Icons.favorite, // favorite
    '0xe8dc': Icons.thumb_up, // thumb_up
    '0xe8f4': Icons.trending_up, // trending_up
    '0xe8b8': Icons.timer, // timer
    '0xe8e5': Icons.today, // today
    '0xe8df': Icons.timeline, // timeline
    '0xe8f8': Icons.verified, // verified
    '0xe8f7': Icons.verified_user, // verified_user
    '0xe8e8': Icons.track_changes, // track_changes
    '0xe8e9': Icons.translate, // translate
    '0xe8ea': Icons.trending_down, // trending_down
    '0xe8eb': Icons.trending_flat, // trending_flat
    '0xe8ec': Icons.trending_neutral, // trending_neutral
    '0xe8ed': Icons.trending_up, // trending_up
    '0xe8ee': Icons.turned_in, // turned_in
    '0xe8ef': Icons.turned_in_not, // turned_in_not
    '0xe8f0': Icons.update, // update
    '0xe8f1': Icons.verified, // verified
    '0xe8f2': Icons.verified_user, // verified_user
    '0xe8f3': Icons.visibility, // visibility
    '0xe8f5': Icons.visibility_off, // visibility_off
    '0xe8f6': Icons.watch_later, // watch_later
    '0xe8f9': Icons.work, // work
    '0xe8fa': Icons.youtube_searched_for, // youtube_searched_for
    '0xe8fb': Icons.zoom_in, // zoom_in
    '0xe8fc': Icons.zoom_out, // zoom_out
    '0xe8fd': Icons.zoom_out_map, // zoom_out_map
  };

  /// الحصول على الأيقونة من الرمز
  /// إذا لم يتم العثور على الرمز، يتم إرجاع الأيقونة الافتراضية
  static IconData getIconFromCode(String iconCode) {
    return achievementIcons[iconCode] ?? defaultIcon;
  }
}
