// مزود إحصائيات المسبحة

import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:share_plus/share_plus.dart';
import '../models/tasbih_stats_model.dart';
import '../models/dhikr_model.dart';
import '../models/achievement_model.dart';

class TasbihStatsProvider extends ChangeNotifier {
  // مفاتيح التخزين المحلي
  static const String _dailyStatsKey = 'tasbih_daily_stats';
  static const String _dhikrStatsKey = 'tasbih_dhikr_stats';
  static const String _usageTimeKey = 'tasbih_usage_time';
  static const String _lastUsedDateKey = 'tasbih_last_used_date';
  static const String _achievementsKey = 'tasbih_achievements';

  // نموذج الإحصائيات
  TasbihStatsModel _stats = TasbihStatsModel.empty();

  // حالة التحميل
  bool _isLoading = true;

  // الحصول على الإحصائيات
  TasbihStatsModel get stats => _stats;

  // الحصول على حالة التحميل
  bool get isLoading => _isLoading;

  // تهيئة المزود
  Future<void> initialize() async {
    try {
      await loadStats();
    } catch (e) {
      debugPrint('خطأ في تهيئة مزود إحصائيات المسبحة: $e');
      // التأكد من تحديث حالة التحميل في حالة حدوث خطأ
      _isLoading = false;
      notifyListeners();
    }
  }

  // تحميل الإحصائيات من التخزين المحلي
  Future<void> loadStats() async {
    try {
      // تعيين حالة التحميل بدون إشعار (لتجنب الإشعار أثناء البناء)
      _isLoading = true;

      final prefs = await SharedPreferences.getInstance();

      // تحميل الإحصائيات الأساسية
      final totalCount = prefs.getInt('totalCount') ?? 0;
      final sessionCount = prefs.getInt('sessionCount') ?? 0;
      final mostUsedDhikrName =
          prefs.getString('mostUsedDhikr') ?? 'سبحان الله';

      // تحميل إحصائيات الأذكار
      final dhikrStatsJson = prefs.getString(_dhikrStatsKey);
      List<DhikrStats> dhikrStats = [];

      if (dhikrStatsJson != null) {
        final List<dynamic> dhikrStatsList = jsonDecode(dhikrStatsJson);
        dhikrStats = dhikrStatsList.map((e) => DhikrStats.fromMap(e)).toList();
      } else {
        // إنشاء إحصائيات افتراضية للأذكار
        dhikrStats = [
          DhikrStats(
            id: -1,
            name: 'سبحان الله',
            count: totalCount > 0 ? (totalCount * 0.4).round() : 0,
            percentage: 40,
          ),
          DhikrStats(
            id: -2,
            name: 'الحمد لله',
            count: totalCount > 0 ? (totalCount * 0.3).round() : 0,
            percentage: 30,
          ),
          DhikrStats(
            id: -3,
            name: 'لا إله إلا الله',
            count: totalCount > 0 ? (totalCount * 0.2).round() : 0,
            percentage: 20,
          ),
          DhikrStats(
            id: -4,
            name: 'الله أكبر',
            count: totalCount > 0 ? (totalCount * 0.1).round() : 0,
            percentage: 10,
          ),
        ];
      }

      // تحديد الذكر الأكثر استخداماً
      DhikrStats mostUsedDhikr;
      if (dhikrStats.isNotEmpty) {
        // ترتيب الأذكار حسب العدد تنازلياً
        dhikrStats.sort((a, b) => b.count.compareTo(a.count));
        mostUsedDhikr = dhikrStats.first;
      } else {
        mostUsedDhikr = DhikrStats(
          id: -1,
          name: mostUsedDhikrName,
          count: totalCount,
          percentage: 100,
        );
      }

      // تحميل إحصائيات الأيام
      final dailyStatsJson = prefs.getString(_dailyStatsKey);
      List<DailyStats> dailyStats = [];

      if (dailyStatsJson != null) {
        final List<dynamic> dailyStatsList = jsonDecode(dailyStatsJson);
        dailyStats = dailyStatsList.map((e) => DailyStats.fromMap(e)).toList();
      } else {
        // إنشاء إحصائيات افتراضية للأيام (آخر 7 أيام)
        final now = DateTime.now();
        for (int i = 6; i >= 0; i--) {
          final date = DateTime(now.year, now.month, now.day - i);
          final count =
              i == 0 ? totalCount : (totalCount / 7 * (7 - i)).round();

          dailyStats.add(DailyStats(
            date: date,
            count: count,
            sessions: (count / 33).round(),
            usageTime: (count / 10).round(),
            topDhikr: mostUsedDhikr,
          ));
        }
      }

      // حساب السلسلة الحالية وأطول سلسلة
      int currentStreak = 0;
      int longestStreak = 0;
      int tempStreak = 0;

      // ترتيب الإحصائيات اليومية حسب التاريخ تصاعدياً
      dailyStats.sort((a, b) => a.date.compareTo(b.date));

      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      DateTime? lastDate;

      for (final stat in dailyStats) {
        final statDate =
            DateTime(stat.date.year, stat.date.month, stat.date.day);

        if (lastDate != null) {
          final difference = statDate.difference(lastDate).inDays;

          if (difference == 1) {
            // يوم متتالي
            tempStreak++;
          } else {
            // انقطاع السلسلة
            if (tempStreak > longestStreak) {
              longestStreak = tempStreak;
            }
            tempStreak = 1;
          }
        } else {
          tempStreak = 1;
        }

        lastDate = statDate;
      }

      // تحديث أطول سلسلة
      if (tempStreak > longestStreak) {
        longestStreak = tempStreak;
      }

      // تحديث السلسلة الحالية
      if (lastDate != null) {
        final difference = today.difference(lastDate).inDays;

        if (difference == 0) {
          // اليوم الحالي
          currentStreak = tempStreak;
        } else if (difference == 1) {
          // آخر استخدام كان بالأمس
          currentStreak = 0;
        } else {
          // انقطاع السلسلة
          currentStreak = 0;
        }
      }

      // حساب متوسط التسبيحات اليومية
      int averageDailyCount = 0;
      if (dailyStats.isNotEmpty) {
        final totalDailyCount =
            dailyStats.fold<int>(0, (sum, stat) => sum + stat.count);
        averageDailyCount = (totalDailyCount / dailyStats.length).round();
      }

      // تحديد أفضل يوم
      DailyStats? bestDay;
      if (dailyStats.isNotEmpty) {
        bestDay = dailyStats.reduce((a, b) => a.count > b.count ? a : b);
      }

      // تحميل تاريخ آخر استخدام
      final lastUsedDateMillis = prefs.getInt(_lastUsedDateKey);
      final lastUsedDate = lastUsedDateMillis != null
          ? DateTime.fromMillisecondsSinceEpoch(lastUsedDateMillis)
          : DateTime.now();

      // تحميل إجمالي وقت الاستخدام
      final totalUsageTime = prefs.getInt(_usageTimeKey) ?? 0;

      // حساب عدد الأيام النشطة
      final activeDaysCount = dailyStats.length;

      // حساب معدل التسبيحات في الساعة
      double hourlyRate = 0.0;
      if (totalUsageTime > 0) {
        hourlyRate = (totalCount / totalUsageTime * 60).toDouble();
      }

      // حساب معدل النمو
      double growthRate = 0.0;
      if (dailyStats.length >= 14) {
        // نحتاج على الأقل أسبوعين من البيانات
        // ترتيب الإحصائيات حسب التاريخ
        final sortedStats = List<DailyStats>.from(dailyStats);
        sortedStats.sort((a, b) => a.date.compareTo(b.date));

        // الأسبوع الحالي
        final currentWeekStats = sortedStats.sublist(sortedStats.length - 7);
        final currentWeekCount =
            currentWeekStats.fold<int>(0, (sum, stat) => sum + stat.count);

        // الأسبوع السابق
        final previousWeekStats = sortedStats.sublist(
            sortedStats.length - 14, sortedStats.length - 7);
        final previousWeekCount =
            previousWeekStats.fold<int>(0, (sum, stat) => sum + stat.count);

        if (previousWeekCount > 0) {
          growthRate = ((currentWeekCount - previousWeekCount) /
              previousWeekCount *
              100);
        }
      }

      // تحميل الإنجازات
      List<Achievement> achievements = [];
      final achievementsJson = prefs.getString(_achievementsKey);

      if (achievementsJson != null) {
        final List<dynamic> achievementsList = jsonDecode(achievementsJson);
        achievements =
            achievementsList.map((e) => Achievement.fromMap(e)).toList();
      } else {
        // إنشاء إنجازات افتراضية
        achievements = _createDefaultAchievements(
            totalCount, currentStreak, longestStreak);
      }

      // تحديث حالة الإنجازات
      achievements = _updateAchievementsProgress(
          achievements, totalCount, currentStreak, longestStreak);

      // حفظ الإنجازات المحدثة
      await prefs.setString(_achievementsKey,
          jsonEncode(achievements.map((e) => e.toMap()).toList()));

      // إنشاء نموذج الإحصائيات
      _stats = TasbihStatsModel(
        totalCount: totalCount,
        sessionCount: sessionCount,
        mostUsedDhikr: mostUsedDhikr,
        dhikrStats: dhikrStats,
        dailyStats: dailyStats,
        longestStreak: longestStreak,
        currentStreak: currentStreak,
        averageDailyCount: averageDailyCount,
        bestDay: bestDay,
        lastUsedDate: lastUsedDate,
        totalUsageTime: totalUsageTime,
        hourlyRate: hourlyRate,
        activeDaysCount: activeDaysCount,
        growthRate: growthRate,
        achievements: achievements,
      );

      _isLoading = false;
      // إشعار المستمعين بعد اكتمال تحميل البيانات
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات المسبحة: $e');
      _isLoading = false;
      // إشعار المستمعين بحدوث خطأ
      notifyListeners();
    }
  }

  // تحديث الإحصائيات عند استخدام المسبحة
  Future<void> updateStats({
    required int count,
    required int sessionCount,
    required DhikrModel dhikr,
    required int usageTime,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now();

      // تحديث الإحصائيات الأساسية
      final totalCount = (prefs.getInt('totalCount') ?? 0) + count;
      await prefs.setInt('totalCount', totalCount);
      await prefs.setInt('sessionCount', sessionCount);

      // تحديث تاريخ آخر استخدام
      await prefs.setInt(_lastUsedDateKey, now.millisecondsSinceEpoch);

      // تحديث إجمالي وقت الاستخدام
      final totalUsageTime = (prefs.getInt(_usageTimeKey) ?? 0) + usageTime;
      await prefs.setInt(_usageTimeKey, totalUsageTime);

      // تحديث إحصائيات الذكر
      List<DhikrStats> dhikrStats = [];
      final dhikrStatsJson = prefs.getString(_dhikrStatsKey);

      if (dhikrStatsJson != null) {
        final List<dynamic> dhikrStatsList = jsonDecode(dhikrStatsJson);
        dhikrStats = dhikrStatsList.map((e) => DhikrStats.fromMap(e)).toList();
      }

      // البحث عن الذكر في القائمة
      final dhikrIndex = dhikrStats.indexWhere((e) => e.id == dhikr.id);

      if (dhikrIndex != -1) {
        // تحديث إحصائيات الذكر الموجود
        final updatedDhikrStats = DhikrStats(
          id: dhikr.id,
          name: dhikr.name,
          count: dhikrStats[dhikrIndex].count + count,
          percentage: 0, // سيتم حسابها لاحقاً
        );

        dhikrStats[dhikrIndex] = updatedDhikrStats;
      } else {
        // إضافة إحصائيات الذكر الجديد
        dhikrStats.add(DhikrStats(
          id: dhikr.id,
          name: dhikr.name,
          count: count,
          percentage: 0, // سيتم حسابها لاحقاً
        ));
      }

      // حساب النسب المئوية
      final totalDhikrCount =
          dhikrStats.fold<int>(0, (sum, stat) => sum + stat.count);

      dhikrStats = dhikrStats.map((stat) {
        return DhikrStats(
          id: stat.id,
          name: stat.name,
          count: stat.count,
          percentage: (stat.count / totalDhikrCount * 100).roundToDouble(),
        );
      }).toList();

      // ترتيب الأذكار حسب العدد تنازلياً
      dhikrStats.sort((a, b) => b.count.compareTo(a.count));

      // تحديث الذكر الأكثر استخداماً
      if (dhikrStats.isNotEmpty) {
        await prefs.setString('mostUsedDhikr', dhikrStats.first.name);
      }

      // حفظ إحصائيات الأذكار
      await prefs.setString(_dhikrStatsKey,
          jsonEncode(dhikrStats.map((e) => e.toMap()).toList()));

      // تحديث إحصائيات اليوم
      List<DailyStats> dailyStats = [];
      final dailyStatsJson = prefs.getString(_dailyStatsKey);

      if (dailyStatsJson != null) {
        final List<dynamic> dailyStatsList = jsonDecode(dailyStatsJson);
        dailyStats = dailyStatsList.map((e) => DailyStats.fromMap(e)).toList();
      }

      // البحث عن إحصائيات اليوم الحالي
      final today = DateTime(now.year, now.month, now.day);
      final todayIndex = dailyStats.indexWhere((e) {
        final statDate = DateTime(e.date.year, e.date.month, e.date.day);
        return statDate.isAtSameMomentAs(today);
      });

      if (todayIndex != -1) {
        // تحديث إحصائيات اليوم الحالي
        final currentDailyStats = dailyStats[todayIndex];

        final updatedDailyStats = DailyStats(
          date: today,
          count: currentDailyStats.count + count,
          sessions: currentDailyStats.sessions + 1,
          usageTime: currentDailyStats.usageTime + usageTime,
          topDhikr: dhikrStats.isNotEmpty ? dhikrStats.first : null,
        );

        dailyStats[todayIndex] = updatedDailyStats;
      } else {
        // إضافة إحصائيات اليوم الجديد
        dailyStats.add(DailyStats(
          date: today,
          count: count,
          sessions: 1,
          usageTime: usageTime,
          topDhikr: dhikrStats.isNotEmpty ? dhikrStats.first : null,
        ));
      }

      // الاحتفاظ بإحصائيات آخر 30 يوم فقط
      if (dailyStats.length > 30) {
        dailyStats.sort((a, b) => b.date.compareTo(a.date));
        dailyStats = dailyStats.sublist(0, 30);
      }

      // حفظ إحصائيات الأيام
      await prefs.setString(_dailyStatsKey,
          jsonEncode(dailyStats.map((e) => e.toMap()).toList()));

      // إعادة تحميل الإحصائيات
      await loadStats();
    } catch (e) {
      debugPrint('خطأ في تحديث إحصائيات المسبحة: $e');
    }
  }

  // إعادة ضبط الإحصائيات
  Future<void> resetStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // حذف الإحصائيات
      await prefs.remove('totalCount');
      await prefs.remove('sessionCount');
      await prefs.remove('mostUsedDhikr');
      await prefs.remove(_dhikrStatsKey);
      await prefs.remove(_dailyStatsKey);
      await prefs.remove(_usageTimeKey);
      await prefs.remove(_lastUsedDateKey);
      await prefs.remove(_achievementsKey);

      // إعادة تهيئة الإحصائيات
      _stats = TasbihStatsModel.empty();

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في إعادة ضبط إحصائيات المسبحة: $e');
    }
  }

  // مشاركة الإحصائيات
  Future<void> shareStats() async {
    try {
      // الحصول على عدد الإنجازات المكتملة
      final completedAchievements =
          _stats.achievements.where((a) => a.isCompleted).length;
      final totalAchievements = _stats.achievements.length;

      // الحصول على أسماء الأذكار الثلاثة الأولى
      String topDhikrs = '';
      if (_stats.dhikrStats.isNotEmpty) {
        final sortedDhikrs = List<DhikrStats>.from(_stats.dhikrStats);
        sortedDhikrs.sort((a, b) => b.count.compareTo(a.count));

        final top3 = sortedDhikrs.take(3).toList();
        for (int i = 0; i < top3.length; i++) {
          topDhikrs += '${i + 1}. ${top3[i].name} (${top3[i].count} مرة)\n';
        }
      }

      final String statsText = '''
إحصائيات المسبحة الخاصة بي من تطبيق وهج السالك:

📊 إجمالي التسبيحات: ${_stats.totalCount}
🔄 عدد الدورات: ${_stats.sessionCount}
📈 متوسط التسبيحات اليومية: ${_stats.averageDailyCount}
🔥 السلسلة الحالية: ${_stats.currentStreak} يوم
🏆 أطول سلسلة: ${_stats.longestStreak} يوم
⏱ إجمالي وقت الاستخدام: ${_formatTime(_stats.totalUsageTime)}
📝 الذكر الأكثر استخداماً: ${_stats.mostUsedDhikr.name} (${_stats.mostUsedDhikr.count} مرة)
📊 معدل الساعة: ${_stats.hourlyRate.toStringAsFixed(1)} تسبيحة/ساعة
📅 عدد الأيام النشطة: ${_stats.activeDaysCount} يوم
🌟 الإنجازات: $completedAchievements من $totalAchievements

أفضل الأذكار:
$topDhikrs
قم بتحميل تطبيق وهج السالك واستمتع بالتسبيح!
''';

      await Share.share(statsText);
    } catch (e) {
      debugPrint('خطأ في مشاركة إحصائيات المسبحة: $e');
    }
  }

  // تنسيق الوقت (بالدقائق) إلى ساعات ودقائق
  String _formatTime(int minutes) {
    final hours = minutes ~/ 60;
    final remainingMinutes = minutes % 60;

    if (hours > 0) {
      return '$hours ساعة و $remainingMinutes دقيقة';
    } else {
      return '$remainingMinutes دقيقة';
    }
  }

  // إنشاء الإنجازات الافتراضية
  List<Achievement> _createDefaultAchievements(
      int totalCount, int currentStreak, int longestStreak) {
    return [
      // إنجازات العدد الإجمالي
      Achievement(
        id: 'total_100',
        title: 'بداية الطريق',
        description: 'أكمل 100 تسبيحة',
        icon: '0xe24e', // emoji_events
        isCompleted: totalCount >= 100,
        completedDate: totalCount >= 100 ? DateTime.now() : null,
        progress: totalCount >= 100 ? 1.0 : totalCount / 100,
      ),
      Achievement(
        id: 'total_1000',
        title: 'المثابرة',
        description: 'أكمل 1000 تسبيحة',
        icon: '0xe24e', // emoji_events
        isCompleted: totalCount >= 1000,
        completedDate: totalCount >= 1000 ? DateTime.now() : null,
        progress: totalCount >= 1000 ? 1.0 : totalCount / 1000,
      ),
      Achievement(
        id: 'total_10000',
        title: 'المتفاني',
        description: 'أكمل 10000 تسبيحة',
        icon: '0xe24e', // emoji_events
        isCompleted: totalCount >= 10000,
        completedDate: totalCount >= 10000 ? DateTime.now() : null,
        progress: totalCount >= 10000 ? 1.0 : totalCount / 10000,
      ),

      // إنجازات السلسلة
      Achievement(
        id: 'streak_3',
        title: 'المواظبة',
        description: 'استخدم المسبحة لمدة 3 أيام متتالية',
        icon: '0xe1a6', // local_fire_department
        isCompleted: longestStreak >= 3,
        completedDate: longestStreak >= 3 ? DateTime.now() : null,
        progress: longestStreak >= 3 ? 1.0 : longestStreak / 3,
      ),
      Achievement(
        id: 'streak_7',
        title: 'المداومة',
        description: 'استخدم المسبحة لمدة أسبوع كامل متتالي',
        icon: '0xe1a6', // local_fire_department
        isCompleted: longestStreak >= 7,
        completedDate: longestStreak >= 7 ? DateTime.now() : null,
        progress: longestStreak >= 7 ? 1.0 : longestStreak / 7,
      ),
      Achievement(
        id: 'streak_30',
        title: 'الملتزم',
        description: 'استخدم المسبحة لمدة شهر كامل متتالي',
        icon: '0xe1a6', // local_fire_department
        isCompleted: longestStreak >= 30,
        completedDate: longestStreak >= 30 ? DateTime.now() : null,
        progress: longestStreak >= 30 ? 1.0 : longestStreak / 30,
      ),
    ];
  }

  // تحديث حالة الإنجازات
  List<Achievement> _updateAchievementsProgress(
    List<Achievement> achievements,
    int totalCount,
    int currentStreak,
    int longestStreak,
  ) {
    return achievements.map((achievement) {
      switch (achievement.id) {
        // إنجازات العدد الإجمالي
        case 'total_100':
          return achievement.copyWith(
            isCompleted: totalCount >= 100,
            completedDate: totalCount >= 100 && !achievement.isCompleted
                ? DateTime.now()
                : achievement.completedDate,
            progress: totalCount >= 100 ? 1.0 : totalCount / 100,
          );
        case 'total_1000':
          return achievement.copyWith(
            isCompleted: totalCount >= 1000,
            completedDate: totalCount >= 1000 && !achievement.isCompleted
                ? DateTime.now()
                : achievement.completedDate,
            progress: totalCount >= 1000 ? 1.0 : totalCount / 1000,
          );
        case 'total_10000':
          return achievement.copyWith(
            isCompleted: totalCount >= 10000,
            completedDate: totalCount >= 10000 && !achievement.isCompleted
                ? DateTime.now()
                : achievement.completedDate,
            progress: totalCount >= 10000 ? 1.0 : totalCount / 10000,
          );

        // إنجازات السلسلة
        case 'streak_3':
          return achievement.copyWith(
            isCompleted: longestStreak >= 3,
            completedDate: longestStreak >= 3 && !achievement.isCompleted
                ? DateTime.now()
                : achievement.completedDate,
            progress: longestStreak >= 3 ? 1.0 : longestStreak / 3,
          );
        case 'streak_7':
          return achievement.copyWith(
            isCompleted: longestStreak >= 7,
            completedDate: longestStreak >= 7 && !achievement.isCompleted
                ? DateTime.now()
                : achievement.completedDate,
            progress: longestStreak >= 7 ? 1.0 : longestStreak / 7,
          );
        case 'streak_30':
          return achievement.copyWith(
            isCompleted: longestStreak >= 30,
            completedDate: longestStreak >= 30 && !achievement.isCompleted
                ? DateTime.now()
                : achievement.completedDate,
            progress: longestStreak >= 30 ? 1.0 : longestStreak / 30,
          );

        default:
          return achievement;
      }
    }).toList();
  }
}
