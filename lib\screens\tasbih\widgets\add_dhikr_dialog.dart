import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:wahaj_alsaalik/screens/tasbih/models/dhikr_model.dart';
import 'package:wahaj_alsaalik/screens/tasbih/models/wird_model.dart';
import 'package:wahaj_alsaalik/screens/tasbih/providers/tasbih_provider.dart';
import 'package:wahaj_alsaalik/screens/tasbih/providers/wird_provider.dart';
import 'package:wahaj_alsaalik/screens/tasbih/utils/tasbih_colors.dart';

/// نموذج إضافة ذكر جديد إلى الورد
/// تم إعادة تصميمه بالكامل لتجنب مشاكل إدارة الحالة
class AddDhikrDialog extends StatefulWidget {
  final WirdModel wird;

  const AddDhikrDialog({
    Key? key,
    required this.wird,
  }) : super(key: key);

  @override
  State<AddDhikrDialog> createState() => _AddDhikrDialogState();
}

class _AddDhikrDialogState extends State<AddDhikrDialog> {
  // متغيرات الحالة
  bool _isCustomDhikr = false;
  String _targetCountText = '33';
  DhikrModel? _selectedDhikr;
  final TextEditingController _customDhikrController = TextEditingController();
  final TextEditingController _targetCountController =
      TextEditingController(text: '33');
  final FocusNode _targetCountFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // تهيئة القيم الافتراضية
    _targetCountController.text = _targetCountText;

    // الحصول على قائمة الأذكار
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final tasbihProvider =
          Provider.of<TasbihProvider>(context, listen: false);

      // التحقق من وجود الأذكار الأساسية وإضافتها إذا لم تكن موجودة
      await _ensureDefaultDhikrsExist(tasbihProvider);

      final dhikrs = tasbihProvider.dhikrs;

      if (dhikrs.isNotEmpty) {
        setState(() {
          _selectedDhikr = dhikrs.first;
          _targetCountText =
              (_selectedDhikr!.count > 0 ? _selectedDhikr!.count : 33)
                  .toString();
          _targetCountController.text = _targetCountText;
        });
      }
    });
  }

  // التأكد من وجود الأذكار الأساسية
  Future<void> _ensureDefaultDhikrsExist(TasbihProvider provider) async {
    try {
      // التحقق من وجود الأذكار الأساسية
      final dhikrs = provider.dhikrs;
      final hasDefaultDhikrs = dhikrs.any((dhikr) => dhikr.isDefault);

      // إذا لم تكن هناك أذكار أساسية، قم بإضافتها
      if (!hasDefaultDhikrs || dhikrs.isEmpty) {
        debugPrint('لا توجد أذكار أساسية، جاري إضافتها...');
        await provider.resetDefaultDhikrs();
        debugPrint('تم إضافة الأذكار الأساسية بنجاح');
      } else {
        debugPrint(
            'الأذكار الأساسية موجودة بالفعل: ${dhikrs.where((d) => d.isDefault).length} ذكر أساسي');

        // طباعة الأذكار الأساسية للتحقق
        for (var dhikr in dhikrs.where((d) => d.isDefault)) {
          debugPrint('ذكر أساسي: ${dhikr.name}, المعرف: ${dhikr.id}');
        }
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود الأذكار الأساسية: $e');
    }
  }

  @override
  void dispose() {
    _customDhikrController.dispose();
    _targetCountController.dispose();
    _targetCountFocusNode.dispose();
    super.dispose();
  }

  // زيادة العدد المستهدف
  void _incrementTargetCount() {
    final currentCount = int.tryParse(_targetCountText) ?? 33;
    final newCount = currentCount + 1;
    setState(() {
      _targetCountText = newCount.toString();
      _targetCountController.text = _targetCountText;
    });
  }

  // تقليل العدد المستهدف
  void _decrementTargetCount() {
    final currentCount = int.tryParse(_targetCountText) ?? 33;
    if (currentCount > 1) {
      final newCount = currentCount - 1;
      setState(() {
        _targetCountText = newCount.toString();
        _targetCountController.text = _targetCountText;
      });
    }
  }

  // إضافة الذكر إلى الورد
  Future<void> _addDhikrToWird() async {
    // إخفاء لوحة المفاتيح
    FocusScope.of(context).unfocus();

    // الحصول على مزودي البيانات
    final tasbihProvider = Provider.of<TasbihProvider>(context, listen: false);
    final wirdProvider = Provider.of<WirdProvider>(context, listen: false);

    // قراءة العدد المستهدف من حقل الإدخال
    int targetCount = 33;
    try {
      final parsedCount = int.tryParse(_targetCountController.text.trim());
      if (parsedCount != null && parsedCount > 0) {
        targetCount = parsedCount;
      }
    } catch (e) {
      debugPrint('خطأ في قراءة العدد المستهدف: $e');
    }

    try {
      if (_isCustomDhikr) {
        // التحقق من إدخال نص الذكر
        final customDhikrText = _customDhikrController.text.trim();
        if (customDhikrText.isEmpty) {
          _showErrorMessage('الرجاء إدخال نص الذكر');
          return;
        }

        // عرض رسالة للمستخدم
        if (mounted) {
          _showLoadingMessage('جاري إضافة الذكر الجديد...');
        }

        // إضافة الذكر الجديد إلى قائمة الأذكار
        final success =
            await tasbihProvider.addNewDhikr(customDhikrText, targetCount);

        if (!success || !mounted) {
          if (mounted) {
            _showErrorMessage('حدث خطأ أثناء إضافة الذكر الجديد');
          }
          return;
        }

        // البحث عن الذكر الجديد المضاف
        final newDhikr = tasbihProvider.dhikrs.firstWhere(
          (dhikr) => dhikr.name == customDhikrText,
          orElse: () => tasbihProvider.dhikrs.first,
        );

        // إغلاق الحوار
        if (mounted) {
          Navigator.of(context).pop();
        }

        // إضافة الذكر إلى الورد
        await wirdProvider.addWirdItem(widget.wird.id, newDhikr, targetCount);

        // إعادة تحميل الأوراد
        await wirdProvider.reloadWirds();

        // عرض رسالة نجاح
        if (mounted) {
          _showSuccessMessage(
              'تم إضافة الذكر "${newDhikr.name}" إلى الورد بنجاح');
        }
      } else {
        // التحقق من اختيار ذكر
        if (_selectedDhikr == null) {
          _showErrorMessage('الرجاء اختيار ذكر');
          return;
        }

        // إغلاق الحوار
        if (mounted) {
          Navigator.of(context).pop();
        }

        // إضافة الذكر إلى الورد
        await wirdProvider.addWirdItem(
            widget.wird.id, _selectedDhikr!, targetCount);

        // إعادة تحميل الأوراد
        await wirdProvider.reloadWirds();

        // عرض رسالة نجاح
        if (mounted) {
          _showSuccessMessage(
              'تم إضافة الذكر "${_selectedDhikr!.name}" إلى الورد بنجاح');
        }
      }
    } catch (e) {
      debugPrint('خطأ في إضافة الذكر: $e');
      if (mounted) {
        _showErrorMessage('حدث خطأ أثناء إضافة الذكر: $e');
      }
    }
  }

  // عرض رسالة خطأ
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  // عرض رسالة تحميل
  void _showLoadingMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  // عرض رسالة نجاح
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: TasbihColors.primary,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  // بناء شريحة اختيار سريع للذكر (عرض النص العربي فقط)
  Widget _buildQuickSelectChip(DhikrModel dhikr, bool isDarkMode) {
    final isSelected = _selectedDhikr?.id == dhikr.id;
    // استخدام النص العربي إذا كان متوفراً، وإلا استخدام الاسم
    final displayText =
        dhikr.arabicText.isNotEmpty ? dhikr.arabicText : dhikr.name;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedDhikr = dhikr;
          _targetCountText = (dhikr.count > 0 ? dhikr.count : 33).toString();
          _targetCountController.text = _targetCountText;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected == true
              ? TasbihColors.primary
              : isDarkMode
                  ? TasbihColors.darkCardColor
                  : Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected == true
                ? TasbihColors.primary
                : TasbihColors.primary.withAlpha(100),
            width: 1,
          ),
          boxShadow: isSelected == true
              ? [
                  BoxShadow(
                    color: TasbihColors.primary.withAlpha(50),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  )
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isSelected == true)
              const Padding(
                padding: EdgeInsets.only(left: 4),
                child: Icon(
                  Icons.check_circle,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            Flexible(
              child: Text(
                displayText, // استخدام النص العربي
                style: TextStyle(
                  color: isSelected == true
                      ? Colors.white
                      : isDarkMode
                          ? Colors.white
                          : TasbihColors.primary,
                  fontWeight:
                      isSelected == true ? FontWeight.bold : FontWeight.normal,
                  fontSize: 16, // زيادة حجم الخط قليلاً
                ),
                overflow: TextOverflow.ellipsis,
                textDirection:
                    TextDirection.rtl, // ضمان اتجاه النص من اليمين إلى اليسار
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final tasbihProvider = Provider.of<TasbihProvider>(context);
    // فرز الأذكار: الأذكار الافتراضية أولاً ثم الأذكار المخصصة
    final dhikrs = tasbihProvider.dhikrs;
    final sortedDhikrs = [...dhikrs];
    sortedDhikrs.sort((a, b) {
      // الأذكار الافتراضية أولاً
      if (a.isDefault && !b.isDefault) return -1;
      if (!a.isDefault && b.isDefault) return 1;
      // ترتيب الأذكار الافتراضية حسب المعرف
      if (a.isDefault && b.isDefault) return a.id.compareTo(b.id);
      // ترتيب الأذكار المخصصة حسب الاسم
      return a.name.compareTo(b.name);
    });

    return DefaultTabController(
      length: 2,
      initialIndex: _isCustomDhikr ? 1 : 0,
      child: AlertDialog(
        backgroundColor: isDarkMode ? TasbihColors.darkCardColor : Colors.white,
        title: Text(
          'إضافة ذكر',
          style: TextStyle(
            color: isDarkMode ? Colors.white : Colors.black87,
          ),
        ),
        contentPadding: const EdgeInsets.all(16.0),
        content: Container(
          width: double.maxFinite,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // علامات التبويب
                Container(
                  decoration: BoxDecoration(
                    color: TasbihColors.primary.withAlpha(25),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: TabBar(
                    indicator: BoxDecoration(
                      color: TasbihColors.primary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    labelColor: Colors.white,
                    unselectedLabelColor: TasbihColors.primary,
                    tabs: const [
                      Tab(text: 'اختر ذكراً'),
                      Tab(text: 'إضافة ذكر جديد'),
                    ],
                    onTap: (index) {
                      setState(() {
                        _isCustomDhikr = index == 1;
                      });
                    },
                  ),
                ),
                const SizedBox(height: 16),

                // محتوى التبويب
                Container(
                  // استخدام ارتفاع ديناميكي بدلاً من ارتفاع ثابت
                  constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.5,
                    minHeight: 300,
                  ),
                  child: TabBarView(
                    physics: const NeverScrollableScrollPhysics(),
                    children: [
                      // قائمة اختيار الذكر مع الأذكار الأساسية
                      SingleChildScrollView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // قسم الأذكار الأساسية
                              Container(
                                width: double.infinity,
                                margin: const EdgeInsets.only(bottom: 16),
                                decoration: BoxDecoration(
                                  color: isDarkMode
                                      ? TasbihColors.darkCardColor
                                          .withAlpha(150)
                                      : TasbihColors.primary.withAlpha(15),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: TasbihColors.primary.withAlpha(50),
                                    width: 1,
                                  ),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // عنوان قسم الأذكار الأساسية
                                    Padding(
                                      padding: const EdgeInsets.all(12.0),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              const Icon(
                                                Icons.star_rounded,
                                                color: TasbihColors.primary,
                                                size: 20,
                                              ),
                                              const SizedBox(width: 8),
                                              Text(
                                                'الأذكار الأساسية',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 16,
                                                  color: isDarkMode
                                                      ? Colors.white
                                                      : TasbihColors.primary,
                                                ),
                                              ),
                                              const SizedBox(width: 8),
                                              Container(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 8,
                                                        vertical: 2),
                                                decoration: BoxDecoration(
                                                  color: TasbihColors.primary
                                                      .withAlpha(50),
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                ),
                                                child: Text(
                                                  '${sortedDhikrs.where((dhikr) => dhikr.isDefault).length}',
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                    color: isDarkMode
                                                        ? Colors.white
                                                        : TasbihColors.primary,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            'اضغط على أي ذكر لاختياره وإضافته إلى الورد',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: isDarkMode
                                                  ? Colors.grey[400]
                                                  : Colors.grey[600],
                                              fontStyle: FontStyle.italic,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),

                                    // قائمة الأذكار الأساسية
                                    Padding(
                                      padding:
                                          const EdgeInsets.only(bottom: 12.0),
                                      child: sortedDhikrs
                                              .where((dhikr) => dhikr.isDefault)
                                              .isEmpty
                                          ? Center(
                                              child: Column(
                                                children: [
                                                  const Icon(
                                                    Icons.info_outline,
                                                    color: Colors.amber,
                                                    size: 24,
                                                  ),
                                                  const SizedBox(height: 8),
                                                  Text(
                                                    'لا توجد أذكار أساسية. سيتم إضافتها في المرة القادمة.',
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      color: isDarkMode
                                                          ? Colors.grey[300]
                                                          : Colors.grey[700],
                                                      fontStyle:
                                                          FontStyle.italic,
                                                    ),
                                                    textAlign: TextAlign.center,
                                                  ),
                                                  const SizedBox(height: 8),
                                                  ElevatedButton(
                                                    onPressed: () {
                                                      // الحصول على مزود TasbihProvider
                                                      final provider = Provider
                                                          .of<TasbihProvider>(
                                                              context,
                                                              listen: false);

                                                      // عرض مؤشر تحميل بسيط
                                                      _showLoadingMessage(
                                                          'جاري إضافة الأذكار الأساسية...');

                                                      // استدعاء دالة إعادة تعيين الأذكار الافتراضية
                                                      provider
                                                          .resetDefaultDhikrs()
                                                          .then((_) {
                                                        // عرض رسالة نجاح
                                                        _showSuccessMessage(
                                                            'تم إضافة الأذكار الأساسية بنجاح');

                                                        // إعادة بناء الواجهة
                                                        if (mounted) {
                                                          setState(() {});
                                                        }
                                                      }).catchError((error) {
                                                        // عرض رسالة خطأ
                                                        _showErrorMessage(
                                                            'حدث خطأ: $error');
                                                      });
                                                    },
                                                    style: ElevatedButton
                                                        .styleFrom(
                                                      backgroundColor:
                                                          TasbihColors.primary,
                                                      foregroundColor:
                                                          Colors.white,
                                                    ),
                                                    child: const Text(
                                                        'إضافة الأذكار الأساسية'),
                                                  ),
                                                ],
                                              ),
                                            )
                                          : Wrap(
                                              spacing: 8,
                                              runSpacing: 8,
                                              alignment: WrapAlignment.center,
                                              children: sortedDhikrs
                                                  .where((dhikr) =>
                                                      dhikr.isDefault)
                                                  .map((dhikr) =>
                                                      _buildQuickSelectChip(
                                                          dhikr, isDarkMode))
                                                  .toList(),
                                            ),
                                    ),
                                  ],
                                ),
                              ),

                              // عرض معلومات الذكر المحدد (النص العربي فقط)
                              if (_selectedDhikr != null &&
                                  _selectedDhikr!.isDefault)
                                Container(
                                  width: double.infinity,
                                  margin: const EdgeInsets.only(bottom: 16),
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: isDarkMode
                                        ? TasbihColors.darkCardColor
                                            .withAlpha(100)
                                        : Colors.grey[50],
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: TasbihColors.primary.withAlpha(30),
                                      width: 1,
                                    ),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      if (_selectedDhikr!
                                              .arabicText.isNotEmpty &&
                                          _selectedDhikr!.arabicText !=
                                              _selectedDhikr!.name)
                                        Text(
                                          _selectedDhikr!.arabicText,
                                          style: TextStyle(
                                            fontSize: 20,
                                            fontWeight: FontWeight.bold,
                                            color: isDarkMode
                                                ? Colors.white
                                                : Colors.black87,
                                          ),
                                          textAlign: TextAlign.center,
                                          textDirection: TextDirection.rtl,
                                        ),
                                    ],
                                  ),
                                ),

                              // القائمة المنسدلة لجميع الأذكار
                              DropdownButtonFormField<DhikrModel>(
                                decoration: InputDecoration(
                                  labelText: 'اختر من جميع الأذكار',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12.0),
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 16.0, vertical: 16.0),
                                  filled: true,
                                  fillColor: isDarkMode
                                      ? TasbihColors.darkCardColor
                                      : Colors.white,
                                  prefixIcon: const Icon(Icons.search,
                                      color: TasbihColors.primary),
                                ),
                                value: _selectedDhikr,
                                hint: const Text('اختر ذكراً من القائمة'),
                                isExpanded: true,
                                icon: const Icon(Icons.arrow_drop_down,
                                    color: TasbihColors.primary),
                                items: sortedDhikrs.map((dhikr) {
                                  return DropdownMenuItem<DhikrModel>(
                                    value: dhikr,
                                    child: Row(
                                      children: [
                                        // أيقونة للأذكار الافتراضية
                                        if (dhikr.isDefault)
                                          const Padding(
                                            padding: EdgeInsets.only(left: 8.0),
                                            child: Icon(
                                              Icons.star,
                                              color: TasbihColors.primary,
                                              size: 16,
                                            ),
                                          ),
                                        Expanded(
                                          child: Text(
                                            // استخدام النص العربي إذا كان متوفراً، وإلا استخدام الاسم
                                            dhikr.arabicText.isNotEmpty
                                                ? dhikr.arabicText
                                                : dhikr.name,
                                            overflow: TextOverflow.ellipsis,
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: dhikr.isDefault
                                                  ? FontWeight.bold
                                                  : FontWeight.normal,
                                            ),
                                            textDirection: TextDirection
                                                .rtl, // ضمان اتجاه النص من اليمين إلى اليسار
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _selectedDhikr = value;
                                      _targetCountText =
                                          (value.count > 0 ? value.count : 33)
                                              .toString();
                                      _targetCountController.text =
                                          _targetCountText;
                                    });
                                  }
                                },
                                dropdownColor: isDarkMode
                                    ? TasbihColors.darkCardColor
                                    : Colors.white,
                              ),
                            ],
                          ),
                        ),
                      ),

                      // مربع نص لإضافة ذكر جديد
                      SingleChildScrollView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8.0, vertical: 4.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              TextFormField(
                                controller: _customDhikrController,
                                decoration: InputDecoration(
                                  labelText: 'نص الذكر الجديد',
                                  hintText: 'اكتب نص الذكر الجديد هنا',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12.0),
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 16.0, vertical: 12.0),
                                  filled: true,
                                  fillColor: isDarkMode
                                      ? TasbihColors.darkCardColor
                                      : Colors.white,
                                ),
                                // إضافة خصائص لتحسين التعامل مع لوحة المفاتيح
                                textInputAction: TextInputAction.done,
                                maxLines: 2,
                                minLines: 1,
                                onEditingComplete: () {
                                  // إخفاء لوحة المفاتيح عند الانتهاء
                                  FocusScope.of(context).unfocus();
                                },
                              ),
                              const SizedBox(height: 6),
                              Text(
                                'اضغط على زر "إضافة إلى الورد" لإضافة الذكر الجديد',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: isDarkMode
                                      ? Colors.grey[300]
                                      : Colors.grey[600],
                                  fontStyle: FontStyle.italic,
                                ),
                                textAlign: TextAlign.center,
                              )
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // العدد المستهدف
                const Text('العدد المستهدف',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                Row(
                  children: [
                    IconButton(
                      onPressed: _decrementTargetCount,
                      icon: const Icon(Icons.remove_circle_outline),
                      color: TasbihColors.primary,
                    ),
                    Expanded(
                      child: TextField(
                        textAlign: TextAlign.center,
                        keyboardType: TextInputType.number,
                        controller: _targetCountController,
                        focusNode: _targetCountFocusNode,
                        // تحسين خصائص الإدخال
                        textInputAction: TextInputAction.done,
                        style: const TextStyle(
                            fontSize: 16, fontWeight: FontWeight.bold),
                        decoration: InputDecoration(
                          border: const OutlineInputBorder(),
                          contentPadding:
                              const EdgeInsets.symmetric(vertical: 8),
                          filled: true,
                          fillColor: isDarkMode
                              ? TasbihColors.darkCardColor
                              : Colors.white,
                          // إضافة تلميح للمستخدم
                          hintText: '33',
                        ),
                        onChanged: (value) {
                          // تخزين القيمة فقط، بدون تحديث الحالة
                          _targetCountText = value;
                        },
                        onEditingComplete: () {
                          // تحديث الحالة عند الانتهاء من التحرير
                          final parsedCount = int.tryParse(_targetCountText);
                          if (parsedCount != null && parsedCount > 0) {
                            setState(() {
                              _targetCountText = parsedCount.toString();
                              _targetCountController.text = _targetCountText;
                            });
                          } else {
                            // إعادة القيمة الافتراضية إذا كانت القيمة غير صالحة
                            setState(() {
                              _targetCountText = '33';
                              _targetCountController.text = _targetCountText;
                            });
                          }
                          // إخفاء لوحة المفاتيح
                          _targetCountFocusNode.unfocus();
                        },
                      ),
                    ),
                    IconButton(
                      onPressed: _incrementTargetCount,
                      icon: const Icon(Icons.add_circle_outline),
                      color: TasbihColors.primary,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'إلغاء',
              style: TextStyle(
                color: isDarkMode ? Colors.white : TasbihColors.primary,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: _addDhikrToWird,
            style:
                ElevatedButton.styleFrom(backgroundColor: TasbihColors.primary),
            child: const Text(
              'إضافة إلى الورد',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// دالة مساعدة لعرض حوار إضافة ذكر
Future<void> showAddDhikrDialog(BuildContext context, WirdModel wird) async {
  try {
    // استخدام showDialog مع خيارات محسنة
    return showDialog(
      context: context,
      barrierDismissible: true,
      // استخدام useSafeArea للتأكد من عدم تداخل الحوار مع لوحة المفاتيح
      useSafeArea: true,
      // استخدام RouteSettings لتحسين التوافق
      routeSettings: const RouteSettings(name: 'add_dhikr_dialog'),
      builder: (BuildContext dialogContext) {
        return Material(
          type: MaterialType.transparency,
          child: AddDhikrDialog(wird: wird),
        );
      },
    );
  } catch (e) {
    debugPrint('خطأ في عرض حوار إضافة الذكر: $e');
    // استخدام طريقة بديلة في حالة حدوث خطأ
    return showDialog(
      context: context,
      builder: (context) => AddDhikrDialog(wird: wird),
    );
  }
}
