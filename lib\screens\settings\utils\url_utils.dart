//  إنشاء ملف للأدوات المساعدة (فتح الروابط)

import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class UrlUtils {
  // تشفير معلمات الاستعلام
  static String encodeQueryParameters(Map<String, String> params) {
    return params.entries
        .map((e) =>
            '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');
  }

  // فتح الرابط بشكل آمن
  static Future<void> launchUrlSafely(Uri url, [BuildContext? context]) async {
    // حفظ مرجع للسياق قبل العملية غير المتزامنة
    final scaffoldMessenger =
        context != null ? ScaffoldMessenger.of(context) : null;

    try {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } catch (e) {
      if (scaffoldMessenger != null) {
        scaffoldMessenger.showSnackBar(
          SnackBar(content: Text('لا يمكن فتح الرابط: ${url.toString()}')),
        );
      }
    }
  }
}
