import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/app_colors.dart';

class FavoritesAppBar extends StatelessWidget {
  final bool isSearching;
  final bool isScrolled;
  final TextEditingController searchController;
  final VoidCallback onSearchToggle;
  final VoidCallback onSortPressed;

  const FavoritesAppBar({
    Key? key,
    required this.isSearching,
    required this.isScrolled,
    required this.searchController,
    required this.onSearchToggle,
    required this.onSortPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return SliverAppBar(
      expandedHeight: isSearching
          ? 0
          : ResponsiveHelper.isTablet(context)
              ? 220.0
              : 200.0,
      pinned: true,
      floating: true,
      stretch: true,
      elevation: 0,
      backgroundColor: theme.scaffoldBackgroundColor,
      title: isSearching
          ? _buildSearchField()
          : RepaintBoundary(
              child: AnimatedOpacity(
                opacity: isScrolled ? 1.0 : 0.0,
                // تقليل مدة الرسوم المتحركة لتحسين الأداء
                duration: const Duration(milliseconds: 200),
                child: Text(
                  'المفضلة',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: Colors.amber,
                    fontWeight: FontWeight.bold,
                    // تبسيط الظلال لتحسين الأداء
                    shadows: [
                      Shadow(
                        color: Colors.amber.withAlpha(40),
                        // تقليل قيمة التمويه لتحسين الأداء
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                ),
              ),
            ),
      actions: [
        IconButton(
          icon: Icon(
            isSearching ? Icons.close : Icons.search,
            color: Colors.amber,
          ),
          onPressed: () {
            HapticFeedback.selectionClick();
            onSearchToggle();
          },
          tooltip: isSearching ? 'إغلاق البحث' : 'بحث',
        ),
        IconButton(
          icon: const Icon(
            Icons.sort,
            color: Colors.amber,
          ),
          onPressed: () {
            HapticFeedback.selectionClick();
            onSortPressed();
          },
          tooltip: 'ترتيب',
        ),
      ],
      flexibleSpace: isSearching
          ? null
          : FlexibleSpaceBar(
              background: Stack(
                children: [
                  // خلفية متدرجة محسنة
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          isDark
                              ? Colors.amber.withAlpha(40) // 0.15 * 255 = ~38
                              : Colors.amber.withAlpha(30), // 0.12 * 255 = ~30
                          theme.scaffoldBackgroundColor,
                        ],
                        stops: const [0.4, 1.0],
                      ),
                    ),
                  ),
                  // زخرفة إسلامية متحركة - متوافقة مع اتجاه اللغة العربية - مُحسّنة للأداء
                  Positioned(
                    top: -20,
                    right: -20, // نحافظ على الموضع من اليمين للغة العربية
                    child: RepaintBoundary(
                      child: TweenAnimationBuilder<double>(
                        tween: Tween<double>(begin: 0.0, end: 1.0),
                        // تقليل مدة الرسوم المتحركة لتحسين الأداء
                        duration: const Duration(milliseconds: 800),
                        // استخدام منحنى أبسط لتحسين الأداء
                        curve: Curves.easeOut,
                        builder: (context, value, child) {
                          return Opacity(
                            opacity: isDark ? 0.15 * value : 0.1 * value,
                            child: Transform.rotate(
                              // تقليل مدى الدوران لتحسين الأداء
                              angle: (1 - value) * 0.1,
                              child: child,
                            ),
                          );
                        },
                        child: SvgPicture.asset(
                          'assets/images/p2.svg',
                          width: 200,
                          height: 200,
                          colorFilter: const ColorFilter.mode(
                            Colors.amber,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),
                  ),
                  // تحسين واجهة العنوان - متوافق مع اتجاه اللغة العربية
                  Positioned(
                    bottom: 20,
                    right: 20, // تغيير الموضع من اليسار إلى اليمين
                    child: Column(
                      crossAxisAlignment:
                          CrossAxisAlignment.end, // تغيير المحاذاة إلى اليمين
                      textDirection: TextDirection
                          .rtl, // تحديد اتجاه العمود من اليمين إلى اليسار
                      children: [
                        // شارة محسنة
                        Container(
                          margin: const EdgeInsets.only(bottom: 8),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 6),
                          decoration: BoxDecoration(
                            color: AppColors.favoritesColor,
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.favoritesColor.withAlpha(100),
                                blurRadius: 10,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            textDirection: TextDirection
                                .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                            children: [
                              Icon(
                                Icons.bookmark,
                                color: Colors.white,
                                size: 14,
                              ),
                              SizedBox(width: 6),
                              Text(
                                'محفوظاتك',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                textDirection: TextDirection
                                    .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                textAlign:
                                    TextAlign.right, // محاذاة النص إلى اليمين
                              ),
                            ],
                          ),
                        ),
                        // عنوان محسن
                        Row(
                          textDirection: TextDirection
                              .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                          children: [
                            Container(
                              width: 4,
                              height: 24,
                              decoration: BoxDecoration(
                                color: AppColors.favoritesColor,
                                borderRadius: BorderRadius.circular(2),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.favoritesColor
                                        .withAlpha(77), // 0.3 * 255 = ~77
                                    blurRadius: 6,
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 8),
                            ShaderMask(
                              shaderCallback: (bounds) {
                                return LinearGradient(
                                  colors: [
                                    AppColors.favoritesColor,
                                    isDark ? Colors.white : Colors.black87,
                                  ],
                                  begin: Alignment
                                      .topRight, // تغيير اتجاه التدرج ليتناسب مع اللغة العربية
                                  end: Alignment.bottomLeft,
                                ).createShader(bounds);
                              },
                              child: Text(
                                'المفضلة',
                                style: theme.textTheme.headlineSmall!.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Colors
                                      .white, // سيتم تجاهل هذا اللون بسبب ShaderMask
                                ),
                                textDirection: TextDirection
                                    .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                textAlign:
                                    TextAlign.right, // محاذاة النص إلى اليمين
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 6),
                        // وصف محسن
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 6),
                          decoration: BoxDecoration(
                            color: isDark
                                ? Colors.black.withAlpha(40) // 0.15 * 255 = ~38
                                : Colors.white
                                    .withAlpha(128), // 0.5 * 255 = 128
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            '  الأذكار و الأدعية و الصلاة على النبي المفضلة لديك',
                            style: TextStyle(
                              color: AppColors.favoritesColor
                                  .withAlpha(230), // 0.9 * 255 = ~230
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                            textDirection: TextDirection
                                .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                            textAlign:
                                TextAlign.right, // محاذاة النص إلى اليمين
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildSearchField() {
    return TextField(
      controller: searchController,
      autofocus: true,
      textDirection: TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
      textAlign: TextAlign.right, // محاذاة النص إلى اليمين
      decoration: InputDecoration(
        hintText: 'ابحث في المفضلة...',
        border: InputBorder.none,
        hintStyle: TextStyle(color: Colors.grey[400]),
        hintTextDirection:
            TextDirection.rtl, // تحديد اتجاه النص التلميحي من اليمين إلى اليسار
        contentPadding: const EdgeInsets.symmetric(horizontal: 16),
      ),
      style: const TextStyle(
        color: Colors.amber,
        fontSize: 16.0,
      ),
      cursorColor: Colors.amber,
    );
  }
}
