import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/poem.dart';
import '../services/database_helper.dart';
import '../services/native_share_service.dart';
import '../utils/app_colors.dart';

class PoemDetailScreen extends StatefulWidget {
  final Poem poem;

  const PoemDetailScreen({
    Key? key,
    required this.poem,
  }) : super(key: key);

  @override
  State<PoemDetailScreen> createState() => _PoemDetailScreenState();
}

class _PoemDetailScreenState extends State<PoemDetailScreen> {
  late Poem _poem;
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  bool _isFavorite = false;
  bool _isLoading = false; // Variable para controlar el estado de carga
  double _fontSize = 18.0;

  @override
  void initState() {
    super.initState();
    _poem = widget.poem;
    _isFavorite = _poem.isFavorite;
  }

  // تبديل حالة المفضلة
  Future<void> _toggleFavorite() async {
    try {
      // تعطيل التفاعل أثناء المعالجة
      setState(() {
        _isLoading = true;
      });

      // تحديث حالة المفضلة فورًا لتحسين تجربة المستخدم
      final bool newFavoriteState = !_isFavorite;

      // استخدام معرف القصيدة الأصلي مباشرة
      dynamic poemId = _poem.id;
      debugPrint('استخدام معرف القصيدة الأصلي: $poemId');

      // محاولة تحويل المعرف إلى رقم إذا كان نصياً
      try {
        if (poemId is String) {
          poemId = int.parse(poemId);
        }
      } catch (e) {
        // إذا فشل التحويل، استخدم المعرف كما هو
        debugPrint('لا يمكن تحويل المعرف إلى رقم: $poemId');
      }

      // تنفيذ عملية قاعدة البيانات
      if (!newFavoriteState) {
        // إذا كانت الحالة الجديدة غير مفضلة
        await _databaseHelper.removeFavorite(poemId, 'poem');
        debugPrint('تمت إزالة القصيدة من المفضلة: $poemId');
      } else {
        // إذا كانت الحالة الجديدة مفضلة
        await _databaseHelper.addFavorite(poemId, 'poem');
        debugPrint('تمت إضافة القصيدة إلى المفضلة: $poemId');
      }

      // تحديث حالة المفضلة
      setState(() {
        _isFavorite = newFavoriteState;
        _isLoading = false;

        // تحديث كائن القصيدة أيضًا لضمان تحديث الحالة في كل مكان
        _poem = _poem.copyWith(isFavorite: _isFavorite);
      });

      // إرسال إشعار للمستخدم
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(newFavoriteState
                ? 'تمت الإضافة إلى المفضلة'
                : 'تمت الإزالة من المفضلة'),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            duration: const Duration(seconds: 1), // تقليل مدة الإشعار
          ),
        );
      }

      // إرسال إشعار لتحديث صفحة القصائد وصفحة المفضلة
      // يمكن استخدام آلية أكثر تقدمًا مثل Provider أو BLoC لاحقًا
    } catch (e) {
      // إعادة تعيين حالة التحميل في حالة الخطأ
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    }
  }

  // مشاركة القصيدة
  void _sharePoem() async {
    HapticFeedback.lightImpact();
    await NativeShareService.sharePoem(_poem);
  }

  // تغيير حجم الخط
  void _changeFontSize(double size) {
    setState(() {
      _fontSize = size;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      body: CustomScrollView(
        physics: const BouncingScrollPhysics(),
        slivers: [
          // شريط التطبيق
          SliverAppBar(
            expandedHeight: 200.0,
            pinned: true,
            stretch: true,
            backgroundColor: isDarkMode
                ? AppColors.poemsColor.withAlpha(50)
                : AppColors.poemsColor.withAlpha(20),
            flexibleSpace: FlexibleSpaceBar(
              title: Text(
                _poem.title,
                style: TextStyle(
                  color: isDarkMode ? Colors.white : AppColors.poemsColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
              background: Stack(
                fit: StackFit.expand,
                children: [
                  // خلفية متدرجة
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          AppColors.poemsColor.withAlpha(isDarkMode ? 100 : 50),
                          isDarkMode
                              ? Colors.black.withAlpha(200)
                              : Colors.white.withAlpha(200),
                        ],
                      ),
                    ),
                  ),

                  // زخرفة إسلامية
                  const Positioned(
                    top: 0,
                    right: 0,
                    child: Opacity(
                      opacity: 0.1,
                      child: Icon(
                        Icons.auto_awesome,
                        size: 150,
                        color: AppColors.poemsColor,
                      ),
                    ),
                  ),

                  // معلومات الشاعر
                  Positioned(
                    bottom: 60,
                    right: 20,
                    child: Row(
                      children: [
                        Icon(
                          Icons.person,
                          size: 16,
                          color: isDarkMode
                              ? Colors.white70
                              : AppColors.poemsColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _poem.poet,
                          style: TextStyle(
                            fontSize: 14,
                            color: isDarkMode
                                ? Colors.white70
                                : AppColors.poemsColor,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // معلومات العصر
                  Positioned(
                    bottom: 60,
                    left: 20,
                    child: Row(
                      children: [
                        Icon(
                          Icons.history,
                          size: 16,
                          color: isDarkMode
                              ? Colors.white70
                              : AppColors.poemsColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _poem.era,
                          style: TextStyle(
                            fontSize: 14,
                            color: isDarkMode
                                ? Colors.white70
                                : AppColors.poemsColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              // زر المفضلة
              _isLoading
                  ? Container(
                      padding: const EdgeInsets.all(8),
                      width: 40,
                      height: 40,
                      child: const CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : IconButton(
                      icon: Icon(
                        _isFavorite ? Icons.favorite : Icons.favorite_border,
                        color: _isFavorite ? Colors.red : Colors.white,
                      ),
                      onPressed: _toggleFavorite,
                      tooltip: _isFavorite
                          ? 'إزالة من المفضلة'
                          : 'إضافة إلى المفضلة',
                    ),

              // زر المشاركة
              IconButton(
                icon: const Icon(
                  Icons.share,
                  color: Colors.white,
                ),
                onPressed: _sharePoem,
                tooltip: 'مشاركة',
              ),
            ],
          ),

          // أدوات التحكم في حجم الخط
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconButton(
                    icon: const Icon(Icons.remove),
                    onPressed: () =>
                        _changeFontSize((_fontSize - 2).clamp(14.0, 30.0)),
                    tooltip: 'تصغير الخط',
                  ),
                  Text(
                    'حجم الخط',
                    style: TextStyle(
                      fontSize: 16,
                      color: theme.textTheme.bodyMedium?.color,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.add),
                    onPressed: () =>
                        _changeFontSize((_fontSize + 2).clamp(14.0, 30.0)),
                    tooltip: 'تكبير الخط',
                  ),
                ],
              ),
            ),
          ),

          // محتوى القصيدة
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 24),
              child: Card(
                elevation: 2,
                shadowColor: AppColors.poemsColor.withAlpha(50),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // عنوان القصيدة
                      Text(
                        _poem.title,
                        style: TextStyle(
                          fontSize: _fontSize + 4,
                          fontWeight: FontWeight.bold,
                          color: AppColors.poemsColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),

                      // اسم الشاعر
                      Text(
                        _poem.poet,
                        style: TextStyle(
                          fontSize: _fontSize - 2,
                          color:
                              theme.textTheme.bodyMedium?.color?.withAlpha(180),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),

                      // أبيات القصيدة
                      if (_poem.verses.isNotEmpty)
                        ..._poem.verses
                            .map((verse) => Padding(
                                  padding: const EdgeInsets.only(bottom: 16),
                                  child: Text(
                                    verse,
                                    style: TextStyle(
                                      fontSize: _fontSize,
                                      height: 1.8,
                                      color: theme.textTheme.bodyLarge?.color,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ))
                            .toList()
                      else
                        Padding(
                          padding: const EdgeInsets.only(bottom: 16),
                          child: Text(
                            _poem.content.isNotEmpty
                                ? _poem.content
                                : 'لا يوجد محتوى للقصيدة',
                            style: TextStyle(
                              fontSize: _fontSize,
                              height: 1.8,
                              color: theme.textTheme.bodyLarge?.color,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // معلومات إضافية
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 32),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // عنوان القسم
                  Row(
                    children: [
                      Container(
                        width: 4,
                        height: 20,
                        decoration: BoxDecoration(
                          color: AppColors.poemsColor,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        'معلومات إضافية',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.poemsColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // بطاقة المعلومات
                  Card(
                    elevation: 1,
                    shadowColor: AppColors.poemsColor.withAlpha(30),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          // الفئة
                          Row(
                            children: [
                              const Icon(
                                Icons.category,
                                size: 20,
                                color: AppColors.poemsColor,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'الفئة:',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: theme.textTheme.titleMedium?.color,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                _poem.category,
                                style: TextStyle(
                                  fontSize: 16,
                                  color: theme.textTheme.bodyMedium?.color,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),

                          // العصر
                          Row(
                            children: [
                              const Icon(
                                Icons.history,
                                size: 20,
                                color: AppColors.poemsColor,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'العصر:',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: theme.textTheme.titleMedium?.color,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                _poem.era,
                                style: TextStyle(
                                  fontSize: 16,
                                  color: theme.textTheme.bodyMedium?.color,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),

                          // عدد الأبيات
                          Row(
                            children: [
                              const Icon(
                                Icons.format_list_numbered,
                                size: 20,
                                color: AppColors.poemsColor,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'عدد الأبيات:',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: theme.textTheme.titleMedium?.color,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                '${_poem.verses.length}',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: theme.textTheme.bodyMedium?.color,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),

      // زر الإجراءات السريعة
      floatingActionButton: FloatingActionButton(
        onPressed: _sharePoem,
        backgroundColor: AppColors.poemsColor,
        tooltip: 'مشاركة القصيدة',
        child: const Icon(Icons.share),
      ),
    );
  }
}
