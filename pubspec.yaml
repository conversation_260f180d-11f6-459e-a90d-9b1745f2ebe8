name: wa<PERSON><PERSON>_al<PERSON><PERSON>k
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'  # تعديل نطاق SDK لتجنب مشاكل التوافق

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  go_router: ^6.0.0
  flutter_pdfview: ^1.3.1
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  # Database
  sqflite: ^2.3.2
  path: ^1.9.0

  # State management
  provider: ^6.1.2

  # UI components
  # google_fonts: ^6.2.1  # تم استبداله بالخطوط المحلية
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.3.1

  # Utilities
  shared_preferences: ^2.2.2
  share_plus: ^7.2.2
  intl: ^0.19.0
  path_provider: ^2.1.2
  # المكتبات المطلوب إضافتها
  url_launcher: ^6.2.4
  package_info_plus: ^4.2.0
  # الاشعارات - مكتبة واحدة كافية
  #awesome_notifications: ^0.9.2
  timezone: ^0.10.0
  vibration: ^1.7.6
  # مكتبة الرسوم البيانية
  fl_chart: ^0.65.0
  flutter_local_notifications: ^19.1.0
  # مكتبة الرسوم المتحركة
  lottie: ^2.7.0



dev_dependencies:
  flutter_test:
    sdk: flutter



  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/data/
    - assets/data/daily_wisdom.json
    - assets/pdf/
    - assets/images/covers/
    - assets/sounds/
    - assets/animations/
    - assets/icons/
    - assets/icons/azkar/
    - assets/icons/duas/
    - assets/icons/prophet_prayers/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font.
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500

    - family: Amiri
      fonts:
        - asset: assets/fonts/Amiri-Regular.ttf
        - asset: assets/fonts/Amiri-Bold.ttf
          weight: 700
        - asset: assets/fonts/Amiri-Italic.ttf
          style: italic
        - asset: assets/fonts/Amiri-BoldItalic.ttf
          weight: 700
          style: italic

    - family: Tajawal
      fonts:
        - asset: assets/fonts/Tajawal-Regular.ttf
        - asset: assets/fonts/Tajawal-Bold.ttf
          weight: 700
        - asset: assets/fonts/Tajawal-Light.ttf
          weight: 300
        - asset: assets/fonts/Tajawal-Medium.ttf
          weight: 500

    - family: Almarai
      fonts:
        - asset: assets/fonts/Almarai-Regular.ttf
        - asset: assets/fonts/Almarai-Bold.ttf
          weight: 700
        - asset: assets/fonts/Almarai-Light.ttf
          weight: 300
        - asset: assets/fonts/Almarai-ExtraBold.ttf
          weight: 800

    - family: ScheherazadeNew
      fonts:
        - asset: assets/fonts/ScheherazadeNew-Regular.ttf
        - asset: assets/fonts/ScheherazadeNew-Bold.ttf
          weight: 700

    - family: NotoKufiArabic
      fonts:
        - asset: assets/fonts/NotoKufiArabic-Regular.ttf
        - asset: assets/fonts/NotoKufiArabic-Bold.ttf
          weight: 700
        - asset: assets/fonts/NotoKufiArabic-Medium.ttf
          weight: 500

    - family: Changa
      fonts:
        - asset: assets/fonts/Changa-Regular.ttf
        - asset: assets/fonts/Changa-Bold.ttf
          weight: 700
        - asset: assets/fonts/Changa-Light.ttf
          weight: 300
        - asset: assets/fonts/Changa-Medium.ttf
          weight: 500

    - family: ElMessiri
      fonts:
        - asset: assets/fonts/ElMessiri-Regular.ttf
        - asset: assets/fonts/ElMessiri-Bold.ttf
          weight: 700
        - asset: assets/fonts/ElMessiri-Medium.ttf
          weight: 500
        - asset: assets/fonts/ElMessiri-SemiBold.ttf
          weight: 600

  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

