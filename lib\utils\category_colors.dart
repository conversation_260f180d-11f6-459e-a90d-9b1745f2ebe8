import 'package:flutter/material.dart';

/// فئة لإدارة ألوان تصنيفات الكتب
class CategoryColors {
  // قاموس يربط بين اسم التصنيف واللون المخصص له
  static final Map<String, Color> _colors = {
    'الفقه': const Color(0xFF4CAF50), // أخضر
    'التفسير': const Color(0xFF2196F3), // أزرق
    'الحديث': const Color(0xFF9C27B0), // بنفسجي
    'العقيدة': const Color(0xFFFF9800), // برتقالي
    'السيرة': const Color(0xFF795548), // بني
    'اللغة العربية': const Color(0xFF00BCD4), // سماوي
    'أدب': const Color(0xFFE91E63), // وردي
    'تاريخ': const Color(0xFF607D8B), // رمادي أزرق
  };

  /// الحصول على لون معين لتصنيف
  /// إذا لم يكن هناك لون محدد للتصنيف، يتم إرجاع اللون الافتراضي
  static Color getColorForCategory(String category) {
    return _colors[category] ?? const Color(0xFF9E9E9E); // رمادي افتراضي
  }

  /// الحصول على لون نص مناسب (أبيض أو أسود) بناءً على خلفية اللون
  static Color getTextColorForBackground(Color backgroundColor) {
    // حساب اللون الأنسب للنص (أسود أو أبيض) بناءً على سطوع الخلفية
    int luminance = ((0.299 * backgroundColor.r) +
            (0.587 * backgroundColor.g) +
            (0.114 * backgroundColor.b))
        .round();
    return luminance > 125 ? Colors.black : Colors.white;
  }

  /// الحصول على لون فاتح من لون التصنيف
  static Color getLightColorForCategory(String category) {
    final baseColor = getColorForCategory(category);
    return baseColor.withAlpha(51); // 0.2 * 255 = 51
  }

  /// الحصول على لون داكن من لون التصنيف
  static Color getDarkColorForCategory(String category) {
    final baseColor = getColorForCategory(category);
    // HSLColor يسمح بتعديل فاتحية اللون بسهولة
    final hslColor = HSLColor.fromColor(baseColor);
    return hslColor
        .withLightness((hslColor.lightness - 0.2).clamp(0.0, 1.0))
        .toColor();
  }

  /// الحصول على تدرج لوني لتصنيف معين
  static LinearGradient getGradientForCategory(String category) {
    final baseColor = getColorForCategory(category);
    final darkColor = getDarkColorForCategory(category);

    return LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [baseColor, darkColor],
    );
  }
}
