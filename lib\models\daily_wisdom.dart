import 'dart:convert';
import 'dart:math';
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter/material.dart' show debugPrint;

class DailyWisdom {
  final String text;
  final String author;
  final String? explanation; // إضافة حقل الشرح كحقل اختياري

  DailyWisdom({
    required this.text,
    required this.author,
    this.explanation,
  });

  factory DailyWisdom.fromJson(Map<String, dynamic> json) {
    return DailyWisdom(
      text: json['text'] as String,
      author: json['author'] as String,
      explanation:
          json['explanation'] as String?, // قراءة الشرح من JSON إذا كان موجوداً
    );
  }

  static Future<DailyWisdom> getRandomQuote() async {
    try {
      final String jsonString =
          await rootBundle.loadString('assets/data/daily_wisdom.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      final List<dynamic> quotes = jsonData['quotes'] as List<dynamic>;

      final random = Random();
      final randomIndex = random.nextInt(quotes.length);

      return DailyWisdom.fromJson(quotes[randomIndex]);
    } catch (e) {
      debugPrint('خطأ في تحميل الحكمة العشوائية: $e');
      // إرجاع حكمة افتراضية في حالة حدوث خطأ
      return DailyWisdom(
        text: 'كن مع الله يكن معك',
        author: 'حكمة إسلامية',
      );
    }
  }

  // Get all wisdom quotes
  static Future<List<DailyWisdom>> getAllQuotes() async {
    try {
      final String jsonString =
          await rootBundle.loadString('assets/data/daily_wisdom.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      final List<dynamic> quotesData = jsonData['quotes'] as List<dynamic>;

      return quotesData
          .map((quoteData) => DailyWisdom.fromJson(quoteData))
          .toList();
    } catch (e) {
      debugPrint('خطأ في تحميل الحكم: $e');
      // إرجاع قائمة افتراضية في حالة حدوث خطأ
      return [
        DailyWisdom(
          text: 'كن مع الله يكن معك',
          author: 'حكمة إسلامية',
        ),
        DailyWisdom(
          text: 'العلم ما نفع، وليس ما حُفظ',
          author: 'الإمام الشافعي',
        ),
      ];
    }
  }
}
