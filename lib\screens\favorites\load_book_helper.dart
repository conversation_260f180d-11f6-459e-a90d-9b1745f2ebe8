import 'package:flutter/material.dart';
import '../../database/database_helper.dart';
import '../../models/book.dart';
import '../../utils/constants.dart';

/// Clase auxiliar para cargar y navegar a los detalles de un libro desde favoritos
class LoadBookHelper {
  /// Carga un libro por su ID y navega a la pantalla de detalles
  static Future<void> loadAndNavigateToBook(
    BuildContext context,
    String bookId,
    Function(bool) setLoading,
    Function(String) showErrorMessage,
  ) async {
    try {
      setLoading(true);
      
      debugPrint('جاري تحميل الكتاب بالمعرف: $bookId');

      final dbHelper = DatabaseHelper();
      final bookData = await dbHelper.getBookById(bookId);

      setLoading(false);

      if (bookData != null) {
        debugPrint('تم العثور على الكتاب: ${bookData['title']}');
        
        // استخراج الوسوم إذا كانت موجودة
        List<String> tags = [];
        if (bookData['tags'] != null &&
            bookData['tags'].toString().isNotEmpty) {
          tags = bookData['tags'].toString().split(', ');
        }

        // محاولة تحويل المعرف إلى رقم
        int bookIdInt;
        try {
          // إذا كان المعرف يبدأ بـ 'b'، قم بإزالتها
          String idToConvert = bookId;
          if (idToConvert.startsWith('b')) {
            idToConvert = idToConvert.substring(1);
          }
          bookIdInt = int.parse(idToConvert);
        } catch (e) {
          bookIdInt = 0;
          debugPrint('خطأ في تحويل معرف الكتاب إلى رقم: $e');
        }

        // إنشاء كائن الكتاب
        final book = Book(
          id: bookIdInt,
          title: bookData['title'] ?? '',
          author: bookData['author'] ?? '',
          description: bookData['description'] ?? '',
          coverUrl: bookData['cover_url'] ?? '',
          localCoverPath: bookData['local_cover_path'],
          category: bookData['category'] ?? '',
          pdfUrl: bookData['pdf_url'] ?? '',
          pdfPath: bookData['pdf_path'],
          localPdfPath: bookData['local_pdf_path'],
          pages: bookData['pages'] ?? 0,
          tags: tags,
          isFavorite: true, // لأنه من المفضلة
        );
        
        debugPrint('الانتقال إلى صفحة تفاصيل الكتاب: ${book.title}');
        Navigator.of(context)
            .pushNamed(AppConstants.bookDetailsRoute, arguments: book);
      } else {
        debugPrint('لم يتم العثور على الكتاب بالمعرف: $bookId');
        
        // محاولة الحصول على الكتب من قاعدة البيانات
        final allBooks = await dbHelper.getBooks();
        debugPrint('تم الحصول على ${allBooks.length} كتاب من قاعدة البيانات');
        
        // محاولة تحويل المعرف إلى رقم
        int? bookIdInt;
        try {
          // إذا كان المعرف يبدأ بـ 'b'، قم بإزالتها
          String idToConvert = bookId;
          if (idToConvert.startsWith('b')) {
            idToConvert = idToConvert.substring(1);
          }
          bookIdInt = int.parse(idToConvert);
          debugPrint('تم تحويل معرف الكتاب إلى رقم: $bookIdInt');
        } catch (e) {
          debugPrint('خطأ في تحويل معرف الكتاب إلى رقم: $e');
        }
        
        // البحث عن الكتاب بالمعرف بعدة طرق
        Book? matchingBook;
        
        // البحث بالمعرف الأصلي
        matchingBook = allBooks
            .where((book) => book.id.toString() == bookId)
            .firstOrNull;
            
        // البحث بالمعرف الرقمي
        if (matchingBook == null && bookIdInt != null) {
          matchingBook = allBooks
              .where((book) => book.id == bookIdInt)
              .firstOrNull;
        }
        
        // البحث بالمعرف مع إضافة 'b'
        if (matchingBook == null && !bookId.startsWith('b')) {
          matchingBook = allBooks
              .where((book) => book.id.toString() == 'b$bookId')
              .firstOrNull;
        }
        
        // البحث بالمعرف بدون 'b'
        if (matchingBook == null && bookId.startsWith('b')) {
          String idWithoutB = bookId.substring(1);
          matchingBook = allBooks
              .where((book) => book.id.toString() == idWithoutB)
              .firstOrNull;
        }
        
        // البحث بالمقارنة الرقمية بعد إزالة 'b'
        if (matchingBook == null && bookIdInt != null) {
          matchingBook = allBooks
              .where((book) {
                try {
                  String bookIdStr = book.id.toString();
                  if (bookIdStr.startsWith('b')) {
                    bookIdStr = bookIdStr.substring(1);
                  }
                  int numericBookId = int.parse(bookIdStr);
                  return numericBookId == bookIdInt;
                } catch (e) {
                  return false;
                }
              })
              .firstOrNull;
        }
        
        if (matchingBook != null) {
          debugPrint('تم العثور على الكتاب في قائمة الكتب: ${matchingBook.title}');
          // إضافة حالة المفضلة للكتاب
          final bookWithFavorite = matchingBook.copyWith(isFavorite: true);
          Navigator.of(context).pushNamed(AppConstants.bookDetailsRoute,
              arguments: bookWithFavorite);
          return;
        }
        
        // إذا لم يتم العثور على الكتاب، قم بإنشاء كتاب افتراضي
        int defaultId = 0;
        try {
          defaultId = int.parse(bookId.replaceAll('b', ''));
        } catch (e) {
          defaultId = 0;
        }
        
        final defaultBook = Book(
          id: defaultId,
          title: 'كتاب غير متوفر',
          author: 'غير معروف',
          description: 'لم يتم العثور على معلومات الكتاب',
          coverUrl: '',
          category: '',
          isFavorite: true,
        );
        
        debugPrint('الانتقال إلى صفحة تفاصيل الكتاب الافتراضي');
        Navigator.of(context).pushNamed(AppConstants.bookDetailsRoute,
            arguments: defaultBook);
      }
    } catch (e) {
      setLoading(false);
      debugPrint('خطأ في تحميل الكتاب: $e');
      showErrorMessage('حدث خطأ أثناء تحميل الكتاب');
    }
  }
}
