// مكون خرزة المسبحة

import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../utils/tasbih_colors.dart';

class BeadComponent extends StatefulWidget {
  final int index;
  final int count;
  final double position;
  final double size;
  final bool isDarkMode;

  const BeadComponent({
    Key? key,
    required this.index,
    required this.count,
    required this.position,
    required this.size,
    required this.isDarkMode,
  }) : super(key: key);

  @override
  State<BeadComponent> createState() => _BeadComponentState();
}

class _BeadComponentState extends State<BeadComponent>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _opacityAnimation;

  // متغير لتتبع ما إذا كانت الخرزة جديدة (تم إضافتها بعد تغيير العدد المستهدف)
  bool _isNewBead = false;

  // متغير لتتبع ما إذا كانت الخرزة تظهر لأول مرة
  bool _isFirstAppearance = true;

  // تخزين قيم الزاوية والموقع مسبقاً لتحسين الأداء
  late double _angle;
  late double _x;
  late double _y;
  late Color _beadColor;
  late Color _highlightColor;
  late Color _inactiveColor;

  @override
  void initState() {
    super.initState();

    // تهيئة الرسوم المتحركة بمدة أقصر لتحسين الأداء
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600), // تقليل مدة الرسوم المتحركة
    );

    // تبسيط الرسوم المتحركة لتحسين الأداء
    _scaleAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.15)
            .chain(CurveTween(curve: Curves.easeOut)),
        weight: 30,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.15, end: 1.0)
            .chain(CurveTween(curve: Curves.easeOut)),
        weight: 70,
      ),
    ]).animate(_animationController);

    _rotationAnimation = Tween<double>(begin: 0, end: 0.05).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeOut),
        reverseCurve: const Interval(0.5, 1.0, curve: Curves.easeIn),
      ),
    );

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.3, curve: Curves.easeIn),
      ),
    );

    // حساب القيم مسبقاً
    _precalculateValues();

    // تشغيل الرسوم المتحركة إذا كانت الخرزة تظهر لأول مرة
    if (_isFirstAppearance) {
      _animationController.forward();
      _isFirstAppearance = false;
    }
  }

  // حساب القيم مسبقاً لتحسين الأداء
  void _precalculateValues() {
    final radius = widget.size / 2 * 0.85;
    _angle = (360 / widget.count * widget.index - 90) * (math.pi / 180);
    _x = radius * math.cos(_angle);
    _y = radius * math.sin(_angle);

    // اختيار لون الخرزة من الألوان المتاحة
    _beadColor =
        TasbihColors.beadColors[widget.index % TasbihColors.beadColors.length];

    // تحديد لون الخرزة غير النشطة
    _inactiveColor = widget.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!;

    // تحديد لون الإضاءة للخرزة النشطة
    _highlightColor = Color.lerp(_beadColor, Colors.white, 0.3)!;
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(BeadComponent oldWidget) {
    super.didUpdateWidget(oldWidget);

    // إعادة حساب القيم عند تغيير عدد الخرز
    if (oldWidget.count != widget.count) {
      _isNewBead = true;
      _precalculateValues();
      // تشغيل الرسوم المتحركة للخرز الجديدة
      _animationController.forward(from: 0.0);
    } else if (widget.position > oldWidget.position) {
      // تشغيل الرسوم المتحركة عند تنشيط الخرزة
      _animationController.forward(from: 0.0);
    }
  }

  @override
  Widget build(BuildContext context) {
    // استخدام القيم المحسوبة مسبقاً
    return AnimatedPositioned(
      duration: const Duration(milliseconds: 500), // تقليل مدة الرسوم المتحركة
      curve: Curves.easeOut, // استخدام منحنى أبسط
      top: widget.size / 2 + _y - 12,
      left: widget.size / 2 + _x - 12,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          // استخدام _opacityAnimation للخرز الجديدة
          final opacity = _isNewBead ? _opacityAnimation.value : 1.0;

          return Opacity(
            opacity: opacity,
            child: Transform.scale(
              scale: widget.position > 0
                  ? _scaleAnimation.value
                  : (_isNewBead ? _scaleAnimation.value * 0.8 : 1.0),
              child: Transform.rotate(
                angle: widget.position > 0
                    ? _rotationAnimation.value
                    : (_isNewBead ? _rotationAnimation.value * 2 : 0.0),
                child: child,
              ),
            ),
          );
        },
        // تبسيط مكون الخرزة لتحسين الأداء
        child: Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            // استخدام لون بسيط بدلاً من التدرج للخرزات غير النشطة
            color: widget.position > 0 ? null : _inactiveColor,
            // استخدام تدرج أبسط للخرزات النشطة
            gradient: widget.position > 0
                ? RadialGradient(
                    center: const Alignment(-0.3, -0.5),
                    radius: 0.9,
                    colors: [
                      _highlightColor,
                      _beadColor,
                      Color.lerp(_beadColor, Colors.black, 0.2)!,
                    ],
                    stops: const [0.0, 0.5, 1.0],
                  )
                : null,
            // تبسيط الظلال
            boxShadow: widget.position > 0
                ? [
                    BoxShadow(
                      color: _beadColor.withAlpha(100),
                      blurRadius: 6,
                      spreadRadius: 1,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : null,
          ),
        ),
      ),
    );
  }
}
