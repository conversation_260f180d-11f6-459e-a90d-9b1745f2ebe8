import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeService {
  static const String _darkModeKey = 'darkMode';
  static const String _fontTypeKey = 'fontType';
  static const String _fontSizeKey = 'fontSize';

  // قائمة بأنواع الخطوط المتاحة
  static const List<String> availableFonts = [
    'النظام',
    '<PERSON>i',
    'Cairo',
    '<PERSON><PERSON>',
    '<PERSON>jawal',
    'Scheherazade New',
    'Noto Kufi Arabic',
    'Noto Naskh Arabic',
    'Chang<PERSON>',
    'El Messiri',
  ];

  // الحصول على نوع الخط الحالي
  Future<String> getCurrentFont() async {
    final prefs = await SharedPreferences.getInstance();
    final fontIndex = prefs.getInt(_fontTypeKey) ?? 0;
    return availableFonts[fontIndex.clamp(0, availableFonts.length - 1)];
  }

  // تغيير نوع الخط
  Future<void> setFontType(int fontIndex) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(
        _fontTypeKey, fontIndex.clamp(0, availableFonts.length - 1));
  }

  // الحصول على فهرس الخط الحالي
  Future<int> getCurrentFontIndex() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_fontTypeKey) ?? 0;
  }

  // التحقق من حالة السمة الداكنة
  Future<bool> isDarkMode() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_darkModeKey) ?? false;
  }

  // تعيين حالة السمة الداكنة
  Future<void> setDarkMode(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_darkModeKey, value);
  }

  // الحصول على حجم الخط
  Future<double> getFontSize() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getDouble(_fontSizeKey) ?? 16.0;
  }

  // تعيين حجم الخط
  Future<void> setFontSize(double value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble(_fontSizeKey, value);
  }

  // الحصول على سمة التطبيق
  Future<ThemeMode> getThemeMode() async {
    final isDark = await isDarkMode();
    return isDark ? ThemeMode.dark : ThemeMode.light;
  }

  // الحصول على سمة تتضمن الخط المحدد
  ThemeData getTheme(bool isDarkMode, dynamic fontOption, double fontSize) {
    final baseTheme = isDarkMode ? ThemeData.dark() : ThemeData.light();

    // تحويل bool إلى int إذا لزم الأمر
    int fontIndex = 0;
    if (fontOption is bool) {
      // للتوافق الخلفي: true = الخط العربي (الفهرس 1)، false = خط النظام (الفهرس 0)
      fontIndex = fontOption ? 1 : 0;
    } else if (fontOption is int) {
      fontIndex = fontOption;
    }

    // استخدام خط النظام
    if (fontIndex == 0) {
      return baseTheme.copyWith(
        textTheme: baseTheme.textTheme.apply(
          bodyColor: isDarkMode ? Colors.white : Colors.black87,
          displayColor: isDarkMode ? Colors.white : Colors.black,
        ),
      );
    }

    // استخدام الخط المحلي
    final fontName = fontIndex < availableFonts.length
        ? availableFonts[fontIndex]
        : availableFonts[1]; // استخدم الفهرس 1 كافتراضي إذا كان الفهرس غير صالح

    // تحويل اسم الخط إلى اسم عائلة الخط المحلية
    String fontFamily;
    switch (fontName) {
      case 'Amiri':
        fontFamily = 'Amiri';
        break;
      case 'Cairo':
        fontFamily = 'Cairo';
        break;
      case 'Almarai':
        fontFamily = 'Almarai';
        break;
      case 'Tajawal':
        fontFamily = 'Tajawal';
        break;
      case 'Scheherazade New':
        fontFamily = 'ScheherazadeNew';
        break;
      case 'Noto Kufi Arabic':
        fontFamily = 'NotoKufiArabic';
        break;
      case 'Noto Naskh Arabic':
        fontFamily = 'NotoNaskhArabic';
        break;
      case 'Changa':
        fontFamily = 'Changa';
        break;
      case 'El Messiri':
        fontFamily = 'ElMessiri';
        break;
      default:
        fontFamily = 'Tajawal';
    }

    // إنشاء سمة جديدة مع الخط المحلي
    return ThemeData(
      // نسخ الخصائص الأساسية من baseTheme
      brightness: baseTheme.brightness,
      colorScheme: baseTheme.colorScheme,
      primaryColor: baseTheme.primaryColor,
      scaffoldBackgroundColor: baseTheme.scaffoldBackgroundColor,
      cardColor: baseTheme.cardColor,
      dividerColor: baseTheme.dividerColor,

      // تعيين الخط
      fontFamily: fontFamily,

      // تطبيق TextTheme مع الخط المحدد
      textTheme: baseTheme.textTheme.apply(
        fontFamily: fontFamily,
        bodyColor: isDarkMode ? Colors.white : Colors.black87,
        displayColor: isDarkMode ? Colors.white : Colors.black,
      ),
      primaryTextTheme: baseTheme.primaryTextTheme.apply(
        fontFamily: fontFamily,
        bodyColor: isDarkMode ? Colors.white : Colors.black87,
        displayColor: isDarkMode ? Colors.white : Colors.black,
      ),
    );
  }
}
