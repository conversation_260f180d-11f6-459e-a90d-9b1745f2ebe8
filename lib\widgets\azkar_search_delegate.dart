import 'package:flutter/material.dart';
import '../models/zikr.dart';
import '../utils/app_colors.dart';
import 'azkar_card.dart';
import '../screens/azkar_details_screen.dart';
import '../screens/azkar_subcategories_screen.dart';

class AzkarSearchDelegate extends SearchDelegate<Zikr?> {
  final List<Zikr> categories;

  AzkarSearchDelegate(this.categories);

  @override
  String get searchFieldLabel => 'ابحث عن الأذكار...';

  @override
  TextStyle get searchFieldStyle => const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.normal,
      );

  @override
  ThemeData appBarTheme(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return theme.copyWith(
      appBarTheme: AppBarTheme(
        backgroundColor: theme.scaffoldBackgroundColor,
        elevation: 0,
        iconTheme: IconThemeData(color: AppColors.getAzkarColor(isDarkMode)),
        titleTextStyle: TextStyle(
          color: theme.textTheme.bodyLarge?.color,
          fontSize: MediaQuery.of(context).size.width < 360 ? 14 : 16,
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: InputBorder.none,
        hintStyle: TextStyle(
          color: isDarkMode ? Colors.grey.shade500 : Colors.grey.shade400,
        ),
      ),
    );
  }

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      if (query.isNotEmpty)
        IconButton(
          icon: const Icon(Icons.clear),
          onPressed: () {
            query = '';
            showSuggestions(context);
          },
        ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: AnimatedIcon(
        icon: AnimatedIcons.menu_arrow,
        progress: transitionAnimation,
      ),
      onPressed: () {
        close(context, null);
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    final results = _getFilteredCategories();
    final screenSize = MediaQuery.of(context).size;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    if (results.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off_rounded,
              size: screenSize.width * 0.15, // Responsive size
              color: isDarkMode
                  ? const Color(0xFFAAAAAA) /* TasbihColors.darkTextSecondary */
                  : Colors.grey[300],
            ),
            SizedBox(height: screenSize.height * 0.02), // Responsive spacing
            Text(
              'لا توجد نتائج بحث لـ "$query"',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize:
                    screenSize.width < 360 ? 14 : 16, // Responsive font size
                color: isDarkMode
                    ? const Color(
                        0xFFAAAAAA) /* TasbihColors.darkTextSecondary */
                    : Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return Padding(
      padding: EdgeInsets.all(screenSize.width * 0.03),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
          maxCrossAxisExtent: screenSize.width > 600 ? 300 : 200,
          childAspectRatio: screenSize.width > 600 ? 0.85 : 0.75,
          crossAxisSpacing: screenSize.width * 0.03,
          mainAxisSpacing: screenSize.height * 0.02,
        ),
        itemCount: results.length,
        itemBuilder: (context, index) {
          final category = results[index];
          return AzkarCard(
            category: category,
            onTap: () {
              Navigator.pop(context);
              _navigateToCategory(context, category);
            },
          );
        },
      ),
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    if (query.isEmpty) {
      // استخدام LayoutBuilder لتوفير سياق تخطيط صحيح
      return LayoutBuilder(
        builder: (context, constraints) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // قسم اقتراحات البحث
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'اقتراحات البحث',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.getAzkarColor(
                            Theme.of(context).brightness == Brightness.dark),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        _buildSuggestionChip(context, 'أذكار الصباح'),
                        _buildSuggestionChip(context, 'أذكار المساء'),
                        _buildSuggestionChip(context, 'أذكار النوم'),
                        _buildSuggestionChip(context, 'أذكار الصلاة'),
                      ],
                    ),
                  ],
                ),
              ),
              const Divider(),
              // استخدام Flexible بدلاً من Expanded لتجنب مشكلة ParentDataWidget
              Flexible(
                child: ListView.separated(
                  // تحسين الأداء بإضافة خصائص إضافية
                  physics: const BouncingScrollPhysics(),
                  itemCount: categories.length,
                  separatorBuilder: (context, index) =>
                      const Divider(height: 1),
                  itemBuilder: (context, index) {
                    final category = categories[index];
                    return ListTile(
                      leading: CircleAvatar(
                        backgroundColor: AppColors.getAzkarColor(
                                Theme.of(context).brightness == Brightness.dark)
                            .withAlpha(26), // 0.1 * 255 = 26
                        child: Icon(
                          Icons.category_outlined,
                          color: AppColors.getAzkarColor(
                              Theme.of(context).brightness == Brightness.dark),
                        ),
                      ),
                      title: Text(category.name),
                      subtitle: Text(
                        category.description,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      onTap: () {
                        close(context, category);
                        _navigateToCategory(context, category);
                      },
                    );
                  },
                ),
              ),
            ],
          );
        },
      );
    }

    final results = _getFilteredCategories();
    return ListView.builder(
      itemCount: results.length,
      itemBuilder: (context, index) {
        final category = results[index];
        return ListTile(
          leading: CircleAvatar(
            backgroundColor: AppColors.getAzkarColor(
                    Theme.of(context).brightness == Brightness.dark)
                .withAlpha(26), // 0.1 * 255 = 26
            child: Icon(
              Icons.category_outlined,
              color: AppColors.getAzkarColor(
                  Theme.of(context).brightness == Brightness.dark),
            ),
          ),
          title: Text(
            category.name,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          subtitle: Text(
            category.description,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          onTap: () {
            query = category.name;
            showResults(context);
          },
        );
      },
    );
  }

  List<Zikr> _getFilteredCategories() {
    if (query.isEmpty) {
      return categories;
    }

    return categories
        .where((category) =>
            category.name.contains(query) ||
            category.description.contains(query))
        .toList();
  }

  Widget _buildSuggestionChip(BuildContext context, String suggestion) {
    final screenSize = MediaQuery.of(context).size;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return ActionChip(
      avatar: Icon(
        Icons.search,
        size: screenSize.width < 360 ? 14 : 16, // Responsive icon size
        color: AppColors.getAzkarColor(isDarkMode),
      ),
      label: Text(
        suggestion,
        style: TextStyle(
          fontSize: screenSize.width < 360 ? 12 : 14, // Responsive font size
        ),
      ),
      onPressed: () {
        query = suggestion;
        showResults(context);
      },
      backgroundColor: isDarkMode
          ? AppColors.getAzkarColor(isDarkMode).withAlpha(51) // 0.2 * 255 = 51
          : AppColors.getAzkarColor(isDarkMode).withAlpha(26), // 0.1 * 255 = 26
      padding: EdgeInsets.symmetric(
        horizontal: screenSize.width * 0.02, // Responsive padding
        vertical: screenSize.height * 0.005, // Responsive padding
      ),
    );
  }

  void _navigateToCategory(BuildContext context, Zikr category) {
    if (category.hasSubcategories && category.subcategories != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AzkarSubcategoriesScreen(
            mainCategory: category.name,
            subcategories: category.subcategories!,
          ),
        ),
      );
    } else {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AzkarDetailsScreen(
            category: category.name,
            azkarItems: category.items,
          ),
        ),
      );
    }
  }
}
