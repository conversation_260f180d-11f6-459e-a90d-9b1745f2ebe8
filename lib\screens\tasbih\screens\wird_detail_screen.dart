import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/tasbih_provider.dart';
import '../providers/wird_provider.dart';
import '../models/wird_model.dart';
import '../utils/tasbih_colors.dart';
import 'wird_player_screen.dart';
import '../widgets/wird_statistics_card.dart';
import '../widgets/add_dhikr_dialog.dart';
import '../widgets/edit_dhikr_dialog.dart';

class WirdDetailScreen extends StatefulWidget {
  final int wirdId;

  const WirdDetailScreen({Key? key, required this.wirdId}) : super(key: key);

  @override
  State<WirdDetailScreen> createState() => _WirdDetailScreenState();
}

class _WirdDetailScreenState extends State<WirdDetailScreen> {
  final TextEditingController _nameController = TextEditingController();
  TimeOfDay? _reminderTime;

  @override
  void initState() {
    super.initState();

    // إعادة تعيين الأوراد المكتملة عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _resetAllCompletedWirds();
        _loadWirdDetails();
      }
    });
  }

  // إعادة تعيين الأوراد المكتملة بناءً على الوقت وعرض تنبيه أنيق
  Future<void> _resetAllCompletedWirds() async {
    final wirdProvider = Provider.of<WirdProvider>(context, listen: false);

    // إعادة تعيين الأوراد المكتملة وجلب قائمة الأوراد التي تم إعادة تعيينها
    final resetWirdNames = await wirdProvider.resetAllCompletedWirds();

    // عرض تنبيه أنيق إذا تم إعادة تعيين أي ورد
    if (resetWirdNames.isNotEmpty && mounted) {
      _showResetWirdsNotification(resetWirdNames);
    }
  }

  // عرض تنبيه أنيق عند إعادة تعيين الأوراد المكتملة
  void _showResetWirdsNotification(List<String> resetWirdNames) {
    // تأخير قليل لضمان أن الشاشة قد تم تحميلها بالكامل
    Future.delayed(const Duration(milliseconds: 500), () {
      if (!mounted) return;

      // إنشاء نص التنبيه
      String message;
      if (resetWirdNames.length == 1) {
        message = 'تم إعادة تعيين الورد "${resetWirdNames[0]}" لبدء يوم جديد';
      } else if (resetWirdNames.length == 2) {
        message =
            'تم إعادة تعيين الوردين "${resetWirdNames[0]}" و "${resetWirdNames[1]}" لبدء يوم جديد';
      } else {
        message = 'تم إعادة تعيين ${resetWirdNames.length} أوراد لبدء يوم جديد';
      }

      // عرض التنبيه
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(
                Icons.refresh_rounded,
                color: Colors.white,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(message),
              ),
            ],
          ),
          backgroundColor: Colors.teal.shade700,
          duration: const Duration(seconds: 4),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          margin: const EdgeInsets.all(8),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          elevation: 4,
          action: SnackBarAction(
            label: 'حسناً',
            textColor: Colors.white,
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    });
  }

  @override
  void dispose() {
    // التأكد من تحرير جميع الموارد عند إغلاق الشاشة
    _nameController.dispose();
    super.dispose();
  }

  void _loadWirdDetails() {
    debugPrint('جاري تحميل تفاصيل الورد...');
    final wirdProvider = Provider.of<WirdProvider>(context, listen: false);
    final tasbihProvider = Provider.of<TasbihProvider>(context, listen: false);

    try {
      // طباعة معلومات عن الأذكار المتاحة
      debugPrint('عدد الأذكار المتاحة: ${tasbihProvider.dhikrs.length}');
      for (var i = 0; i < tasbihProvider.dhikrs.length; i++) {
        final dhikr = tasbihProvider.dhikrs[i];
        debugPrint('الذكر $i: ${dhikr.name}, المعرف: ${dhikr.id}');
      }

      // طباعة معلومات عن الأوراد
      debugPrint('عدد الأوراد: ${wirdProvider.wirds.length}');
      for (var i = 0; i < wirdProvider.wirds.length; i++) {
        final wird = wirdProvider.wirds[i];
        debugPrint(
            'الورد $i: ${wird.name}, المعرف: ${wird.id}, عدد العناصر: ${wird.items.length}');
      }

      final wird = wirdProvider.wirds.firstWhere(
        (w) => w.id == widget.wirdId,
      );
      _nameController.text = wird.name;
      _reminderTime = wird.reminderTime;

      // طباعة معلومات عن الورد المحمل
      debugPrint('تم تحميل الورد: ${wird.name}, المعرف: ${wird.id}');
      debugPrint('عدد عناصر الورد: ${wird.items.length}');

      // طباعة معلومات عن عناصر الورد
      for (var i = 0; i < wird.items.length; i++) {
        final item = wird.items[i];
        debugPrint('العنصر $i: ${item.dhikr.name}, المعرف: ${item.dhikr.id}');
      }
    } catch (e) {
      debugPrint('خطأ في تحميل تفاصيل الورد: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Consumer<WirdProvider>(
      builder: (context, wirdProvider, child) {
        // البحث عن الورد بواسطة المعرف
        final wird = wirdProvider.wirds.firstWhere(
          (w) => w.id == widget.wirdId,
          orElse: () => WirdModel(id: -1, name: 'غير موجود', items: []),
        );

        // التحقق من وجود الورد
        if (wird.id == -1) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('الورد غير موجود'),
            ),
            body: const Center(
              child: Text('الورد المطلوب غير موجود'),
            ),
          );
        }

        return Scaffold(
          appBar: AppBar(
            title: Text(
              wird.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            elevation: 0,
            backgroundColor: TasbihColors.primary,
            actions: [
              IconButton(
                icon: const Icon(
                  Icons.play_arrow,
                  color: Colors.white,
                ),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => WirdPlayerScreen(wirdId: wird.id),
                    ),
                  );
                },
                tooltip: 'تشغيل الورد',
              ),
              IconButton(
                icon: const Icon(
                  Icons.edit,
                  color: Colors.white,
                ),
                onPressed: () {
                  try {
                    _showEditWirdDialog(context, wird);
                  } catch (e) {
                    debugPrint('خطأ في فتح حوار تعديل الورد: $e');
                  }
                },
                tooltip: 'تعديل الورد',
              ),
              IconButton(
                icon: const Icon(
                  Icons.delete,
                  color: Colors.white,
                ),
                onPressed: () {
                  try {
                    _showDeleteWirdDialog(context, wird);
                  } catch (e) {
                    debugPrint('خطأ في فتح حوار حذف الورد: $e');
                  }
                },
                tooltip: 'حذف الورد',
              ),
            ],
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: isDarkMode
                    ? [
                        TasbihColors.primary,
                        TasbihColors.darkBackground,
                      ]
                    : [
                        TasbihColors.primary,
                        Colors.white,
                      ],
                stops: const [0.0, 0.3],
              ),
            ),
            child: wird.items.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // بطاقة الإحصائيات
                        WirdStatisticsCard(wird: wird),

                        // رسالة لا توجد أذكار
                        Container(
                          margin: const EdgeInsets.all(16),
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: isDarkMode
                                ? TasbihColors.darkCardColor
                                : Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withAlpha(10),
                                blurRadius: 10,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          child: Column(
                            children: [
                              Image.asset(
                                'assets/images/empty_list.png',
                                height: 120,
                                width: 120,
                                errorBuilder: (context, error, stackTrace) =>
                                    const Icon(
                                  Icons.format_list_bulleted,
                                  size: 80,
                                  color: TasbihColors.primary,
                                ),
                              ),
                              const SizedBox(height: 16),
                              const Text(
                                'لا توجد أذكار في هذا الورد',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'قم بإضافة أذكار للورد لتبدأ رحلتك الروحانية',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: isDarkMode
                                      ? TasbihColors.darkTextSecondary
                                      : Colors.grey,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 24),
                              ElevatedButton.icon(
                                onPressed: () {
                                  try {
                                    _showAddWirdItemDialog(context, wird);
                                  } catch (e) {
                                    debugPrint(
                                        'خطأ في فتح حوار إضافة الذكر: $e');
                                  }
                                },
                                icon: const Icon(Icons.add),
                                label: const Text('إضافة ذكر'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: TasbihColors.primary,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 24, vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  )
                : Column(
                    children: [
                      // بطاقة الإحصائيات
                      WirdStatisticsCard(wird: wird),

                      // عنوان قائمة الأذكار
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 14, vertical: 8),
                              decoration: BoxDecoration(
                                color: isDarkMode
                                    ? TasbihColors.darkCardColor
                                    : Colors.white,
                                borderRadius: BorderRadius.circular(16),
                                boxShadow: [
                                  BoxShadow(
                                    color: TasbihColors.primary.withAlpha(20),
                                    blurRadius: 6,
                                    offset: const Offset(0, 3),
                                    spreadRadius: 1,
                                  ),
                                  BoxShadow(
                                    color: Colors.black.withAlpha(10),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                                border: Border.all(
                                  color: TasbihColors.primary.withAlpha(30),
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(6),
                                    decoration: BoxDecoration(
                                      color: TasbihColors.primary.withAlpha(25),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: const Icon(
                                      Icons.format_list_bulleted,
                                      size: 16,
                                      color: TasbihColors.primary,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'قائمة الأذكار (${wird.items.length})',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: TasbihColors.primary,
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const Spacer(),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 10,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: isDarkMode
                                    ? Colors.grey[800]!.withAlpha(100)
                                    : Colors.grey[100],
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: isDarkMode
                                      ? Colors.grey[700]!
                                      : Colors.grey[300]!,
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.swap_vert,
                                    size: 16,
                                    color: isDarkMode
                                        ? TasbihColors.darkTextSecondary
                                        : Colors.grey[600],
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'اسحب لإعادة الترتيب',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                      color: isDarkMode
                                          ? TasbihColors.darkTextSecondary
                                          : Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),

                      // قائمة الأذكار
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            color: isDarkMode
                                ? TasbihColors.darkBackground
                                : Colors.white,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(24),
                              topRight: Radius.circular(24),
                            ),
                          ),
                          child: ReorderableListView.builder(
                            padding: const EdgeInsets.only(top: 16, bottom: 80),
                            itemCount: wird.items.length,
                            onReorder: (oldIndex, newIndex) {
                              if (oldIndex < newIndex) {
                                newIndex -= 1;
                              }
                              wirdProvider.reorderWirdItems(
                                  wird.id, oldIndex, newIndex);
                            },
                            itemBuilder: (context, index) {
                              final item = wird.items[index];
                              return Container(
                                key: ValueKey(item.id),
                                margin: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 10),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(20),
                                  boxShadow: [
                                    BoxShadow(
                                      color: TasbihColors.primary.withAlpha(20),
                                      blurRadius: 10,
                                      offset: const Offset(0, 4),
                                      spreadRadius: 1,
                                    ),
                                    BoxShadow(
                                      color: Colors.black.withAlpha(10),
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Card(
                                  margin: EdgeInsets.zero,
                                  elevation: 0,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: InkWell(
                                    onTap: () {
                                      try {
                                        _showEditWirdItemDialog(
                                            context, wird, item);
                                      } catch (e) {
                                        debugPrint(
                                            'خطأ في فتح حوار تعديل الذكر: $e');
                                      }
                                    },
                                    borderRadius: BorderRadius.circular(20),
                                    child: Padding(
                                      padding: const EdgeInsets.all(20.0),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              // رقم الترتيب
                                              Container(
                                                width: 46,
                                                height: 46,
                                                decoration: BoxDecoration(
                                                  color: TasbihColors.primary
                                                      .withAlpha(30),
                                                  borderRadius:
                                                      BorderRadius.circular(14),
                                                  boxShadow: [
                                                    BoxShadow(
                                                      color: TasbihColors
                                                          .primary
                                                          .withAlpha(20),
                                                      blurRadius: 4,
                                                      offset:
                                                          const Offset(0, 2),
                                                    ),
                                                  ],
                                                  border: Border.all(
                                                    color: TasbihColors.primary
                                                        .withAlpha(50),
                                                    width: 1.5,
                                                  ),
                                                ),
                                                child: Center(
                                                  child: Text(
                                                    '${item.order}',
                                                    style: const TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color:
                                                          TasbihColors.primary,
                                                      fontSize: 18,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              const SizedBox(width: 12),

                                              // معلومات الذكر
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      item.dhikr.name,
                                                      style: const TextStyle(
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        fontSize: 16,
                                                      ),
                                                    ),
                                                    if (item.dhikr.arabicText
                                                            .isNotEmpty &&
                                                        item.dhikr.arabicText !=
                                                            item.dhikr.name)
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(top: 4.0),
                                                        child: Text(
                                                          item.dhikr.arabicText,
                                                          style: TextStyle(
                                                            fontSize: 14,
                                                            color: isDarkMode
                                                                ? TasbihColors
                                                                    .darkTextSecondary
                                                                : Colors
                                                                    .grey[600],
                                                          ),
                                                          maxLines: 1,
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                        ),
                                                      ),
                                                  ],
                                                ),
                                              ),

                                              // العدد المستهدف
                                              Container(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 14,
                                                        vertical: 10),
                                                decoration: BoxDecoration(
                                                  color: TasbihColors.primary
                                                      .withAlpha(30),
                                                  borderRadius:
                                                      BorderRadius.circular(16),
                                                  boxShadow: [
                                                    BoxShadow(
                                                      color: TasbihColors
                                                          .primary
                                                          .withAlpha(15),
                                                      blurRadius: 4,
                                                      offset:
                                                          const Offset(0, 2),
                                                    ),
                                                  ],
                                                  border: Border.all(
                                                    color: TasbihColors.primary
                                                        .withAlpha(50),
                                                    width: 1,
                                                  ),
                                                ),
                                                child: Row(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  children: [
                                                    const Icon(
                                                      Icons.repeat,
                                                      size: 18,
                                                      color:
                                                          TasbihColors.primary,
                                                    ),
                                                    const SizedBox(width: 6),
                                                    Text(
                                                      '${item.targetCount}',
                                                      style: const TextStyle(
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        color: TasbihColors
                                                            .primary,
                                                        fontSize: 16,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),

                                          // أزرار التحكم
                                          Container(
                                            margin:
                                                const EdgeInsets.only(top: 16),
                                            padding: const EdgeInsets.symmetric(
                                              vertical: 12,
                                              horizontal: 12,
                                            ),
                                            decoration: BoxDecoration(
                                              color: isDarkMode
                                                  ? TasbihColors.darkCardColor
                                                      .withAlpha(150)
                                                  : Colors.grey[50],
                                              borderRadius:
                                                  BorderRadius.circular(16),
                                              border: Border.all(
                                                color: isDarkMode
                                                    ? Colors.grey[700]!
                                                    : Colors.grey[200]!,
                                                width: 1,
                                              ),
                                            ),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.end,
                                              children: [
                                                // زر التعديل
                                                Expanded(
                                                  child: OutlinedButton.icon(
                                                    onPressed: () {
                                                      try {
                                                        _showEditWirdItemDialog(
                                                            context,
                                                            wird,
                                                            item);
                                                      } catch (e) {
                                                        debugPrint(
                                                            'خطأ في فتح حوار تعديل الذكر: $e');
                                                      }
                                                    },
                                                    icon: const Icon(Icons.edit,
                                                        size: 18),
                                                    label: const Text(
                                                      'تعديل',
                                                      style: TextStyle(
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                    ),
                                                    style: OutlinedButton
                                                        .styleFrom(
                                                      foregroundColor:
                                                          TasbihColors.primary,
                                                      side: const BorderSide(
                                                          color: TasbihColors
                                                              .primary,
                                                          width: 1.5),
                                                      shape:
                                                          RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12),
                                                      ),
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                        vertical: 12,
                                                        horizontal: 8,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                const SizedBox(width: 12),

                                                // زر الحذف
                                                Expanded(
                                                  child: OutlinedButton.icon(
                                                    onPressed: () {
                                                      try {
                                                        _showDeleteWirdItemDialog(
                                                            context,
                                                            wird,
                                                            item);
                                                      } catch (e) {
                                                        debugPrint(
                                                            'خطأ في فتح حوار حذف الذكر: $e');
                                                      }
                                                    },
                                                    icon: const Icon(
                                                        Icons.delete,
                                                        size: 18),
                                                    label: const Text(
                                                      'حذف',
                                                      style: TextStyle(
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                    ),
                                                    style: OutlinedButton
                                                        .styleFrom(
                                                      foregroundColor:
                                                          Colors.red,
                                                      side: const BorderSide(
                                                          color: Colors.red,
                                                          width: 1.5),
                                                      shape:
                                                          RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12),
                                                      ),
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                        vertical: 12,
                                                        horizontal: 8,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
          ),

          // زر إضافة ذكر جديد (عائم)
          floatingActionButton: wird.items.isNotEmpty
              ? Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(30),
                    boxShadow: [
                      BoxShadow(
                        color: TasbihColors.primary.withAlpha(60),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: FloatingActionButton.extended(
                    onPressed: () {
                      try {
                        _showAddWirdItemDialog(context, wird);
                      } catch (e) {
                        debugPrint('خطأ في فتح حوار إضافة الذكر: $e');
                      }
                    },
                    icon: const Icon(Icons.add, size: 22),
                    label: const Text(
                      'إضافة ذكر',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    backgroundColor: TasbihColors.primary,
                    elevation: 0,
                  ),
                )
              : null,
        );
      },
    );
  }

  void _showEditWirdDialog(BuildContext context, WirdModel wird) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    _nameController.text = wird.name;
    _reminderTime = wird.reminderTime;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Row(
                children: [
                  Icon(
                    Icons.edit,
                    color: TasbihColors.primary,
                    size: 24,
                  ),
                  SizedBox(width: 8),
                  Text('تعديل الورد'),
                ],
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // حقل اسم الورد
                    const Text(
                      'اسم الورد',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: TasbihColors.primary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _nameController,
                      decoration: InputDecoration(
                        hintText: 'أدخل اسم الورد',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor: isDarkMode
                            ? TasbihColors.darkCardColor
                            : Colors.grey[50],
                        prefixIcon: const Icon(Icons.text_fields,
                            color: TasbihColors.primary),
                      ),
                    ),
                    const SizedBox(height: 24),

                    // تم إخفاء قسم وقت التذكير مؤقتاً
                  ],
                ),
              ),
              actions: [
                // زر الإلغاء
                OutlinedButton(
                  onPressed: () => Navigator.pop(context),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.grey[700],
                    side: BorderSide(color: Colors.grey[300]!),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('إلغاء'),
                ),
                // زر الحفظ
                ElevatedButton(
                  onPressed: () async {
                    // التحقق من صحة البيانات
                    if (_nameController.text.trim().isEmpty) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('الرجاء إدخال اسم الورد'),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }

                    // تحديث الورد
                    final wirdProvider =
                        Provider.of<WirdProvider>(context, listen: false);
                    final updatedWird = wird.copyWith(
                      name: _nameController.text.trim(),
                      reminderTime: _reminderTime,
                    );

                    // إغلاق حوار التعديل
                    Navigator.pop(context);

                    // عرض مؤشر التحميل
                    showDialog(
                      context: context,
                      barrierDismissible: false,
                      builder: (dialogContext) => AlertDialog(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        backgroundColor: isDarkMode
                            ? TasbihColors.darkCardColor
                            : Colors.white,
                        content: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                  TasbihColors.primary),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'جاري تحديث الورد وجدولة الإشعارات...',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color:
                                    isDarkMode ? Colors.white : Colors.black87,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );

                    // تحديث الورد
                    final success = await wirdProvider.updateWird(updatedWird);

                    // إغلاق مؤشر التحميل وعرض رسالة نجاح أو فشل
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        // إغلاق مؤشر التحميل
                        Navigator.of(context).pop();

                        // عرض رسالة نجاح أو فشل
                        if (success) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                  'تم تحديث ورد "${updatedWird.name}" بنجاح'),
                              backgroundColor: TasbihColors.primary,
                              behavior: SnackBarBehavior.floating,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          );
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                  'تم تحديث ورد "${updatedWird.name}" ولكن قد تكون هناك مشكلة في جدولة الإشعارات'),
                              backgroundColor: Colors.orange,
                              behavior: SnackBarBehavior.floating,
                              action: SnackBarAction(
                                label: 'إعادة المحاولة',
                                textColor: Colors.white,
                                onPressed: () {
                                  if (mounted) {
                                    _showEditWirdDialog(context, updatedWird);
                                  }
                                },
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          );
                        }
                      }
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: TasbihColors.primary,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('حفظ'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showDeleteWirdDialog(BuildContext context, WirdModel wird) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(
                Icons.warning,
                color: Colors.red,
                size: 24,
              ),
              SizedBox(width: 8),
              Text('حذف الورد'),
            ],
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // أيقونة التحذير
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.red.withAlpha(30),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.delete_forever,
                  color: Colors.red,
                  size: 48,
                ),
              ),
              const SizedBox(height: 24),

              // نص التحذير
              const Text(
                'هل أنت متأكد من حذف الورد:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),

              // اسم الورد
              Container(
                width: double.infinity,
                margin: const EdgeInsets.symmetric(vertical: 8),
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? TasbihColors.darkCardColor
                      : Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                      color:
                          isDarkMode ? Colors.grey[700]! : Colors.grey[300]!),
                ),
                child: Text(
                  wird.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    color: Colors.red,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 16),

              // معلومات إضافية
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isDarkMode ? Colors.red.withAlpha(25) : Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                      color: isDarkMode
                          ? Colors.red.withAlpha(76)
                          : Colors.red[100]!),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline,
                            color: Colors.red[700], size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'سيتم حذف جميع الأذكار المرتبطة بهذا الورد',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.red[700],
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(Icons.warning_amber,
                            color: Colors.red[700], size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'لا يمكن التراجع عن هذه العملية',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: Colors.red[700],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            // زر الإلغاء
            OutlinedButton.icon(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(Icons.cancel, size: 16),
              label: const Text('إلغاء'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.grey[700],
                side: BorderSide(color: Colors.grey[300]!),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            // زر الحذف
            ElevatedButton.icon(
              onPressed: () {
                final wirdProvider =
                    Provider.of<WirdProvider>(context, listen: false);

                // حذف الورد
                wirdProvider.deleteWird(wird.id);

                // إغلاق الحوار
                Navigator.pop(context);

                // العودة إلى قائمة الأوراد
                Navigator.pop(context);

                // عرض رسالة نجاح
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('تم حذف ورد "${wird.name}" بنجاح'),
                    backgroundColor: Colors.red,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
              icon: const Icon(Icons.delete_forever, size: 16),
              label: const Text('حذف'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showAddWirdItemDialog(BuildContext context, WirdModel wird) {
    // استخدام النموذج الجديد لإضافة الذكر بطريقة آمنة
    try {
      // استخدام طريقة مباشرة لتجنب مشاكل التزامن
      showAddDhikrDialog(context, wird);
    } catch (e) {
      debugPrint('خطأ في استدعاء حوار إضافة الذكر: $e');
      // التحقق من أن الـ widget لا يزال مركباً قبل عرض رسالة الخطأ
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ في فتح نموذج إضافة الذكر'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showEditWirdItemDialog(
      BuildContext context, WirdModel wird, WirdItemModel item) {
    // استخدام النموذج الجديد لتعديل الذكر
    showEditDhikrDialog(context, wird, item);
  }

  void _showDeleteWirdItemDialog(
      BuildContext context, WirdModel wird, WirdItemModel item) {
    showDialog(
      context: context,
      builder: (context) {
        final isDarkMode = Theme.of(context).brightness == Brightness.dark;
        return AlertDialog(
          backgroundColor:
              isDarkMode ? TasbihColors.darkCardColor : Colors.white,
          title: const Row(
            children: [
              Icon(
                Icons.warning,
                color: Colors.red,
                size: 24,
              ),
              SizedBox(width: 8),
              Text('حذف الذكر'),
            ],
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // أيقونة التحذير
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.red.withAlpha(30),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.delete_forever,
                  color: Colors.red,
                  size: 48,
                ),
              ),
              const SizedBox(height: 24),

              // نص التحذير
              const Text(
                'هل أنت متأكد من حذف الذكر:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),

              // اسم الذكر
              Container(
                width: double.infinity,
                margin: const EdgeInsets.symmetric(vertical: 8),
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? TasbihColors.darkCardColor
                      : Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                      color:
                          isDarkMode ? Colors.grey[700]! : Colors.grey[300]!),
                ),
                child: Column(
                  children: [
                    Text(
                      item.dhikr.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                        color: Colors.red,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    if (item.dhikr.arabicText.isNotEmpty &&
                        item.dhikr.arabicText != item.dhikr.name) ...[
                      const SizedBox(height: 4),
                      Text(
                        item.dhikr.arabicText,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
              const SizedBox(height: 8),

              // معلومات إضافية
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isDarkMode ? Colors.red.withAlpha(25) : Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                      color: isDarkMode
                          ? Colors.red.withAlpha(76)
                          : Colors.red[100]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning_amber, color: Colors.red[700], size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'لا يمكن التراجع عن هذه العملية',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.red[700],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            // زر الإلغاء
            OutlinedButton.icon(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(Icons.cancel, size: 16),
              label: const Text('إلغاء'),
              style: OutlinedButton.styleFrom(
                foregroundColor: isDarkMode ? Colors.white : Colors.grey[700],
                side: BorderSide(
                    color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            // زر الحذف
            ElevatedButton.icon(
              onPressed: () {
                final wirdProvider = Provider.of<WirdProvider>(
                  context,
                  listen: false,
                );

                // حذف الذكر
                wirdProvider.deleteWirdItem(
                  wird.id,
                  item.id,
                );

                // إغلاق الحوار
                Navigator.pop(context);

                // عرض رسالة نجاح
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('تم حذف ذكر "${item.dhikr.name}" بنجاح'),
                    backgroundColor: Colors.red,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
              icon: const Icon(Icons.delete_forever, size: 16),
              label: const Text('حذف'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
