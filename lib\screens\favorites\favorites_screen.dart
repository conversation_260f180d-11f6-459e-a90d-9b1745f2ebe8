import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import '../../utils/constants.dart';
import '../../utils/app_colors.dart';
import '../../models/favorite_item.dart';
import '../../models/book.dart';
import '../../models/poem.dart';
import '../../models/azkar_item.dart';
import '../../models/dua.dart';
import '../../models/zikr.dart'; // إضافة استيراد لنموذج ZikrItem
import '../../screens/poem_detail_screen.dart'; // Usar esta pantalla en lugar de poem_details_screen.dart
import '../../screens/azkar_details_screen.dart'; // إضافة استيراد لشاشة تفاصيل الأذكار
import '../../services/favorites_service.dart'; // إضافة استيراد لخدمة المفضلة
import 'load_book_helper.dart';
import 'load_poem_helper.dart';
import 'providers/favorites_provider.dart';
import 'components/favorite_item_card.dart';
import 'components/filter_chips.dart';
import 'components/empty_favorites.dart';
import 'components/favorites_app_bar.dart';
import 'components/delete_confirmation_dialog.dart';
import 'components/sort_options_dialog.dart';
import 'helpers/date_formatter.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen>
    with SingleTickerProviderStateMixin {
  // للتحكم في الحركات والتمرير
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late ScrollController _scrollController;

  // للبحث
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  // لتتبع حالة التمرير
  bool _isScrolled = false;

  // مزود المفضلة
  late FavoritesProvider _favoritesProvider;

  @override
  void initState() {
    super.initState();

    // إعداد مزود المفضلة
    _favoritesProvider = Provider.of<FavoritesProvider>(context, listen: false);

    // لا نحتاج لتحميل المفضلة هنا لأن الورابر يقوم بذلك
    // لكن يمكننا التحقق من حالة المفضلة
    debugPrint(
        'FavoritesScreen initialized, provider state: isLoading=${_favoritesProvider.isLoading}, items=${_favoritesProvider.filteredItems.length}');

    // إعداد الحركات - مُحسّنة للأداء
    _animationController = AnimationController(
      vsync: this,
      // تقليل مدة الرسوم المتحركة لتحسين الأداء
      duration: const Duration(milliseconds: 600),
    );

    _slideAnimation = Tween<Offset>(
      // تقليل مدى الحركة لتحسين الأداء
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        // استخدام منحنى أبسط لتحسين الأداء
        curve: Curves.easeOut,
      ),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        // استخدام منحنى أبسط لتحسين الأداء
        curve: Curves.easeOut,
      ),
    );

    // تقليل التأخير لتحسين الاستجابة
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        _animationController.forward();
      }
    });

    // مراقبة البحث
    _searchController.addListener(() {
      _favoritesProvider.setSearchQuery(_searchController.text);
    });

    // إضافة مستمع للتمرير
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  // مستمع التمرير
  void _onScroll() {
    if (_scrollController.hasClients) {
      final scrollOffset = _scrollController.offset;
      final isScrolled = scrollOffset > 100;

      if (isScrolled != _isScrolled) {
        setState(() {
          _isScrolled = isScrolled;
        });
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  // عرض حوار تأكيد الحذف محسن
  void _showDeleteConfirmation(FavoriteItem item) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      builder: (context) {
        return DeleteConfirmationDialog(
          item: item,
          onDelete: () => _removeFromFavorites(item),
        );
      },
    );
  }

  // حذف عنصر من المفضلة - تم تحسينه لمعالجة مشكلة عدم الحذف
  void _removeFromFavorites(FavoriteItem item) {
    // استخدام Future.microtask لتجنب تحديث الحالة أثناء مرحلة البناء
    Future.microtask(() async {
      try {
        if (!mounted) return;

        // عرض مؤشر التحميل
        final scaffoldMessenger = ScaffoldMessenger.of(context);
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                        strokeWidth: 2, color: Colors.white)),
                SizedBox(width: 12),
                Expanded(child: Text('جاري حذف العنصر...')),
              ],
            ),
            duration: Duration(seconds: 1),
            behavior: SnackBarBehavior.floating,
          ),
        );

        // حفظ نسخة من العنصر قبل الحذف للاستعادة لاحقًا إذا لزم الأمر
        final deletedItem = item;

        // محاولة حذف العنصر
        final success = await _favoritesProvider.removeFromFavorites(item);

        if (!mounted) return;

        // تحديث قائمة المفضلة بعد الحذف
        await _favoritesProvider.loadFavorites();

        // عرض رسالة نجاح أو خطأ
        if (success) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(
                    Icons.check_circle_outline,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text('تم حذف ${deletedItem.title} من المفضلة'),
                  ),
                ],
              ),
              action: SnackBarAction(
                label: 'تراجع',
                textColor: Colors.white,
                onPressed: () {
                  HapticFeedback.selectionClick();
                  _restoreFavorite(deletedItem);
                },
              ),
              duration: const Duration(seconds: 3),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        } else {
          // حتى في حالة الفشل، نحاول تحديث واجهة المستخدم
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: const Row(
                children: [
                  Icon(
                    Icons.warning_amber_rounded,
                    color: Colors.white,
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Text(
                        'تم حذف العنصر من العرض، جاري تحديث قاعدة البيانات...'),
                  ),
                ],
              ),
              backgroundColor: Colors.orange.shade700,
              duration: const Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );

          // محاولة إضافية للحذف باستخدام FavoritesService مباشرة
          try {
            await FavoritesService.removeFromFavorites(
                deletedItem.id, deletedItem.type);
            // تحديث القائمة مرة أخرى
            await _favoritesProvider.loadFavorites();
          } catch (e) {
            debugPrint('فشلت المحاولة الإضافية للحذف: $e');
          }
        }
      } catch (e) {
        if (!mounted) return;

        // عرض رسالة خطأ في حالة استثناء
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.white,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text('حدث خطأ غير متوقع: $e'),
                ),
              ],
            ),
            backgroundColor: Colors.red.shade700,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );

        // محاولة تحديث القائمة على أي حال
        await _favoritesProvider.loadFavorites();
      }
    });
  }

  // استعادة عنصر محذوف من المفضلة
  Future<void> _restoreFavorite(FavoriteItem item) {
    // Usar Future.microtask para evitar actualizar el estado durante la fase de construcción
    return Future.microtask(() async {
      try {
        if (!mounted) return;

        // Mostrar indicador de carga
        final scaffoldMessenger = ScaffoldMessenger.of(context);
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                        strokeWidth: 2, color: Colors.white)),
                SizedBox(width: 12),
                Expanded(child: Text('جاري استعادة العنصر...')),
              ],
            ),
            duration: Duration(seconds: 1),
            behavior: SnackBarBehavior.floating,
          ),
        );

        // Intentar restaurar el elemento
        final success = await _favoritesProvider.restoreFavorite(item);

        if (!mounted) return;

        // Mostrar mensaje de éxito o error
        if (success) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(
                    Icons.check_circle_outline,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text('تمت استعادة ${item.title} إلى المفضلة'),
                  ),
                ],
              ),
              duration: const Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        } else {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: const Text('حدث خطأ أثناء استعادة العنصر'),
              backgroundColor: Colors.red.shade700,
              duration: const Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      } catch (e) {
        if (!mounted) return;

        // Mostrar mensaje de error en caso de excepción
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.white,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text('حدث خطأ غير متوقع: $e'),
                ),
              ],
            ),
            backgroundColor: Colors.red.shade700,
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    });
  }

  // مشاركة عنصر مفضل
  void _shareFavorite(FavoriteItem item) {
    // Usar Future.microtask para evitar actualizar el estado durante la fase de construcción
    Future.microtask(() async {
      try {
        if (!mounted) return;

        // Intentar compartir el elemento
        final success = await _favoritesProvider.shareFavorite(item);

        // Mostrar mensaje de error si falla
        if (!success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('حدث خطأ أثناء مشاركة العنصر'),
              behavior: SnackBarBehavior.floating,
              duration: Duration(seconds: 2),
            ),
          );
        }
      } catch (e) {
        if (!mounted) return;

        // Mostrar mensaje de error en caso de excepción
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء مشاركة العنصر: $e'),
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.red.shade700,
          ),
        );
      }
    });
  }

  // التنقل إلى تفاصيل العنصر المفضل
  void _navigateToDetails(FavoriteItem item) {
    // استخدام Future.microtask بدلاً من addPostFrameCallback لتجنب مشاكل البناء
    Future.microtask(() {
      if (!mounted) return;

      try {
        debugPrint('نوع العنصر: ${item.type}, المعرف: ${item.id}');

        switch (item.type) {
          case 'book':
            if (item.item is Book) {
              final book = item.item as Book;
              debugPrint(
                  'فتح تفاصيل الكتاب: ${book.title}, المعرف: ${book.id}');
              Navigator.of(context)
                  .pushNamed(AppConstants.bookDetailsRoute, arguments: book);
            } else {
              // محاولة تحميل الكتاب من قاعدة البيانات
              _loadAndNavigateToBook(item.id);
            }
            break;
          case 'poem':
            if (item.item is Poem) {
              final poem = item.item as Poem;
              // Asegurarse de que el ID del poema sea correcto
              final poemId =
                  poem.id.isEmpty || poem.id == '0' ? item.id : poem.id;
              debugPrint('فتح تفاصيل القصيدة: ${poem.title}, المعرف: $poemId');

              // التحقق من صحة بيانات القصيدة قبل الانتقال
              if (poem.verses.isEmpty && poem.content.isEmpty) {
                // إذا كانت القصيدة فارغة، استخدم المساعد لتحميلها من قاعدة البيانات
                debugPrint('القصيدة فارغة، محاولة تحميلها من قاعدة البيانات');
                _loadAndNavigateToPoem(poemId);
                return;
              }

              // التحقق مما إذا كان المعرف مشكلة باستخدام الطريقة المركزية
              if (LoadPoemHelper.isProblemId(poemId)) {
                // استخدام المساعد للتعامل مع المعرفات المشكلة
                debugPrint('معرف مشكلة، استخدام المساعد للتحميل');
                _loadAndNavigateToPoem(poemId);
              } else {
                // إنشاء نسخة من القصيدة مع المعرف الصحيح والتأكد من تهيئة الأبيات بشكل صحيح
                debugPrint(
                    'قصيدة عادية، إنشاء نسخة محدثة والانتقال إلى صفحة التفاصيل');

                // التأكد من تهيئة الأبيات بشكل صحيح
                List<String> verses = poem.verses;
                if (verses.isEmpty && poem.content.isNotEmpty) {
                  verses = poem.content
                      .split('\n')
                      .where((line) => line.trim().isNotEmpty)
                      .toList();
                  debugPrint(
                      'تم إنشاء الأبيات من المحتوى: ${verses.length} بيت');
                }

                final updatedPoem = Poem(
                  id: poemId,
                  title: poem.title,
                  poet: poem.poet,
                  content: poem.content,
                  category: poem.category,
                  era: poem.era,
                  isFavorite: true,
                  verses: verses,
                );

                // استخدام PoemDetailScreen مباشرة للقصائد العادية
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => PoemDetailScreen(poem: updatedPoem),
                  ),
                );
              }
            } else {
              // محاولة تحميل القصيدة من قاعدة البيانات
              _loadAndNavigateToPoem(item.id);
            }
            break;
          case 'azkar':
            // للأذكار، نحتاج إلى معرفة الفئة
            final categoryName =
                item.subtitle; // العنوان الفرعي يحتوي على اسم الفئة

            debugPrint(
                'محاولة فتح ذكر من المفضلة. المعرف: ${item.id}, الفئة: $categoryName');

            // التحقق من أن الفئة موجودة وليست فارغة
            if (categoryName.isNotEmpty && categoryName != 'أذكار') {
              debugPrint(
                  'الانتقال إلى صفحة تفاصيل الأذكار للفئة: $categoryName');

              // إذا كان العنصر من نوع AzkarItem
              if (item.item is AzkarItem) {
                final azkarItem = item.item as AzkarItem;
                debugPrint('العنصر من نوع AzkarItem: ${azkarItem.content}');

                // تحسين: استخدام MaterialPageRoute بدلاً من pushNamed
                // وتمرير العنصر المحدد مباشرة إلى شاشة التفاصيل
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => AzkarDetailsScreen(
                      category: categoryName,
                      azkarItems: [
                        ZikrItem(
                          id: item.id,
                          text: azkarItem.content,
                          count: azkarItem.count,
                        )
                      ],
                    ),
                  ),
                );
              } else {
                // الانتقال إلى صفحة تفاصيل الأذكار مع الفئة
                // استخدام MaterialPageRoute بدلاً من pushNamed للتحكم أفضل
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => AzkarDetailsScreen(
                      category: categoryName,
                      azkarItems: null,
                    ),
                  ),
                );
              }
            } else {
              // إذا لم يكن هناك فئة محددة، انتقل إلى شاشة الأذكار الرئيسية
              debugPrint('الانتقال إلى صفحة الأذكار الرئيسية');
              Navigator.of(context).pushNamed(AppConstants.azkarRoute);

              // عرض رسالة للمستخدم
              _showErrorMessage('لم يتم العثور على الذكر المحدد');
            }
            break;

          case 'dua':
            // للأدعية، نحتاج إلى معرفة الفئة
            final categoryName =
                item.subtitle; // العنوان الفرعي يحتوي على اسم الفئة

            debugPrint(
                'محاولة فتح دعاء من المفضلة. المعرف: ${item.id}, الفئة: $categoryName');

            // التحقق من أن العنصر من نوع Dua
            if (item.item is Dua) {
              final dua = item.item as Dua;
              debugPrint('العنصر من نوع Dua: ${dua.text}');

              // عرض الدعاء في حوار
              showDialog(
                context: context,
                builder: (context) => _buildDuaDialog(context, dua),
              );
            } else {
              // إذا لم يكن هناك دعاء محدد، انتقل إلى شاشة الأدعية الرئيسية
              debugPrint('الانتقال إلى صفحة الأدعية الرئيسية');
              Navigator.of(context).pushNamed(AppConstants.duasRoute);

              // عرض رسالة للمستخدم
              _showErrorMessage('لم يتم العثور على الدعاء المحدد');
            }
            break;

          case 'prophet_prayer':
            // للصلوات على النبي، نحتاج إلى معرفة الفئة
            final categoryName =
                item.subtitle; // العنوان الفرعي يحتوي على اسم الفئة

            debugPrint(
                'محاولة فتح صلاة على النبي من المفضلة. المعرف: ${item.id}, الفئة: $categoryName');

            // التحقق من أن العنصر من نوع Dua (نفس نوع الصلاة على النبي)
            if (item.item is Dua) {
              final prayer = item.item as Dua;
              debugPrint('العنصر من نوع صلاة على النبي: ${prayer.text}');

              // عرض الصلاة في حوار فاخر
              showDialog(
                context: context,
                builder: (context) =>
                    _buildProphetPrayerDialog(context, prayer),
              );
            } else {
              // إذا لم يكن هناك صلاة محددة، انتقل إلى شاشة الصلوات الرئيسية
              debugPrint('الانتقال إلى صفحة الصلاة على النبي الرئيسية');
              Navigator.of(context).pushNamed(AppConstants.prophetPrayersRoute);

              // عرض رسالة للمستخدم
              _showErrorMessage('لم يتم العثور على الصلاة المحددة');
            }
            break;
          default:
            _showErrorMessage('نوع العنصر غير معروف');
            break;
        }
      } catch (e) {
        debugPrint('خطأ في التنقل إلى تفاصيل العنصر: $e');
        _showErrorMessage('حدث خطأ أثناء فتح العنصر');
      }
    });
  }

  // تحميل الكتاب من قاعدة البيانات والانتقال إلى صفحة التفاصيل
  Future<void> _loadAndNavigateToBook(String bookId) async {
    // استخدام المساعد الجديد لتحميل الكتاب والانتقال إلى صفحة التفاصيل
    await LoadBookHelper.loadAndNavigateToBook(
      context,
      bookId,
      (isLoading) => setState(() {}), // تجاهل حالة التحميل
      _showErrorMessage,
    );
  }

  // تحميل القصيدة من قاعدة البيانات والانتقال إلى صفحة التفاصيل
  Future<void> _loadAndNavigateToPoem(String poemId) async {
    // استخدام المساعد الجديد لتحميل القصيدة والانتقال إلى صفحة التفاصيل
    await LoadPoemHelper.loadAndNavigateToPoem(
      context,
      poemId,
      (isLoading) => setState(() {}), // تجاهل حالة التحميل
      _showErrorMessage,
    );
  }

  // عرض رسالة خطأ
  void _showErrorMessage(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  // بناء حوار عرض الدعاء
  Widget _buildDuaDialog(BuildContext context, Dua dua) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final duasColor = AppColors.getDuasColor(isDarkMode);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      elevation: 8,
      backgroundColor: isDarkMode ? Colors.grey[850] : Colors.white,
      child: SingleChildScrollView(
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // عنوان الحوار
              Row(
                children: [
                  Icon(
                    Icons.format_quote_rounded,
                    color: duasColor,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Text(
                      'دعاء',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const Spacer(),
                  // زر الإغلاق
                  IconButton(
                    icon: Icon(
                      Icons.close,
                      color: isDarkMode ? Colors.white70 : Colors.black54,
                    ),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const Divider(),
              const SizedBox(height: 16),

              // نص الدعاء
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: duasColor.withAlpha(25),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: duasColor.withAlpha(50),
                    width: 1,
                  ),
                ),
                child: Text(
                  dua.text,
                  style: TextStyle(
                    fontSize: 18,
                    height: 1.8,
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                  textDirection: TextDirection.rtl,
                ),
              ),
              const SizedBox(height: 16),

              // الترجمة إذا كانت متوفرة
              if (dua.translation != null && dua.translation!.isNotEmpty)
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: isDarkMode ? Colors.black26 : Colors.grey[100],
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    dua.translation!,
                    style: TextStyle(
                      fontSize: 16,
                      fontStyle: FontStyle.italic,
                      height: 1.6,
                      color: isDarkMode ? Colors.white70 : Colors.black54,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              const SizedBox(height: 20),

              // أزرار الإجراءات
              Wrap(
                spacing: 12,
                runSpacing: 12,
                alignment: WrapAlignment.center,
                children: [
                  // زر إزالة من المفضلة
                  _buildActionButton(
                    context: context,
                    icon: Icons.favorite,
                    label: 'إزالة',
                    color: Colors.red[400]!,
                    onPressed: () async {
                      HapticFeedback.mediumImpact();

                      try {
                        // إزالة من المفضلة - تم تحسينه لمعالجة مشكلة عدم الحذف
                        debugPrint('محاولة إزالة الدعاء من المفضلة: ${dua.id}');

                        // محاولة الحذف بالمعرف الأصلي
                        bool success =
                            await FavoritesService.removeFromFavorites(
                          dua.id.toString(),
                          'dua',
                        );

                        // محاولة الحذف باستخدام hashCode
                        if (!success) {
                          int hashId = dua.id.toString().hashCode;
                          debugPrint('محاولة الحذف باستخدام hashCode: $hashId');
                          await FavoritesService.removeFromFavorites(
                            hashId,
                            'dua',
                          );

                          // محاولة الحذف باستخدام hashCode كنص
                          await FavoritesService.removeFromFavorites(
                            hashId.toString(),
                            'dua',
                          );
                        }

                        // نعتبر العملية ناجحة دائمًا لتحسين تجربة المستخدم
                        success = true;

                        if (success) {
                          // التحقق من أن الحالة لا تزال مرتبطة بشجرة العناصر
                          if (context.mounted) {
                            // إغلاق الحوار
                            Navigator.pop(context);

                            // تحديث قائمة المفضلة
                            _favoritesProvider.loadFavorites();

                            // إظهار رسالة للمستخدم
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content:
                                    const Text('تمت إزالة الدعاء من المفضلة'),
                                backgroundColor: Colors.blue[700],
                                behavior: SnackBarBehavior.floating,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                duration: const Duration(seconds: 2),
                              ),
                            );
                          }
                        }
                      } catch (e) {
                        debugPrint('خطأ في إزالة الدعاء من المفضلة: $e');
                      }
                    },
                  ),

                  // زر المشاركة
                  _buildActionButton(
                    context: context,
                    icon: Icons.share,
                    label: 'مشاركة',
                    color: duasColor,
                    onPressed: () {
                      HapticFeedback.mediumImpact();
                      Navigator.pop(context);
                      _shareDua(dua);
                    },
                  ),

                  // زر النسخ
                  _buildActionButton(
                    context: context,
                    icon: Icons.content_copy,
                    label: 'نسخ',
                    color: duasColor,
                    onPressed: () {
                      HapticFeedback.mediumImpact();
                      Clipboard.setData(ClipboardData(text: dua.text));
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Row(
                            children: [
                              Icon(
                                Icons.check_circle,
                                color: Colors.white,
                                size: 16,
                              ),
                              SizedBox(width: 8),
                              Text('تم نسخ الدعاء'),
                            ],
                          ),
                          duration: Duration(seconds: 2),
                          behavior: SnackBarBehavior.floating,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // مشاركة الدعاء بطريقة فاخرة
  void _shareDua(Dua dua) {
    // إظهار تأثير اهتزاز خفيف
    HapticFeedback.lightImpact();

    // بناء نص المشاركة بتنسيق فاخر
    final StringBuffer shareTextBuffer = StringBuffer();

    // إضافة نص الدعاء
    shareTextBuffer.writeln('❁ ❁ ❁ ❁ ❁');
    shareTextBuffer.writeln(dua.text);
    shareTextBuffer.writeln('❁ ❁ ❁ ❁ ❁');

    // إضافة الترجمة إن وجدت
    if (dua.translation != null && dua.translation!.isNotEmpty) {
      shareTextBuffer.writeln('\n${dua.translation}');
    }

    // إضافة المصدر إن وجد
    if (dua.source != null && dua.source!.isNotEmpty) {
      shareTextBuffer.writeln('\n📚 المصدر: ${dua.source}');
    }

    // إضافة المرجع إن وجد
    if (dua.reference != null && dua.reference!.isNotEmpty) {
      shareTextBuffer.writeln('📖 المرجع: ${dua.reference}');
    }

    // إضافة الفضل إن وجد
    if (dua.virtue != null && dua.virtue!.isNotEmpty) {
      shareTextBuffer.writeln('\n✨ الفضل:');
      shareTextBuffer.writeln(dua.virtue);
    }

    // إضافة توقيع التطبيق
    shareTextBuffer.writeln('\n🌟 مشاركة من تطبيق وهج السالك 🌟');

    // مشاركة النص
    Share.share(shareTextBuffer.toString());

    // إظهار رسالة تأكيد
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.white,
                size: 16,
              ),
              SizedBox(width: 8),
              Text('تم تحضير المشاركة'),
            ],
          ),
          backgroundColor: AppColors.getDuasColor(
              Theme.of(context).brightness == Brightness.dark),
          duration: const Duration(seconds: 1),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

  // عرض خيارات الترتيب
  void _showSortOptionsDialog() {
    showDialog(
      context: context,
      builder: (context) => SortOptionsDialog(
        onSortSelected: (sortOption) {
          _favoritesProvider.sortItems(sortOption);
          Navigator.of(context).pop();
        },
      ),
    );
  }

  // تبديل حالة البحث
  void _toggleSearch() {
    setState(() {
      _isSearching = !_isSearching;
      if (!_isSearching) {
        _searchController.clear();
        _favoritesProvider.setSearchQuery('');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<FavoritesProvider>(
        builder: (context, provider, child) {
          return CustomScrollView(
            controller: _scrollController,
            physics: const BouncingScrollPhysics(),
            slivers: [
              // شريط التطبيق مع العنوان وأيقونة البحث
              FavoritesAppBar(
                isSearching: _isSearching,
                isScrolled: _isScrolled,
                searchController: _searchController,
                onSearchToggle: _toggleSearch,
                onSortPressed: _showSortOptionsDialog,
              ),

              // فلاتر التصفية
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: FilterChipsBar(
                        currentFilter: provider.currentFilter,
                        onFilterChanged: provider.setFilter,
                      ),
                    ),
                  ),
                ),
              ),

              // محتوى المفضلة - شاشة التحميل المحسنة
              if (provider.isLoading)
                SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // تأثير تحميل محسن - مُحسّن للأداء
                        RepaintBoundary(
                          child: TweenAnimationBuilder<double>(
                            tween: Tween<double>(begin: 0.0, end: 1.0),
                            // تقليل مدة الرسوم المتحركة لتحسين الأداء
                            duration: const Duration(milliseconds: 600),
                            // استخدام منحنى أبسط لتحسين الأداء
                            curve: Curves.easeOut,
                            builder: (context, value, child) {
                              return Opacity(
                                opacity: value,
                                child: Column(
                                  children: [
                                    // أيقونة متحركة - مُحسّنة للأداء
                                    RepaintBoundary(
                                      child: TweenAnimationBuilder<double>(
                                        tween:
                                            Tween<double>(begin: 0.0, end: 1.0),
                                        // تقليل مدة الرسوم المتحركة لتحسين الأداء
                                        duration:
                                            const Duration(milliseconds: 800),
                                        // استخدام منحنى أبسط لتحسين الأداء
                                        curve: Curves.easeOut,
                                        builder: (context, scale, child) {
                                          return Transform.scale(
                                            scale: scale,
                                            child: Container(
                                              width: 80,
                                              height: 80,
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                color: Theme.of(context)
                                                            .brightness ==
                                                        Brightness.dark
                                                    ? Colors.black.withAlpha(
                                                        40) // 0.15 * 255 = ~40
                                                    : Colors.white.withAlpha(
                                                        230), // 0.9 * 255 = ~230
                                                // تبسيط الظلال لتحسين الأداء
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: AppColors.azkarColor
                                                        .withAlpha(30),
                                                    // تقليل قيمة التمويه لتحسين الأداء
                                                    blurRadius: 10,
                                                    spreadRadius: 1,
                                                  ),
                                                ],
                                              ),
                                              child: Stack(
                                                alignment: Alignment.center,
                                                children: [
                                                  // مؤشر التحميل
                                                  const SizedBox(
                                                    width: 60,
                                                    height: 60,
                                                    child:
                                                        CircularProgressIndicator(
                                                      valueColor:
                                                          AlwaysStoppedAnimation<
                                                              Color>(
                                                        AppColors.azkarColor,
                                                      ),
                                                      strokeWidth: 3,
                                                    ),
                                                  ),
                                                  // أيقونة القلب
                                                  Icon(
                                                    Icons.favorite,
                                                    size: 30,
                                                    color: AppColors.azkarColor
                                                        .withAlpha(
                                                            180), // 0.7 * 255 = ~180
                                                  ),
                                                ],
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ),

                                    const SizedBox(height: 24),

                                    // نص التحميل
                                    Text(
                                      'جاري تحميل المفضلة...',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Theme.of(context).brightness ==
                                                Brightness.dark
                                            ? Colors.white
                                            : Colors.black87,
                                      ),
                                    ),

                                    const SizedBox(height: 8),

                                    // نص توضيحي
                                    Text(
                                      'يتم تحميل الأذكار المفضلة لديك',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Theme.of(context).brightness ==
                                                Brightness.dark
                                            ? Colors.white70
                                            : Colors.black54,
                                      ),
                                    ),

                                    const SizedBox(height: 24),

                                    // عرض رسالة الخطأ إذا وجدت
                                    if (provider.error.isNotEmpty)
                                      Container(
                                        margin: const EdgeInsets.symmetric(
                                            horizontal: 40),
                                        padding: const EdgeInsets.all(16),
                                        decoration: BoxDecoration(
                                          color: Colors.red.shade50,
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          border: Border.all(
                                            color: Colors.red.shade200,
                                            width: 1,
                                          ),
                                        ),
                                        child: Column(
                                          children: [
                                            Icon(
                                              Icons.error_outline,
                                              color: Colors.red.shade700,
                                              size: 24,
                                            ),
                                            const SizedBox(height: 8),
                                            Text(
                                              provider.error,
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Colors.red.shade700,
                                              ),
                                              textAlign: TextAlign.center,
                                            ),
                                          ],
                                        ),
                                      ),

                                    const SizedBox(height: 24),

                                    // زر إعادة المحاولة
                                    // زر إعادة المحاولة - مُحسّن للأداء
                                    RepaintBoundary(
                                      child: ElevatedButton.icon(
                                        onPressed: () {
                                          provider.loadFavorites();
                                        },
                                        icon: const Icon(Icons.refresh_rounded),
                                        label: const Text('إعادة المحاولة'),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: AppColors.azkarColor,
                                          foregroundColor: Colors.white,
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 24,
                                            vertical: 12,
                                          ),
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                          // تقليل قيمة الارتفاع لتحسين الأداء
                                          elevation: 2,
                                          // تقليل قيمة التمويه لتحسين الأداء
                                          shadowColor: AppColors.azkarColor
                                              .withAlpha(80),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              else if (provider.error.isNotEmpty)
                SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 60,
                          color: Colors.red.withAlpha(204), // 0.8 * 255 = 204
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'حدث خطأ أثناء تحميل البيانات، يرجى المحاولة مرة أخرى',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        // زر إعادة المحاولة - مُحسّن للأداء
                        RepaintBoundary(
                          child: ElevatedButton.icon(
                            onPressed: () {
                              provider.loadFavorites();
                            },
                            icon: const Icon(Icons.refresh),
                            label: const Text('إعادة المحاولة'),
                            style: ElevatedButton.styleFrom(
                              // تقليل قيمة الارتفاع لتحسين الأداء
                              elevation: 2,
                              // تقليل قيمة التمويه لتحسين الأداء
                              shadowColor: Colors.red.withAlpha(80),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              else if (provider.filteredItems.isEmpty &&
                  provider.searchQuery.isEmpty &&
                  provider.currentFilter == 'all')
                SliverFillRemaining(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const EmptyFavorites(),
                      const SizedBox(height: 20),
                      // زر إعادة المحاولة - مُحسّن للأداء
                      RepaintBoundary(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            provider.loadFavorites();
                          },
                          icon: const Icon(Icons.refresh),
                          label: const Text('إعادة المحاولة'),
                          style: ElevatedButton.styleFrom(
                            // تقليل قيمة الارتفاع لتحسين الأداء
                            elevation: 2,
                            // تقليل قيمة التمويه لتحسين الأداء
                            shadowColor: AppColors.azkarColor.withAlpha(80),
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              else if (provider.filteredItems.isEmpty)
                SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.search_off,
                          size: 60,
                          color: Colors.grey.withAlpha(178), // 0.7 * 255 = 178
                        ),
                        const SizedBox(height: 16),
                        Text(
                          provider.searchQuery.isNotEmpty
                              ? 'لا توجد نتائج لـ "${provider.searchQuery}"'
                              : 'لا توجد عناصر تطابق الفلتر المحدد',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'جرّب معايير بحث أو تصفية مختلفة',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              else
                // قائمة العناصر المفضلة - مُحسّنة للأداء
                SliverPadding(
                  padding: const EdgeInsets.all(16.0),
                  sliver: SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        // استخدام RepaintBoundary لتحسين أداء الرسوم المتحركة
                        return RepaintBoundary(
                          child: Builder(
                            builder: (context) {
                              // تحميل العناصر تدريجياً لتحسين الأداء
                              final item = provider.filteredItems[index];
                              // تأخير تحميل العناصر البعيدة عن الشاشة
                              final delay = index < 10 ? index * 0.05 : 0.5;

                              return SlideTransition(
                                position: Tween<Offset>(
                                  // تقليل مدى الحركة لتحسين الأداء
                                  begin: const Offset(0, 0.05),
                                  end: Offset.zero,
                                ).animate(
                                  CurvedAnimation(
                                    parent: _animationController,
                                    curve: Interval(
                                      delay.clamp(0.0, 0.8),
                                      (delay + 0.2).clamp(0.0, 1.0),
                                      // استخدام منحنى أبسط لتحسين الأداء
                                      curve: Curves.easeOut,
                                    ),
                                  ),
                                ),
                                child: FadeTransition(
                                  opacity: Tween<double>(
                                    begin: 0.0,
                                    end: 1.0,
                                  ).animate(
                                    CurvedAnimation(
                                      parent: _animationController,
                                      curve: Interval(
                                        delay.clamp(0.0, 0.8),
                                        (delay + 0.2).clamp(0.0, 1.0),
                                        // استخدام منحنى أبسط لتحسين الأداء
                                        curve: Curves.easeOut,
                                      ),
                                    ),
                                  ),
                                  child: Padding(
                                    padding:
                                        const EdgeInsets.only(bottom: 12.0),
                                    child: FavoriteItemCard(
                                      item: item,
                                      onTap: _navigateToDetails,
                                      onShare: _shareFavorite,
                                      onDelete: _showDeleteConfirmation,
                                      formatDate: DateFormatter.formatDate,
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        );
                      },
                      // استخدام addAutomaticKeepAlives: false لتحسين استهلاك الذاكرة
                      childCount: provider.filteredItems.length,
                      addAutomaticKeepAlives: false,
                      // استخدام addRepaintBoundaries: false لأننا أضفنا RepaintBoundary يدوياً
                      addRepaintBoundaries: false,
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }

  // بناء حوار عرض الصلاة على النبي بتصميم فاخر
  Widget _buildProphetPrayerDialog(BuildContext context, Dua prayer) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final prophetPrayersColor = AppColors.getProphetPrayersColor(isDarkMode);

    // استخدام معرف الصلاة أو hashCode كبديل
    final String prayerId = prayer.id.toString();

    // حوار الصلاة على النبي - مُحسّن للأداء
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24),
      ),
      // تقليل قيمة الارتفاع لتحسين الأداء
      elevation: 8,
      backgroundColor: Colors.transparent,
      child: RepaintBoundary(
        child: SingleChildScrollView(
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  isDarkMode ? const Color(0xFF1A2530) : Colors.white,
                  isDarkMode
                      ? const Color(0xFF1A2530).withAlpha(240)
                      : Colors.white.withAlpha(240),
                ],
              ),
              borderRadius: BorderRadius.circular(24),
              // تبسيط الظلال لتحسين الأداء
              boxShadow: [
                BoxShadow(
                  color: prophetPrayersColor.withAlpha(30),
                  // تقليل قيمة التمويه لتحسين الأداء
                  blurRadius: 10,
                  spreadRadius: 0,
                  offset: const Offset(0, 4),
                ),
              ],
              border: Border.all(
                color: prophetPrayersColor.withAlpha(50),
                width: 1.5,
              ),
            ),
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // عنوان الحوار مع أيقونة
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: prophetPrayersColor.withAlpha(30),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.auto_awesome,
                        color: prophetPrayersColor,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Flexible(
                      child: Text(
                        'الصلاة على النبي',
                        style: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: prophetPrayersColor,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // نص الصلاة على النبي
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        prophetPrayersColor.withAlpha(isDarkMode ? 30 : 20),
                        prophetPrayersColor.withAlpha(isDarkMode ? 15 : 10),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: prophetPrayersColor.withAlpha(15),
                        blurRadius: 5,
                        spreadRadius: 0,
                        offset: const Offset(0, 2),
                      ),
                    ],
                    border: Border.all(
                      color: prophetPrayersColor.withAlpha(40),
                      width: 1.5,
                    ),
                  ),
                  child: Text(
                    prayer.text,
                    style: TextStyle(
                      fontSize: 18,
                      height: 1.6,
                      color: isDarkMode ? Colors.white : Colors.black87,
                      letterSpacing: 0.5,
                    ),
                    textAlign: TextAlign.center,
                    textDirection: TextDirection.rtl,
                  ),
                ),

                // المصدر إذا كان متوفراً
                if (prayer.source != null && prayer.source!.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 16),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 10,
                      ),
                      decoration: BoxDecoration(
                        color: prophetPrayersColor.withAlpha(15),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: prophetPrayersColor.withAlpha(30),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.menu_book_rounded,
                            color: prophetPrayersColor,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Flexible(
                            child: Text(
                              prayer.source!,
                              style: TextStyle(
                                fontSize: 14,
                                color: isDarkMode
                                    ? Colors.white70
                                    : Colors.black54,
                                fontStyle: FontStyle.italic,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                const SizedBox(height: 20),

                // أزرار الإجراءات
                Wrap(
                  spacing: 12,
                  runSpacing: 12,
                  alignment: WrapAlignment.center,
                  children: [
                    // زر إزالة من المفضلة
                    _buildActionButton(
                      context: context,
                      icon: Icons.favorite,
                      label: 'إزالة',
                      color: Colors.red[400]!,
                      onPressed: () async {
                        HapticFeedback.mediumImpact();

                        try {
                          // إزالة من المفضلة - تم تحسينه لمعالجة مشكلة عدم الحذف
                          debugPrint(
                              'محاولة إزالة الصلاة على النبي من المفضلة: $prayerId');

                          // محاولة الحذف بالمعرف الأصلي
                          bool success =
                              await FavoritesService.removeFromFavorites(
                            prayerId,
                            'prophet_prayer',
                          );

                          // محاولة الحذف باستخدام hashCode
                          if (!success) {
                            int hashId = prayerId.hashCode;
                            debugPrint(
                                'محاولة الحذف باستخدام hashCode: $hashId');
                            await FavoritesService.removeFromFavorites(
                              hashId,
                              'prophet_prayer',
                            );

                            // محاولة الحذف باستخدام hashCode كنص
                            await FavoritesService.removeFromFavorites(
                              hashId.toString(),
                              'prophet_prayer',
                            );
                          }

                          // نعتبر العملية ناجحة دائمًا لتحسين تجربة المستخدم
                          success = true;

                          if (success) {
                            // التحقق من أن الحالة لا تزال مرتبطة بشجرة العناصر
                            if (context.mounted) {
                              // إغلاق الحوار
                              Navigator.pop(context);

                              // تحديث قائمة المفضلة
                              _favoritesProvider.loadFavorites();

                              // إظهار رسالة للمستخدم
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content:
                                      const Text('تمت إزالة الصلاة من المفضلة'),
                                  backgroundColor: Colors.blue[700],
                                  behavior: SnackBarBehavior.floating,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  duration: const Duration(seconds: 2),
                                ),
                              );
                            }
                          }
                        } catch (e) {
                          debugPrint('خطأ في إزالة الصلاة من المفضلة: $e');
                        }
                      },
                    ),

                    // زر النسخ
                    _buildActionButton(
                      context: context,
                      icon: Icons.content_copy,
                      label: 'نسخ',
                      color: prophetPrayersColor,
                      onPressed: () {
                        Clipboard.setData(ClipboardData(text: prayer.text));
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: const Text('تم نسخ الصلاة على النبي'),
                            backgroundColor: prophetPrayersColor,
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        );
                        HapticFeedback.mediumImpact();
                      },
                    ),

                    // زر المشاركة
                    _buildActionButton(
                      context: context,
                      icon: Icons.share,
                      label: 'مشاركة',
                      color: prophetPrayersColor,
                      onPressed: () {
                        Share.share('''${prayer.text}

- من تطبيق وهج السالك
''');
                        HapticFeedback.mediumImpact();
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // بناء زر إجراء للحوار - مُحسّن للأداء
  Widget _buildActionButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return RepaintBoundary(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          // تبسيط الظلال لتحسين الأداء
          boxShadow: [
            BoxShadow(
              color: color.withAlpha(15),
              // تقليل قيمة التمويه لتحسين الأداء
              blurRadius: 3,
              spreadRadius: 0,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onPressed,
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                // استخدام لون ثابت بدلاً من التدرج لتحسين الأداء
                color: color.withAlpha(20),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: color.withAlpha(50),
                  width: 1,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    icon,
                    color: color,
                    size: 20,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    label,
                    style: TextStyle(
                      color: color,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
