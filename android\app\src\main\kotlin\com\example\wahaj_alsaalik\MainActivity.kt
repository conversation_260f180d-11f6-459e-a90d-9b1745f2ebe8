package com.example.wahaj_alsaalik

import android.app.DownloadManager
import android.content.Context
import android.content.Intent
import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.os.Looper
import androidx.annotation.NonNull
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugins.GeneratedPluginRegistrant
import java.io.File

class MainActivity: FlutterActivity() {
    private val SHARE_CHANNEL = "com.example.wahaj_alsaalik/share"
    private val DOWNLOAD_CHANNEL = "com.example.wahaj_alsaalik/download"
    private val NOTIFICATION_CHANNEL_ID = "wahaj_alsaalik_channel"
    private val downloadStatusMap = mutableMapOf<Long, Boolean>()
    private val handler = Handler(Looper.getMainLooper())

    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        GeneratedPluginRegistrant.registerWith(flutterEngine)

        // قناة مشاركة المحتوى
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, SHARE_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "sharePoem" -> {
                    val title = call.argument<String>("title") ?: ""
                    val poet = call.argument<String>("poet") ?: ""
                    val content = call.argument<String>("content") ?: ""

                    sharePoem(title, poet, content)
                    result.success(null)
                }
                "shareBook" -> {
                    val title = call.argument<String>("title") ?: ""
                    val author = call.argument<String>("author") ?: ""
                    val description = call.argument<String>("description") ?: ""

                    shareBook(title, author, description)
                    result.success(null)
                }
                "shareAzkar" -> {
                    val title = call.argument<String>("title") ?: ""
                    val category = call.argument<String>("category") ?: ""
                    val content = call.argument<String>("content") ?: ""

                    shareAzkar(title, category, content)
                    result.success(null)
                }
                else -> result.notImplemented()
            }
        }

        // قناة تنزيل الملفات
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, DOWNLOAD_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "downloadFile" -> {
                    val url = call.argument<String>("url") ?: ""
                    val fileName = call.argument<String>("fileName") ?: "download.pdf"
                    val title = call.argument<String>("title") ?: "جاري التنزيل"
                    val description = call.argument<String>("description") ?: "يتم تنزيل الملف..."

                    if (url.isEmpty()) {
                        result.error("INVALID_URL", "رابط التنزيل غير صالح", null)
                        return@setMethodCallHandler
                    }

                    try {
                        val downloadId = downloadFile(url, fileName, title, description)
                        result.success(downloadId.toString())

                        // بدء مراقبة حالة التنزيل
                        monitorDownload(downloadId)
                    } catch (e: Exception) {
                        result.error("DOWNLOAD_ERROR", "حدث خطأ أثناء بدء التنزيل: ${e.message}", null)
                    }
                }
                "getDownloadStatus" -> {
                    val downloadId = call.argument<String>("downloadId")?.toLongOrNull() ?: -1L
                    if (downloadId == -1L) {
                        result.error("INVALID_ID", "معرف التنزيل غير صالح", null)
                        return@setMethodCallHandler
                    }

                    val status = getDownloadStatus(downloadId)
                    result.success(status)
                }
                "cancelDownload" -> {
                    val downloadId = call.argument<String>("downloadId")?.toLongOrNull() ?: -1L
                    if (downloadId == -1L) {
                        result.error("INVALID_ID", "معرف التنزيل غير صالح", null)
                        return@setMethodCallHandler
                    }

                    val success = cancelDownload(downloadId)
                    result.success(success)
                }
                else -> result.notImplemented()
            }
        }
    }

    // مشاركة القصيدة
    private fun sharePoem(title: String, poet: String, content: String) {
        val shareText = """$title
            |للشاعر: $poet
            |
            |$content
            |
            |مشاركة من تطبيق وهج السالك""".trimMargin()

        val sendIntent: Intent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(Intent.EXTRA_TEXT, shareText)
            type = "text/plain"
        }

        val shareIntent = Intent.createChooser(sendIntent, "مشاركة القصيدة")
        startActivity(shareIntent)
    }

    // مشاركة الكتاب
    private fun shareBook(title: String, author: String, description: String) {
        val shareText = """$title
            |المؤلف: $author
            |
            |$description
            |
            |مشاركة من تطبيق وهج السالك""".trimMargin()

        val sendIntent: Intent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(Intent.EXTRA_TEXT, shareText)
            putExtra(Intent.EXTRA_SUBJECT, "مشاركة كتاب: $title")
            type = "text/plain"
        }

        val shareIntent = Intent.createChooser(sendIntent, "مشاركة الكتاب")
        startActivity(shareIntent)
    }

    // مشاركة الأذكار
    private fun shareAzkar(title: String, category: String, content: String) {
        val shareText = """$title
            |$category
            |
            |$content
            |
            |مشاركة من تطبيق وهج السالك""".trimMargin()

        val sendIntent: Intent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(Intent.EXTRA_TEXT, shareText)
            putExtra(Intent.EXTRA_SUBJECT, "مشاركة ذكر: $title")
            type = "text/plain"
        }

        val shareIntent = Intent.createChooser(sendIntent, "مشاركة الذكر")
        startActivity(shareIntent)
    }

    // تنزيل ملف
    private fun downloadFile(url: String, fileName: String, title: String, description: String): Long {
        val request = DownloadManager.Request(Uri.parse(url))
            .setTitle(title)
            .setDescription(description)
            .setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
            .setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, fileName)
            .setAllowedOverMetered(true)
            .setAllowedOverRoaming(true)

        val downloadManager = getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
        val downloadId = downloadManager.enqueue(request)
        downloadStatusMap[downloadId] = false
        return downloadId
    }

    // مراقبة حالة التنزيل
    private fun monitorDownload(downloadId: Long) {
        val runnable = object : Runnable {
            override fun run() {
                val status = getDownloadStatus(downloadId)
                val isCompleted = status == "SUCCESSFUL" || status == "FAILED"

                if (isCompleted) {
                    downloadStatusMap[downloadId] = true
                } else {
                    handler.postDelayed(this, 1000) // التحقق كل ثانية
                }
            }
        }
        handler.post(runnable)
    }

    // الحصول على حالة التنزيل
    private fun getDownloadStatus(downloadId: Long): String {
        val downloadManager = getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
        val query = DownloadManager.Query().setFilterById(downloadId)
        val cursor = downloadManager.query(query)

        if (cursor.moveToFirst()) {
            val statusIndex = cursor.getColumnIndex(DownloadManager.COLUMN_STATUS)
            val reasonIndex = cursor.getColumnIndex(DownloadManager.COLUMN_REASON)
            val bytesDownloadedIndex = cursor.getColumnIndex(DownloadManager.COLUMN_BYTES_DOWNLOADED_SO_FAR)
            val bytesTotalIndex = cursor.getColumnIndex(DownloadManager.COLUMN_TOTAL_SIZE_BYTES)

            val status = cursor.getInt(statusIndex)
            val reason = cursor.getInt(reasonIndex)
            val bytesDownloaded = cursor.getLong(bytesDownloadedIndex)
            val bytesTotal = cursor.getLong(bytesTotalIndex)

            cursor.close()

            val progress = if (bytesTotal > 0) {
                (bytesDownloaded * 100 / bytesTotal).toInt()
            } else {
                -1
            }

            return when (status) {
                DownloadManager.STATUS_SUCCESSFUL -> "SUCCESSFUL"
                DownloadManager.STATUS_FAILED -> "FAILED:$reason"
                DownloadManager.STATUS_PAUSED -> "PAUSED:$progress"
                DownloadManager.STATUS_PENDING -> "PENDING:$progress"
                DownloadManager.STATUS_RUNNING -> "RUNNING:$progress"
                else -> "UNKNOWN"
            }
        }
        cursor.close()
        return "UNKNOWN"
    }

    // إلغاء التنزيل
    private fun cancelDownload(downloadId: Long): Boolean {
        val downloadManager = getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
        val result = downloadManager.remove(downloadId)
        downloadStatusMap.remove(downloadId)
        return result > 0
    }
}
