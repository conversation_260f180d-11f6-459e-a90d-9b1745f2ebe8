// شاشة الشرح التوضيحي للمسبحة
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
//import 'package:lottie/lottie.dart';
import '../utils/tasbih_colors.dart';

class TasbihTutorialScreen extends StatefulWidget {
  final VoidCallback onComplete;

  const TasbihTutorialScreen({
    Key? key,
    required this.onComplete,
  }) : super(key: key);

  @override
  State<TasbihTutorialScreen> createState() => _TasbihTutorialScreenState();

  /// عرض الشرح التوضيحي للمسبحة فقط عند تثبيت التطبيق لأول مرة
  static Future<bool> showIfFirstTime(BuildContext context) async {
    final prefs = await SharedPreferences.getInstance();

    // استخدام مفتاح خاص للتحقق من أول تثبيت للتطبيق
    final isFirstInstall = prefs.getBool('app_first_install') ?? true;

    // التحقق مما إذا تم عرض الشرح التوضيحي من قبل
    final hasSeenTutorial = prefs.getBool('tasbih_tutorial_shown') ?? false;

    // عرض الشرح التوضيحي فقط إذا كان هذا أول تثبيت ولم يتم عرض الشرح من قبل
    if (isFirstInstall && !hasSeenTutorial) {
      // تعيين أن هذا ليس أول تثبيت بعد الآن
      await prefs.setBool('app_first_install', false);

      if (context.mounted) {
        await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => Dialog(
            backgroundColor: Colors.transparent,
            elevation: 0,
            insetPadding: EdgeInsets.zero,
            child: TasbihTutorialScreen(
              onComplete: () async {
                // تعيين أن المستخدم قد رأى الشرح التوضيحي
                await prefs.setBool('tasbih_tutorial_shown', true);
                if (context.mounted) {
                  Navigator.of(context).pop();
                }
              },
            ),
          ),
        );
      }
      return true;
    }
    return false;
  }

  /// إعادة تعيين حالة الشرح التوضيحي (للاختبار فقط)
  static Future<void> resetTutorialState() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('app_first_install', true);
    await prefs.setBool('tasbih_tutorial_shown', false);
  }
}

class _TasbihTutorialScreenState extends State<TasbihTutorialScreen>
    with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final List<TutorialStep> _tutorialSteps = [
    TutorialStep(
      title: 'المسبحة الإلكترونية',
      description:
          'مرحباً بك في المسبحة الإلكترونية، أداة مميزة تساعدك على الذكر والتسبيح بطريقة سهلة وفاخرة',
      animation: 'assets/animations/tasbih_intro.json',
      buttonIcon: Icons.touch_app,
      buttonLabel: 'دائرة التسبيح',
    ),
    TutorialStep(
      title: 'عداد التسبيح',
      description:
          'انقر على دائرة العداد لزيادة عدد التسبيحات، وشاهد الخرز تتحرك مع كل تسبيحة',
      animation: 'assets/animations/tasbih_counter.json',
      buttonIcon: Icons.radio_button_checked,
      buttonLabel: 'زر العداد',
    ),
    TutorialStep(
      title: 'اختيار الذكر',
      description:
          'يمكنك اختيار الذكر المفضل لديك من قائمة الأذكار المتاحة، أو إضافة أذكار جديدة',
      animation: 'assets/animations/tasbih_dhikr.json',
      buttonIcon: Icons.format_list_bulleted,
      buttonLabel: 'قائمة الأذكار',
    ),
    TutorialStep(
      title: 'تعديل العدد المستهدف',
      description:
          'يمكنك تغيير العدد المستهدف للتسبيح حسب رغبتك، مثل 33 أو 99 أو 100',
      animation: 'assets/animations/tasbih_count.json',
      buttonIcon: Icons.edit,
      buttonLabel: 'تعديل العدد',
    ),
    TutorialStep(
      title: 'الإحصائيات',
      description:
          'تتبع إحصائيات التسبيح الخاصة بك، مثل عدد الدورات المكتملة والمجموع الكلي',
      animation: 'assets/animations/tasbih_stats.json',
      buttonIcon: Icons.bar_chart,
      buttonLabel: 'الإحصائيات',
    ),
    TutorialStep(
      title: 'الأوراد اليومية',
      description:
          'يمكنك إنشاء أوراد يومية تحتوي على مجموعة من الأذكار لتساعدك على المداومة عليها',
      animation: 'assets/animations/tasbih_wird.json',
      buttonIcon: Icons.menu_book_outlined,
      buttonLabel: 'الأوراد',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _slideController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeIn),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutQuart),
    );

    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _tutorialSteps.length - 1) {
      _fadeController.reset();
      _slideController.reset();
      _pageController.nextPage(
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
      _fadeController.forward();
      _slideController.forward();
    } else {
      widget.onComplete();
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      width: size.width,
      height: size.height,
      color: Colors.transparent,
      child: Center(
        child: Container(
          width: size.width * 0.9,
          height: size.height *
              (size.height < 600 ? 0.85 : 0.8), // تعديل النسبة للشاشات الصغيرة
          decoration: BoxDecoration(
            color: isDarkMode ? TasbihColors.darkBackground : Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: TasbihColors.primary.withValues(alpha: 40),
                blurRadius: 10,
                spreadRadius: 1,
              ),
            ],
            border: Border.all(
              color: TasbihColors.primary.withValues(alpha: 30),
              width: 1,
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentPage = index;
                });
                // إعادة تشغيل الرسوم المتحركة عند تغيير الصفحة
                _fadeController.reset();
                _slideController.reset();
                _fadeController.forward();
                _slideController.forward();
              },
              itemCount: _tutorialSteps.length,
              itemBuilder: (context, index) {
                final step = _tutorialSteps[index];
                return _buildTutorialPage(step, isDarkMode, size);
              },
            ),
          ),
        ),
      ),
    );
  }

  // بناء عنصر الشرح التوضيحي المناسب لكل خطوة بشكل فاخر
  Widget _buildTutorialElement(
      TutorialStep step, BuildContext context, bool isDarkMode) {
    // بناءً على نوع الخطوة، نعرض العنصر المناسب
    switch (step.title) {
      case 'المسبحة الإلكترونية':
        // عرض دائرة التسبيح المبسطة
        return _buildCounterCircle(context, isDarkMode);

      case 'عداد التسبيح':
        // عرض دائرة العداد مع تأثير النقر
        return _buildCounterCircle(context, isDarkMode, showTapEffect: true);

      case 'اختيار الذكر':
        // عرض محدد الذكر
        return _buildDhikrSelector(context, isDarkMode);

      case 'تعديل العدد المستهدف':
        // عرض محدد العدد المستهدف
        return _buildCountSelector(context, isDarkMode);

      case 'الإحصائيات':
        // عرض بطاقة الإحصائيات المبسطة
        return _buildStatsCard(context, isDarkMode);

      case 'الأوراد اليومية':
        // عرض بطاقة الورد المبسطة
        return _buildWirdCard(context, isDarkMode);

      default:
        // عرض زر عام في حالة عدم وجود عنصر محدد
        return Container(
          margin: const EdgeInsets.symmetric(vertical: 8),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: TasbihColors.primary.withValues(alpha: 51),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: TasbihColors.primary.withValues(alpha: 77),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                step.buttonIcon,
                color: TasbihColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                step.buttonLabel,
                style: const TextStyle(
                  color: TasbihColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );
    }
  }

  // بناء دائرة العداد المبسطة
  Widget _buildCounterCircle(BuildContext context, bool isDarkMode,
      {bool showTapEffect = false}) {
    final screenWidth = MediaQuery.of(context).size.width;
    // تعديل حجم الدائرة ليتناسب مع الشاشات الصغيرة
    final size = screenWidth < 360 ? screenWidth * 0.2 : screenWidth * 0.25;

    return Stack(
      alignment: Alignment.center,
      children: [
        // الدائرة الخارجية المتوهجة
        Container(
          width: size + 10,
          height: size + 10,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                TasbihColors.primary.withValues(alpha: 80),
                TasbihColors.primary.withValues(alpha: 0),
              ],
              stops: const [0.7, 1.0],
            ),
          ),
        ),

        // الدائرة الرئيسية
        Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              center: const Alignment(-0.2, -0.3),
              radius: 1.0,
              colors: [
                Color.lerp(TasbihColors.primary, Colors.white, 0.3)!,
                TasbihColors.primary,
                Color.lerp(TasbihColors.primary, Colors.black, 0.2)!,
              ],
              stops: const [0.0, 0.6, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: TasbihColors.primary.withValues(alpha: 80),
                blurRadius: 12,
                spreadRadius: 2,
              ),
            ],
            border: Border.all(
              color: Colors.white.withValues(alpha: 40),
              width: 1.5,
            ),
          ),
          child: Center(
            child: Text(
              '33',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: Colors.black.withValues(alpha: 150),
                    offset: const Offset(0, 1),
                    blurRadius: 3,
                  ),
                ],
              ),
            ),
          ),
        ),

        // تأثير النقر
        if (showTapEffect)
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 50),
                    blurRadius: 5,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: const Icon(
                Icons.touch_app,
                color: TasbihColors.primary,
                size: 16,
              ),
            ),
          ),
      ],
    );
  }

  // بناء محدد الذكر المبسط
  Widget _buildDhikrSelector(BuildContext context, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: isDarkMode ? TasbihColors.darkCardColor : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDarkMode ? 60 : 20),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: TasbihColors.primary.withValues(alpha: 26),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.menu_book_rounded,
              color: TasbihColors.primary,
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'الذكر',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                      fontSize: 10,
                    ),
              ),
              const SizedBox(height: 2),
              Text(
                'سبحان الله',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
          const SizedBox(width: 8),
          Icon(
            Icons.arrow_drop_down,
            color: Colors.grey[600],
          ),
        ],
      ),
    );
  }

  // بناء محدد العدد المستهدف المبسط
  Widget _buildCountSelector(BuildContext context, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: isDarkMode ? TasbihColors.darkCardColor : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDarkMode ? 60 : 20),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'الهدف',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                  fontSize: 10,
                ),
          ),
          const SizedBox(height: 2),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '33',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: TasbihColors.primary,
                    ),
              ),
              Icon(
                Icons.arrow_drop_down,
                color: Colors.grey[600],
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بناء بطاقة الإحصائيات المبسطة
  Widget _buildStatsCard(BuildContext context, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: isDarkMode ? TasbihColors.darkCardColor : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDarkMode ? 60 : 20),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: TasbihColors.primary.withValues(alpha: 26),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.bar_chart,
              color: TasbihColors.primary,
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'إحصائيات المسبحة',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 2),
              Text(
                'عرض تفاصيل جلسات التسبيح',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                      fontSize: 10,
                    ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بناء بطاقة الورد المبسطة
  Widget _buildWirdCard(BuildContext context, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: isDarkMode ? TasbihColors.darkCardColor : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDarkMode ? 60 : 20),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: TasbihColors.primary.withValues(alpha: 26),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.menu_book_outlined,
              color: TasbihColors.primary,
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'الأوراد اليومية',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 2),
              Text(
                'إنشاء وتشغيل أوراد يومية',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                      fontSize: 10,
                    ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTutorialPage(TutorialStep step, bool isDarkMode, Size size) {
    return Column(
      children: [
        // رأس الصفحة بتصميم بسيط وفاخر
        Container(
          padding: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                TasbihColors.primary,
                Color.lerp(TasbihColors.primary, TasbihColors.secondary, 0.5)!,
              ],
            ),
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(12),
              bottomRight: Radius.circular(12),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                step.buttonIcon,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 10),
              Text(
                step.title,
                style: TextStyle(
                  fontSize: size.width < 360
                      ? 18
                      : 20, // تعديل حجم الخط للشاشات الصغيرة
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),

        // محتوى الصفحة
        Expanded(
          child: Container(
            padding: EdgeInsets.all(
                size.width < 360 ? 15 : 20), // تعديل الهوامش للشاشات الصغيرة
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // عرض العناصر المرئية بشكل بسيط وفاخر
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: Container(
                      height: size.height < 600
                          ? size.height * 0.25
                          : size.height * 0.3, // تعديل الارتفاع للشاشات الصغيرة
                      width: double.infinity,
                      color: Colors.transparent,
                      child: Center(
                        child: Container(
                          padding: EdgeInsets.all(size.width < 360
                              ? 12
                              : 16), // تعديل الهوامش للشاشات الصغيرة
                          decoration: BoxDecoration(
                            color: isDarkMode
                                ? TasbihColors.darkCardColor
                                : Colors.white,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color:
                                    TasbihColors.primary.withValues(alpha: 30),
                                blurRadius: 6,
                                spreadRadius: 1,
                              ),
                            ],
                            border: Border.all(
                              color: TasbihColors.primary.withValues(alpha: 30),
                              width: 1,
                            ),
                          ),
                          child: Transform.scale(
                            scale: size.width < 360
                                ? 1.0
                                : 1.2, // تعديل حجم التحويل للشاشات الصغيرة
                            child: _buildTutorialElement(
                                step, context, isDarkMode),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                SizedBox(
                    height: size.height < 600
                        ? 12
                        : 16), // تعديل المسافة للشاشات الصغيرة
                // الوصف
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: Text(
                    step.description,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: size.width < 360
                          ? 14
                          : 16, // تعديل حجم الخط للشاشات الصغيرة
                      color: isDarkMode ? Colors.white70 : Colors.black87,
                      height: 1.5,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        // أزرار التنقل بتصميم بسيط وفاخر
        Container(
          padding: EdgeInsets.all(
              size.width < 360 ? 15 : 20), // تعديل الهوامش للشاشات الصغيرة
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // مؤشر الصفحات
              Row(
                children: List.generate(
                  _tutorialSteps.length,
                  (index) {
                    final isActive = _currentPage == index;
                    return Container(
                      width: isActive ? 16 : 8,
                      height: 8,
                      margin: const EdgeInsets.symmetric(horizontal: 3),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4),
                        color: isActive
                            ? TasbihColors.primary
                            : (isDarkMode
                                ? TasbihColors.primary.withValues(alpha: 40)
                                : TasbihColors.primary.withValues(alpha: 30)),
                      ),
                    );
                  },
                ),
              ),
              // زر التالي
              ElevatedButton(
                onPressed: _nextPage,
                style: ElevatedButton.styleFrom(
                  backgroundColor: TasbihColors.primary,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                      horizontal: size.width < 360 ? 16 : 20,
                      vertical: size.width < 360
                          ? 8
                          : 10), // تعديل الهوامش للشاشات الصغيرة
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(
                      color: Colors.white.withValues(alpha: 30),
                      width: 1,
                    ),
                  ),
                  elevation: 1,
                  shadowColor: TasbihColors.primary.withValues(alpha: 40),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      _currentPage == _tutorialSteps.length - 1
                          ? 'إنهاء'
                          : 'التالي',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 4),
                    const Icon(
                      Icons.arrow_forward_ios,
                      size: 12,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// نموذج خطوة الشرح التوضيحي
class TutorialStep {
  final String title;
  final String description;
  final String animation;
  final IconData buttonIcon;
  final String buttonLabel;

  TutorialStep({
    required this.title,
    required this.description,
    required this.animation,
    required this.buttonIcon,
    required this.buttonLabel,
  });
}
