import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/app_colors.dart';

class EmptyFavorites extends StatelessWidget {
  const EmptyFavorites({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final primaryColor = AppColors.azkarColor;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // رسوم متحركة للقلب الفارغ مع تأثيرات محسنة
          Stack(
            alignment: Alignment.center,
            children: [
              // زخرفة خلفية
              Opacity(
                opacity: isDarkMode ? 0.05 : 0.08,
                child: SvgPicture.asset(
                  'assets/images/p2.svg',
                  width: 200,
                  height: 200,
                  colorFilter: ColorFilter.mode(
                    primaryColor,
                    BlendMode.srcIn,
                  ),
                ),
              ),

              // تأثير نبض للقلب - مُحسّن للأداء
              RepaintBoundary(
                child: TweenAnimationBuilder<double>(
                  tween: Tween<double>(begin: 0.9, end: 1.0),
                  // تقليل مدة الرسوم المتحركة لتحسين الأداء
                  duration: const Duration(milliseconds: 1200),
                  // استخدام منحنى أبسط لتحسين الأداء
                  curve: Curves.easeOut,
                  builder: (context, scale, child) {
                    return RepaintBoundary(
                      child: TweenAnimationBuilder<double>(
                        tween: Tween<double>(begin: 0.97, end: 1.03),
                        // تقليل مدة الرسوم المتحركة لتحسين الأداء
                        duration: const Duration(milliseconds: 800),
                        // استخدام منحنى أبسط لتحسين الأداء
                        curve: Curves.easeInOut,
                        builder: (context, pulse, child) {
                          return Transform.scale(
                            scale: scale * pulse,
                            child: Container(
                              width: ResponsiveHelper.getIconSize(context, 120),
                              height:
                                  ResponsiveHelper.getIconSize(context, 120),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: isDarkMode
                                    ? Colors.black
                                        .withAlpha(40) // 0.15 * 255 = ~40
                                    : Colors.white
                                        .withAlpha(230), // 0.9 * 255 = ~230
                                // تبسيط الظلال لتحسين الأداء
                                boxShadow: [
                                  BoxShadow(
                                    color: primaryColor.withAlpha(30),
                                    // تقليل قيمة التمويه لتحسين الأداء
                                    blurRadius: 15,
                                    spreadRadius: 3,
                                  ),
                                ],
                              ),
                              child: Icon(
                                Icons.favorite_border_rounded,
                                size: ResponsiveHelper.getIconSize(context, 60),
                                color: primaryColor
                                    .withAlpha(180), // 0.7 * 255 = ~180
                              ),
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
              ),
            ],
          ),

          const SizedBox(height: 30),

          // عنوان محسن
          Text(
            'لا توجد أذكار في المفضلة',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black87,
              fontSize: 22,
            ),
          ),

          const SizedBox(height: 16),

          // وصف محسن
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 40),
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? Colors.black.withAlpha(40) // 0.15 * 255 = ~40
                  : primaryColor.withAlpha(15), // 0.06 * 255 = ~15
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: primaryColor.withAlpha(30), // 0.12 * 255 = ~30
                width: 0.5,
              ),
            ),
            child: Text(
              'أضف الأذكار إلى المفضلة لتظهر هنا\nالكتب والقصائد ستكون متاحة قريباً',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDarkMode ? Colors.white70 : Colors.black87,
                height: 1.5,
                fontSize: 15,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(height: 30),

          // زر محسن
          ElevatedButton.icon(
            onPressed: () {
              HapticFeedback.mediumImpact();
              Navigator.pushNamed(context, '/azkar');
            },
            icon: const Icon(Icons.menu_book_rounded),
            label: const Text('استعراض الأذكار'),
            style: ElevatedButton.styleFrom(
              backgroundColor: primaryColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(
                horizontal: ResponsiveHelper.getPadding(
                        context, const EdgeInsets.symmetric(horizontal: 30))
                    .horizontal,
                vertical: ResponsiveHelper.getPadding(
                        context, const EdgeInsets.symmetric(vertical: 15))
                    .vertical,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              elevation: 4,
              shadowColor: primaryColor.withAlpha(100), // 0.4 * 255 = ~100
            ),
          ),
        ],
      ),
    );
  }
}
