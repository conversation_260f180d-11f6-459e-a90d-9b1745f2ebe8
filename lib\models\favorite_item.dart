import 'package:flutter/material.dart';
import '../utils/app_colors.dart';

/// نموذج عنصر المفضلة
class FavoriteItem {
  final String id;
  final String title;
  final String subtitle;
  final String type; // نوع العنصر: كتاب، قصيدة، ذكر
  final dynamic item; // الكائن الأصلي
  final DateTime addedDate; // تاريخ الإضافة إلى المفضلة

  FavoriteItem({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.type,
    required this.item,
    required this.addedDate,
  });

  // طريقة مساعدة للحصول على لون العنصر
  Color get color {
    switch (type) {
      case 'book':
        return Colors.blue;
      case 'poem':
        return Colors.purple;
      case 'azkar':
        return Colors.green;
      case 'dua':
        return Colors.teal;
      case 'prophet_prayer':
        return AppColors.prophetPrayersColor;
      default:
        return Colors.amber;
    }
  }

  // طريقة مساعدة للحصول على أيقونة العنصر
  IconData get icon {
    switch (type) {
      case 'book':
        return Icons.book;
      case 'poem':
        return Icons.music_note;
      case 'azkar':
        return Icons.favorite;
      case 'dua':
        return Icons.format_quote_rounded;
      case 'prophet_prayer':
        return Icons.auto_awesome;
      default:
        return Icons.bookmark;
    }
  }

  // طريقة مساعدة للحصول على تسمية نوع العنصر
  String get typeLabel {
    switch (type) {
      case 'book':
        return 'كتاب';
      case 'poem':
        return 'قصيدة';
      case 'azkar':
        return 'ذكر';
      case 'dua':
        return 'دعاء';
      case 'prophet_prayer':
        return 'صلاة على النبي';
      default:
        return 'عنصر';
    }
  }
}
