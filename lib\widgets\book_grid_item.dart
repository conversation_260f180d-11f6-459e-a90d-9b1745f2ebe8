import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // Necesario para HapticFeedback
import '../models/book.dart';
import '../utils/app_colors.dart';
import 'book_cover.dart';

class BookGridItem extends StatefulWidget {
  final Book book;
  final VoidCallback onTap;
  final bool animate;
  final Animation<double>? animation;

  const BookGridItem({
    Key? key,
    required this.book,
    required this.onTap,
    this.animate = false,
    this.animation,
  }) : super(key: key);

  @override
  State<BookGridItem> createState() => _BookGridItemState();
}

class _BookGridItemState extends State<BookGridItem>
    with SingleTickerProviderStateMixin {
  bool _isHovering = false;
  late AnimationController _hoverController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.04).animate(
      CurvedAnimation(
        parent: _hoverController,
        curve: Curves.easeOut,
      ),
    );

    _elevationAnimation = Tween<double>(begin: 3.0, end: 8.0).animate(
      CurvedAnimation(
        parent: _hoverController,
        curve: Curves.easeOut,
      ),
    );
  }

  @override
  void dispose() {
    _hoverController.dispose();
    super.dispose();
  }

  void _onHoverChanged(bool isHovering) {
    if (_isHovering != isHovering) {
      setState(() {
        _isHovering = isHovering;
      });

      if (isHovering) {
        _hoverController.forward();
      } else {
        _hoverController.reverse();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // استخدام AnimationBuilder واحد فقط للتحكم في جميع الرسوم المتحركة
    final animatedContent = AnimatedBuilder(
      animation: Listenable.merge([_hoverController]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Material(
            elevation: _elevationAnimation.value,
            shadowColor: AppColors.getCategoryColor(widget.book.category)
                .withValues(alpha: 77), // 0.3 * 255 = ~77
            borderRadius: const BorderRadius.all(Radius.circular(20)),
            child: child,
          ),
        );
      },
      child: _buildCard(),
    );

    // تطبيق الرسوم المتحركة إذا كانت مطلوبة
    if (widget.animate && widget.animation != null) {
      return FadeTransition(
        opacity: widget.animation!,
        child: SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, 0.2),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: widget.animation!,
            curve: Curves.easeOutCubic,
          )),
          child: animatedContent,
        ),
      );
    }

    return animatedContent;
  }

  Widget _buildCard() {
    final categoryColor = AppColors.getCategoryColor(widget.book.category);
    final textTheme = Theme.of(context).textTheme;

    return MouseRegion(
      onEnter: (_) => _onHoverChanged(true),
      onExit: (_) => _onHoverChanged(false),
      child: GestureDetector(
        onTap: () {
          HapticFeedback.lightImpact();
          widget.onTap();
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(Radius.circular(20)),
            border: Border.all(
              color: Colors.grey.withValues(alpha: 26), // 0.1 * 255 = ~26
              width: 0.5,
            ),
          ),
          clipBehavior: Clip.antiAlias,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // غلاف الكتاب
              Expanded(
                flex: 20,
                child: Hero(
                  tag: 'book_cover_${widget.book.id}',
                  child: Stack(
                    key: ValueKey('book_cover_stack_${widget.book.id}'),
                    fit: StackFit.expand,
                    children: [
                      // غلاف الكتاب مع حاوية للظل
                      Container(
                        decoration: BoxDecoration(
                          borderRadius:
                              const BorderRadius.all(Radius.circular(16)),
                          boxShadow: _isHovering
                              ? [
                                  BoxShadow(
                                    color: categoryColor.withValues(
                                        alpha: 77), // 0.3 * 255 = ~77
                                    blurRadius: 15,
                                    offset: const Offset(0, 5),
                                  )
                                ]
                              : null,
                        ),
                        child: BookCover(
                          key: ValueKey('book_cover_widget_${widget.book.id}'),
                          book: widget.book,
                          borderRadius: 16,
                        ),
                      ),

                      // طبقة تدرج محسنة
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Container(
                          height: 80,
                          decoration: const BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Color.fromRGBO(0, 0, 0, 0.1),
                                Color.fromRGBO(0, 0, 0, 0.7),
                              ],
                              stops: [0.0, 0.4, 1.0],
                            ),
                          ),
                        ),
                      ),

                      // فئة الكتاب بتصميم محسن
                      Positioned(
                        top: 12,
                        right: 12,
                        child: _buildCategoryChip(categoryColor),
                      ),

                      // عرض عدد الصفحات بتصميم محسن
                      if (widget.book.pages > 0)
                        Positioned(
                          bottom: 12,
                          left: 12,
                          child: _buildPagesCounter(),
                        ),

                      // زر التحميل أو القراءة
                      Positioned(
                        bottom: 12,
                        right: 12,
                        child: _buildActionButton(),
                      ),
                    ],
                  ),
                ),
              ),

              // معلومات الكتاب
              Expanded(
                flex: 8,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: const EdgeInsets.fromLTRB(12, 10, 12, 10),
                  decoration: BoxDecoration(
                    color: _isHovering
                        ? categoryColor.withValues(
                            alpha: 13) // 0.05 * 255 = ~13
                        : Colors.white,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(20),
                      bottomRight: Radius.circular(20),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // عنوان الكتاب
                      Text(
                        widget.book.title,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 2),

                      // معلومات الكاتب والنشر
                      if (widget.book.author.isNotEmpty)
                        Row(
                          children: [
                            Icon(
                              Icons.person_outline,
                              size: 12,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                widget.book.author,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: textTheme.bodySmall?.copyWith(
                                  color: Colors.grey[600],
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryChip(Color categoryColor) {
    return AnimatedContainer(
      key: ValueKey('category_chip_${widget.book.category}'),
      duration: const Duration(milliseconds: 200),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _isHovering
            ? categoryColor.withValues(alpha: 217) // 0.85 * 255 = ~217
            : categoryColor.withValues(alpha: 191), // 0.75 * 255 = ~191
        borderRadius: const BorderRadius.all(Radius.circular(30)),
        boxShadow: _isHovering
            ? [
                BoxShadow(
                  color:
                      categoryColor.withValues(alpha: 102), // 0.4 * 255 = ~102
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                )
              ]
            : null,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getCategoryIcon(widget.book.category),
            color: Colors.white,
            size: 10,
          ),
          const SizedBox(width: 4),
          Text(
            widget.book.category,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.bold,
              letterSpacing: 0.3,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPagesCounter() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.black.withValues(
            alpha:
                _isHovering ? 179 : 153), // 0.7 * 255 = ~179, 0.6 * 255 = ~153
        borderRadius: BorderRadius.circular(30),
        border: Border.all(
          color: Colors.white.withValues(alpha: 51), // 0.2 * 255 = ~51
          width: 0.5,
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.menu_book,
            color: Colors.white,
            size: 10,
          ),
          const SizedBox(width: 4),
          Text(
            '${widget.book.pages} صفحة',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton() {
    final categoryColor = AppColors.getCategoryColor(widget.book.category);

    return AnimatedOpacity(
      opacity: _isHovering ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 200),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(30),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 51), // 0.2 * 255 = ~51
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(30),
            onTap: widget.onTap,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.visibility,
                    size: 12,
                    color: categoryColor,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'عرض',
                    style: TextStyle(
                      color: categoryColor,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'رواية':
        return Icons.auto_stories;
      case 'ديني':
        return Icons.mosque;
      case 'تاريخ':
        return Icons.history_edu;
      case 'فلسفة':
        return Icons.psychology;
      case 'علمي':
        return Icons.science;
      case 'أدب':
        return Icons.menu_book;
      default:
        return Icons.category;
    }
  }
}
