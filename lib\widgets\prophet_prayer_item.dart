// مكون عرض صلاة فردية على النبي بتصميم فاخر

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;

import '../models/dua.dart'; // مستخدم لـ widget.prayer
import '../utils/app_colors.dart';
import 'package:share_plus/share_plus.dart';
import '../services/favorites_service.dart';
// import '../models/favorite_item.dart'; // هذا الاستيراد غير مستخدم، سأبقيه كما هو بناءً على طلبك

class ProphetPrayerItem extends StatefulWidget {
  final Dua prayer;
  final int index;
  final bool isLast;

  const ProphetPrayerItem({
    Key? key, // استخدام super.key إذا كان الإصدار يسمح
    required this.prayer,
    required this.index,
    this.isLast = false,
  }) : super(key: key);

  @override
  State<ProphetPrayerItem> createState() => _ProphetPrayerItemState();
}

class _ProphetPrayerItemState extends State<ProphetPrayerItem>
    with TickerProviderStateMixin {
  // تم تغيير SingleTickerProviderStateMixin إلى TickerProviderStateMixin
  late AnimationController _animationController;
  late AnimationController _shimmerController;
  late AnimationController _expandController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _shimmerAnimation;
  late Animation<double> _expandAnimation;
  bool _isExpanded = false;
  bool _isCopied = false;
  bool _isHovering = false;
  bool _isFavorite = false; // حالة المفضلة

  @override
  void initState() {
    super.initState();

    // التحقق من حالة المفضلة
    _checkFavoriteStatus();

    // تهيئة محرك الرسوم المتحركة للضغط - تم الإبقاء على المدة لأنها قصيرة
    _animationController = AnimationController(
      duration: const Duration(
          milliseconds: 200), // تقليل المدة من 300 إلى 200 مللي ثانية
      vsync: this,
    );

    // تقليل مدى التصغير لتحسين الأداء
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.99).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut, // تبسيط منحنى الحركة
      ),
    );

    // تهيئة محرك الرسوم المتحركة للتوهج - تم زيادة المدة لتقليل استهلاك المعالج
    _shimmerController = AnimationController(
      duration:
          const Duration(seconds: 3), // زيادة المدة من 1.5 ثانية إلى 3 ثوانٍ
      vsync: this,
    )..repeat(reverse: false);

    // تقليل مدى التوهج لتحسين الأداء
    _shimmerAnimation = Tween<double>(begin: -0.2, end: 1.2).animate(
      CurvedAnimation(
        parent: _shimmerController,
        curve: Curves.easeInOut,
      ),
    );

    // تهيئة محرك الرسوم المتحركة للتوسيع - تم الإبقاء على المدة لأنها قصيرة
    _expandController = AnimationController(
      duration: const Duration(
          milliseconds: 250), // تقليل المدة من 300 إلى 250 مللي ثانية
      vsync: this,
    );

    // تبسيط منحنى الحركة لتحسين الأداء
    _expandAnimation = CurvedAnimation(
      parent: _expandController,
      curve: Curves.easeOut, // تبسيط منحنى الحركة
      reverseCurve: Curves.easeIn, // تبسيط منحنى الحركة
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _shimmerController.dispose();
    _expandController.dispose();
    super.dispose();
  }

  // تم تحسين دالة التوسيع للأداء
  void _toggleExpanded() {
    if (!mounted) return; // إضافة تحقق mounted لتجنب الأخطاء

    // تقليل استدعاءات setState
    _isExpanded = !_isExpanded;

    if (_isExpanded) {
      _expandController.forward();
    } else {
      _expandController.reverse();
    }

    // استدعاء setState بعد تغيير الحالة
    setState(() {});

    // تقليل قوة الاهتزاز
    HapticFeedback.selectionClick(); // استخدام اهتزاز أخف
  }

  // تم تحسين دالة تغيير التحويم للأداء
  void _onHoverChanged(bool isHovering) {
    if (!mounted) return; // إضافة تحقق mounted لتجنب الأخطاء

    // تجنب استدعاء setState إذا لم تتغير الحالة
    if (_isHovering == isHovering) return;

    setState(() {
      _isHovering = isHovering;
    });
  }

  // تم تحسين دالة النسخ للأداء
  void _copyToClipboard() {
    if (!mounted) return; // إضافة تحقق mounted لتجنب الأخطاء

    // نسخ النص إلى الحافظة
    Clipboard.setData(ClipboardData(text: widget.prayer.text));

    // تحديث حالة النسخ
    setState(() {
      _isCopied = true;
    });

    // تقليل قوة الاهتزاز
    HapticFeedback.lightImpact(); // استخدام اهتزاز أخف

    // إظهار رسالة نجاح
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('تم نسخ الصلاة بنجاح'),
        backgroundColor: Colors.green[700],
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        // تقليل مدة الظهور من 2 ثانية إلى 1.5 ثانية
        duration: const Duration(milliseconds: 1500),
      ),
    );

    // إعادة تعيين حالة النسخ بعد 1.5 ثانية
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (mounted) {
        setState(() {
          _isCopied = false;
        });
      }
    });
  }

  // تم تحسين دالة المشاركة للأداء
  void _sharePrayer() {
    if (!mounted) return; // إضافة تحقق mounted لتجنب الأخطاء

    final String shareText = '''
${widget.prayer.text}

- من تطبيق وهج السالك
''';

    // مشاركة النص
    Share.share(shareText);

    // تقليل قوة الاهتزاز
    HapticFeedback.lightImpact(); // استخدام اهتزاز أخف
  }

  // التحقق من حالة المفضلة
  Future<void> _checkFavoriteStatus() async {
    try {
      final isFavorite = await FavoritesService.isFavorite(
        // الأصلي
        widget.prayer.id,
        'prophet_prayer',
      );

      if (mounted) {
        // الأصلي
        setState(() {
          _isFavorite = isFavorite;
        });
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من حالة المفضلة: $e'); // الأصلي
    }
  }

  // إضافة أو إزالة من المفضلة
  Future<void> _toggleFavorite() async {
    try {
      HapticFeedback.mediumImpact(); // الأصلي

      final bool success; // الأصلي
      if (_isFavorite) {
        // الأصلي
        // إزالة من المفضلة
        success = await FavoritesService.removeFromFavorites(
          // الأصلي
          widget.prayer.id,
          'prophet_prayer',
        );
      } else {
        // إضافة إلى المفضلة
        success = await FavoritesService.addToFavorites(
          // الأصلي
          widget.prayer.id,
          'prophet_prayer',
        );
      }

      if (success && mounted) {
        // الأصلي
        setState(() {
          _isFavorite = !_isFavorite;
        });

        // إظهار رسالة للمستخدم
        ScaffoldMessenger.of(context).showSnackBar(
          // الأصلي
          SnackBar(
            content: Text(_isFavorite // الأصلي
                ? 'تمت إضافة الصلاة إلى المفضلة'
                : 'تمت إزالة الصلاة من المفضلة'),
            backgroundColor:
                _isFavorite ? Colors.green[700] : Colors.blue[700], // الأصلي
            behavior: SnackBarBehavior.floating, // الأصلي
            shape: RoundedRectangleBorder(
              // الأصلي
              borderRadius: BorderRadius.circular(10),
            ),
            duration: const Duration(seconds: 2), // الأصلي
          ),
        );
      }
    } catch (e) {
      debugPrint('خطأ في تبديل حالة المفضلة: $e'); // الأصلي

      // إظهار رسالة خطأ للمستخدم
      if (mounted) {
        // الأصلي
        ScaffoldMessenger.of(context).showSnackBar(
          // الأصلي
          SnackBar(
            content: const Text('حدث خطأ أثناء تحديث المفضلة'), // الأصلي
            backgroundColor: Colors.red[700], // الأصلي
            behavior: SnackBarBehavior.floating, // الأصلي
            shape: RoundedRectangleBorder(
              // الأصلي
              borderRadius: BorderRadius.circular(10),
            ),
            duration: const Duration(seconds: 2), // الأصلي
          ),
        );
      }
    }
  }

  // بناء زر تفاعلي فاخر - تم تحسينه للأداء
  Widget _buildActionButton({
    required IconData icon,
    required IconData activeIcon,
    required bool isActive,
    required Color activeColor,
    required String tooltip,
    required VoidCallback onPressed,
    required bool isDarkMode,
  }) {
    // استخدام RepaintBoundary لتحسين أداء الرسم
    return RepaintBoundary(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          // تبسيط الألوان لتحسين الأداء
          color: isActive ? activeColor.withAlpha(25) : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: onPressed,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: AnimatedSwitcher(
                // تقليل مدة الرسوم المتحركة من 300 إلى 200 مللي ثانية
                duration: const Duration(milliseconds: 200),
                // تبسيط منحنى الحركة
                transitionBuilder: (child, animation) {
                  return ScaleTransition(
                    scale: CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeOut, // تبسيط منحنى الحركة
                    ),
                    child: child,
                  );
                },
                child: Icon(
                  isActive ? activeIcon : icon,
                  key: ValueKey(isActive),
                  color: isActive
                      ? activeColor
                      : isDarkMode
                          ? Colors.grey[400]
                          : Colors.grey[600],
                  size: 22,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode =
        Theme.of(context).brightness == Brightness.dark; // الأصلي
    final prophetPrayersColor =
        AppColors.getProphetPrayersColor(isDarkMode); // الأصلي

    return Padding(
      // الأصلي
      padding: const EdgeInsets.only(bottom: 20), // الأصلي
      child: MouseRegion(
        // الأصلي
        onEnter: (_) => _onHoverChanged(true), // الأصلي
        onExit: (_) => _onHoverChanged(false), // الأصلي
        child: GestureDetector(
          // الأصلي
          onTapDown: (_) => _animationController.forward(), // الأصلي
          onTapUp: (_) => _animationController.reverse(), // الأصلي
          onTapCancel: () => _animationController.reverse(), // الأصلي
          onTap: _toggleExpanded, // الأصلي
          child: AnimatedBuilder(
            // الأصلي
            animation: _scaleAnimation, // الأصلي
            builder: (context, child) {
              // لا يوجد child parameter في الأصلي
              return Transform.scale(
                scale: _scaleAnimation.value, // الأصلي
                child: child, // الأصلي (يجب أن يكون child!)
              );
            },
            child: Container(
              // هذا هو الـ child لـ AnimatedBuilder
              // تم تحسين الظلال لتقليل استهلاك الموارد
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                // تقليل عدد الظلال من 2 إلى 1 لتحسين الأداء
                boxShadow: [
                  BoxShadow(
                    color: prophetPrayersColor
                        .withAlpha(_isHovering ? 40 : 25), // تقليل قوة الظل
                    blurRadius: _isHovering ? 15 : 10, // تقليل قوة البلور
                    spreadRadius: _isHovering ? 1 : 0.5, // تقليل انتشار الظل
                    offset: const Offset(0, 4), // تقليل الإزاحة
                  ),
                ],
              ),
              child: ClipRRect(
                // الأصلي
                borderRadius: BorderRadius.circular(24), // الأصلي
                child: Container(
                  // الأصلي
                  decoration: BoxDecoration(
                    color: isDarkMode // الأصلي
                        ? Color.lerp(Colors.grey[900], prophetPrayersColor,
                            _isHovering ? 0.05 : 0.03)
                        : Color.lerp(Colors.white, prophetPrayersColor,
                            _isHovering ? 0.04 : 0.02),
                    border: Border.all(
                      // الأصلي
                      color: prophetPrayersColor
                          .withAlpha(_isHovering ? 80 : 60), // الأصلي
                      width: _isHovering ? 2.0 : 1.5, // الأصلي
                    ),
                    borderRadius: BorderRadius.circular(24), // الأصلي
                  ),
                  child: Column(
                    // الأصلي
                    crossAxisAlignment: CrossAxisAlignment.stretch, // الأصلي
                    children: [
                      // رأس البطاقة مع رقم الصلاة والأيقونات
                      Stack(
                        // الأصلي
                        children: [
                          // تأثير توهج متحرك - تم تحسينه للأداء وتجنب مشكلة ParentDataWidget
                          Positioned(
                            // تقليل حجم التوهج وتبسيط الحسابات
                            left: -100 +
                                (MediaQuery.of(context).size.width *
                                    _shimmerAnimation.value),
                            top: 0,
                            bottom: 0,
                            width: 100, // تقليل عرض التوهج من 150 إلى 100
                            child: RepaintBoundary(
                              child: AnimatedBuilder(
                                animation: _shimmerAnimation,
                                builder: (context, child) {
                                  return Transform.rotate(
                                    angle: math.pi / 4,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          colors: [
                                            Colors.white.withAlpha(0),
                                            Colors.white.withAlpha(
                                                10), // تقليل قوة التوهج من 15 إلى 10
                                            Colors.white.withAlpha(0),
                                          ],
                                          stops: const [0.0, 0.5, 1.0],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),

                          // محتوى الرأس
                          Container(
                            // الأصلي
                            padding: const EdgeInsets.symmetric(
                              horizontal: 18, // الأصلي
                              vertical: 14, // الأصلي
                            ),
                            decoration: BoxDecoration(
                              // الأصلي
                              gradient: LinearGradient(
                                begin: Alignment.topLeft, // الأصلي
                                end: Alignment.bottomRight, // الأصلي
                                colors: [
                                  // الأصلي
                                  prophetPrayersColor.withAlpha(40),
                                  prophetPrayersColor.withAlpha(20),
                                ],
                                stops: const [0.0, 1.0], // الأصلي
                              ),
                              border: Border(
                                // الأصلي
                                bottom: BorderSide(
                                  color: prophetPrayersColor
                                      .withAlpha(40), // الأصلي
                                  width: 1.5, // الأصلي
                                ),
                              ),
                            ),
                            child: Row(
                              // الأصلي
                              children: [
                                // رقم الصلاة - تم تحسينه للأداء
                                RepaintBoundary(
                                  child: TweenAnimationBuilder<double>(
                                    tween: Tween<double>(begin: 0.0, end: 1.0),
                                    // تقليل مدة الرسوم المتحركة من 500 إلى 300 مللي ثانية
                                    duration: const Duration(milliseconds: 300),
                                    // تبسيط منحنى الحركة
                                    curve: Curves.easeOut,
                                    builder: (context, value, child) {
                                      return Transform.scale(
                                        scale: value,
                                        child: child,
                                      );
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 8,
                                      ),
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors: [
                                            prophetPrayersColor.withAlpha(60),
                                            prophetPrayersColor.withAlpha(40),
                                          ],
                                        ),
                                        borderRadius: BorderRadius.circular(16),
                                        // إزالة الظل لتحسين الأداء
                                        border: Border.all(
                                          color:
                                              prophetPrayersColor.withAlpha(70),
                                          width: 1,
                                        ),
                                      ),
                                      child: Text(
                                        '${widget.index + 1}',
                                        style: TextStyle(
                                          color: isDarkMode
                                              ? Colors.white
                                              : prophetPrayersColor,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 15,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                const Spacer(), // الأصلي

                                // أزرار التفاعل
                                Row(
                                  // الأصلي
                                  children: [
                                    // أيقونة المفضلة
                                    _buildActionButton(
                                      // الأصلي
                                      icon: Icons.favorite_border,
                                      activeIcon: Icons.favorite,
                                      isActive: _isFavorite,
                                      activeColor: Colors.red[400]!,
                                      tooltip: _isFavorite
                                          ? 'إزالة من المفضلة'
                                          : 'إضافة إلى المفضلة',
                                      onPressed: _toggleFavorite,
                                      isDarkMode: isDarkMode,
                                    ),

                                    // أيقونة النسخ
                                    _buildActionButton(
                                      // الأصلي
                                      icon: Icons.content_copy,
                                      activeIcon: Icons.check,
                                      isActive: _isCopied,
                                      activeColor: Colors.green[400]!,
                                      tooltip: 'نسخ الصلاة',
                                      onPressed: _copyToClipboard,
                                      isDarkMode: isDarkMode,
                                    ),

                                    // أيقونة المشاركة
                                    _buildActionButton(
                                      // الأصلي
                                      icon: Icons.share,
                                      activeIcon: Icons.share,
                                      isActive: false, // الأصلي
                                      activeColor: Colors.blue[400]!, // الأصلي
                                      tooltip: 'مشاركة الصلاة',
                                      onPressed: _sharePrayer,
                                      isDarkMode: isDarkMode,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      // محتوى الصلاة
                      AnimatedContainer(
                        // الأصلي
                        duration: const Duration(milliseconds: 300), // الأصلي
                        padding: const EdgeInsets.all(20), // الأصلي
                        decoration: BoxDecoration(
                          // الأصلي
                          gradient: LinearGradient(
                            begin: Alignment.topCenter, // الأصلي
                            end: Alignment.bottomCenter, // الأصلي
                            colors: [
                              // الأصلي
                              Colors.transparent,
                              prophetPrayersColor.withAlpha(8),
                            ],
                          ),
                        ),
                        child: Column(
                          // الأصلي
                          crossAxisAlignment:
                              CrossAxisAlignment.stretch, // الأصلي
                          children: [
                            // نص الصلاة - تم تحسينه للأداء
                            RepaintBoundary(
                              child: AnimatedCrossFade(
                                firstChild: Text(
                                  widget.prayer.text,
                                  style: TextStyle(
                                    fontSize: 18,
                                    height: 1.8,
                                    color: isDarkMode
                                        ? Colors.grey[200]
                                        : Colors.grey[800],
                                  ),
                                  maxLines: 3,
                                  overflow: TextOverflow.ellipsis,
                                  textAlign: TextAlign.right,
                                ),
                                secondChild: Text(
                                  widget.prayer.text,
                                  style: TextStyle(
                                    fontSize: 18,
                                    height: 1.8,
                                    color: isDarkMode
                                        ? Colors.grey[200]
                                        : Colors.grey[800],
                                  ),
                                  textAlign: TextAlign.right,
                                ),
                                crossFadeState: _isExpanded
                                    ? CrossFadeState.showSecond
                                    : CrossFadeState.showFirst,
                                // تقليل مدة الرسوم المتحركة من 300 إلى 250 مللي ثانية
                                duration: const Duration(milliseconds: 250),
                                // تبسيط منحنى الحركة
                                layoutBuilder: (topChild, topChildKey,
                                    bottomChild, bottomChildKey) {
                                  // استخدام طريقة أبسط لتجنب مشكلة BoxConstraints forces an infinite height
                                  return Stack(
                                    // استخدام StackFit.passthrough بدلاً من loose
                                    fit: StackFit.passthrough,
                                    clipBehavior: Clip.none,
                                    children: <Widget>[
                                      // استخدام الأطفال مباشرة بدون Positioned
                                      Opacity(
                                        opacity: 1.0,
                                        key: bottomChildKey,
                                        child: bottomChild,
                                      ),
                                      Opacity(
                                        opacity: 1.0,
                                        key: topChildKey,
                                        child: topChild,
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ),

                            // مؤشر التوسيع
                            if (!_isExpanded) // الأصلي
                              Align(
                                alignment: Alignment.center, // الأصلي
                                child: Padding(
                                  // الأصلي
                                  padding:
                                      const EdgeInsets.only(top: 12), // الأصلي
                                  child: Container(
                                    // الأصلي
                                    padding: const EdgeInsets.all(8), // الأصلي
                                    decoration: BoxDecoration(
                                      color: prophetPrayersColor
                                          .withAlpha(15), // الأصلي
                                      shape: BoxShape.circle, // الأصلي
                                    ),
                                    child: Icon(
                                      // الأصلي
                                      Icons.keyboard_arrow_down,
                                      color: prophetPrayersColor
                                          .withAlpha(180), // الأصلي
                                      size: 20, // الأصلي
                                    ),
                                  ),
                                ),
                              ),

                            // معلومات إضافية عند التوسيع - تم تحسينه للأداء
                            if (_isExpanded && widget.prayer.source != null)
                              RepaintBoundary(
                                child: AnimatedBuilder(
                                  animation: _expandAnimation,
                                  builder: (context, child) {
                                    return Opacity(
                                      opacity: _expandAnimation.value,
                                      child: Transform.translate(
                                        // تقليل مسافة الحركة من 20 إلى 10
                                        offset: Offset(0,
                                            10 * (1 - _expandAnimation.value)),
                                        child: Padding(
                                          padding:
                                              const EdgeInsets.only(top: 20),
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 16,
                                              vertical: 12,
                                            ),
                                            decoration: BoxDecoration(
                                              color: prophetPrayersColor
                                                  .withAlpha(20),
                                              borderRadius:
                                                  BorderRadius.circular(16),
                                              border: Border.all(
                                                color: prophetPrayersColor
                                                    .withAlpha(40),
                                                width: 1,
                                              ),
                                              // إزالة الظل لتحسين الأداء
                                            ),
                                            child: Row(
                                              children: [
                                                Container(
                                                  padding:
                                                      const EdgeInsets.all(6),
                                                  decoration: BoxDecoration(
                                                    color: AppColors
                                                            .getSourceColor(
                                                                isDarkMode)
                                                        .withAlpha(30),
                                                    shape: BoxShape.circle,
                                                  ),
                                                  child: Icon(
                                                    Icons.source,
                                                    size: 16,
                                                    color: AppColors
                                                        .getSourceColor(
                                                            isDarkMode),
                                                  ),
                                                ),
                                                const SizedBox(width: 12),
                                                Expanded(
                                                  child: Text(
                                                    widget.prayer.source!,
                                                    style: TextStyle(
                                                      fontSize: 15,
                                                      height: 1.5,
                                                      color: AppColors
                                                          .getSourceColor(
                                                              isDarkMode),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),

                            // فضل الصلاة إذا كان متوفراً - تم تحسينه للأداء
                            if (_isExpanded && widget.prayer.virtue != null)
                              RepaintBoundary(
                                child: AnimatedBuilder(
                                  animation: _expandAnimation,
                                  builder: (context, child) {
                                    return Opacity(
                                      opacity: _expandAnimation.value,
                                      child: Transform.translate(
                                        // تقليل مسافة الحركة من 20 إلى 10
                                        offset: Offset(0,
                                            10 * (1 - _expandAnimation.value)),
                                        child: Padding(
                                          padding:
                                              const EdgeInsets.only(top: 16),
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 16,
                                              vertical: 12,
                                            ),
                                            decoration: BoxDecoration(
                                              color: AppColors.getFadlColor(
                                                      isDarkMode)
                                                  .withAlpha(20),
                                              borderRadius:
                                                  BorderRadius.circular(16),
                                              border: Border.all(
                                                color: AppColors.getFadlColor(
                                                        isDarkMode)
                                                    .withAlpha(40),
                                                width: 1,
                                              ),
                                              // إزالة الظل لتحسين الأداء
                                            ),
                                            child: Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Container(
                                                  margin: const EdgeInsets.only(
                                                      top: 3),
                                                  padding:
                                                      const EdgeInsets.all(6),
                                                  decoration: BoxDecoration(
                                                    color:
                                                        AppColors.getFadlColor(
                                                                isDarkMode)
                                                            .withAlpha(30),
                                                    shape: BoxShape.circle,
                                                  ),
                                                  child: Icon(
                                                    Icons.star,
                                                    size: 16,
                                                    color:
                                                        AppColors.getFadlColor(
                                                            isDarkMode),
                                                  ),
                                                ),
                                                const SizedBox(width: 12),
                                                Expanded(
                                                  child: Text(
                                                    widget.prayer.virtue!,
                                                    style: TextStyle(
                                                      fontSize: 15,
                                                      height: 1.6,
                                                      color: AppColors
                                                          .getFadlColor(
                                                              isDarkMode),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  } // نهاية دالة build
} // <<<--- !!! تم التأكد من إغلاق الكلاس هنا بشكل صحيح !!!
// --- لا يوجد أي كود إضافي هنا ---
