// نموذج إحصائيات المسبحة

import 'achievement_model.dart';

/// نموذج لتخزين إحصائيات المسبحة
class TasbihStatsModel {
  /// إجمالي عدد التسبيحات
  final int totalCount;

  /// عدد الدورات المكتملة
  final int sessionCount;

  /// الذكر الأكثر استخداماً
  final DhikrStats mostUsedDhikr;

  /// إحصائيات الأذكار المستخدمة
  final List<DhikrStats> dhikrStats;

  /// إحصائيات الأيام
  final List<DailyStats> dailyStats;

  /// أطول سلسلة متتالية (بالأيام)
  final int longestStreak;

  /// السلسلة الحالية (بالأيام)
  final int currentStreak;

  /// متوسط التسبيحات اليومية
  final int averageDailyCount;

  /// أفضل يوم (أكثر عدد تسبيحات)
  final DailyStats? bestDay;

  /// تاريخ آخر استخدام
  final DateTime lastUsedDate;

  /// إجمالي وقت الاستخدام (بالدقائق)
  final int totalUsageTime;

  /// متوسط التسبيحات في الساعة
  final double hourlyRate;

  /// عدد الأيام النشطة
  final int activeDaysCount;

  /// معدل النمو (نسبة الزيادة في الأسبوع الحالي مقارنة بالأسبوع السابق)
  final double growthRate;

  /// الإنجازات المحققة
  final List<Achievement> achievements;

  TasbihStatsModel({
    required this.totalCount,
    required this.sessionCount,
    required this.mostUsedDhikr,
    required this.dhikrStats,
    required this.dailyStats,
    required this.longestStreak,
    required this.currentStreak,
    required this.averageDailyCount,
    this.bestDay,
    required this.lastUsedDate,
    required this.totalUsageTime,
    this.hourlyRate = 0.0,
    this.activeDaysCount = 0,
    this.growthRate = 0.0,
    this.achievements = const [],
  });

  /// إنشاء نموذج فارغ
  factory TasbihStatsModel.empty() {
    return TasbihStatsModel(
      totalCount: 0,
      sessionCount: 0,
      mostUsedDhikr: DhikrStats(
        id: -1,
        name: 'سبحان الله',
        count: 0,
        percentage: 0,
      ),
      dhikrStats: [],
      dailyStats: [],
      longestStreak: 0,
      currentStreak: 0,
      averageDailyCount: 0,
      bestDay: null,
      lastUsedDate: DateTime.now(),
      totalUsageTime: 0,
      hourlyRate: 0.0,
      activeDaysCount: 0,
      growthRate: 0.0,
      achievements: [],
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'totalCount': totalCount,
      'sessionCount': sessionCount,
      'mostUsedDhikr': mostUsedDhikr.toMap(),
      'dhikrStats': dhikrStats.map((e) => e.toMap()).toList(),
      'dailyStats': dailyStats.map((e) => e.toMap()).toList(),
      'longestStreak': longestStreak,
      'currentStreak': currentStreak,
      'averageDailyCount': averageDailyCount,
      'bestDay': bestDay?.toMap(),
      'lastUsedDate': lastUsedDate.millisecondsSinceEpoch,
      'totalUsageTime': totalUsageTime,
      'hourlyRate': hourlyRate,
      'activeDaysCount': activeDaysCount,
      'growthRate': growthRate,
      'achievements': achievements.map((e) => e.toMap()).toList(),
    };
  }

  /// إنشاء نموذج من Map
  factory TasbihStatsModel.fromMap(Map<String, dynamic> map) {
    List<DhikrStats> dhikrStats = [];
    if (map['dhikrStats'] != null) {
      dhikrStats = List<DhikrStats>.from(
        map['dhikrStats'].map((x) => DhikrStats.fromMap(x)),
      );
    }

    List<DailyStats> dailyStats = [];
    if (map['dailyStats'] != null) {
      dailyStats = List<DailyStats>.from(
        map['dailyStats'].map((x) => DailyStats.fromMap(x)),
      );
    }

    List<Achievement> achievements = [];
    if (map['achievements'] != null) {
      achievements = List<Achievement>.from(
        map['achievements'].map((x) => Achievement.fromMap(x)),
      );
    }

    return TasbihStatsModel(
      totalCount: map['totalCount'] ?? 0,
      sessionCount: map['sessionCount'] ?? 0,
      mostUsedDhikr: map['mostUsedDhikr'] != null
          ? DhikrStats.fromMap(map['mostUsedDhikr'])
          : DhikrStats(id: -1, name: 'سبحان الله', count: 0, percentage: 0),
      dhikrStats: dhikrStats,
      dailyStats: dailyStats,
      longestStreak: map['longestStreak'] ?? 0,
      currentStreak: map['currentStreak'] ?? 0,
      averageDailyCount: map['averageDailyCount'] ?? 0,
      bestDay:
          map['bestDay'] != null ? DailyStats.fromMap(map['bestDay']) : null,
      lastUsedDate: map['lastUsedDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['lastUsedDate'])
          : DateTime.now(),
      totalUsageTime: map['totalUsageTime'] ?? 0,
      hourlyRate: map['hourlyRate']?.toDouble() ?? 0.0,
      activeDaysCount: map['activeDaysCount'] ?? 0,
      growthRate: map['growthRate']?.toDouble() ?? 0.0,
      achievements: achievements,
    );
  }

  /// نسخة جديدة مع تحديث بعض القيم
  TasbihStatsModel copyWith({
    int? totalCount,
    int? sessionCount,
    DhikrStats? mostUsedDhikr,
    List<DhikrStats>? dhikrStats,
    List<DailyStats>? dailyStats,
    int? longestStreak,
    int? currentStreak,
    int? averageDailyCount,
    DailyStats? bestDay,
    DateTime? lastUsedDate,
    int? totalUsageTime,
    double? hourlyRate,
    int? activeDaysCount,
    double? growthRate,
    List<Achievement>? achievements,
  }) {
    return TasbihStatsModel(
      totalCount: totalCount ?? this.totalCount,
      sessionCount: sessionCount ?? this.sessionCount,
      mostUsedDhikr: mostUsedDhikr ?? this.mostUsedDhikr,
      dhikrStats: dhikrStats ?? this.dhikrStats,
      dailyStats: dailyStats ?? this.dailyStats,
      longestStreak: longestStreak ?? this.longestStreak,
      currentStreak: currentStreak ?? this.currentStreak,
      averageDailyCount: averageDailyCount ?? this.averageDailyCount,
      bestDay: bestDay ?? this.bestDay,
      lastUsedDate: lastUsedDate ?? this.lastUsedDate,
      totalUsageTime: totalUsageTime ?? this.totalUsageTime,
      hourlyRate: hourlyRate ?? this.hourlyRate,
      activeDaysCount: activeDaysCount ?? this.activeDaysCount,
      growthRate: growthRate ?? this.growthRate,
      achievements: achievements ?? this.achievements,
    );
  }
}

/// نموذج لإحصائيات الذكر
class DhikrStats {
  /// معرف الذكر
  final int id;

  /// اسم الذكر
  final String name;

  /// عدد مرات التسبيح
  final int count;

  /// النسبة المئوية من إجمالي التسبيحات
  final double percentage;

  DhikrStats({
    required this.id,
    required this.name,
    required this.count,
    required this.percentage,
  });

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'count': count,
      'percentage': percentage,
    };
  }

  /// إنشاء نموذج من Map
  factory DhikrStats.fromMap(Map<String, dynamic> map) {
    return DhikrStats(
      id: map['id'] ?? -1,
      name: map['name'] ?? '',
      count: map['count'] ?? 0,
      percentage: map['percentage']?.toDouble() ?? 0.0,
    );
  }
}

/// نموذج لإحصائيات اليوم
class DailyStats {
  /// تاريخ اليوم
  final DateTime date;

  /// عدد التسبيحات في هذا اليوم
  final int count;

  /// عدد الدورات في هذا اليوم
  final int sessions;

  /// وقت الاستخدام في هذا اليوم (بالدقائق)
  final int usageTime;

  /// الذكر الأكثر استخداماً في هذا اليوم
  final DhikrStats? topDhikr;

  DailyStats({
    required this.date,
    required this.count,
    required this.sessions,
    required this.usageTime,
    this.topDhikr,
  });

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'date': date.millisecondsSinceEpoch,
      'count': count,
      'sessions': sessions,
      'usageTime': usageTime,
      'topDhikr': topDhikr?.toMap(),
    };
  }

  /// إنشاء نموذج من Map
  factory DailyStats.fromMap(Map<String, dynamic> map) {
    return DailyStats(
      date: DateTime.fromMillisecondsSinceEpoch(map['date']),
      count: map['count'] ?? 0,
      sessions: map['sessions'] ?? 0,
      usageTime: map['usageTime'] ?? 0,
      topDhikr:
          map['topDhikr'] != null ? DhikrStats.fromMap(map['topDhikr']) : null,
    );
  }

  /// الحصول على اسم اليوم بالعربية
  String get dayName {
    final List<String> days = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];
    return days[date.weekday - 1];
  }

  /// التحقق مما إذا كان اليوم هو اليوم الحالي
  bool get isToday {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }
}
