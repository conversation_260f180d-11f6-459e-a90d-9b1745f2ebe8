// بطاقة إحصائيات

import 'package:flutter/material.dart';
import '../utils/tasbih_colors.dart';

class StatsCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final VoidCallback? onTap;
  final Widget? trailing;
  final String? subtitle;
  final bool showTrend;
  final double? trendValue;
  final bool useCircularIcon;

  const StatsCard({
    Key? key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    this.onTap,
    this.trailing,
    this.subtitle,
    this.showTrend = false,
    this.trendValue,
    this.useCircularIcon = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final backgroundColor =
        isDarkMode ? TasbihColors.darkCardColor : Colors.white;
    final textColor = isDarkMode ? Colors.white : Colors.black87;

    // الحصول على حجم الشاشة للتصميم المتجاوب
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 360; // للشاشات الصغيرة جدًا
    final isMediumScreen =
        size.width >= 360 && size.width < 600; // للهواتف العادية
    final isLargeScreen = size.width >= 600; // للأجهزة اللوحية والشاشات الكبيرة

    // تحديد أحجام العناصر بناءً على حجم الشاشة
    final iconSize = isSmallScreen ? 20.0 : (isLargeScreen ? 28.0 : 24.0);
    final containerSize = isSmallScreen ? 40.0 : (isLargeScreen ? 52.0 : 46.0);
    final horizontalPadding =
        isSmallScreen ? 8.0 : (isLargeScreen ? 16.0 : 12.0);
    final verticalPadding =
        isSmallScreen ? 10.0 : (isLargeScreen ? 18.0 : 14.0);
    final titleFontSize = isSmallScreen ? 12.0 : (isLargeScreen ? 16.0 : 14.0);
    final valueFontSize = isSmallScreen ? 16.0 : (isLargeScreen ? 24.0 : 20.0);
    final subtitleFontSize =
        isSmallScreen ? 10.0 : (isLargeScreen ? 14.0 : 12.0);

    // تحديد لون واتجاه الاتجاه
    Color trendColor = Colors.grey;
    IconData trendIcon = Icons.trending_flat;

    if (showTrend && trendValue != null) {
      if (trendValue! > 0) {
        trendColor = Colors.green;
        trendIcon = Icons.trending_up;
      } else if (trendValue! < 0) {
        trendColor = Colors.red;
        trendIcon = Icons.trending_down;
      }
    }

    return Card(
      elevation: 4,
      shadowColor: color.withAlpha(76), // 0.3 * 255 = 76
      color: backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: color.withAlpha(51), // 0.2 * 255 = 51
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: EdgeInsets.symmetric(
              horizontal: horizontalPadding, vertical: verticalPadding),
          child: Row(
            textDirection:
                TextDirection.rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
            children: [
              // أيقونة البطاقة
              Container(
                width: containerSize,
                height: containerSize,
                decoration: BoxDecoration(
                  color: color.withAlpha(25), // 0.1 * 255 = 25
                  borderRadius: useCircularIcon
                      ? BorderRadius.circular(containerSize / 2)
                      : BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: color.withAlpha(30),
                      blurRadius: 5,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: iconSize,
                ),
              ),
              SizedBox(width: isSmallScreen ? 8 : 12),

              // المحتوى الرئيسي
              Expanded(
                child: Column(
                  crossAxisAlignment:
                      CrossAxisAlignment.end, // تغيير المحاذاة إلى اليمين
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه العمود من اليمين إلى اليسار
                  children: [
                    // العنوان
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: titleFontSize,
                        color: textColor.withAlpha(178), // 0.7 * 255 = 178
                      ),
                      textDirection: TextDirection
                          .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                      textAlign: TextAlign.right, // محاذاة النص إلى اليمين
                    ),
                    SizedBox(height: isSmallScreen ? 2 : 4),

                    // القيمة الرئيسية مع اتجاه التغيير (اختياري)
                    Wrap(
                      crossAxisAlignment: WrapCrossAlignment.end,
                      textDirection: TextDirection
                          .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                      children: [
                        Text(
                          value,
                          style: TextStyle(
                            fontSize: valueFontSize,
                            fontWeight: FontWeight.bold,
                            color: textColor,
                          ),
                          overflow: TextOverflow.ellipsis,
                          textDirection: TextDirection
                              .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                          textAlign: TextAlign.right, // محاذاة النص إلى اليمين
                        ),

                        // عرض اتجاه التغيير (اختياري)
                        if (showTrend && trendValue != null)
                          Padding(
                            padding: EdgeInsets.only(
                                right: 4, bottom: isSmallScreen ? 2 : 4),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              textDirection: TextDirection
                                  .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                              children: [
                                Icon(
                                  trendIcon,
                                  color: trendColor,
                                  size: isSmallScreen ? 12 : 14,
                                ),
                                SizedBox(width: isSmallScreen ? 1 : 2),
                                Text(
                                  trendValue!.abs() < 10
                                      ? '${trendValue!.abs().toStringAsFixed(1)}%'
                                      : '${trendValue!.abs().toInt()}%',
                                  style: TextStyle(
                                    color: trendColor,
                                    fontWeight: FontWeight.bold,
                                    fontSize: isSmallScreen ? 9 : 11,
                                  ),
                                  textDirection: TextDirection
                                      .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                  textAlign:
                                      TextAlign.right, // محاذاة النص إلى اليمين
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),

                    // العنوان الفرعي (اختياري)
                    if (subtitle != null)
                      Padding(
                        padding: EdgeInsets.only(top: isSmallScreen ? 2 : 4),
                        child: Text(
                          subtitle!,
                          style: TextStyle(
                            fontSize: subtitleFontSize,
                            color: textColor.withAlpha(153), // 0.6 * 255 = 153
                          ),
                          textDirection: TextDirection
                              .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                          textAlign: TextAlign.right, // محاذاة النص إلى اليمين
                        ),
                      ),
                  ],
                ),
              ),

              // عنصر إضافي (اختياري)
              if (trailing != null) trailing!,
            ],
          ),
        ),
      ),
    );
  }
}
