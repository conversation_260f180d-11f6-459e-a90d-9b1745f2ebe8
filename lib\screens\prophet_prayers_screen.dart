// شاشة الصلاة على النبي الرئيسية

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'dart:ui'; // لـ ImageFilter و BackdropFilter
import 'package:flutter_svg/flutter_svg.dart';
import 'package:share_plus/share_plus.dart';
import '../providers/prophet_prayers_provider.dart'; // افتراض وجود هذا الملف
import '../models/dua.dart'; // افتراض وجود Dua و DuaCategory
import '../utils/app_colors.dart'; // افتراض وجود AppColors و getProphetPrayersColor
import '../widgets/prophet_prayer_card.dart'; // افتراض وجود هذا الويدجت مُحسّن
import '../widgets/featured_prophet_prayers_section.dart'; // افتراض وجود هذا الويدجت مُحسّن
import 'prophet_prayer_details_screen.dart'; // افتراض وجود هذه الشاشة
import 'dart:math' as math; // لإستخدام math.pi

class ProphetPrayersScreen extends StatefulWidget {
  const ProphetPrayersScreen({Key? key}) : super(key: key); // استخدام const

  @override
  State<ProphetPrayersScreen> createState() => _ProphetPrayersScreenState();
}

class _ProphetPrayersScreenState extends State<ProphetPrayersScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late ScrollController _scrollController;
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();
  List<DuaCategory> _filteredCategories = [];
  bool _showScrollToTop = false;
  // تعريف FocusNode لحقل البحث
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // تهيئة وحدة التحكم بالرسوم المتحركة - تقليل المدة لتحسين السلاسة
    _animationController = AnimationController(
      duration: const Duration(
          milliseconds: 800), // تقليل المدة بشكل أكبر لتحسين السلاسة
      vsync: this,
    );

    _scrollController = ScrollController();
    _scrollController.addListener(_scrollListener);

    // تهيئة مزود الصلاة على النبي بشكل أكثر كفاءة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // التأكد من أن الواجهة لا تزال موجودة
      if (mounted) {
        _initializeData();
      }
    });
  }

  // دالة منفصلة لتهيئة البيانات
  Future<void> _initializeData() async {
    final prophetPrayersProvider =
        Provider.of<ProphetPrayersProvider>(context, listen: false);

    try {
      // تحميل البيانات بغض النظر عن الحالة السابقة
      await prophetPrayersProvider.loadProphetPrayers();
      await prophetPrayersProvider.loadFavorites();

      // يجب أن يتم تحديث الحالة وبدء الأنيميشن فقط إذا كانت الواجهة لا تزال موجودة
      if (mounted) {
        setState(() {
          _filteredCategories = prophetPrayersProvider.categories;
        });
        _animationController.forward();
      }
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات الصلوات على النبي: $e');
      // عرض رسالة خطأ للمستخدم
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
                'حدث خطأ أثناء تحميل البيانات. يرجى المحاولة مرة أخرى.',
                textDirection: TextDirection.rtl),
            backgroundColor: Colors.red[600],
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: _initializeData,
            ),
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _searchFocusNode.dispose(); // التخلص من FocusNode
    super.dispose();
  }

  void _scrollListener() {
    // تحسين منطق setState
    final shouldShow = _scrollController.offset > 300;
    if (shouldShow != _showScrollToTop) {
      if (mounted) {
        // التأكد من أن الواجهة لا تزال موجودة
        setState(() {
          _showScrollToTop = shouldShow;
        });
      }
    }
  }

  void _scrollToTop() {
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 500), // تقليل المدة لسرعة أكبر
      curve: Curves.easeOutCubic, // منحنى سلس
    );
  }

  // الانتقال إلى فئة
  void _navigateToCategory(DuaCategory category) {
    HapticFeedback.mediumImpact();
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            ProphetPrayerDetailsScreen(category: category),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOutCubic;
          var tween =
              Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
          var offsetAnimation = animation.drive(tween);
          return SlideTransition(position: offsetAnimation, child: child);
        },
        transitionDuration: const Duration(milliseconds: 400), // تقليل المدة
      ),
    );
  }

  // الحصول على رسوم متحركة متدرجة - تبسيط الحساب لتحسين الأداء
  Animation<double> _getAnimation(int index) {
    // تبسيط حساب التأخير لتحسين الأداء
    final double startDelay = (index * 0.05).clamp(0.0, 0.5);
    final double endDelay = (startDelay + 0.3).clamp(startDelay, 1.0);

    return Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Interval(
          startDelay,
          endDelay,
          curve: Curves.easeOutCubic,
        ),
      ),
    );
  }

  // تحديث نتائج البحث - محسن للأداء
  void _updateSearchResults(String query) {
    final currentQuery = query.trim(); // إزالة المسافات البيضاء
    final prophetPrayersProvider =
        Provider.of<ProphetPrayersProvider>(context, listen: false);

    // تأخير البحث لتجنب التحديثات المتكررة أثناء الكتابة - تقليل التأخير لتحسين الاستجابة
    Future.delayed(const Duration(milliseconds: 150), () {
      // التحقق من أن الواجهة لا تزال موجودة وأن الاستعلام لم يتغير
      if (!mounted || _searchController.text.trim() != currentQuery) {
        return;
      }
      // تحديث الحالة فقط إذا كانت الواجهة لا تزال موجودة
      if (mounted) {
        setState(() {
          if (currentQuery.isEmpty) {
            _filteredCategories = prophetPrayersProvider.categories;
          } else {
            _filteredCategories =
                prophetPrayersProvider.searchProphetPrayers(currentQuery);
          }
        });
      }
    });
  }

  // إظهار/إخفاء حقل البحث
  void _toggleSearch() {
    HapticFeedback.mediumImpact();
    if (mounted) {
      // التأكد من أن الواجهة لا تزال موجودة
      setState(() {
        _isSearching = !_isSearching;
        if (!_isSearching) {
          _searchController.clear();
          // تحديث الفئات مباشرة عند إغلاق البحث
          final prophetPrayersProvider =
              Provider.of<ProphetPrayersProvider>(context, listen: false);
          _filteredCategories = prophetPrayersProvider.categories;
          _searchFocusNode.unfocus(); // إلغاء تركيز حقل البحث
        }
      });
      // طلب التركيز بعد أن يصبح حقل البحث مرئيًا
      if (_isSearching) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) _searchFocusNode.requestFocus();
        });
      }
    }
  }

  // عرض حوار الصلاة بتصميم فاخر (مع تحسينات السلاسة)
  void _showPrayerDialog(Dua prayer) {
    HapticFeedback.mediumImpact();

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final prophetPrayersColor = AppColors.getProphetPrayersColor(isDarkMode);

    // استخدام قيم ثابتة حيثما أمكن
    const dialogBorderRadius = BorderRadius.all(Radius.circular(24));
    const dialogElevation = 0.0; // Elevation يُدار بواسطة BoxShadow
    const dialogInsetPadding =
        EdgeInsets.symmetric(horizontal: 20, vertical: 24);

    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: 'إغلاق', // مهم للوصولية
      barrierColor: Colors.black.withValues(alpha: 0.7), // تعديل شفافية الحاجز
      transitionDuration:
          const Duration(milliseconds: 350), // تقليل مدة الانتقال
      pageBuilder: (context, animation1, animation2) {
        // هذا الجزء لا يُستخدم مباشرة مع transitionBuilder المخصص
        return const SizedBox.shrink();
      },
      transitionBuilder: (context, animation, secondaryAnimation, _) {
        // تجاهل child هنا
        final curvedAnimation = CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutQuint, // منحنى أكثر سلاسة وسرعة
        );

        // استخدام ChangeNotifierProvider.value لتمرير ProphetPrayersProvider إلى StatefulBuilder
        // هذا يضمن أننا نستخدم نفس نسخة Provider الموجودة في الشاشة الرئيسية
        // ونستمع للتغييرات بشكل صحيح إذا حدثت.
        return ChangeNotifierProvider.value(
          value: Provider.of<ProphetPrayersProvider>(context, listen: false),
          child: StatefulBuilder(
            // StatefulBuilder لإدارة حالة الحوار الداخلية (isCopied, isFavorite)
            builder: (dialogContext, setDialogState) {
              // استخدام dialogContext و setDialogState
              // الوصول إلى Provider داخل StatefulBuilder
              final prophetPrayersProvider =
                  Provider.of<ProphetPrayersProvider>(dialogContext);
              bool isFavorite = prophetPrayersProvider
                  .isFavorite(prayer.id); // التحقق من Provider
              bool isCopied = false; // حالة النسخ محلية للحوار

              void copyToClipboard() {
                Clipboard.setData(ClipboardData(text: prayer.text));
                setDialogState(() => isCopied = true);
                HapticFeedback.mediumImpact();

                ScaffoldMessenger.of(dialogContext).showSnackBar(
                  SnackBar(
                    content: const Text('تم نسخ الصلاة بنجاح',
                        textDirection: TextDirection.rtl),
                    backgroundColor: Colors.green[600], // تعديل اللون
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10)),
                    duration: const Duration(seconds: 2),
                    margin: const EdgeInsets.all(16), // إضافة margin
                  ),
                );
                Future.delayed(const Duration(seconds: 2), () {
                  if (mounted) {
                    setDialogState(() => isCopied = false); // التحقق من mounted
                  }
                });
              }

              void toggleFavorite() async {
                HapticFeedback.mediumImpact();
                final bool success = await prophetPrayersProvider
                    .toggleFavorite(prayer); // استخدام دالة toggleFavorite

                if (success && mounted) {
                  // التحقق من mounted قبل setDialogState
                  setDialogState(
                      () => isFavorite = !isFavorite); // تحديث الحالة المحلية

                  // Capture the BuildContext in a local variable
                  final currentContext = dialogContext;
                  if (currentContext.mounted) {
                    ScaffoldMessenger.of(currentContext).showSnackBar(
                      SnackBar(
                        content: Text(
                            isFavorite
                                ? 'تمت الإضافة للمفضلة'
                                : 'تمت الإزالة من المفضلة',
                            textDirection: TextDirection.rtl),
                        backgroundColor: isFavorite
                            ? AppColors.favoritesColor
                            : Colors.red[600], // استخدام ألوان متناسقة
                        behavior: SnackBarBehavior.floating,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10)),
                        duration: const Duration(seconds: 2),
                        margin: const EdgeInsets.all(16),
                        action: SnackBarAction(
                          // إضافة زر تراجع
                          label: 'تراجع',
                          textColor: Colors.white,
                          onPressed: () async {
                            await prophetPrayersProvider
                                .toggleFavorite(prayer); // التراجع عن التغيير
                            if (mounted) {
                              setDialogState(() => isFavorite = !isFavorite);
                            }
                          },
                        ),
                      ),
                    );
                  }
                }
              }

              void sharePrayer() {
                final String shareText = '''
${prayer.text}

- من تطبيق وهج السالك
''';
                Share.share(shareText);
                HapticFeedback.mediumImpact();
              }

              return ScaleTransition(
                scale: curvedAnimation,
                // RepaintBoundary حول BackdropFilter قد يساعد إذا كان المحتوى خلفه يتغير
                child: RepaintBoundary(
                  child: BackdropFilter(
                    filter: ImageFilter.blur(
                      sigmaX: 3.0 *
                          animation.value, // تقليل قوة البلور لتحسين الأداء
                      sigmaY: 3.0 * animation.value,
                    ),
                    child: Dialog(
                      backgroundColor: Colors.transparent, // Dialog شفاف
                      elevation: dialogElevation,
                      insetPadding: dialogInsetPadding, // const
                      child: Container(
                        // الحاوية الرئيسية للحوار
                        constraints: BoxConstraints(
                            maxHeight:
                                MediaQuery.of(context).size.height * 0.8),
                        decoration: BoxDecoration(
                          color: isDarkMode
                              ? Colors.grey[850]
                              : Colors.white, // تعديل الألوان قليلاً
                          borderRadius: dialogBorderRadius, // const
                          boxShadow: [
                            // استخدام const للظل
                            BoxShadow(
                              color: prophetPrayersColor.withValues(
                                  alpha: 0.25), // تعديل شفافية الظل
                              blurRadius: 15, // تقليل قوة البلور
                              offset: const Offset(0, 8), // تعديل الإزاحة
                            ),
                          ],
                          border: Border.all(
                            color: prophetPrayersColor.withValues(
                                alpha: 0.5), // تعديل شفافية الحدود
                            width: 1.5, // تقليل سمك الحدود
                          ),
                        ),
                        child: ClipRRect(
                          // لضمان أن المحتوى يتبع borderRadius
                          borderRadius: dialogBorderRadius,
                          child: Column(
                            // استخدام Column لتنظيم المحتوى
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              // --- رأس الحوار ---
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 12), // تعديل padding
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    // تدرج لوني أخف
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      prophetPrayersColor.withValues(
                                          alpha: 0.2),
                                      prophetPrayersColor.withValues(
                                          alpha: 0.1),
                                    ],
                                  ),
                                  border: Border(
                                      bottom: BorderSide(
                                          color: prophetPrayersColor.withValues(
                                              alpha: 0.2),
                                          width: 1)), // تعديل
                                ),
                                child: Row(
                                  textDirection: TextDirection.rtl,
                                  mainAxisAlignment: MainAxisAlignment
                                      .spaceBetween, // لتوزيع العناصر
                                  children: [
                                    // أيقونة متحركة (يمكن تبسيطها إذا كانت مكلفة)
                                    // أو استخدام child في TweenAnimationBuilder
                                    TweenAnimationBuilder<double>(
                                      tween:
                                          Tween<double>(begin: 0.0, end: 1.0),
                                      duration: const Duration(
                                          milliseconds: 800), // تقليل المدة
                                      curve: Curves.elasticOut,
                                      child: Icon(Icons.auto_awesome,
                                          color: prophetPrayersColor,
                                          size: 20), // child للأيقونة
                                      builder: (context, value, iconChild) {
                                        return Transform.rotate(
                                          angle: value *
                                              2 *
                                              math.pi *
                                              0.05, // دوران أخف
                                          child: Container(
                                            padding: const EdgeInsets.all(
                                                6), // تعديل
                                            decoration: BoxDecoration(
                                              color: prophetPrayersColor
                                                  .withValues(
                                                      alpha: 0.15), // تعديل
                                              shape: BoxShape.circle,
                                            ),
                                            child: iconChild,
                                          ),
                                        );
                                      },
                                    ),
                                    Text(
                                      // عنوان الحوار
                                      'صيغة مختارة', // عنوان أكثر تحديدًا
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight:
                                            FontWeight.bold, // تعديل الحجم
                                        color: isDarkMode
                                            ? Colors.white
                                                .withValues(alpha: 0.9)
                                            : Colors.black
                                                .withValues(alpha: 0.8),
                                      ),
                                      textDirection: TextDirection.rtl,
                                    ),
                                    IconButton(
                                      // زر الإغلاق
                                      icon: Icon(Icons.close,
                                          color: isDarkMode
                                              ? Colors.white70
                                              : Colors.black54,
                                          size: 22), // تعديل
                                      onPressed: () =>
                                          Navigator.of(dialogContext).pop(),
                                      splashRadius: 20, // const
                                      tooltip: 'إغلاق', // إضافة tooltip
                                    ),
                                  ],
                                ),
                              ),
                              // --- محتوى الصلاة ---
                              Flexible(
                                child: SingleChildScrollView(
                                  physics:
                                      const BouncingScrollPhysics(), // const
                                  padding:
                                      const EdgeInsets.all(16), // تعديل padding
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.end, // محاذاة لليمين
                                    children: [
                                      if (prayer.source != null &&
                                          prayer.source!.isNotEmpty)
                                        _buildDialogMetaChip(
                                            // دالة مساعدة للمصدر
                                            icon: Icons.source_outlined,
                                            text: prayer.source!,
                                            color: AppColors.getSourceColor(
                                                isDarkMode),
                                            isDarkMode: isDarkMode),
                                      const SizedBox(height: 12), // const
                                      Container(
                                        // حاوية نص الصلاة
                                        padding:
                                            const EdgeInsets.all(14), // تعديل
                                        decoration: BoxDecoration(
                                          color: prophetPrayersColor.withValues(
                                              alpha: 0.05), // تعديل
                                          borderRadius: BorderRadius.circular(
                                              12), // تعديل
                                          border: Border.all(
                                              color: prophetPrayersColor
                                                  .withValues(alpha: 0.15),
                                              width: 1), // تعديل
                                        ),
                                        child: SelectableText(
                                          // استخدام SelectableText
                                          prayer.text,
                                          style: TextStyle(
                                            fontSize: 17, height: 1.7, // تعديل
                                            color: isDarkMode
                                                ? Colors.white
                                                    .withValues(alpha: 0.9)
                                                : Colors.black
                                                    .withValues(alpha: 0.85),
                                          ),
                                          textDirection: TextDirection.rtl,
                                          textAlign: TextAlign.right,
                                        ),
                                      ),
                                      if (prayer.virtue != null &&
                                          prayer.virtue!.isNotEmpty) ...[
                                        const SizedBox(height: 16), // const
                                        _buildDialogVirtueSection(prayer,
                                            isDarkMode), // دالة مساعدة للفضل
                                      ],
                                    ],
                                  ),
                                ),
                              ),
                              // --- شريط الأدوات ---
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 10), // تعديل
                                decoration: BoxDecoration(
                                  color: isDarkMode
                                      ? Colors.grey[800]
                                      : Colors.grey[200], // تعديل الألوان
                                  border: Border(
                                      top: BorderSide(
                                          color: prophetPrayersColor.withValues(
                                              alpha: 0.15),
                                          width: 1)), // تعديل
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment
                                      .spaceAround, // توزيع أفضل
                                  textDirection: TextDirection.rtl,
                                  children: [
                                    _buildDialogActionButton(
                                      // استخدام دالة مساعدة لإنشاء الأزرار
                                      icon: isFavorite
                                          ? Icons.favorite
                                          : Icons.favorite_border,
                                      label: isFavorite
                                          ? 'إزالة'
                                          : 'مفضلة', // نص أقصر
                                      color: isFavorite
                                          ? AppColors.favoritesColor
                                          : prophetPrayersColor,
                                      onTap: toggleFavorite,
                                    ),
                                    _buildDialogActionButton(
                                      icon: isCopied
                                          ? Icons.check_circle_outline
                                          : Icons
                                              .copy_outlined, // أيقونات مختلفة
                                      label: isCopied ? 'تم النسخ' : 'نسخ',
                                      color: isCopied
                                          ? Colors.green[600]!
                                          : prophetPrayersColor, // استخدام non-null assertion
                                      onTap: copyToClipboard,
                                    ),
                                    _buildDialogActionButton(
                                      icon:
                                          Icons.share_outlined, // أيقونة مختلفة
                                      label: 'مشاركة',
                                      color: prophetPrayersColor,
                                      onTap: sharePrayer,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  // --- دوال مساعدة إضافية لمحتوى الحوار ---
  Widget _buildDialogMetaChip(
      {required IconData icon,
      required String text,
      required Color color,
      required bool isDarkMode}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8), // const
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5), // تعديل
      decoration: BoxDecoration(
        color: color.withValues(alpha: isDarkMode ? 0.15 : 0.1), // تعديل
        borderRadius: BorderRadius.circular(12), // تعديل
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        textDirection: TextDirection.rtl,
        children: [
          Icon(icon, size: 16, color: color), // تعديل الحجم
          const SizedBox(width: 6), // const
          Text(
            text,
            style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: color), // تعديل
          ),
        ],
      ),
    );
  }

  Widget _buildDialogVirtueSection(Dua prayer, bool isDarkMode) {
    final fadlColor =
        AppColors.getFadlColor(isDarkMode); // الحصول على لون الفضل
    return Container(
      padding: const EdgeInsets.all(14), // تعديل
      decoration: BoxDecoration(
        color: fadlColor.withValues(alpha: 0.08), // تعديل
        borderRadius: BorderRadius.circular(12), // تعديل
        border: Border.all(
            color: fadlColor.withValues(alpha: 0.2), width: 1), // تعديل
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end, // محاذاة لليمين
        children: [
          Row(
            textDirection: TextDirection.rtl,
            mainAxisAlignment: MainAxisAlignment.end, // محاذاة لليمين
            children: [
              Text(
                'فضل هذه الصلاة',
                style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                    color: fadlColor), // تعديل
              ),
              const SizedBox(width: 8), // const
              Icon(Icons.star_border_purple500_outlined,
                  size: 20, color: fadlColor), // أيقونة مختلفة
            ],
          ),
          const SizedBox(height: 8), // const
          SelectableText(
            // استخدام SelectableText
            prayer.virtue!,
            style: TextStyle(
              fontSize: 14, height: 1.6, // تعديل
              color: isDarkMode
                  ? Colors.white.withValues(alpha: 0.8)
                  : Colors.black.withValues(alpha: 0.75), // تعديل
            ),
            textDirection: TextDirection.rtl,
            textAlign: TextAlign.right,
          ),
        ],
      ),
    );
  }

  // بناء زر إجراء في شريط أدوات الحوار (مُحسّن)
  Widget _buildDialogActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Expanded(
      // جعل الأزرار تتمدد لتوزيع المساحة
      child: Material(
        // لـ InkWell
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12), // const
        clipBehavior: Clip.antiAlias, // const
        child: InkWell(
          onTap: onTap,
          splashColor: color.withValues(alpha: 0.2), // تعديل
          highlightColor: color.withValues(alpha: 0.1), // تعديل
          child: Padding(
            // إضافة padding حول محتوى الزر
            padding: const EdgeInsets.symmetric(vertical: 8.0), // const
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(icon, color: color, size: 20), // تعديل الحجم
                const SizedBox(height: 4), // const
                Text(
                  label,
                  style: TextStyle(
                      fontSize: 11,
                      color: color,
                      fontWeight: FontWeight.w500), // تعديل
                  textAlign: TextAlign.center, // توسيط النص
                  overflow: TextOverflow.ellipsis, // لمنع تجاوز النص
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final prophetPrayersColor = AppColors.getProphetPrayersColor(isDarkMode);
    final prophetPrayersProvider = Provider.of<ProphetPrayersProvider>(context);
    final List<Dua> featuredPrayers =
        prophetPrayersProvider.featuredPrayers; // Dua بدلاً من ProphetPrayer

    return Scaffold(
      // استخدام لون خلفية أكثر اتساقًا مع السمات
      backgroundColor: isDarkMode ? const Color(0xFF121212) : Colors.grey[100],
      body: prophetPrayersProvider.isLoading
          ? Center(
              // عرض مؤشر تحميل مركزي
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: prophetPrayersColor),
                  const SizedBox(height: 16), // const
                  const Text(
                    // const
                    'جاري تحميل الصلوات على النبي...',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            )
          : CustomScrollView(
              controller: _scrollController,
              physics: const BouncingScrollPhysics(), // const
              slivers: [
                // شريط التطبيق الفاخر والمحسن
                SliverAppBar(
                  expandedHeight: 280, // تعديل الارتفاع قليلاً
                  floating: false, // كما في التصميم
                  pinned: true, // كما في التصميم
                  elevation: _isSearching || _showScrollToTop
                      ? 2.0
                      : 0.0, // تعديل الظل
                  backgroundColor: prophetPrayersColor,
                  stretch: true,
                  shape: const RoundedRectangleBorder(
                    // إضافة شكل لحواف AppBar
                    borderRadius: BorderRadius.vertical(
                        bottom: Radius.circular(25)), // const
                  ),
                  // استخدام FlexibleSpaceBar لتحكم أفضل في المحتوى القابل للتمدد
                  flexibleSpace: FlexibleSpaceBar(
                    titlePadding: const EdgeInsets.only(
                        bottom: 16,
                        left: 56,
                        right: 56), // تعديل padding العنوان
                    centerTitle: true, // توسيط العنوان
                    // عنوان شريط التطبيق
                    title: AnimatedOpacity(
                      // تأثير تلاشي للعنوان
                      duration: const Duration(milliseconds: 300), // const
                      opacity:
                          _isSearching ? 0.0 : 1.0, // إخفاء العنوان عند البحث
                      child: const Text(
                        'صيغ الصلاة على النبي',
                        style: TextStyle(
                            // const
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            shadows: [
                              Shadow(
                                  blurRadius: 2,
                                  color: Colors.black38,
                                  offset: Offset(0, 1))
                            ] // ظل خفيف للنص
                            ),
                        textAlign: TextAlign.center, // توسيط النص
                      ),
                    ),
                    // خلفية شريط التطبيق
                    background: Stack(
                      fit: StackFit.expand,
                      children: [
                        // خلفية متدرجة
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                prophetPrayersColor,
                                prophetPrayersColor.withValues(alpha: 0.85),
                                prophetPrayersColor.withValues(alpha: 0.7)
                              ], // تعديل التدرج
                              stops: const [0.0, 0.7, 1.0], // const
                            ),
                          ),
                        ),
                        // زخرفة SVG ثابتة بدلاً من متحركة لتحسين الأداء
                        RepaintBoundary(
                          child: Opacity(
                            opacity: 0.05, // تقليل الشفافية
                            child: SvgPicture.asset('assets/images/p2.svg',
                                fit: BoxFit.cover),
                          ),
                        ),
                        // تظليل سفلي
                        const DecoratedBox(
                          // const
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Colors.black26,
                                Colors.black54
                              ], // تعديل
                              stops: [0.4, 0.8, 1.0], // const
                            ),
                          ),
                        ),
                        // --- محتوى العنوان الرئيسي في FlexibleSpaceBar ---
                        // (تم تبسيط هذا الجزء ليكون ضمن نطاق Title)
                        // إذا كان هناك تصميم محدد هنا، يمكن إضافته
                        // حاليًا، العنوان يعرض اسم الشاشة فقط.
                        Positioned(
                          left: 0, right: 0,
                          bottom: 60, // تعديل الموضع
                          child: AnimatedOpacity(
                            duration: const Duration(milliseconds: 300),
                            opacity: _isSearching ? 0.0 : 1.0,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.spa_outlined,
                                    color: Colors.white.withValues(alpha: 0.5),
                                    size: 40), // أيقونة بسيطة
                                const SizedBox(height: 8),
                                Text(
                                  'فضل عظيم وأجر كبير', // نص إضافي بسيط
                                  style: TextStyle(
                                      color:
                                          Colors.white.withValues(alpha: 0.7),
                                      fontSize: 14),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // حقل البحث أو زر البحث
                  bottom: PreferredSize(
                    // استخدام PreferredSize لتحديد ارتفاع ثابت لحقل البحث
                    preferredSize: const Size.fromHeight(60.0), // const
                    child: AnimatedContainer(
                      // حاوية متحركة لحقل البحث
                      duration: const Duration(milliseconds: 300), // const
                      padding: EdgeInsets.symmetric(
                          horizontal: 16, vertical: _isSearching ? 8 : 0),
                      height: _isSearching
                          ? 60
                          : 0, // التحكم في الارتفاع لإظهار/إخفاء حقل البحث
                      child: _isSearching
                          ? Opacity(
                              // تأثير تلاشي لحقل البحث
                              opacity: _isSearching ? 1.0 : 0.0,
                              child: TextField(
                                controller: _searchController,
                                focusNode:
                                    _searchFocusNode, // استخدام FocusNode
                                onChanged: _updateSearchResults,
                                textDirection: TextDirection.rtl,
                                textAlign: TextAlign.right,
                                style: TextStyle(
                                    color: isDarkMode
                                        ? Colors.white
                                        : Colors.black87,
                                    fontSize: 15),
                                cursorColor: isDarkMode
                                    ? Colors.white70
                                    : prophetPrayersColor,
                                decoration: InputDecoration(
                                  hintText: 'ابحث عن صيغة...',
                                  hintStyle: TextStyle(
                                      color: isDarkMode
                                          ? Colors.grey[400]
                                          : Colors.grey[600],
                                      fontSize: 14),
                                  filled: true,
                                  fillColor: (isDarkMode
                                          ? Colors.grey[800]
                                          : Colors.white)
                                      ?.withValues(
                                          alpha: 0.9), // خلفية شفافة قليلاً
                                  contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 20, vertical: 12), // const
                                  border: OutlineInputBorder(
                                    borderRadius:
                                        BorderRadius.circular(30), // const
                                    borderSide: BorderSide.none, // إزالة الحدود
                                  ),
                                  prefixIcon: Icon(Icons.search,
                                      color: isDarkMode
                                          ? Colors.grey[400]
                                          : Colors.grey[600],
                                      size: 20),
                                  suffixIcon: _searchController.text.isNotEmpty
                                      ? IconButton(
                                          icon: Icon(Icons.clear,
                                              color: isDarkMode
                                                  ? Colors.grey[400]
                                                  : Colors.grey[600],
                                              size: 18),
                                          onPressed: () {
                                            _searchController.clear();
                                            _updateSearchResults('');
                                          },
                                          splashRadius: 20, // const
                                        )
                                      : null,
                                ),
                              ),
                            )
                          : const SizedBox.shrink(), // const
                    ),
                  ),
                  actions: [
                    // زر البحث/الإغلاق
                    IconButton(
                      icon: AnimatedSwitcher(
                        // أنيميشن لتبديل أيقونة البحث/الإغلاق
                        duration: const Duration(milliseconds: 300), // const
                        transitionBuilder:
                            (Widget child, Animation<double> animation) {
                          return ScaleTransition(
                              scale: animation, child: child);
                        },
                        child: Icon(
                          _isSearching ? Icons.close : Icons.search,
                          color: Colors.white,
                          size: 24, // تعديل الحجم
                          key: ValueKey<bool>(
                              _isSearching), // مفتاح لـ AnimatedSwitcher
                        ),
                      ),
                      onPressed: _toggleSearch,
                      tooltip: _isSearching ? 'إغلاق البحث' : 'بحث',
                      splashRadius: 22, // const
                    ),
                    const SizedBox(width: 8), // const
                  ],
                ),

                // قسم الصلوات المميزة المحسن (إذا كان موجودًا ومطلوبًا)
                if (featuredPrayers.isNotEmpty &&
                    !_isSearching) // عرض فقط إذا لم يكن البحث نشطًا
                  SliverToBoxAdapter(
                    child: RepaintBoundary(
                      // عزل هذا القسم
                      child: FeaturedProphetPrayersSection(
                        featuredPrayers: featuredPrayers,
                        onPrayerTap: _showPrayerDialog, // استخدام نفس الحوار
                        animation:
                            _getAnimation(0), // استخدام الأنيميشن العام للقائمة
                      ),
                    ),
                  ),

                // قسم جميع صيغ الصلاة على النبي (عنوان القسم)
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(
                        16, 24, 16, 12), // تعديل padding
                    child: Row(
                      // استخدام Row لعنوان القسم
                      textDirection: TextDirection.rtl,
                      children: [
                        Icon(Icons.format_list_bulleted_rounded,
                            color: prophetPrayersColor,
                            size: 26), // أيقونة أوضح
                        const SizedBox(width: 12), // const
                        Text(
                          _isSearching && _searchController.text.isNotEmpty
                              ? 'نتائج البحث'
                              : 'جميع الصيغ',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold, // تعديل الحجم والوزن
                            color: isDarkMode
                                ? Colors.white.withValues(alpha: 0.9)
                                : Colors.black.withValues(alpha: 0.85),
                          ),
                        ),
                        const Spacer(), // const
                        if (_filteredCategories.isNotEmpty) // عرض عدد العناصر
                          Text(
                            '(${_filteredCategories.fold<int>(0, (prev, cat) => prev + cat.items.length)} صيغة)', // حساب إجمالي الأدعية
                            style: TextStyle(
                                fontSize: 13,
                                color: isDarkMode
                                    ? Colors.grey[400]
                                    : Colors.grey[600]),
                          ),
                      ],
                    ),
                  ),
                ),

                // قائمة الصلوات (قائمة الفئات) - محسنة بتصميم فاخر
                if (_filteredCategories.isNotEmpty)
                  SliverPadding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 8), // تعديل padding
                    sliver: SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          if (index >= _filteredCategories.length) {
                            return null; // ضمان عدم تجاوز الحدود
                          }
                          final category = _filteredCategories[index];

                          // إضافة مسافة بين العناصر
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 12.0),
                            child: RepaintBoundary(
                              key: ValueKey(
                                  'prophet_prayer_category_${category.id}'), // مفتاح فريد
                              child: ProphetPrayerCard(
                                // استخدام ProphetPrayerCard
                                category: category,
                                onTap: () => _navigateToCategory(category),
                                animation: _getAnimation(index +
                                    (featuredPrayers.isNotEmpty && !_isSearching
                                        ? 1
                                        : 0)), // تعديل index للأنيميشن
                                displayMode: ProphetPrayerCardDisplayMode
                                    .list, // تعيين نوع العرض كقائمة
                              ),
                            ),
                          );
                        },
                        childCount: _filteredCategories.length,
                        addAutomaticKeepAlives: true, // الإبقاء على الحالة
                        addRepaintBoundaries:
                            false, // نضيف RepaintBoundary يدويًا
                      ),
                    ),
                  ),

                // رسالة عند عدم وجود نتائج (محسنة)
                if (_filteredCategories.isEmpty && _isSearching)
                  SliverFillRemaining(
                    // استخدام SliverFillRemaining لملء المساحة المتبقية
                    hasScrollBody: false, // إذا كان المحتوى صغيرًا وفي المنتصف
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.all(24.0), // تعديل padding
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.search_off_rounded,
                                size: 72,
                                color: prophetPrayersColor.withValues(
                                    alpha: 0.6)), // أيقونة أوضح
                            const SizedBox(height: 20), // const
                            Text(
                              'لا توجد صيغ مطابقة',
                              style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: isDarkMode
                                      ? Colors.grey[400]
                                      : Colors.grey[700]), // تعديل
                              textAlign: TextAlign.center, // توسيط
                            ),
                            const SizedBox(height: 10), // const
                            Text(
                              'حاول البحث بكلمات أخرى أو تحقق من الإملاء.',
                              style: TextStyle(
                                  fontSize: 14,
                                  color: isDarkMode
                                      ? Colors.grey[500]
                                      : Colors.grey[600]), // تعديل
                              textAlign: TextAlign.center, // توسيط
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                // مساحة إضافية في النهاية
                const SliverToBoxAdapter(child: SizedBox(height: 80)), // const
              ],
            ),
      // زر التمرير لأعلى (محسن)
      floatingActionButtonLocation:
          FloatingActionButtonLocation.startFloat, // لـ RTL
      floatingActionButton: AnimatedOpacity(
        // تأثير ظهور واختفاء سلس
        duration: const Duration(milliseconds: 300), // const
        opacity: _showScrollToTop ? 1.0 : 0.0,
        child: AnimatedScale(
          // تأثير تكبير وتصغير سلس
          duration: const Duration(milliseconds: 300), // const
          scale: _showScrollToTop ? 1.0 : 0.0,
          child: FloatingActionButton(
            // استخدام FAB قياسي
            onPressed: () {
              _scrollToTop();
              HapticFeedback.mediumImpact();
            },
            backgroundColor: prophetPrayersColor,
            elevation: 4, // تعديل الظل
            mini: true, // زر أصغر
            tooltip: 'العودة للأعلى', // إضافة tooltip
            child: const Icon(Icons.keyboard_arrow_up,
                color: Colors.white, size: 24), // أيقونة أوضح
          ),
        ),
      ),
    );
  }
}
