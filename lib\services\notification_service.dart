/*
import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:typed_data';
import '../models/notification_info.dart';
import 'dart:async';

class NotificationService {
  // نمط singleton للوصول لنفس النسخة من الخدمة في جميع أنحاء التطبيق
  static final NotificationService _instance = NotificationService._internal();

  factory NotificationService() => _instance;

  NotificationService._internal();

  // متغير يخزن ما إذا كانت الإشعارات مُمكّنة
  bool _initialized = false;

  // أنماط اهتزاز فاخرة للإشعارات
  static final Map<String, Int64List?> _vibrationPatterns = {
    // نمط اهتزاز فاخر لأذكار الصباح - نمط متدرج ناعم
    'morning':
        Int64List.fromList([0, 50, 100, 150, 200, 250, 300, 350, 400, 450]),

    // نمط اهتزاز فاخر لأذكار المساء - نمط متموج
    'evening':
        Int64List.fromList([0, 100, 50, 200, 100, 300, 150, 400, 200, 500]),

    // نمط اهتزاز للتذكير - نمط نبضات متكررة
    'reminder':
        Int64List.fromList([0, 100, 50, 100, 50, 100, 50, 100, 50, 100]),

    // نمط اهتزاز للتأجيل - نمط خفيف
    'snooze': Int64List.fromList([0, 50, 30, 50, 30, 50]),
  };

  Timer? _reminderTimer;

  // تهيئة الإشعارات
  Future<void> init({bool showTestNotification = false}) async {
    if (_initialized) return;

    await AwesomeNotifications().initialize(
      null,
      [
        // قناة إشعارات أذكار الصباح - بتصميم فاخر
        NotificationChannel(
          channelGroupKey: 'azkar_channel_group',
          channelKey: 'morning_azkar_channel',
          channelName: 'أذكار الصباح',
          channelDescription: 'إشعارات للتذكير بأذكار الصباح بتصميم فاخر',
          defaultColor: Colors.amber.shade600,
          ledColor: Colors.amber.shade600,
          // إضافة وقت تشغيل وإيقاف LED لتجنب الخطأ
          ledOnMs: 500,
          ledOffMs: 500,
          importance: NotificationImportance.High,
          playSound: false, // تعطيل الصوت بناءً على طلب المستخدم
          enableVibration: true,
          vibrationPattern: _vibrationPatterns['morning'],
          groupKey: 'azkar_group',
          channelShowBadge: true,
          onlyAlertOnce: false,
          criticalAlerts: true,
        ),
        // قناة إشعارات أذكار المساء - بتصميم فاخر
        NotificationChannel(
          channelGroupKey: 'azkar_channel_group',
          channelKey: 'evening_azkar_channel',
          channelName: 'أذكار المساء',
          channelDescription: 'إشعارات للتذكير بأذكار المساء بتصميم فاخر',
          defaultColor: Colors.indigo,
          ledColor: Colors.indigo,
          // إضافة وقت تشغيل وإيقاف LED لتجنب الخطأ
          ledOnMs: 500,
          ledOffMs: 500,
          importance: NotificationImportance.High,
          playSound: false, // تعطيل الصوت بناءً على طلب المستخدم
          enableVibration: true,
          vibrationPattern: _vibrationPatterns['evening'],
          groupKey: 'azkar_group',
          channelShowBadge: true,
          onlyAlertOnce: false,
          criticalAlerts: true,
        ),
        // قناة تذكير بالإشعارات غير المرئية
        NotificationChannel(
          channelGroupKey: 'azkar_channel_group',
          channelKey: 'reminder_channel',
          channelName: 'تذكير بالإشعارات',
          channelDescription: 'تذكير بالإشعارات التي لم يطلع عليها المستخدم',
          defaultColor: Colors.orange,
          ledColor: Colors.orange,
          // إضافة وقت تشغيل وإيقاف LED لتجنب الخطأ
          ledOnMs: 500,
          ledOffMs: 500,
          importance: NotificationImportance.High,
          playSound: false, // تعطيل الصوت بناءً على طلب المستخدم
          vibrationPattern: _vibrationPatterns['reminder'],
        ),
        // قناة خاصة بإشعارات التأجيل
        NotificationChannel(
          channelGroupKey: 'azkar_channel_group',
          channelKey: 'snooze_channel',
          channelName: 'إشعارات مؤجلة',
          channelDescription: 'إشعارات تم تأجيلها من قبل المستخدم',
          defaultColor: Colors.purple,
          ledColor: Colors.purple,
          // إضافة وقت تشغيل وإيقاف LED لتجنب الخطأ
          ledOnMs: 500,
          ledOffMs: 500,
          importance: NotificationImportance.High,
          playSound: false, // تعطيل الصوت بناءً على طلب المستخدم
          vibrationPattern: _vibrationPatterns['snooze'],
        ),
      ],
      debug: true, // تغيير إلى true للتصحيح
    );

    _initialized = true;

    // طلب الصلاحيات تلقائيًا عند التهيئة
    await requestPermission();

    if (showTestNotification) {
      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: 0,
          channelKey: 'reminder_channel',
          title: 'تم تفعيل الإشعارات',
          body: 'تم تهيئة نظام الإشعارات بنجاح',
        ),
      );
    }
  }

  // طلب أذونات الإشعارات
  Future<bool> requestPermission() async {
    return await AwesomeNotifications().isNotificationAllowed();
  }

  // جدولة إشعار أذكار الصباح بتصميم فاخر
  Future<void> scheduleMorningNotification(TimeOfDay time) async {
    await cancelMorningNotification(); // إلغاء أي إشعارات سابقة

    const id = 1;

    // اختيار رسالة عشوائية من مجموعة رسائل فاخرة
    final morningMessages = [
      'حان موعد أذكار الصباح 🌅 ابدأ يومك بذكر الله وتسبيحه',
      'أذكار الصباح 🌞 لحظات من النور والسكينة في بداية يومك',
      'وقت أذكار الصباح 🌄 "من قال حين يصبح: سبحان الله وبحمده، غُفرت ذنوبه"',
      'تذكير بأذكار الصباح 🌹 اغتنم هذه اللحظات المباركة لذكر الله',
      'أذكار الصباح 🕌 "من قال لا إله إلا الله وحده لا شريك له... كان له عدل عشر رقاب"',
    ];

    // اختيار رسالة عشوائية
    final random = DateTime.now().millisecond % morningMessages.length;
    final title = 'أذكار الصباح';
    final body = morningMessages[random];

    // تأكيد جدولة الإشعار بشكل صحيح
    final now = DateTime.now();

    // استخدام التاريخ والوقت المناسبين للإشعار
    final scheduledDateTime = DateTime(
      now.year,
      now.month,
      now.day,
      time.hour,
      time.minute,
    );

    // للتأكد من أن الوقت لم يمر بعد
    DateTime validScheduleTime = scheduledDateTime;
    if (validScheduleTime.isBefore(now)) {
      validScheduleTime = validScheduleTime.add(const Duration(days: 1));
    }

    // طباعة للتأكد من صحة الجدولة
    debugPrint('جدولة إشعار الصباح في: ${validScheduleTime.toString()}');

    await AwesomeNotifications().createNotification(
      content: NotificationContent(
        id: id,
        channelKey: 'morning_azkar_channel',
        title: title,
        body: body,
        notificationLayout: NotificationLayout.BigText,
        category: NotificationCategory.Reminder,
        wakeUpScreen: true,
        autoDismissible: false,
        showWhen: true,
        // largeIcon: 'resource://drawable/morning_azkar_icon', // تم تعطيل الأيقونة المخصصة
        color: Colors.amber.shade600,
        backgroundColor: Colors.amber.shade100,
        payload: {'type': 'morning'},
        criticalAlert: true,
        fullScreenIntent: true,
      ),
      actionButtons: [
        NotificationActionButton(
          key: 'OPEN_AZKAR',
          label: 'فتح الأذكار',
          actionType: ActionType.Default,
        ),
        NotificationActionButton(
          key: 'SNOOZE',
          label: 'تأجيل',
          actionType: ActionType.SilentAction,
        ),
      ],
      schedule: NotificationCalendar(
        hour: validScheduleTime.hour,
        minute: validScheduleTime.minute,
        second: 0,
        millisecond: 0,
        repeats: true,
        allowWhileIdle: true,
        preciseAlarm: true,
      ),
    );

    // تخزين معلومات الإشعار
    _storeNotificationInfo(
      NotificationInfo(
        id: id,
        title: title,
        body: body,
        channelKey: 'morning_azkar_channel',
        displayTime: validScheduleTime,
      ),
    );
  }

  // جدولة إشعار أذكار المساء بتصميم فاخر
  Future<void> scheduleEveningNotification(TimeOfDay time) async {
    await cancelEveningNotification();

    const id = 2;

    // اختيار رسالة عشوائية من مجموعة رسائل فاخرة
    final eveningMessages = [
      'حان موعد أذكار المساء 🌙 اختم يومك بذكر الله وتسبيحه',
      'أذكار المساء 🌆 لحظات من السكينة والطمأنينة في نهاية يومك',
      'وقت أذكار المساء 🌃 "من قال حين يمسي: أعوذ بكلمات الله التامات من شر ما خلق، لم تضره حمة"',
      'تذكير بأذكار المساء ✨ اغتنم هذه اللحظات المباركة لذكر الله',
      'أذكار المساء 🕋 "من قال سبحان الله وبحمده مائة مرة، حُطت خطاياه وإن كانت مثل زبد البحر"',
    ];

    // اختيار رسالة عشوائية
    final random = DateTime.now().millisecond % eveningMessages.length;
    const title = 'أذكار المساء';
    final body = eveningMessages[random];

    final now = DateTime.now();
    final scheduledDateTime = DateTime(
      now.year,
      now.month,
      now.day,
      time.hour,
      time.minute,
    );

    DateTime validScheduleTime = scheduledDateTime;
    if (validScheduleTime.isBefore(now)) {
      validScheduleTime = validScheduleTime.add(const Duration(days: 1));
    }

    // طباعة للتأكد من صحة الجدولة
    debugPrint('جدولة إشعار المساء في: ${validScheduleTime.toString()}');

    await AwesomeNotifications().createNotification(
      content: NotificationContent(
        id: id,
        channelKey: 'evening_azkar_channel',
        title: title,
        body: body,
        notificationLayout: NotificationLayout.BigText,
        category: NotificationCategory.Reminder,
        wakeUpScreen: true,
        autoDismissible: false,
        showWhen: true,
        // largeIcon: 'resource://drawable/evening_azkar_icon', // تم تعطيل الأيقونة المخصصة
        color: Colors.indigo,
        backgroundColor: Colors.indigo.shade100,
        payload: {'type': 'evening'},
        criticalAlert: true,
        fullScreenIntent: true,
      ),
      actionButtons: [
        NotificationActionButton(
          key: 'OPEN_AZKAR',
          label: 'فتح الأذكار',
          actionType: ActionType.Default,
        ),
        NotificationActionButton(
          key: 'SNOOZE',
          label: 'تأجيل',
          actionType: ActionType.SilentAction,
        ),
      ],
      schedule: NotificationCalendar(
        hour: validScheduleTime.hour,
        minute: validScheduleTime.minute,
        second: 0,
        millisecond: 0,
        repeats: true,
        allowWhileIdle: true,
        preciseAlarm: true,
      ),
    );

    _storeNotificationInfo(
      NotificationInfo(
        id: id,
        title: title,
        body: body,
        channelKey: 'evening_azkar_channel',
        displayTime: validScheduleTime,
      ),
    );
  }

  // إلغاء إشعار أذكار الصباح
  Future<void> cancelMorningNotification() async {
    await AwesomeNotifications().cancel(1);
    // إلغاء أي إشعارات تأجيل مرتبطة
    await AwesomeNotifications().cancel(199);
  }

  // إلغاء إشعار أذكار المساء
  Future<void> cancelEveningNotification() async {
    await AwesomeNotifications().cancel(2);
    // إلغاء أي إشعارات تأجيل مرتبطة
    await AwesomeNotifications().cancel(299);
  }

  /// دالة لتأجيل الإشعار لمدة محددة
  Future<bool> snoozeNotification(int id, int minutes) async {
    final DateTime now = DateTime.now();
    final DateTime snoozeTime = now.add(Duration(minutes: minutes));

    debugPrint(
        'تأجيل الإشعار $id لمدة $minutes دقيقة حتى ${snoozeTime.toString()}');

    // معرّف جديد لتجنب التعارض مع الإشعارات المجدولة الأصلية
    final int snoozeId = id == 1 ? 199 : 299;

    String title = '';
    String type = id == 1 ? 'morning' : 'evening';

    // تحديد نوع الإشعار بناءً على معرّفه
    if (id == 1) {
      title = 'تذكير بأذكار الصباح';
    } else if (id == 2) {
      title = 'تذكير بأذكار المساء';
    } else {
      title = 'تذكير بالأذكار';
    }

    // تحديد ما إذا كان إشعار الصباح أم المساء
    final isMorning = type == 'morning';

    // تحديد الألوان والأيقونات بناءً على نوع الإشعار
    final Color notificationColor =
        isMorning ? Colors.amber.shade600 : Colors.indigo;
    final Color backgroundColor =
        isMorning ? Colors.amber.shade100 : Colors.indigo.shade100;
    // تم تعطيل الأيقونات المخصصة
    // final String largeIcon = isMorning
    //     ? 'resource://drawable/morning_azkar_icon'
    //     : 'resource://drawable/evening_azkar_icon';
    final String summary =
        isMorning ? 'تم تأجيل أذكار الصباح' : 'تم تأجيل أذكار المساء';

    // اختيار رسائل تأجيل فاخرة
    final snoozeMessages = [
      'تم تأجيل $title لمدة $minutes دقيقة ⏰',
      'سنذكرك بـ $title بعد $minutes دقيقة ⌛',
      'تم تأجيل التذكير، سنعود إليك قريباً ✨',
    ];

    // اختيار رسالة عشوائية
    final random = DateTime.now().millisecond % snoozeMessages.length;
    final snoozeBody = snoozeMessages[random];

    return await AwesomeNotifications().createNotification(
      content: NotificationContent(
        id: snoozeId,
        channelKey: 'snooze_channel', // استخدام قناة التأجيل المخصصة
        title: title,
        body: snoozeBody,
        summary: summary,
        notificationLayout: NotificationLayout.BigText,
        category: NotificationCategory.Reminder,
        wakeUpScreen: true,
        autoDismissible: false,
        showWhen: true,
        // largeIcon: largeIcon, // تم تعطيل الأيقونة المخصصة
        color: notificationColor,
        backgroundColor: backgroundColor,
        payload: {
          'original_id': id.toString(),
          'type': type,
          'snooze_minutes': minutes.toString(),
        },
        criticalAlert: true,
        fullScreenIntent: true,
      ),
      actionButtons: [
        NotificationActionButton(
          key: 'OPEN_AZKAR',
          label: 'فتح الأذكار',
          actionType: ActionType.Default,
        ),
        NotificationActionButton(
          key: 'DISMISS',
          label: 'إغلاق',
          actionType: ActionType.DismissAction,
        ),
      ],
      schedule: NotificationCalendar.fromDate(date: snoozeTime),
    );
  }

  // تحديث حالة الإشعار ليصبح مرئيًا
  Future<void> markNotificationAsOpened(int id) async {
    final info = await _getNotificationInfo(id);
    if (info != null) {
      info.isOpened = true;
      await _storeNotificationInfo(info);
    }
  }

  // الحصول على الإشعارات غير المرئية
  Future<List<NotificationInfo>> getUnseenNotifications() async {
    final prefs = await SharedPreferences.getInstance();
    final notificationsJson = prefs.getStringList('notifications') ?? [];

    final List<NotificationInfo> allNotifications = [];

    for (final json in notificationsJson) {
      try {
        final notif = NotificationInfo.fromJson(jsonDecode(json));
        allNotifications.add(notif);
      } catch (e) {
        debugPrint('خطأ في تحليل إشعار: $e');
      }
    }

    // تصفية الإشعارات غير المرئية فقط والتي تم عرضها في الماضي
    return allNotifications
        .where((notif) =>
            !notif.isOpened && notif.displayTime.isBefore(DateTime.now()))
        .toList();
  }

  // عرض تذكير للإشعارات التي لم يطلع عليها المستخدم
  Future<void> showReminderForUnseenNotifications() async {
    final unseenNotifications = await getUnseenNotifications();

    if (unseenNotifications.isEmpty) return;

    // الحصول على أحدث إشعار غير مرئي
    unseenNotifications.sort((a, b) => b.displayTime.compareTo(a.displayTime));
    final latestNotification = unseenNotifications.first;

    // عرض تذكير
    await AwesomeNotifications().createNotification(
      content: NotificationContent(
        id: 1000, // معرف ثابت للتذكير
        channelKey: 'reminder_channel',
        title: 'تذكير: لم تشاهد ${latestNotification.title}',
        body: 'لديك ${unseenNotifications.length} إشعار لم تطلع عليه بعد',
        notificationLayout: NotificationLayout.Default,
      ),
      actionButtons: [
        NotificationActionButton(
          key: 'VIEW_ALL',
          label: 'عرض الكل',
        ),
      ],
    );
  }

  // حفظ إعدادات الإشعارات في التخزين المحلي
  Future<void> saveNotificationTime(
      {required bool isMorning,
      required TimeOfDay time,
      required bool isEnabled}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = isMorning ? 'morning_notification' : 'evening_notification';
      final enabledKey = isMorning
          ? 'morning_notification_enabled'
          : 'evening_notification_enabled';

      // حفظ الوقت كدقائق من بداية اليوم (لسهولة التخزين)
      final minutes = time.hour * 60 + time.minute;
      await prefs.setInt('${key}_time', minutes);
      await prefs.setBool(enabledKey, isEnabled);

      // تحديث جدولة الإشعارات بناءً على الإعدادات الجديدة
      if (isEnabled) {
        if (isMorning) {
          await scheduleMorningNotification(time);
        } else {
          await scheduleEveningNotification(time);
        }
      } else {
        if (isMorning) {
          await cancelMorningNotification();
        } else {
          await cancelEveningNotification();
        }
      }
    } catch (e) {
      debugPrint('خطأ في حفظ وقت الإشعار: $e');
    }
  }

  // تحميل إعدادات الإشعارات من التخزين المحلي
  Future<Map<String, dynamic>> loadNotificationSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // أذكار الصباح
      final morningMinutes = prefs.getInt('morning_notification_time') ??
          (6 * 60); // 6:00 صباحاً كافتراضي
      final morningEnabled =
          prefs.getBool('morning_notification_enabled') ?? false;

      // أذكار المساء
      final eveningMinutes = prefs.getInt('evening_notification_time') ??
          (17 * 60); // 5:00 مساءً كافتراضي
      final eveningEnabled =
          prefs.getBool('evening_notification_enabled') ?? false;

      // خيار التأجيل
      final snoozeMinutes =
          prefs.getInt('snooze_minutes') ?? 10; // 10 دقائق كوقت افتراضي للتأجيل

      // تحويل الدقائق إلى كائن TimeOfDay
      final morningTime =
          TimeOfDay(hour: morningMinutes ~/ 60, minute: morningMinutes % 60);

      final eveningTime =
          TimeOfDay(hour: eveningMinutes ~/ 60, minute: eveningMinutes % 60);

      return {
        'morningTime': morningTime,
        'morningEnabled': morningEnabled,
        'eveningTime': eveningTime,
        'eveningEnabled': eveningEnabled,
        'snoozeMinutes': snoozeMinutes,
      };
    } catch (e) {
      debugPrint('خطأ في تحميل إعدادات الإشعارات: $e');
      return {
        'morningTime': const TimeOfDay(hour: 6, minute: 0),
        'morningEnabled': false,
        'eveningTime': const TimeOfDay(hour: 17, minute: 0),
        'eveningEnabled': false,
        'snoozeMinutes': 10,
      };
    }
  }

  // حفظ مدة التأجيل المفضلة
  Future<void> saveSnoozeTime(int minutes) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('snooze_minutes', minutes);
  }

  // تهيئة الإشعارات عند بدء التطبيق استناداً إلى الإعدادات المحفوظة
  Future<void> initializeNotifications(
      {bool showTestNotification = false}) async {
    await init(showTestNotification: showTestNotification);

    // تفعيل الإشعارات المجدولة بعد التأكد من عمل الإشعار البسيط
    final settings = await loadNotificationSettings();

    // تفعيل مع طباعة معلومات للتصحيح
    debugPrint('إعدادات الإشعارات: $settings');

    if (settings['morningEnabled'] == true) {
      debugPrint('جدولة إشعارات الصباح...');
      await scheduleMorningNotification(settings['morningTime']);
    }

    if (settings['eveningEnabled'] == true) {
      debugPrint('جدولة إشعارات المساء...');
      await scheduleEveningNotification(settings['eveningTime']);
    }
  }

  // تخزين معلومات الإشعار
  Future<void> _storeNotificationInfo(NotificationInfo info) async {
    final prefs = await SharedPreferences.getInstance();
    List<String> notificationsJson = prefs.getStringList('notifications') ?? [];

    // التحقق من وجود الإشعار مسبقاً وإزالته إذا كان موجوداً
    notificationsJson = notificationsJson.where((json) {
      final notif = NotificationInfo.fromJson(jsonDecode(json));
      return notif.id != info.id;
    }).toList();

    // إضافة الإشعار الجديد
    notificationsJson.add(jsonEncode(info.toJson()));

    // حفظ القائمة المحدثة
    await prefs.setStringList('notifications', notificationsJson);
  }

  // الحصول على معلومات إشعار محدد
  Future<NotificationInfo?> _getNotificationInfo(int id) async {
    final prefs = await SharedPreferences.getInstance();
    final notificationsJson = prefs.getStringList('notifications') ?? [];

    for (final json in notificationsJson) {
      final notif = NotificationInfo.fromJson(jsonDecode(json));
      if (notif.id == id) {
        return notif;
      }
    }

    return null;
  }

  // إضافة دالة لتفعيل التذكير بالإشعارات غير المرئية
  Future<void> enableUnseenNotificationsReminder() async {
    // إلغاء أي مؤقت سابق
    _reminderTimer?.cancel();

    // إنشاء مؤقت جديد
    _reminderTimer = Timer.periodic(const Duration(hours: 1), (_) async {
      await showReminderForUnseenNotifications();
    });
  }

  // دالة للتحقق من الإشعارات المجدولة
  Future<bool> checkScheduledNotifications() async {
    final pendingNotifications =
        await AwesomeNotifications().listScheduledNotifications();
    return pendingNotifications.isNotEmpty;
  }

  // دالة لتنظيف موارد الخدمة (يجب استدعاؤها عند إغلاق التطبيق)
  void cleanUp() {
    _reminderTimer?.cancel();
    _initialized = false;
  }

  // إضافة دالة لاختبار أنماط الاهتزاز المختلفة
  Future<void> testVibrationPatterns() async {
    // اختبار نمط الصباح
    await AwesomeNotifications().createNotification(
      content: NotificationContent(
        id: 901,
        channelKey: 'morning_azkar_channel',
        title: 'اختبار اهتزاز الصباح',
        body: 'هذا اختبار لنمط اهتزاز إشعارات الصباح',
        notificationLayout: NotificationLayout.Default,
      ),
    );

    // انتظار لحظة
    await Future.delayed(const Duration(seconds: 2));

    // اختبار نمط المساء
    await AwesomeNotifications().createNotification(
      content: NotificationContent(
        id: 902,
        channelKey: 'evening_azkar_channel',
        title: 'اختبار اهتزاز المساء',
        body: 'هذا اختبار لنمط اهتزاز إشعارات المساء',
        notificationLayout: NotificationLayout.Default,
      ),
    );
  }

  // إضافة دالة لتنظيف الإشعارات القديمة (أضف في نهاية الملف)
  Future<void> cleanOldNotifications() async {
    await AwesomeNotifications().cancelAllSchedules();
    await AwesomeNotifications().cancelAll();

    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('notifications');

    _initialized = false;
    await init();

    final settings = await loadNotificationSettings();

    if (settings['morningEnabled'] == true) {
      await scheduleMorningNotification(settings['morningTime']);
    }

    if (settings['eveningEnabled'] == true) {
      await scheduleEveningNotification(settings['eveningTime']);
    }

    debugPrint('تم تنظيف الإشعارات القديمة وإعادة جدولة الإشعارات النشطة');
  }

  /// دالة اختبار لإرسال إشعار فوري للتحقق من توافق الإصدار 0.9.2
  Future<bool> sendTestNotification() async {
    return await AwesomeNotifications().createNotification(
      content: NotificationContent(
        id: 0,
        channelKey: 'basic_channel',
        title: 'اختبار الإشعارات',
        body: 'هذا إشعار اختباري للتأكد من توافق الإصدار 0.9.2',
        category: NotificationCategory.Message,
        wakeUpScreen: true,
        fullScreenIntent: false,
        autoDismissible: true,
        backgroundColor: Colors.blue,
      ),
      actionButtons: [
        NotificationActionButton(
          key: 'DISMISS',
          label: 'إغلاق',
          actionType: ActionType.DismissAction,
          isDangerousOption: false,
        ),
        NotificationActionButton(
          key: 'VIEW',
          label: 'عرض',
          actionType: ActionType.Default,
          isDangerousOption: false,
        ),
      ],
    );
  }

  /// دالة اختبار لإشعارات أذكار الصباح
  Future<bool> testMorningAzkarNotification() async {
    return await AwesomeNotifications().createNotification(
      content: NotificationContent(
        id: 901,
        channelKey: 'morning_azkar_channel',
        title: 'اختبار أذكار الصباح',
        body: 'هذا اختبار لإشعارات أذكار الصباح، اضغط لفتح صفحة أذكار الصباح',
        summary: 'تذكير يومي',
        notificationLayout: NotificationLayout.BigText,
        category: NotificationCategory.Reminder,
        wakeUpScreen: true,
        autoDismissible: true,
        showWhen: true,
        largeIcon: 'resource://drawable/morning_azkar_icon',
        color: Colors.amber.shade600,
        backgroundColor: Colors.amber.shade100,
        payload: {'type': 'morning'},
      ),
      actionButtons: [
        NotificationActionButton(
          key: 'OPEN_AZKAR',
          label: 'فتح الأذكار',
          actionType: ActionType.Default,
        ),
        NotificationActionButton(
          key: 'DISMISS',
          label: 'إغلاق',
          actionType: ActionType.DismissAction,
        ),
      ],
    );
  }

  /// دالة اختبار لإشعارات أذكار المساء
  Future<bool> testEveningAzkarNotification() async {
    return await AwesomeNotifications().createNotification(
      content: NotificationContent(
        id: 902,
        channelKey: 'evening_azkar_channel',
        title: 'اختبار أذكار المساء',
        body: 'هذا اختبار لإشعارات أذكار المساء، اضغط لفتح صفحة أذكار المساء',
        summary: 'تذكير يومي',
        notificationLayout: NotificationLayout.BigText,
        category: NotificationCategory.Reminder,
        wakeUpScreen: true,
        autoDismissible: true,
        showWhen: true,
        largeIcon: 'resource://drawable/evening_azkar_icon',
        color: Colors.indigo,
        backgroundColor: Colors.indigo.shade100,
        payload: {'type': 'evening'},
      ),
      actionButtons: [
        NotificationActionButton(
          key: 'OPEN_AZKAR',
          label: 'فتح الأذكار',
          actionType: ActionType.Default,
        ),
        NotificationActionButton(
          key: 'DISMISS',
          label: 'إغلاق',
          actionType: ActionType.DismissAction,
        ),
      ],
    );
  }

  // لجدولة إشعار الأذكار - تعديلات للإصدار 0.9.2
  Future<bool> scheduleAzkarNotification(
      int id, String title, String body, DateTime scheduleTime,
      {String? channelKey}) async {
    final notificationChannel = channelKey ?? 'basic_channel';
    final isMorning = notificationChannel == 'morning_azkar_channel';

    // تحديد الألوان والأيقونات بناءً على نوع الإشعار
    final Color notificationColor =
        isMorning ? Colors.amber.shade600 : Colors.indigo;
    final Color backgroundColor =
        isMorning ? Colors.amber.shade100 : Colors.indigo.shade100;
    final String largeIcon = isMorning
        ? 'resource://drawable/morning_azkar_icon'
        : 'resource://drawable/evening_azkar_icon';
    final String type = isMorning ? 'morning' : 'evening';
    final String summary = isMorning ? 'أذكار الصباح' : 'أذكار المساء';

    return await AwesomeNotifications().createNotification(
      content: NotificationContent(
        id: id,
        channelKey: notificationChannel,
        title: title,
        body: body,
        summary: summary,
        category: NotificationCategory.Reminder,
        wakeUpScreen: true,
        autoDismissible: false,
        showWhen: true,
        largeIcon: largeIcon,
        color: notificationColor,
        backgroundColor: backgroundColor,
        notificationLayout: NotificationLayout.BigText,
        criticalAlert: true,
        payload: {'type': type},
      ),
      actionButtons: [
        NotificationActionButton(
          key: 'OPEN_AZKAR',
          label: 'عرض الأذكار',
          actionType: ActionType.Default,
        ),
        NotificationActionButton(
          key: 'SNOOZE',
          label: 'تأجيل',
          actionType: ActionType.SilentAction,
        ),
      ],
      schedule: NotificationCalendar.fromDate(date: scheduleTime),
    );
  }
}
*/