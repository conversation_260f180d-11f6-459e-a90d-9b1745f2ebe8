/// مساعد لتنسيق التواريخ بشكل مقروء
class DateFormatter {
  /// تنسيق التاريخ بشكل مقروء
  static String formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        if (difference.inMinutes == 0) {
          return 'الآن';
        }
        return 'منذ ${difference.inMinutes} دقيقة';
      }
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays <= 7) {
      return 'منذ ${difference.inDays} أيام';
    } else if (difference.inDays <= 30) {
      final weeks = (difference.inDays / 7).floor();
      return 'منذ $weeks أسبوع';
    } else {
      return '${date.year}/${date.month}/${date.day}';
    }
  }
}
