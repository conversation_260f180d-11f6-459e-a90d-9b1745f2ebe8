import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../utils/app_colors.dart';
import '../utils/icon_helper.dart';
import '../models/zikr.dart';

class AzkarCard extends StatefulWidget {
  final Zikr category;
  final VoidCallback onTap;
  final Animation<double>? animation;

  const AzkarCard({
    super.key,
    required this.category,
    required this.onTap,
    this.animation,
  });

  @override
  State<AzkarCard> createState() => _AzkarCardState();
}

class _AzkarCardState extends State<AzkarCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _hoverController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  late Animation<double> _bgOpacityAnimation;
  bool _isHovered = false;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      vsync: this,
      // تقليل مدة الرسوم المتحركة لتحسين الأداء
      duration: const Duration(milliseconds: 200),
    );

    _scaleAnimation = Tween<double>(
            // تقليل مدى التكبير لتحسين الأداء
            begin: 1.0,
            end: 1.02)
        .animate(
      CurvedAnimation(
          parent: _hoverController,
          // استخدام منحنى أبسط لتحسين الأداء
          curve: Curves.easeOut),
    );

    _elevationAnimation = Tween<double>(
            // تقليل مدى الارتفاع لتحسين الأداء
            begin: 2.0,
            end: 5.0)
        .animate(
      CurvedAnimation(parent: _hoverController, curve: Curves.easeOut),
    );

    _bgOpacityAnimation = Tween<double>(begin: 0.15, end: 0.22).animate(
      CurvedAnimation(parent: _hoverController, curve: Curves.easeOut),
    );
  }

  @override
  void dispose() {
    _hoverController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // اختيار ألوان مناسبة بناءً على الوضع المظلم/الفاتح
    final Color cardBgColor = isDarkMode
        ? const Color(0xFF252A34) /* TasbihColors.darkCardColor */
        : Colors.white;
    final Color textColor = isDarkMode
        ? const Color(0xFFE0E0E0) /* TasbihColors.darkTextColor */
        : Colors.grey.shade800;
    final Color descriptionColor = isDarkMode
        ? const Color(0xFFAAAAAA) /* TasbihColors.darkTextSecondary */
        : Colors.grey.shade700;
    final Color shadowColor = isDarkMode
        ? Colors.black54
        : AppColors.getAzkarColor(isDarkMode).withAlpha(77); // 0.3 * 255 = 77

    // استخدام RepaintBoundary لتحسين الأداء
    final baseCard = RepaintBoundary(
      child: AnimatedBuilder(
        animation: _hoverController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: GestureDetector(
              onTapDown: (_) => setState(() => _isPressed = true),
              onTapUp: (_) => setState(() => _isPressed = false),
              onTapCancel: () => setState(() => _isPressed = false),
              onTap: () {
                HapticFeedback.lightImpact();
                widget.onTap();
              },
              child: Card(
                clipBehavior: Clip.antiAlias,
                elevation: _elevationAnimation.value,
                shadowColor: shadowColor,
                color: cardBgColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(screenSize.width * 0.045),
                  side: BorderSide(
                    color: _isHovered || _isPressed
                        ? isDarkMode
                            ? AppColors.getAzkarColor(isDarkMode)
                                .withAlpha(128) // 0.5 * 255 = 128
                            : AppColors.getAzkarColor(isDarkMode)
                                .withAlpha(77) // 0.3 * 255 = 77
                        : isDarkMode
                            ? AppColors.getAzkarColor(isDarkMode)
                                .withAlpha(51) // 0.2 * 255 = 51
                            : AppColors.getAzkarColor(isDarkMode)
                                .withAlpha(26), // 0.1 * 255 = 26
                    width: _isHovered || _isPressed ? 1.5 : 0.5,
                  ),
                ),
                child: InkWell(
                  onHover: (value) {
                    setState(() {
                      _isHovered = value;
                    });
                    if (value) {
                      _hoverController.forward();
                    } else {
                      _hoverController.reverse();
                    }
                  },
                  splashColor: AppColors.getAzkarColor(isDarkMode)
                      .withAlpha(38), // 0.15 * 255 = 38
                  highlightColor: AppColors.getAzkarColor(isDarkMode)
                      .withAlpha(13), // 0.05 * 255 = 13
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // الجزء العلوي (الرأس)
                      Expanded(
                        flex: 5,
                        child: _buildCardHeader(screenSize, isDarkMode),
                      ),
                      // الجزء السفلي (المحتوى)
                      Expanded(
                        flex: 6,
                        child: _buildCardBody(theme, screenSize, textColor,
                            descriptionColor, isDarkMode),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );

    // تطبيق حركة الظهور إذا كانت متوفرة
    if (widget.animation != null) {
      return FadeTransition(
        opacity: widget.animation!,
        child: SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, 0.1),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: widget.animation!,
            curve: Curves.easeOutCubic,
          )),
          child: baseCard,
        ),
      );
    }

    return baseCard;
  }

  Widget _buildCardHeader(Size screenSize, bool isDarkMode) {
    final Color cardHeaderColor = isDarkMode
        ? AppColors.getAzkarColor(isDarkMode).withAlpha(38) // 0.15 * 255 = 38
        : AppColors.getAzkarColor(isDarkMode).withAlpha(26); // 0.1 * 255 = 26

    return Stack(
      fit: StackFit.expand,
      children: [
        // الخلفية المتدرجة - يمكن استخدام cardHeaderColor هنا
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                cardHeaderColor.withAlpha(179), // 0.7 * 255 = 179
                cardHeaderColor,
              ],
              stops: const [0.3, 1.0],
            ),
          ),
        ),

        // النقوش الإسلامية مع حركة - مُحسّنة للأداء وتجنب مشكلة ParentDataWidget
        Positioned(
          // تقليل مدى الحركة لتحسين الأداء
          right: -30,
          bottom: -20,
          child: RepaintBoundary(
            child: AnimatedBuilder(
              animation: _hoverController,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(_isHovered ? 3 : 0, _isHovered ? 2 : 0),
                  child: Opacity(
                    opacity: _bgOpacityAnimation.value - 0.05,
                    // إزالة التدوير لتحسين الأداء
                    child: SvgPicture.asset(
                      'assets/images/p2.svg',
                      width: screenSize.width * 0.4,
                      height: screenSize.width * 0.4,
                      colorFilter: ColorFilter.mode(
                        isDarkMode
                            ? Colors.white.withAlpha(179) // 0.7 * 255 = 179
                            : AppColors.getAzkarColor(isDarkMode)
                                .withAlpha(179), // 0.7 * 255 = 179
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ),

        // تأثير توهج خفيف عند المرور - مُحسّن للأداء وتجنب مشكلة ParentDataWidget
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: RepaintBoundary(
            child: AnimatedBuilder(
              animation: _hoverController,
              builder: (context, child) {
                return Container(
                  // إزالة الرسوم المتحركة من الحاوية لتحسين الأداء
                  height: screenSize.height * 0.08,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        _isHovered
                            ? Colors.white.withAlpha(26)
                            : Colors.transparent, // 0.1 * 255 = 26
                        Colors.transparent,
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),

        // المحتوى المركزي - أيقونة الفئة - مُحسّنة للأداء
        Center(
          // استخدام RepaintBoundary لتحسين الأداء
          child: RepaintBoundary(
            child: AnimatedBuilder(
              animation: _hoverController,
              builder: (context, child) {
                return Container(
                  // تقليل مدى التغيير في الحجم لتحسين الأداء
                  width: screenSize.width * 0.16,
                  height: screenSize.width * 0.16,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isDarkMode ? Colors.grey.shade700 : Colors.white,
                    gradient: RadialGradient(
                      colors: [
                        isDarkMode ? Colors.grey.shade500 : Colors.white,
                        isDarkMode
                            ? Colors.grey.shade700
                            : Colors.white.withAlpha(235), // 0.92 * 255 = 235
                      ],
                      radius: 0.7,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: _isHovered
                            ? AppColors.getAzkarColor(isDarkMode)
                                .withAlpha(77) // 0.3 * 255 = 77
                            : AppColors.getAzkarColor(isDarkMode)
                                .withAlpha(26), // 0.1 * 255 = 26
                        // تقليل قيمة التمويه لتحسين الأداء
                        blurRadius: _isHovered ? 8 : 6,
                        spreadRadius: _isHovered ? 1 : 0.5,
                      ),
                    ],
                  ),
                  child: IconHelper.getIconWidget(
                    categoryName: widget.category.name,
                    iconPath: widget.category.iconPath,
                    iconName: widget.category.iconName,
                    size: screenSize.width * 0.085,
                    color: AppColors.getAzkarColor(isDarkMode),
                    section: 'azkar',
                  ),
                );
              },
            ),
          ),
        ),

        // عدد الأذكار علامة في الزاوية - مُحسّنة للأداء
        Positioned(
          top: screenSize.height * 0.015,
          left: screenSize.width * 0.03,
          // استخدام RepaintBoundary لتحسين الأداء
          child: RepaintBoundary(
            child: Container(
              // إزالة الرسوم المتحركة من الحاوية لتحسين الأداء
              padding: EdgeInsets.symmetric(
                horizontal: screenSize.width * 0.02,
                vertical: screenSize.height * 0.005,
              ),
              decoration: BoxDecoration(
                color: isDarkMode
                    ? const Color(0xFF252A34) // TasbihColors.darkCardColor
                    : Colors.white.withAlpha(217), // 0.85 * 255 = 217
                borderRadius: BorderRadius.circular(screenSize.width * 0.03),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(26), // 0.1 * 255 = 26
                    // تقليل قيمة التمويه لتحسين الأداء
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: widget.category.hasSubcategories
                  // إذا كانت فئة تحتوي على أقسام فرعية
                  ? Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.account_tree_outlined,
                          size: screenSize.width * 0.03,
                          color: AppColors.getAzkarColor(isDarkMode),
                        ),
                        SizedBox(width: screenSize.width * 0.01),
                        Text(
                          '${widget.category.subcategories?.length ?? 0} أقسام',
                          style: TextStyle(
                            fontSize: screenSize.width * 0.025,
                            fontWeight: FontWeight.bold,
                            color: AppColors.getAzkarColor(isDarkMode),
                          ),
                        ),
                      ],
                    )
                  // إذا كانت فئة تحتوي على أذكار مباشرة
                  : Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.format_list_numbered,
                          size: screenSize.width * 0.03,
                          color: AppColors.getAzkarColor(isDarkMode),
                        ),
                        SizedBox(width: screenSize.width * 0.01),
                        Text(
                          '${widget.category.items?.length ?? 0} ذكر',
                          style: TextStyle(
                            fontSize: screenSize.width * 0.025,
                            fontWeight: FontWeight.bold,
                            color: AppColors.getAzkarColor(isDarkMode),
                          ),
                        ),
                      ],
                    ),
            ),
          ),
        ),
      ],
    );
  }

  // دالة لعرض وصف الذكر كاملاً في نافذة منبثقة
  void _showFullDescription(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final azkarColor = AppColors.getAzkarColor(isDarkMode);
    final backgroundColor = isDarkMode
        ? const Color(0xFF252A34) // TasbihColors.darkCardColor
        : Colors.white;
    final textColor = isDarkMode
        ? const Color(0xFFE0E0E0) // TasbihColors.darkTextColor
        : Colors.grey.shade800;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        backgroundColor: backgroundColor,
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // عنوان الحوار - مُحسّن لتجنب مشكلة ParentDataWidget
              LayoutBuilder(
                builder: (context, constraints) {
                  return Row(
                    textDirection: TextDirection
                        .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      IconButton(
                        icon: Icon(
                          Icons.close,
                          color: isDarkMode ? Colors.white70 : Colors.black54,
                        ),
                        onPressed: () => Navigator.of(context).pop(),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                      Row(
                        children: [
                          Text(
                            widget.category.name,
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: azkarColor,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Icon(
                            IconHelper.getIconForCategory(
                              widget.category.name,
                              iconName: widget.category.iconName,
                            ),
                            size: 20,
                            color: azkarColor,
                          ),
                        ],
                      ),
                    ],
                  );
                },
              ),

              const Divider(height: 24),

              // محتوى الوصف في منطقة قابلة للتمرير
              ConstrainedBox(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.5,
                ),
                child: SingleChildScrollView(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? azkarColor.withAlpha(13) // 0.05 * 255 = 13
                          : azkarColor.withAlpha(10), // 0.04 * 255 = 10
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: azkarColor.withAlpha(38), // 0.15 * 255 = 38
                        width: 0.5,
                      ),
                    ),
                    child: Text(
                      widget.category.description,
                      style: TextStyle(
                        fontSize: 16,
                        height: 1.8,
                        color: textColor,
                        fontWeight:
                            isDarkMode ? FontWeight.w500 : FontWeight.normal,
                      ),
                      textAlign: TextAlign.justify,
                      textDirection: TextDirection.rtl,
                    ),
                  ),
                ),
              ),

              // أزرار الإجراءات - مُحسّن لتجنب مشكلة ParentDataWidget
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  TextButton.icon(
                    onPressed: () async {
                      await Clipboard.setData(
                          ClipboardData(text: widget.category.description));

                      // التحقق من أن السياق لا يزال مرتبطاً
                      if (context.mounted) {
                        Navigator.of(context).pop();
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('تم نسخ النص'),
                            behavior: SnackBarBehavior.floating,
                            duration: Duration(seconds: 1),
                          ),
                        );
                      }
                    },
                    icon: const Icon(Icons.copy),
                    label: const Text('نسخ'),
                    style: TextButton.styleFrom(
                      foregroundColor: azkarColor,
                    ),
                  ),
                  TextButton.icon(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    icon: const Icon(Icons.check_circle_outline),
                    label: const Text('تم'),
                    style: TextButton.styleFrom(
                      foregroundColor: azkarColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCardBody(ThemeData theme, Size screenSize, Color textColor,
      Color descriptionColor, bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(screenSize.width * 0.04),
      decoration: BoxDecoration(
        color: isDarkMode
            ? const Color(0xFF252A34) /* TasbihColors.darkCardColor */
            : Colors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(screenSize.width * 0.045),
          bottomRight: Radius.circular(screenSize.width * 0.045),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان الفئة
          Text(
            widget.category.name,
            style: TextStyle(
              fontSize: screenSize.width * 0.042,
              fontWeight: FontWeight.bold,
              color: isDarkMode
                  ? Colors.white
                  : AppColors.getAzkarColor(isDarkMode)
                      .withAlpha(230), // 0.9 * 255 = 230
              height: 1.3,
            ),
            maxLines: screenSize.width < 300 ? 1 : 2,
            overflow: TextOverflow.ellipsis,
          ),

          // خط فاصل جمالي مع حركة - مُحسّن للأداء
          // استخدام RepaintBoundary لتحسين الأداء
          RepaintBoundary(
            child: AnimatedContainer(
              // تقليل مدة الرسوم المتحركة لتحسين الأداء
              duration: const Duration(milliseconds: 200),
              height: 2,
              width:
                  _isHovered ? screenSize.width * 0.15 : screenSize.width * 0.1,
              margin: EdgeInsets.symmetric(vertical: screenSize.height * 0.008),
              decoration: BoxDecoration(
                color: _isHovered
                    ? AppColors.getAzkarColor(isDarkMode)
                        .withAlpha(204) // 0.8 * 255 = 204
                    : AppColors.getAzkarColor(isDarkMode)
                        .withAlpha(102), // 0.4 * 255 = 102
                borderRadius: BorderRadius.circular(1),
              ),
            ),
          ),

          // وصف الفئة مع زر عرض المزيد للنصوص الطويلة
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // نص الوصف
                Expanded(
                  child: Text(
                    widget.category.description,
                    style: TextStyle(
                      fontSize: screenSize.width * 0.032,
                      color: descriptionColor,
                      height: 1.2,
                    ),
                    maxLines: screenSize.width < 300 ? 2 : 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                // زر عرض المزيد إذا كان النص طويلاً
                if (widget.category.description.length > 100)
                  GestureDetector(
                    onTap: () => _showFullDescription(context),
                    child: Padding(
                      padding: const EdgeInsets.only(top: 4.0),
                      // زر عرض المزيد - مُحسّن لتجنب مشكلة ParentDataWidget
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          return Row(
                            textDirection: TextDirection
                                .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'عرض المزيد',
                                style: TextStyle(
                                  fontSize: screenSize.width * 0.025,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.getAzkarColor(isDarkMode)
                                      .withAlpha(179), // 0.7 * 255 = 179
                                ),
                              ),
                              SizedBox(width: screenSize.width * 0.005),
                              Icon(
                                Icons.more_horiz,
                                size: screenSize.width * 0.03,
                                color: AppColors.getAzkarColor(isDarkMode)
                                    .withAlpha(179), // 0.7 * 255 = 179
                              ),
                            ],
                          );
                        },
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // شريط السفلي مع مؤشر قابلية التصفح
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // مؤشر يوضح تفرع الأقسام إن وجدت
              if (widget.category.hasSubcategories)
                Flexible(
                  // مؤشر الأقسام الفرعية - مُحسّن لتجنب مشكلة ParentDataWidget
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      return Row(
                        textDirection: TextDirection
                            .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Flexible(
                            child: Text(
                              'أقسام فرعية',
                              style: TextStyle(
                                fontSize: screenSize.width * 0.025,
                                fontWeight: FontWeight.w500,
                                color: AppColors.getAzkarColor(isDarkMode)
                                    .withAlpha(179), // 0.7 * 255 = 179
                              ),
                              overflow: TextOverflow.ellipsis,
                              textDirection: TextDirection
                                  .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                              textAlign:
                                  TextAlign.right, // محاذاة النص إلى اليمين
                            ),
                          ),
                          SizedBox(width: screenSize.width * 0.01),
                          Icon(
                            Icons.account_tree_outlined,
                            size: screenSize.width * 0.035,
                            color: AppColors.getAzkarColor(isDarkMode)
                                .withAlpha(179), // 0.7 * 255 = 179
                          ),
                        ],
                      );
                    },
                  ),
                ),

              // مؤشر للتفاعل والانتقال - مُحسّن للأداء
              // استخدام RepaintBoundary لتحسين الأداء
              RepaintBoundary(
                child: AnimatedBuilder(
                  animation: _hoverController,
                  builder: (context, child) {
                    return Container(
                      // إزالة الرسوم المتحركة من الحاوية لتحسين الأداء
                      padding: EdgeInsets.symmetric(
                        horizontal: screenSize.width * 0.015,
                        vertical: screenSize.height * 0.004,
                      ),
                      decoration: BoxDecoration(
                        color: _isHovered
                            ? isDarkMode
                                ? AppColors.getAzkarColor(isDarkMode)
                                    .withAlpha(51) // 0.2 * 255 = 51
                                : AppColors.getAzkarColor(isDarkMode)
                                    .withAlpha(26) // 0.1 * 255 = 26
                            : Colors.transparent,
                        borderRadius:
                            BorderRadius.circular(screenSize.width * 0.03),
                      ),
                      // زر الاستعراض - مُحسّن لتجنب مشكلة ParentDataWidget
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          return Row(
                            textDirection: TextDirection
                                .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.arrow_forward_ios,
                                size: screenSize.width * 0.026,
                                color: AppColors.getAzkarColor(isDarkMode)
                                    .withAlpha(_isHovered
                                        ? 255
                                        : 179), // 1.0 * 255 = 255, 0.7 * 255 = 179
                              ),
                              SizedBox(width: screenSize.width * 0.003),
                              Container(
                                constraints: BoxConstraints(
                                  maxWidth: screenSize.width * 0.15,
                                ),
                                child: Text(
                                  'استعراض',
                                  style: TextStyle(
                                    fontSize: screenSize.width * 0.026,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.getAzkarColor(isDarkMode)
                                        .withAlpha(_isHovered
                                            ? 255
                                            : 179), // 1.0 * 255 = 255, 0.7 * 255 = 179
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
