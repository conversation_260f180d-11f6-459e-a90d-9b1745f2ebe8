import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/book.dart';
import '../database/database_helper.dart';
import '../services/native_share_service.dart';
import '../services/native_download_service.dart';

import '../utils/app_colors.dart';
import 'pdf_viewer_screen.dart';
import '../utils/pdf_utils.dart';
import 'package:cached_network_image/cached_network_image.dart';

class BookDetailsScreen extends StatefulWidget {
  final Book book;

  const BookDetailsScreen({Key? key, required this.book}) : super(key: key);

  @override
  State<BookDetailsScreen> createState() => _BookDetailsScreenState();
}

class _BookDetailsScreenState extends State<BookDetailsScreen>
    with SingleTickerProviderStateMixin {
  bool _isFavorite = false;
  bool _isLoading = false;
  double _downloadProgress = 0;
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _isPdfDownloaded = false;
  String? _localPdfPath;
  int _lastReadPage = 0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    _checkFavoriteStatus();
    _checkLocalPdfFile();
    _loadLastReadPosition();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // التحقق إذا كان الكتاب مفضلاً
  Future<void> _checkFavoriteStatus() async {
    try {
      final dbHelper = DatabaseHelper();
      final isFavorite = await dbHelper.isFavorite(widget.book.id, 'book');
      if (mounted) {
        setState(() {
          _isFavorite = isFavorite;
        });
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من المفضلة: $e');
    }
  }

  // التحقق من وجود الملف محلياً
  Future<void> _checkLocalPdfFile() async {
    try {
      final dbHelper = DatabaseHelper();
      final bookDetails = await dbHelper.getBookById(widget.book.id);
      final localPath = bookDetails?['pdf_path'] as String?;

      if (localPath != null &&
          localPath.isNotEmpty &&
          File(localPath).existsSync()) {
        if (mounted) {
          setState(() {
            _isPdfDownloaded = true;
            _localPdfPath = localPath;
          });
        }
      } else if (widget.book.pdfPath != null &&
          widget.book.pdfPath!.isNotEmpty &&
          File(widget.book.pdfPath!).existsSync()) {
        if (mounted) {
          setState(() {
            _isPdfDownloaded = true;
            _localPdfPath = widget.book.pdfPath;
          });
        }
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من ملف PDF المحلي: $e');
    }
  }

  // تبديل حالة المفضلة
  Future<void> _toggleFavorite() async {
    // استخدام متغير _isLoading لإظهار حالة التحميل
    setState(() {
      _isLoading = true;
    });

    try {
      final dbHelper = DatabaseHelper();

      if (_isFavorite) {
        await dbHelper.removeFromFavorites(widget.book.id, itemType: 'book');
      } else {
        await dbHelper.addToFavorites(widget.book.id, itemType: 'book');
      }

      if (mounted) {
        setState(() {
          _isFavorite = !_isFavorite;
          _isLoading = false;
        });
      }

      // تأثير ملموس للتفاعل
      HapticFeedback.lightImpact();

      // تشغيل الرسوم المتحركة
      if (_isFavorite) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }

      // رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isFavorite
                ? 'تمت إضافة الكتاب إلى المفضلة'
                : 'تمت إزالة الكتاب من المفضلة'),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // مشاركة الكتاب
  Future<void> _shareBook() async {
    HapticFeedback.lightImpact();
    await NativeShareService.shareBook(widget.book);
  }

  // تنزيل ملف PDF
  Future<void> _downloadPDF() async {
    if (widget.book.pdfUrl.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content:  Text('رابط تنزيل الكتاب غير متوفر'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
      _downloadProgress = 0;
    });

    try {
      // استخدام خدمة التنزيل الأصلية
      final downloadId = await NativeDownloadService.downloadBook(widget.book);

      if (downloadId == null) {
        throw Exception('فشل بدء التنزيل');
      }

      // الاشتراك في تدفق تقدم التنزيل
      final progressStream =
          NativeDownloadService.getDownloadProgress(downloadId);
      if (progressStream != null) {
        progressStream.listen(
          (progress) {
            if (mounted) {
              setState(() {
                _downloadProgress = progress / 100;

                // إذا اكتمل التنزيل
                if (progress >= 100) {
                  _isPdfDownloaded = true;
                  _isLoading = false;

                  // إعادة تحميل معلومات الكتاب
                  _checkLocalPdfFile();

                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم تنزيل الكتاب بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              });
            }
          },
          onError: (error) {
            if (mounted) {
              setState(() {
                _isLoading = false;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('حدث خطأ أثناء التنزيل: $error'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
          onDone: () {
            if (mounted && _isLoading) {
              setState(() {
                _isLoading = false;
              });
            }
          },
        );
      } else {
        throw Exception('فشل متابعة تقدم التنزيل');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء التنزيل: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // فتح الكتاب (إما عبر PDF داخلي أو رابط خارجي)
  Future<void> _openBook() async {
    // التحقق من وجود ملف PDF محلي تم تنزيله مسبقًا
    if (_isPdfDownloaded && _localPdfPath != null) {
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PdfViewerScreen(
            pdfPath: _localPdfPath!,
            bookTitle: widget.book.title,
            bookId: widget.book.id,
          ),
        ),
      );

      if (result != null && result is Map<String, dynamic>) {
        setState(() {
          _lastReadPage = result['lastPage'];
        });
      }
      return;
    }

    // التحقق من وجود ملف PDF مضمن مع التطبيق
    if (widget.book.localPdfPath != null &&
        widget.book.localPdfPath!.isNotEmpty) {
      try {
        setState(() {
          _isLoading = true;
        });

        // استخراج الملف المضمن إلى مجلد مؤقت
        final extractedPath =
            await PdfUtils.extractAssetPdf(widget.book.localPdfPath!);

        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          // فتح الملف المستخرج
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PdfViewerScreen(
                pdfPath: extractedPath,
                bookTitle: widget.book.title,
                bookId: widget.book.id,
              ),
            ),
          );

          if (result != null && result is Map<String, dynamic>) {
            setState(() {
              _lastReadPage = result['lastPage'];
            });
          }
        }
        return;
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('خطأ في فتح ملف PDF: $e')),
          );
        }
        return;
      }
    }

    // إذا كان هناك رابط PDF ولكن غير منزل، اعرض خيارات للمستخدم
    if (widget.book.pdfUrl.isNotEmpty) {
      showModalBottomSheet(
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        builder: (context) => Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'اختر طريقة فتح الكتاب',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              ListTile(
                leading: const Icon(Icons.download_rounded),
                title: const Text('تنزيل وفتح داخل التطبيق'),
                subtitle: const Text('للقراءة بدون إنترنت'),
                onTap: () async {
                  Navigator.pop(context);
                  await _downloadPDF();
                  if (_isPdfDownloaded && _localPdfPath != null && mounted) {
                    final result = await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => PdfViewerScreen(
                          pdfPath: _localPdfPath!,
                          bookTitle: widget.book.title,
                          bookId: widget.book.id,
                        ),
                      ),
                    );

                    if (result != null && result is Map<String, dynamic>) {
                      setState(() {
                        _lastReadPage = result['lastPage'];
                      });
                    }
                  }
                },
              ),
              const Divider(),
              ListTile(
                leading: const Icon(Icons.open_in_browser),
                title: const Text('فتح في المتصفح'),
                subtitle: const Text('باستخدام متصفح الويب'),
                onTap: () async {
                  Navigator.pop(context);
                  final Uri url = Uri.parse(widget.book.pdfUrl);
                  try {
                    await launchUrl(url, mode: LaunchMode.externalApplication);
                  } catch (e) {
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('حدث خطأ أثناء فتح الرابط: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                },
              ),
            ],
          ),
        ),
      );
      return;
    }

    // إذا لم يكن هناك رابط أو ملف محلي
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('عذراً، هذا الكتاب غير متوفر للقراءة حالياً'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  // عرض مؤشر تقدم التنزيل
  Widget _buildDownloadProgress() {
    // استخدام متغير _downloadProgress لعرض تقدم التنزيل
    if (!_isLoading || _downloadProgress == 0) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'جاري تنزيل الكتاب... ${(_downloadProgress * 100).toStringAsFixed(0)}%',
            style: const TextStyle(fontStyle: FontStyle.italic),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: _downloadProgress,
            backgroundColor: Colors.grey[300],
          ),
        ],
      ),
    );
  }

  // تحديث طريقة عرض صورة الغلاف
  Widget _buildCoverImage() {
    // التحقق من وجود صورة محلية أولاً
    if (widget.book.localCoverPath != null &&
        widget.book.localCoverPath!.isNotEmpty) {
      return Hero(
        tag: 'book-cover-${widget.book.id}',
        child: Image.asset(
          widget.book.localCoverPath!,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            // إذا فشل تحميل الصورة المحلية، تحقق من الرابط الخارجي
            return _buildFallbackOrNetworkImage();
          },
        ),
      );
    } else {
      // إذا لم تكن هناك صورة محلية، استخدم الرابط الخارجي أو الصورة الاحتياطية
      return _buildFallbackOrNetworkImage();
    }
  }

  Widget _buildFallbackOrNetworkImage() {
    if (widget.book.coverUrl.isNotEmpty) {
      return Hero(
        tag: 'book-cover-${widget.book.id}',
        child: CachedNetworkImage(
          imageUrl: widget.book.coverUrl,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            color: _getCategoryColor(widget.book.category).withOpacity(0.3),
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          ),
          errorWidget: (context, url, error) => _buildFallbackCover(),
        ),
      );
    } else {
      return _buildFallbackCover();
    }
  }

  Widget _buildFallbackCover() {
    // صورة احتياطية تعرض الحرف الأول من عنوان الكتاب
    return Container(
      color: _getCategoryColor(widget.book.category),
      child: Center(
        child: Text(
          widget.book.title.isNotEmpty
              ? widget.book.title.substring(0, 1).toUpperCase()
              : '?',
          style: const TextStyle(
            fontSize: 100,
            fontWeight: FontWeight.bold,
            color: Colors.white54,
          ),
        ),
      ),
    );
  }

  // أضف هذه الدالة الجديدة
  Future<void> _loadLastReadPosition() async {
    final dbHelper = DatabaseHelper();
    final lastPosition = await dbHelper.getLastReadPosition(widget.book.id);
    if (lastPosition != null) {
      setState(() {
        _lastReadPage = lastPosition;
      });
    }
  }

  Widget _buildLastReadInfo() {
    if (_lastReadPage > 0) {
      final isDarkMode = Theme.of(context).brightness == Brightness.dark;
      return Container(
        margin: const EdgeInsets.symmetric(vertical: 16.0),
        padding: const EdgeInsets.all(12.0),
        decoration: BoxDecoration(
          color: isDarkMode
              ? AppColors.booksColor.withAlpha(
                  38) // 0.15 * 255 = 38 (más oscuro para modo oscuro)
              : AppColors.booksColor.withAlpha(26), // 0.1 * 255 = 26
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
              color: isDarkMode
                  ? AppColors.booksColor.withAlpha(
                      77) // 0.3 * 255 = 77 (más visible en modo oscuro)
                  : AppColors.booksColor.withAlpha(51)), // 0.2 * 255 = 51
        ),
        child: Row(
          children: [
            const Icon(
              Icons.bookmark,
              color: AppColors.booksColor,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'آخر صفحة تمت قراءتها: $_lastReadPage',
              style: const TextStyle(
                color: AppColors.booksColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      );
    }
    return const SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      backgroundColor:
          isDarkMode ? theme.scaffoldBackgroundColor : AppColors.background,
      body: CustomScrollView(
        physics: const BouncingScrollPhysics(),
        slivers: [
          // شريط التطبيق المرن
          SliverAppBar(
            expandedHeight: 300,
            pinned: true,
            stretch: true,
            backgroundColor: theme.canvasColor,
            actions: [
              IconButton(
                icon: _isLoading
                    ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: theme.colorScheme.primary,
                        ),
                      )
                    : AnimatedBuilder(
                        animation: _animation,
                        builder: (context, child) {
                          return Icon(
                            _isFavorite
                                ? Icons.favorite
                                : Icons.favorite_border,
                            color: _isFavorite
                                ? Color.lerp(theme.iconTheme.color, Colors.red,
                                    _animation.value)
                                : theme.iconTheme.color,
                          );
                        },
                      ),
                onPressed: _isLoading ? null : _toggleFavorite,
              ),
              IconButton(
                icon: const Icon(Icons.share),
                onPressed: _shareBook,
              ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              background: Stack(
                fit: StackFit.expand,
                children: [
                  // استخدم الدالة الجديدة لعرض صورة الغلاف
                  _buildCoverImage(),

                  // تدرج لتحسين قراءة العنوان على الصورة
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      height: 100,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withAlpha(179), // 0.7 * 255 = 179
                          ],
                        ),
                      ),
                    ),
                  ),

                  // عنوان الكتاب في الأسفل
                  Positioned(
                    bottom: 16,
                    left: 16,
                    right: 16,
                    child: Text(
                      widget.book.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        shadows: [
                          Shadow(
                            offset: Offset(1, 1),
                            blurRadius: 3,
                            color: Colors.black45,
                          ),
                        ],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // محتوى الكتاب
          SliverToBoxAdapter(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // المؤلف
                  if (widget.book.author.isNotEmpty) ...[
                    Text(
                      'تأليف: ${widget.book.author}',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color:
                            theme.textTheme.titleMedium?.color?.withAlpha(200),
                      ),
                    ),
                    const SizedBox(height: 20),
                  ],

                  // الوسوم والتصنيف
                  Row(
                    children: [
                      Chip(
                        label: Text(widget.book.category),
                        backgroundColor: _getCategoryColor(widget.book.category)
                            .withAlpha(50),
                        labelStyle: TextStyle(
                          color: _getCategoryColor(widget.book.category),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 8),
                      if (widget.book.pages > 0) ...[
                        Chip(
                          label: Text('${widget.book.pages} صفحة'),
                          backgroundColor: theme.chipTheme.backgroundColor,
                        ),
                      ],

                      // مؤشر حالة التنزيل
                      if (_isPdfDownloaded) ...[
                        const SizedBox(width: 8),
                        Chip(
                          avatar: Icon(
                            Icons.download_done,
                            size: 16,
                            color: Colors.green[700],
                          ),
                          label: const Text('متاح للقراءة'),
                          backgroundColor: Colors.green[50],
                          labelStyle: TextStyle(
                            color: Colors.green[700],
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ],
                  ),

                  // مؤشر تقدم التنزيل
                  _buildDownloadProgress(),

                  const SizedBox(height: 24),

                  // عرض آخر صفحة قراءة
                  _buildLastReadInfo(),

                  // الوصف
                  Text(
                    'نبذة عن الكتاب',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    widget.book.description.isNotEmpty
                        ? widget.book.description
                        : 'لا يوجد وصف متاح لهذا الكتاب.',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      height: 1.5,
                    ),
                  ),

                  // الوسوم
                  if (widget.book.tags.isNotEmpty) ...[
                    const SizedBox(height: 24),
                    Text(
                      'الوسوم',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: widget.book.tags.map((tag) {
                        return Chip(
                          label: Text(tag),
                          backgroundColor: theme.chipTheme.backgroundColor,
                        );
                      }).toList(),
                    ),
                  ],

                  // إضافة مساحة أكبر في الأسفل لتجنب تداخل الزر العائم مع المحتوى
                  const SizedBox(height: 120),
                ],
              ),
            ),
          ),
        ],
      ),

      // إضافة صف في الأسفل لعرض معلومات آخر قراءة
      // تم إزالة bottomSheet لتجنب التداخل مع الزر العائم

      // زر قراءة الكتاب
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _openBook,
        icon: Icon(_isPdfDownloaded ? Icons.book : Icons.cloud_download),
        label: Text(_isPdfDownloaded ? 'قراءة الكتاب' : 'تنزيل وقراءة'),
        backgroundColor: AppColors.booksColor,
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  // الحصول على لون للتصنيف
  Color _getCategoryColor(String category) {
    final Map<String, Color> categoryColors = {
      'الفقه': Colors.teal,
      'التفسير': Colors.indigo,
      'الحديث': Colors.green,
      'العقيدة': Colors.red[800]!,
      'السيرة': Colors.amber[800]!,
      'اللغة العربية': Colors.purple,
      'أدب': Colors.blue,
      'تاريخ': Colors.brown,
    };

    if (!categoryColors.containsKey(category)) {
      final int hashCode = category.hashCode;
      final double hue = (hashCode % 360).toDouble();
      return HSVColor.fromAHSV(1.0, hue, 0.6, 0.8).toColor();
    }

    return categoryColors[category]!;
  }
}
