import 'package:flutter/material.dart';

class AppColors {
  // ألوان أساسية
  static const Color primary = Color(0xFF2A5B84); // اللون الأساسي للتطبيق
  static const Color secondary = Color(0xFF6C63FF); // اللون الثانوي
  static const Color accent = Color(0xFFE3A02B); // لون التأكيد
  static const Color background = Color(0xFFF8F8F8); // لون الخلفية
  static const Color darkBackground =
      Color(0xFF121212); // لون الخلفية في الوضع المظلم محسن
  static const Color success = Color(0xFF4CAF50); // لون النجاح
  static const Color error = Color(0xFFE53935); // لون الخطأ

  // ألوان الأقسام الرئيسية
  static const Color booksColor = Color(0xFF1F4E79); // لون قسم الكتب
  static const Color poemsColor = Color(0xFF7D3C98); // لون قسم القصائد
  static const Color azkarColor = Color(0xFF00897B); // لون قسم الأذكار
  static const Color azkarColorDark =
      Color(0xFF00A896); // لون قسم الأذكار في الوضع المظلم (أكثر إشراقاً)
  static const Color favoritesColor = Color(0xFFD4AF37); // لون قسم المفضلة
  static const Color tasbihColor = Color(0xFF00897B); // لون قسم المسبحة
  static const Color duasColor =
      Color(0xFF00897B); // لون قسم الأدعية (نفس لون المسبحة)
  static const Color duasColorDark =
      Color(0xFF00A896); // لون قسم الأدعية في الوضع المظلم
  static const Color prophetPrayersColor =
      Color(0xFF00897B); // لون قسم الصلاة على النبي (نفس لون المسبحة)
  static const Color prophetPrayersColorDark =
      Color(0xFF00A896); // لون قسم الصلاة على النبي في الوضع المظلم

  // ألوان إضافية
  static const Color cardColor = Colors.white; // لون البطاقات
  static const Color darkCardColor =
      Color(0xFF1E1E1E); // لون البطاقات في الوضع المظلم محسن
  static const Color textColor = Color(0xFF333333); // لون النص
  static const Color darkTextColor =
      Color(0xFFEEEEEE); // لون النص في الوضع المظلم محسن
  static const Color darkTextSecondaryColor =
      Color(0xFFBDBDBD); // لون النص الثانوي في الوضع المظلم

  // ألوان الفضل والمصدر
  static const Color fadlColor = Color(0xFFAA8800); // لون الفضل
  static const Color fadlColorDark =
      Color(0xFFFFD54F); // لون الفضل في الوضع المظلم
  static const Color sourceColor = Color(0xFF795548); // لون المصدر
  static const Color sourceColorDark =
      Color(0xFFBCAAA4); // لون المصدر في الوضع المظلم

  // ثوابت الشفافية الموحدة
  static const double opacity5 = 0.05;
  static const double opacity8 = 0.08;
  static const double opacity10 = 0.1;
  static const double opacity12 = 0.12;
  static const double opacity15 = 0.15;
  static const double opacity18 = 0.18;
  static const double opacity20 = 0.2;
  static const double opacity22 = 0.22;
  static const double opacity25 = 0.25;
  static const double opacity30 = 0.3;
  static const double opacity35 = 0.35;
  static const double opacity40 = 0.4;
  static const double opacity50 = 0.5;
  static const double opacity60 = 0.6;
  static const double opacity70 = 0.7;
  static const double opacity75 = 0.75;
  static const double opacity80 = 0.8;
  static const double opacity85 = 0.85;
  static const double opacity90 = 0.9;
  static const double opacity95 = 0.95;

  // ألوان فئات الكتب
  static const Map<String, Color> categoryColors = {
    'الفقه': Color(0xFF009688),
    'التفسير': Color(0xFF3F51B5),
    'الحديث': Color(0xFF4CAF50),
    'العقيدة': Color(0xFFC62828),
    'السيرة': Color(0xFFFF8F00),
    'اللغة العربية': Color(0xFF9C27B0),
    'أدب': Color(0xFF2196F3),
    'تاريخ': Color(0xFF795548),
  };

  // الحصول على لون الفئة
  static Color getCategoryColor(String category) {
    if (categoryColors.containsKey(category)) {
      return categoryColors[category]!;
    } else {
      final int hashCode = category.hashCode;
      final double hue = (hashCode % 360).toDouble();
      return HSVColor.fromAHSV(1.0, hue, 0.6, 0.8).toColor();
    }
  }

  // الحصول على لون الأذكار بناءً على الوضع المظلم
  static Color getAzkarColor(bool isDarkMode) {
    return isDarkMode ? azkarColorDark : azkarColor;
  }

  // الحصول على لون الأدعية بناءً على الوضع المظلم
  static Color getDuasColor(bool isDarkMode) {
    return isDarkMode ? tasbihColor.withAlpha(230) : tasbihColor;
  }

  // الحصول على لون الصلاة على النبي بناءً على الوضع المظلم
  static Color getProphetPrayersColor(bool isDarkMode) {
    return isDarkMode ? prophetPrayersColorDark : prophetPrayersColor;
  }

  // الحصول على لون الأذكار مع شفافية
  static Color getAzkarColorWithOpacity(bool isDarkMode, double opacity) {
    final Color baseColor = getAzkarColor(isDarkMode);
    // استخدام الطريقة الموصى بها
    return baseColor.withAlpha((opacity * 255).round());
  }

  // الحصول على لون النص بناءً على الوضع المظلم
  static Color getTextColor(bool isDarkMode) {
    return isDarkMode ? darkTextColor : textColor;
  }

  // الحصول على لون النص الثانوي بناءً على الوضع المظلم
  static Color getSecondaryTextColor(bool isDarkMode) {
    return isDarkMode ? darkTextSecondaryColor : Colors.grey[700]!;
  }

  // الحصول على لون البطاقة بناءً على الوضع المظلم
  static Color getCardColor(bool isDarkMode) {
    return isDarkMode ? darkCardColor : cardColor;
  }

  // الحصول على لون الفضل بناءً على الوضع المظلم
  static Color getFadlColor(bool isDarkMode) {
    return isDarkMode ? fadlColorDark : fadlColor;
  }

  // الحصول على لون المصدر بناءً على الوضع المظلم
  static Color getSourceColor(bool isDarkMode) {
    return isDarkMode ? sourceColorDark : sourceColor;
  }

  // الحصول على لون الخلفية بناءً على الوضع المظلم
  static Color getBackgroundColor(bool isDarkMode) {
    return isDarkMode ? darkBackground : background;
  }
}
