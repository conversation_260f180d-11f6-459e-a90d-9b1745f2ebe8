// خدمة الإشعارات المحلية باستخدام flutter_local_notifications
// تم تطويرها لتكون متوافقة مع جميع أنواع الهواتف وتعمل بشكل احترافي وفاخر

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
import '../main.dart'; // للوصول إلى navigatorKey
import '../screens/tasbih/models/user_activity_model.dart'; // نموذج نشاط المستخدم
import '../screens/tasbih/utils/tasbih_colors.dart'; // ألوان المسبحة
// import 'package:awesome_notifications/awesome_notifications.dart'; // تم تعطيل استيراد مكتبة الإشعارات الرائعة
// import 'package:vibration/vibration.dart'; // هذا الاستيراد غير مستخدم في الكود المقدم، سيتم تركه إذا كان موجوداً في الأصلي
// import '../utils/constants.dart'; // هذا الاستيراد غير مستخدم في الكود المقدم، سيتم تركه إذا كان موجوداً في الأصلي

// متغير عام لتتبع حالة تهيئة منطقة التوقيت
bool _timeZoneInitialized = false;

/// ثوابت مسارات التطبيق
class AppRoutes {
  static const String homeRoute = '/';
  static const String tasbihRoute = '/tasbih';
  static const String azkarRoute = '/azkar';
  static const String wirdListRoute = '/wird-list';
  static const String wirdDetailRoute = '/wird-detail';
  static const String wirdPlayerRoute = '/wird-player';
}

/// نموذج لتخزين معلومات الإشعار
class NotificationInfo {
  final int id;
  final String title;
  final String body;
  final String channelKey;
  final DateTime displayTime;
  final Map<String, dynamic>? payload;

  NotificationInfo({
    required this.id,
    required this.title,
    required this.body,
    required this.channelKey,
    required this.displayTime,
    this.payload,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'channelKey': channelKey,
      'displayTime': displayTime.toIso8601String(),
      'payload': payload,
    };
  }

  factory NotificationInfo.fromJson(Map<String, dynamic> json) {
    return NotificationInfo(
      id: json['id'],
      title: json['title'],
      body: json['body'],
      channelKey: json['channelKey'],
      displayTime: DateTime.parse(json['displayTime']),
      payload: json['payload'],
    );
  }
}

// <<< --- بداية تعريف الكلاس المفقود --- >>>
class LocalNotificationService {
  // --- بداية كود الـ Singleton الذي كان موضوعًا بشكل خاطئ ---
  // تطبيق نمط Singleton للتأكد من وجود نسخة واحدة فقط من الخدمة
  static final LocalNotificationService _instance =
      LocalNotificationService._internal();
  factory LocalNotificationService() => _instance;
  LocalNotificationService._internal();
  // --- نهاية كود الـ Singleton ---

  // --- بداية المتغيرات التي كانت معرفة كـ static بشكل خاطئ ---
  // مكتبة الإشعارات المحلية
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // مؤقت للتحقق من الإشعارات
  Timer? _verificationTimer;

  // حالة التهيئة
  bool _initialized = false;

  // معرفات قنوات الإشعارات (تبقى static لأنها ثوابت للكلاس)
  // تم تغيير أسماء القنوات لتجنب التعارض مع النظام القديم
  static const String _morningAzkarChannelId = 'local_morning_azkar_channel';
  static const String _eveningAzkarChannelId = 'local_evening_azkar_channel';
  static const String _wirdReminderChannelId = 'local_wird_reminder_channel';
  static const String _returnReminderChannelId =
      'local_return_reminder_channel';

  // أسماء قنوات الإشعارات (تبقى static)
  static const String _morningAzkarChannelName = 'أذكار الصباح';
  static const String _eveningAzkarChannelName = 'أذكار المساء';
  static const String _wirdReminderChannelName = 'تذكيرات الأوراد';
  static const String _returnReminderChannelName = 'تذكيرات العودة';

  // أوصاف قنوات الإشعارات (تبقى static)
  static const String _morningAzkarChannelDesc =
      'إشعارات للتذكير بأذكار الصباح';
  static const String _eveningAzkarChannelDesc =
      'إشعارات للتذكير بأذكار المساء';
  static const String _wirdReminderChannelDesc =
      'إشعارات للتذكير بالأوراد اليومية';
  static const String _returnReminderChannelDesc =
      'إشعارات للتذكير بالعودة إلى التطبيق';

  // مفاتيح تخزين البيانات (تبقى static)
  // تم تغيير أسماء المفاتيح لتجنب التعارض مع النظام القديم
  static const String _notificationLogKey = 'local_notification_log';
  static const String _userActivityKey = 'local_tasbih_user_activity';
  static const String _failedNotificationsKey = 'local_failed_notifications';

  // تم إزالة المتغيرات غير المستخدمة
  // --- نهاية المتغيرات التي كانت معرفة كـ static بشكل خاطئ ---

  /// جدولة إشعار أذكار الصباح - تم تحسين معالجة التوقيت
  Future<bool> scheduleMorningAzkarNotification(TimeOfDay time) async {
    try {
      // إلغاء أي إشعارات سابقة
      await cancelMorningAzkarNotification();

      // طباعة معلومات التوقيت للتشخيص
      debugPrint('جدولة إشعار أذكار الصباح');
      debugPrint('الوقت المحدد: ${time.hour}:${time.minute}');

      // إصلاح مشكلة التاريخ المستقبلي - استخدام DateTime.now() مع التحقق من صحة التاريخ
      final DateTime realNow = _getValidDateTime();
      debugPrint('التوقيت الحالي الحقيقي: ${realNow.toString()}');
      debugPrint('السنة الحالية: ${realNow.year}');

      // إنشاء وقت الإشعار بشكل صحيح
      DateTime scheduledTime = DateTime(
        realNow.year,
        realNow.month,
        realNow.day,
        time.hour,
        time.minute,
        0, // ثواني
      );

      // تحقق إضافي من صحة التاريخ
      if (scheduledTime.year > DateTime.now().year + 1) {
        debugPrint('⚠️ تم اكتشاف تاريخ مستقبلي بعيد، تصحيح التاريخ...');
        scheduledTime = DateTime(
          DateTime.now().year,
          scheduledTime.month,
          scheduledTime.day,
          scheduledTime.hour,
          scheduledTime.minute,
          0,
        );
      }

      // التأكد من أن الوقت لم يمر بعد
      if (scheduledTime.isBefore(realNow)) {
        scheduledTime = scheduledTime.add(const Duration(days: 1));
        debugPrint(
            'تم تعديل التوقيت ليكون في الغد: ${scheduledTime.toString()}');
      }

      // اختيار رسالة عشوائية
      final List<String> messages = [
        'حان موعد أذكار الصباح 🌅 ابدأ يومك بذكر الله وتسبيحه',
        'أذكار الصباح 🌞 لحظات من النور والسكينة في بداية يومك',
        'وقت أذكار الصباح 🌄 "من قال حين يصبح: سبحان الله وبحمده، غُفرت ذنوبه"',
        'تذكير بأذكار الصباح 🌹 اغتنم هذه اللحظات المباركة لذكر الله',
        'أذكار الصباح 🕌 "من قال لا إله إلا الله وحده لا شريك له... كان له عدل عشر رقاب"',
      ];

      // استخدام DateTime.now().millisecondsSinceEpoch لضمان عشوائية أفضل
      final int randomIndex =
          DateTime.now().millisecondsSinceEpoch % messages.length;
      const String title = 'أذكار الصباح';
      final String body = messages[randomIndex];

      // بيانات الإشعار
      final Map<String, dynamic> payload = {
        'type': 'morning_azkar',
        'time': scheduledTime.toIso8601String(),
      };

      // جدولة الإشعار مع محاولات إعادة
      bool success = await _scheduleMorningAzkarNotification(
        10001, // معرف ثابت لإشعار أذكار الصباح (تم تغييره لتجنب التعارض)
        title,
        body,
        scheduledTime,
        payload,
      );

      // إذا فشلت المحاولة الأولى، نحاول مرة أخرى بعد تأخير قصير
      if (!success) {
        debugPrint(
            'فشلت المحاولة الأولى لجدولة إشعار أذكار الصباح، محاولة ثانية بعد تأخير قصير...');
        await Future.delayed(const Duration(milliseconds: 500));
        success = await _scheduleMorningAzkarNotification(
          10001,
          title,
          body,
          scheduledTime,
          payload,
        );
      }

      if (success) {
        // تسجيل معلومات الإشعار
        final NotificationInfo notificationInfo = NotificationInfo(
          id: 10001,
          title: title,
          body: body,
          channelKey: _morningAzkarChannelId,
          displayTime: scheduledTime,
          payload: payload,
        );

        await _logNotification(notificationInfo);
        debugPrint(
            'تمت جدولة إشعار أذكار الصباح في: ${scheduledTime.toString()}');

        // طباعة معلومات إضافية للتشخيص
        debugPrint(
            'تمت جدولة إشعار أذكار الصباح داخلياً: 10001 في ${scheduledTime.toIso8601String()}');
        debugPrint(
            'تمت جدولة إشعار أذكار الصباح في: ${scheduledTime.hour}:${scheduledTime.minute}');
      } else {
        debugPrint('فشلت جميع محاولات جدولة إشعار أذكار الصباح');

        // حفظ الإشعار الفاشل لمحاولة لاحقة
        final NotificationInfo failedNotification = NotificationInfo(
          id: 10001,
          title: title,
          body: body,
          channelKey: _morningAzkarChannelId,
          displayTime: scheduledTime,
          payload: payload,
        );

        await _saveFailedNotificationSimple(failedNotification);
        debugPrint('تم حفظ إشعار أذكار الصباح الفاشل لمحاولة لاحقة');
      }

      return success;
    } catch (e) {
      debugPrint('خطأ في جدولة إشعار أذكار الصباح: $e');
      return false;
    }
  }

  /// إرسال إشعار اختباري فوري لأذكار الصباح
  Future<bool> sendTestMorningAzkarNotification() async {
    try {
      // إلغاء أي إشعارات سابقة بنفس المعرف
      await _flutterLocalNotificationsPlugin.cancel(10001);

      // تم تعطيل إلغاء الإشعارات في النظام القديم
      // await AwesomeNotifications().cancel(901);

      // اختيار رسالة عشوائية
      final List<String> messages = [
        'اختبار إشعار أذكار الصباح 🌅 هذا إشعار تجريبي',
        'أذكار الصباح 🌞 هذا إشعار اختباري للتأكد من عمل النظام',
        'اختبار إشعارات الصباح 🌄 اضغط لفتح صفحة أذكار الصباح',
      ];

      final int randomIndex =
          DateTime.now().millisecondsSinceEpoch % messages.length;
      const String title = 'اختبار إشعار الصباح';
      final String body = messages[randomIndex];

      // بيانات الإشعار
      final Map<String, dynamic> payload = {
        'type': 'morning_azkar_test',
        'time': DateTime.now().toIso8601String(),
      };

      // إنشاء تفاصيل الإشعار
      final AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
        _morningAzkarChannelId,
        _morningAzkarChannelName,
        channelDescription: _morningAzkarChannelDesc,
        importance: Importance.max, // تغيير إلى أعلى أهمية
        priority: Priority.max, // تغيير إلى أعلى أولوية
        playSound: false, // تعطيل الصوت بناءً على طلب المستخدم
        enableVibration: true,
        vibrationPattern:
            Int64List.fromList([0, 100, 200, 300, 400, 300, 200, 100]),
        enableLights: true,
        ledColor: TasbihColors.primary,
        ledOnMs: 500,
        ledOffMs: 500,
        category: AndroidNotificationCategory.reminder,
        visibility: NotificationVisibility.public,
        fullScreenIntent: true, // إضافة نية الشاشة الكاملة
        ticker: 'اختبار إشعار أذكار الصباح', // إضافة شريط متحرك
      );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: false,
        interruptionLevel:
            InterruptionLevel.timeSensitive, // تغيير مستوى المقاطعة
        categoryIdentifier: 'morning_azkar',
      );

      final NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // إرسال الإشعار فوراً
      await _flutterLocalNotificationsPlugin.show(
        10001,
        title,
        body,
        notificationDetails,
        payload: json.encode(payload),
      );

      // تم تعطيل إرسال إشعار باستخدام النظام القديم
      // try {
      //   await AwesomeNotifications().createNotification(
      //     content: NotificationContent(
      //       id: 901,
      //       channelKey: 'morning_azkar_channel',
      //       title: title,
      //       body: body,
      //       notificationLayout: NotificationLayout.BigText,
      //       category: NotificationCategory.Reminder,
      //       wakeUpScreen: true,
      //       fullScreenIntent: true,
      //       criticalAlert: true,
      //       color: Colors.amber.shade600,
      //       payload: {'type': 'morning_azkar_test'},
      //     ),
      //   );
      // } catch (innerError) {
      //   debugPrint(
      //       'خطأ في إرسال إشعار اختباري باستخدام النظام القديم: $innerError');
      //   // لا نريد أن نفشل الدالة بأكملها إذا فشل النظام القديم فقط
      // }

      debugPrint('تم إرسال إشعار اختباري لأذكار الصباح');
      return true;
    } catch (e) {
      debugPrint('خطأ في إرسال إشعار اختباري لأذكار الصباح: $e');

      // تم تعطيل إرسال إشعار باستخدام النظام القديم
      // try {
      //   await AwesomeNotifications().createNotification(
      //     content: NotificationContent(
      //       id: 901,
      //       channelKey: 'morning_azkar_channel',
      //       title: 'اختبار إشعار الصباح',
      //       body:
      //           'هذا اختبار لإشعارات أذكار الصباح، اضغط لفتح صفحة أذكار الصباح',
      //       notificationLayout: NotificationLayout.BigText,
      //       category: NotificationCategory.Reminder,
      //       wakeUpScreen: true,
      //       fullScreenIntent: true,
      //       criticalAlert: true,
      //       color: Colors.amber.shade600,
      //       payload: {'type': 'morning_azkar_test'},
      //     ),
      //   );
      //   debugPrint(
      //       'تم إرسال إشعار اختباري لأذكار الصباح باستخدام النظام القديم فقط');
      //   return true;
      // } catch (fallbackError) {
      //   debugPrint('فشل إرسال الإشعار باستخدام كلا النظامين: $fallbackError');
      //   return false;
      // }

      // في حالة فشل النظام الجديد، نعيد محاولة إرسال الإشعار بطريقة أبسط
      try {
        await _flutterLocalNotificationsPlugin.show(
          10001,
          'اختبار إشعار الصباح',
          'هذا اختبار لإشعارات أذكار الصباح، اضغط لفتح صفحة أذكار الصباح',
          const NotificationDetails(
            android: AndroidNotificationDetails(
              'test_channel',
              'قناة الاختبار',
              channelDescription: 'قناة للإشعارات الاختبارية',
              importance: Importance.max,
              priority: Priority.high,
            ),
            iOS: DarwinNotificationDetails(
              presentAlert: true,
              presentBadge: true,
              presentSound: false,
            ),
          ),
          payload: json.encode({'type': 'morning_azkar_test'}),
        );
        debugPrint('تم إرسال إشعار اختباري بسيط لأذكار الصباح');
        return true;
      } catch (fallbackError) {
        debugPrint('فشل إرسال الإشعار بالطريقة البسيطة أيضاً: $fallbackError');
        return false;
      }
    }
  }

  /// إلغاء إشعار أذكار الصباح
  Future<void> cancelMorningAzkarNotification() async {
    await _flutterLocalNotificationsPlugin.cancel(10001);
  }

  /// جدولة إشعار أذكار المساء - تم تحسين معالجة التوقيت
  Future<bool> scheduleEveningAzkarNotification(TimeOfDay time) async {
    try {
      // إلغاء أي إشعارات سابقة
      await cancelEveningAzkarNotification();

      // طباعة معلومات التوقيت للتشخيص
      debugPrint('جدولة إشعار أذكار المساء');
      debugPrint('الوقت المحدد: ${time.hour}:${time.minute}');

      // إصلاح مشكلة التاريخ المستقبلي - استخدام DateTime.now() مع التحقق من صحة التاريخ
      final DateTime realNow = _getValidDateTime();
      debugPrint('التوقيت الحالي الحقيقي: ${realNow.toString()}');
      debugPrint('السنة الحالية: ${realNow.year}');

      // إنشاء وقت الإشعار بشكل صحيح
      DateTime scheduledTime = DateTime(
        realNow.year,
        realNow.month,
        realNow.day,
        time.hour,
        time.minute,
        0, // ثواني
      );

      // تحقق إضافي من صحة التاريخ
      if (scheduledTime.year > DateTime.now().year + 1) {
        debugPrint('⚠️ تم اكتشاف تاريخ مستقبلي بعيد، تصحيح التاريخ...');
        scheduledTime = DateTime(
          DateTime.now().year,
          scheduledTime.month,
          scheduledTime.day,
          scheduledTime.hour,
          scheduledTime.minute,
          0,
        );
      }

      // التأكد من أن الوقت لم يمر بعد
      if (scheduledTime.isBefore(realNow)) {
        scheduledTime = scheduledTime.add(const Duration(days: 1));
        debugPrint(
            'تم تعديل التوقيت ليكون في الغد: ${scheduledTime.toString()}');
      }

      // اختيار رسالة عشوائية
      final List<String> messages = [
        'حان موعد أذكار المساء 🌙 اختم يومك بذكر الله وتسبيحه',
        'أذكار المساء 🌆 لحظات من السكينة والهدوء في نهاية يومك',
        'وقت أذكار المساء 🌃 "من قال حين يمسي: أعوذ بكلمات الله التامات من شر ما خلق، لم تضره حمة"',
        'تذكير بأذكار المساء 🌹 اغتنم هذه اللحظات المباركة لذكر الله',
        'أذكار المساء 🕌 "اللهم بك أمسينا وبك أصبحنا وبك نحيا وبك نموت وإليك المصير"',
      ];

      // استخدام DateTime.now().millisecondsSinceEpoch لضمان عشوائية أفضل
      final int randomIndex =
          DateTime.now().millisecondsSinceEpoch % messages.length;
      const String title = 'أذكار المساء';
      final String body = messages[randomIndex];

      // بيانات الإشعار
      final Map<String, dynamic> payload = {
        'type': 'evening_azkar',
        'time': scheduledTime.toIso8601String(),
      };

      // جدولة الإشعار مع محاولات إعادة
      bool success = await _scheduleEveningAzkarNotification(
        10002, // معرف ثابت لإشعار أذكار المساء (تم تغييره لتجنب التعارض)
        title,
        body,
        scheduledTime,
        payload,
      );

      // إذا فشلت المحاولة الأولى، نحاول مرة أخرى بعد تأخير قصير
      if (!success) {
        debugPrint(
            'فشلت المحاولة الأولى لجدولة إشعار أذكار المساء، محاولة ثانية بعد تأخير قصير...');
        await Future.delayed(const Duration(milliseconds: 500));
        success = await _scheduleEveningAzkarNotification(
          10002,
          title,
          body,
          scheduledTime,
          payload,
        );
      }

      if (success) {
        // تسجيل معلومات الإشعار
        final NotificationInfo notificationInfo = NotificationInfo(
          id: 10002,
          title: title,
          body: body,
          channelKey: _eveningAzkarChannelId,
          displayTime: scheduledTime,
          payload: payload,
        );

        await _logNotification(notificationInfo);
        debugPrint(
            'تمت جدولة إشعار أذكار المساء في: ${scheduledTime.toString()}');

        // طباعة معلومات إضافية للتشخيص
        debugPrint(
            'تمت جدولة إشعار أذكار المساء داخلياً: 10002 في ${scheduledTime.toIso8601String()}');
        debugPrint(
            'تمت جدولة إشعار أذكار المساء في: ${scheduledTime.hour}:${scheduledTime.minute}');
      } else {
        debugPrint('فشلت جميع محاولات جدولة إشعار أذكار المساء');

        // حفظ الإشعار الفاشل لمحاولة لاحقة
        final NotificationInfo failedNotification = NotificationInfo(
          id: 10002,
          title: title,
          body: body,
          channelKey: _eveningAzkarChannelId,
          displayTime: scheduledTime,
          payload: payload,
        );

        await _saveFailedNotificationSimple(failedNotification);
        debugPrint('تم حفظ إشعار أذكار المساء الفاشل لمحاولة لاحقة');
      }

      return success;
    } catch (e) {
      debugPrint('خطأ في جدولة إشعار أذكار المساء: $e');
      return false;
    }
  }

  /// إرسال إشعار اختباري فوري لأذكار المساء
  Future<bool> sendTestEveningAzkarNotification() async {
    try {
      // إلغاء أي إشعارات سابقة بنفس المعرف
      await _flutterLocalNotificationsPlugin.cancel(10002);

      // تم تعطيل إلغاء الإشعارات في النظام القديم
      // await AwesomeNotifications().cancel(902);

      // اختيار رسالة عشوائية
      final List<String> messages = [
        'اختبار إشعار أذكار المساء 🌙 هذا إشعار تجريبي',
        'أذكار المساء 🌆 هذا إشعار اختباري للتأكد من عمل النظام',
        'اختبار إشعارات المساء 🌃 اضغط لفتح صفحة أذكار المساء',
      ];

      final int randomIndex =
          DateTime.now().millisecondsSinceEpoch % messages.length;
      const String title = 'اختبار إشعار المساء';
      final String body = messages[randomIndex];

      // بيانات الإشعار
      final Map<String, dynamic> payload = {
        'type': 'evening_azkar_test',
        'time': DateTime.now().toIso8601String(),
      };

      // إنشاء تفاصيل الإشعار
      final AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
        _eveningAzkarChannelId,
        _eveningAzkarChannelName,
        channelDescription: _eveningAzkarChannelDesc,
        importance: Importance.max, // تغيير إلى أعلى أهمية
        priority: Priority.max, // تغيير إلى أعلى أولوية
        playSound: false, // تعطيل الصوت بناءً على طلب المستخدم
        enableVibration: true,
        vibrationPattern:
            Int64List.fromList([0, 100, 200, 300, 400, 300, 200, 100]),
        enableLights: true,
        ledColor: TasbihColors.secondary,
        ledOnMs: 500,
        ledOffMs: 500,
        category: AndroidNotificationCategory.reminder,
        visibility: NotificationVisibility.public,
        fullScreenIntent: true, // إضافة نية الشاشة الكاملة
        ticker: 'اختبار إشعار أذكار المساء', // إضافة شريط متحرك
      );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: false,
        interruptionLevel:
            InterruptionLevel.timeSensitive, // تغيير مستوى المقاطعة
        categoryIdentifier: 'evening_azkar',
      );

      final NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // إرسال الإشعار فوراً
      await _flutterLocalNotificationsPlugin.show(
        10002,
        title,
        body,
        notificationDetails,
        payload: json.encode(payload),
      );

      // تم تعطيل إرسال إشعار باستخدام النظام القديم
      // try {
      //   await AwesomeNotifications().createNotification(
      //     content: NotificationContent(
      //       id: 902,
      //       channelKey: 'evening_azkar_channel',
      //       title: title,
      //       body: body,
      //       notificationLayout: NotificationLayout.BigText,
      //       category: NotificationCategory.Reminder,
      //       wakeUpScreen: true,
      //       fullScreenIntent: true,
      //       criticalAlert: true,
      //       color: Colors.indigo,
      //       payload: {'type': 'evening_azkar_test'},
      //     ),
      //   );
      // } catch (innerError) {
      //   debugPrint(
      //       'خطأ في إرسال إشعار اختباري باستخدام النظام القديم: $innerError');
      //   // لا نريد أن نفشل الدالة بأكملها إذا فشل النظام القديم فقط
      // }

      debugPrint('تم إرسال إشعار اختباري لأذكار المساء');
      return true;
    } catch (e) {
      debugPrint('خطأ في إرسال إشعار اختباري لأذكار المساء: $e');

      // تم تعطيل إرسال إشعار باستخدام النظام القديم
      // try {
      //   await AwesomeNotifications().createNotification(
      //     content: NotificationContent(
      //       id: 902,
      //       channelKey: 'evening_azkar_channel',
      //       title: 'اختبار إشعار المساء',
      //       body:
      //           'هذا اختبار لإشعارات أذكار المساء، اضغط لفتح صفحة أذكار المساء',
      //       notificationLayout: NotificationLayout.BigText,
      //       category: NotificationCategory.Reminder,
      //       wakeUpScreen: true,
      //       fullScreenIntent: true,
      //       criticalAlert: true,
      //       color: Colors.indigo,
      //       payload: {'type': 'evening_azkar_test'},
      //     ),
      //   );
      //   debugPrint(
      //       'تم إرسال إشعار اختباري لأذكار المساء باستخدام النظام القديم فقط');
      //   return true;
      // } catch (fallbackError) {
      //   debugPrint('فشل إرسال الإشعار باستخدام كلا النظامين: $fallbackError');
      //   return false;
      // }

      // في حالة فشل النظام الجديد، نعيد محاولة إرسال الإشعار بطريقة أبسط
      try {
        await _flutterLocalNotificationsPlugin.show(
          10002,
          'اختبار إشعار المساء',
          'هذا اختبار لإشعارات أذكار المساء، اضغط لفتح صفحة أذكار المساء',
          const NotificationDetails(
            android: AndroidNotificationDetails(
              'test_channel',
              'قناة الاختبار',
              channelDescription: 'قناة للإشعارات الاختبارية',
              importance: Importance.max,
              priority: Priority.high,
            ),
            iOS: DarwinNotificationDetails(
              presentAlert: true,
              presentBadge: true,
              presentSound: false,
            ),
          ),
          payload: json.encode({'type': 'evening_azkar_test'}),
        );
        debugPrint('تم إرسال إشعار اختباري بسيط لأذكار المساء');
        return true;
      } catch (fallbackError) {
        debugPrint('فشل إرسال الإشعار بالطريقة البسيطة أيضاً: $fallbackError');
        return false;
      }
    }
  }

  /// إلغاء إشعار أذكار المساء
  Future<void> cancelEveningAzkarNotification() async {
    await _flutterLocalNotificationsPlugin.cancel(10002);
  }

  /// جدولة إشعار تذكير بالورد - تم تحسين معالجة التوقيت
  Future<bool> scheduleWirdReminderNotification(
    int wirdId,
    String wirdName,
    TimeOfDay time,
  ) async {
    try {
      // إنشاء معرف فريد للإشعار (تم تغييره لتجنب التعارض)
      final int notificationId = 13000 + wirdId;

      // إلغاء أي إشعارات سابقة بنفس المعرف
      await _flutterLocalNotificationsPlugin.cancel(notificationId);

      // طباعة معلومات التوقيت للتشخيص
      debugPrint('جدولة إشعار تذكير بالورد: $wirdName');
      debugPrint('الوقت المحدد: ${time.hour}:${time.minute}');

      // إصلاح مشكلة التاريخ المستقبلي - استخدام _getValidDateTime
      final DateTime realNow = _getValidDateTime();
      debugPrint('التوقيت الحالي الحقيقي: ${realNow.toString()}');
      debugPrint('السنة الحالية: ${realNow.year}');

      // إنشاء وقت الإشعار بشكل صحيح
      DateTime scheduledTime = DateTime(
        realNow.year,
        realNow.month,
        realNow.day,
        time.hour,
        time.minute,
        0, // ثواني
      );

      // تحقق إضافي من صحة التاريخ
      if (scheduledTime.year > DateTime.now().year + 1) {
        debugPrint('⚠️ تم اكتشاف تاريخ مستقبلي بعيد، تصحيح التاريخ...');
        scheduledTime = DateTime(
          DateTime.now().year,
          scheduledTime.month,
          scheduledTime.day,
          scheduledTime.hour,
          scheduledTime.minute,
          0,
        );
      }

      // التأكد من أن الوقت لم يمر بعد
      if (scheduledTime.isBefore(realNow)) {
        scheduledTime = scheduledTime.add(const Duration(days: 1));
        debugPrint(
            'تم تعديل التوقيت ليكون في الغد: ${scheduledTime.toString()}');
      }

      // اختيار رسالة عشوائية
      final List<String> messages = [
        'حان موعد ورد $wirdName 🌟 اغتنم هذه اللحظات الروحانية',
        'تذكير بورد $wirdName 🕌 "من لزم الاستغفار جعل الله له من كل هم فرجا"',
        'وقت ورد $wirdName 📿 لحظات من الصفاء الروحي تنتظرك',
        'ورد $wirdName 🌙 "من أحب أن يجد حلاوة الإيمان فليكثر من ذكر الله"',
        'حان وقت ورد $wirdName 💫 "إن لله ملائكة يطوفون في الطرق يلتمسون أهل الذكر"',
      ];

      // استخدام DateTime.now().millisecondsSinceEpoch لضمان عشوائية أفضل
      final int randomIndex =
          DateTime.now().millisecondsSinceEpoch % messages.length;
      const String title = 'تذكير بالورد';
      final String body = messages[randomIndex];

      // بيانات الإشعار
      final Map<String, dynamic> payload = {
        'type': 'wird_reminder',
        'wird_id': wirdId,
        'wird_name': wirdName,
        'time': scheduledTime.toIso8601String(),
      };

      // جدولة الإشعار مع محاولات إعادة
      bool success = await _scheduleWirdReminderNotification(
        notificationId,
        title,
        body,
        scheduledTime,
        payload,
      );

      // إذا فشلت المحاولة الأولى، نحاول مرة أخرى بعد تأخير قصير
      if (!success) {
        debugPrint(
            'فشلت المحاولة الأولى لجدولة إشعار الورد، محاولة ثانية بعد تأخير قصير...');
        await Future.delayed(const Duration(milliseconds: 500));
        success = await _scheduleWirdReminderNotification(
          notificationId,
          title,
          body,
          scheduledTime,
          payload,
        );
      }

      if (success) {
        // تسجيل معلومات الإشعار
        final NotificationInfo notificationInfo = NotificationInfo(
          id: notificationId,
          title: title,
          body: body,
          channelKey: _wirdReminderChannelId,
          displayTime: scheduledTime,
          payload: payload,
        );

        await _logNotification(notificationInfo);
        debugPrint(
            'تمت جدولة إشعار تذكير بالورد في: ${scheduledTime.toString()}');

        // طباعة معلومات إضافية للتشخيص
        debugPrint(
            'تمت جدولة إشعار تذكير بالورد داخلياً: $notificationId في ${scheduledTime.toIso8601String()}');
        debugPrint(
            'تمت جدولة إشعار تذكير بالورد في: ${scheduledTime.hour}:${scheduledTime.minute}');
      } else {
        debugPrint('فشلت جميع محاولات جدولة إشعار تذكير بالورد');

        // حفظ الإشعار الفاشل لمحاولة لاحقة
        final NotificationInfo failedNotification = NotificationInfo(
          id: notificationId,
          title: title,
          body: body,
          channelKey: _wirdReminderChannelId,
          displayTime: scheduledTime,
          payload: payload,
        );

        await _saveFailedNotificationSimple(failedNotification);
        debugPrint('تم حفظ الإشعار الفاشل لمحاولة لاحقة');
      }

      return success;
    } catch (e) {
      debugPrint('خطأ في جدولة إشعار تذكير بالورد: $e');
      return false;
    }
  }

  /// إلغاء إشعار تذكير بالورد
  Future<void> cancelWirdReminderNotification(int wirdId) async {
    final int notificationId = 13000 + wirdId;
    await _flutterLocalNotificationsPlugin.cancel(notificationId);
  }

  /// الحصول على قائمة الإشعارات المعلقة
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    try {
      final pendingNotifications =
          await _flutterLocalNotificationsPlugin.pendingNotificationRequests();
      debugPrint('عدد الإشعارات المعلقة: ${pendingNotifications.length}');

      // طباعة معلومات الإشعارات المعلقة للتشخيص
      for (final notification in pendingNotifications) {
        debugPrint(
            'إشعار معلق: ${notification.id}, العنوان: ${notification.title}');
      }

      return pendingNotifications;
    } catch (e) {
      debugPrint('خطأ في الحصول على الإشعارات المعلقة: $e');
      return [];
    }
  }

  /// حفظ الإشعار الفاشل لمحاولة لاحقة - نسخة محسنة مع تحسين التشخيص
  Future<void> _saveFailedNotificationSimple(
      NotificationInfo notification) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final failedNotifications =
          prefs.getStringList('failed_notifications') ?? [];

      // تحقق إضافي من صحة التاريخ قبل الحفظ
      DateTime displayTime = notification.displayTime;
      if (displayTime.year > DateTime.now().year + 1) {
        debugPrint(
            '⚠️ تم اكتشاف تاريخ مستقبلي في الإشعار الفاشل: ${notification.id}');

        // تصحيح التاريخ
        displayTime = DateTime(
          DateTime.now().year,
          displayTime.month,
          displayTime.day,
          displayTime.hour,
          displayTime.minute,
          displayTime.second,
        );

        // إنشاء نسخة جديدة من الإشعار مع التاريخ المصحح
        notification = NotificationInfo(
          id: notification.id,
          title: notification.title,
          body: notification.body,
          channelKey: notification.channelKey,
          displayTime: displayTime,
          payload: notification.payload,
        );

        debugPrint(
            '✅ تم تصحيح تاريخ الإشعار الفاشل: ${notification.id} إلى ${displayTime.toString()}');
      }

      // تحويل معلومات الإشعار إلى JSON
      final notificationJson = json.encode({
        'id': notification.id,
        'title': notification.title,
        'body': notification.body,
        'channelKey': notification.channelKey,
        'displayTime': notification.displayTime.toIso8601String(),
        'payload': notification.payload,
      });

      // إضافة الإشعار إلى القائمة
      failedNotifications.add(notificationJson);

      // حفظ القائمة
      await prefs.setStringList('failed_notifications', failedNotifications);
      debugPrint(
          '📝 تم حفظ الإشعار الفاشل: ${notification.id}, العنوان: ${notification.title}');
      debugPrint('📅 وقت العرض: ${notification.displayTime.toString()}');
    } catch (e, stackTrace) {
      debugPrint('⚠️ خطأ في حفظ الإشعار الفاشل: $e');
      debugPrint('تفاصيل الخطأ: $stackTrace');
    }
  }

  /// إرسال إشعار اختباري فوري للورد
  Future<bool> sendTestWirdReminderNotification(
    int wirdId,
    String wirdName,
  ) async {
    try {
      // إنشاء معرف فريد للإشعار الاختباري
      final int notificationId = 13000 + wirdId;

      // إلغاء أي إشعارات سابقة بنفس المعرف
      await _flutterLocalNotificationsPlugin.cancel(notificationId);

      // تم تعطيل إلغاء الإشعارات في النظام القديم
      // await AwesomeNotifications().cancel(9000 + wirdId);
      // await AwesomeNotifications().cancel(9000 + wirdId + 500);

      // اختيار رسالة عشوائية للتذكير بالورد
      final List<String> messages = [
        'اختبار إشعار الورد: $wirdName 🌟 هذا إشعار تجريبي',
        'تذكير بالورد: $wirdName ✨ هذا إشعار اختباري للتأكد من عمل النظام',
        'حان وقت الورد: $wirdName 🕌 اضغط لفتح صفحة الورد',
      ];

      final int randomIndex =
          DateTime.now().millisecondsSinceEpoch % messages.length;
      const String title = 'اختبار تذكير الورد';
      final String body = messages[randomIndex];

      // بيانات الإشعار
      final Map<String, dynamic> payload = {
        'type': 'wird_reminder_test',
        'wird_id': wirdId,
        'wird_name': wirdName,
        'time': DateTime.now().toIso8601String(),
      };

      // إنشاء تفاصيل الإشعار
      final AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
        _wirdReminderChannelId,
        _wirdReminderChannelName,
        channelDescription: _wirdReminderChannelDesc,
        importance: Importance.max, // تغيير إلى أعلى أهمية
        priority: Priority.max, // تغيير إلى أعلى أولوية
        playSound: false, // تعطيل الصوت بناءً على طلب المستخدم
        enableVibration: true,
        vibrationPattern:
            Int64List.fromList([0, 100, 200, 300, 400, 300, 200, 100]),
        enableLights: true,
        ledColor: TasbihColors.tertiary,
        ledOnMs: 500,
        ledOffMs: 500,
        category: AndroidNotificationCategory.reminder,
        visibility: NotificationVisibility.public,
        fullScreenIntent: true, // إضافة نية الشاشة الكاملة
        ticker: 'اختبار إشعار الورد: $wirdName', // إضافة شريط متحرك
      );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: false,
        interruptionLevel:
            InterruptionLevel.timeSensitive, // تغيير مستوى المقاطعة
        categoryIdentifier: 'wird_reminder',
      );

      final NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // إرسال الإشعار فوراً
      await _flutterLocalNotificationsPlugin.show(
        notificationId,
        title,
        body,
        notificationDetails,
        payload: json.encode(payload),
      );

      // تم تعطيل إرسال إشعار باستخدام النظام القديم
      // try {
      //   await AwesomeNotifications().createNotification(
      //     content: NotificationContent(
      //       id: 9000 + wirdId,
      //       channelKey: 'wird_channel',
      //       title: title,
      //       body: body,
      //       notificationLayout: NotificationLayout.BigText,
      //       category: NotificationCategory.Reminder,
      //       wakeUpScreen: true,
      //       fullScreenIntent: true,
      //       criticalAlert: true,
      //       color: TasbihColors.tertiary,
      //       payload: {
      //         'type': 'wird_reminder_test',
      //         'wird_id': wirdId.toString(),
      //         'wird_name': wirdName,
      //         'is_test': 'true',
      //       },
      //     ),
      //   );
      // } catch (innerError) {
      //   debugPrint(
      //       'خطأ في إرسال إشعار اختباري للورد باستخدام النظام القديم: $innerError');
      //   // لا نريد أن نفشل الدالة بأكملها إذا فشل النظام القديم فقط
      // }

      debugPrint('تم إرسال إشعار اختباري للورد: $wirdName');
      return true;
    } catch (e) {
      debugPrint('خطأ في إرسال إشعار اختباري للورد: $e');

      // تم تعطيل إرسال إشعار باستخدام النظام القديم
      // try {
      //   await AwesomeNotifications().createNotification(
      //     content: NotificationContent(
      //       id: 9000 + wirdId,
      //       channelKey: 'wird_channel',
      //       title: 'اختبار تذكير الورد',
      //       body: 'هذا اختبار لإشعارات الورد: $wirdName، اضغط لفتح صفحة الورد',
      //       notificationLayout: NotificationLayout.BigText,
      //       category: NotificationCategory.Reminder,
      //       wakeUpScreen: true,
      //       fullScreenIntent: true,
      //       criticalAlert: true,
      //       color: TasbihColors.tertiary,
      //       payload: {
      //         'type': 'wird_reminder_test',
      //         'wird_id': wirdId.toString(),
      //         'wird_name': wirdName,
      //         'is_test': 'true',
      //       },
      //     ),
      //   );
      //   debugPrint(
      //       'تم إرسال إشعار اختباري للورد باستخدام النظام القديم فقط: $wirdName');
      //   return true;
      // } catch (fallbackError) {
      //   debugPrint('فشل إرسال الإشعار باستخدام كلا النظامين: $fallbackError');
      //   return false;
      // }

      // في حالة فشل النظام الجديد، نعيد محاولة إرسال الإشعار بطريقة أبسط
      try {
        await _flutterLocalNotificationsPlugin.show(
          20000 + wirdId,
          'اختبار تذكير الورد',
          'هذا اختبار لإشعارات الورد: $wirdName، اضغط لفتح صفحة الورد',
          const NotificationDetails(
            android: AndroidNotificationDetails(
              'test_channel',
              'قناة الاختبار',
              channelDescription: 'قناة للإشعارات الاختبارية',
              importance: Importance.max,
              priority: Priority.high,
            ),
            iOS: DarwinNotificationDetails(
              presentAlert: true,
              presentBadge: true,
              presentSound: false,
            ),
          ),
          payload: json.encode({
            'type': 'wird_reminder_test',
            'wird_id': wirdId.toString(),
            'wird_name': wirdName,
            'is_test': 'true',
          }),
        );
        debugPrint('تم إرسال إشعار اختباري بسيط للورد: $wirdName');
        return true;
      } catch (fallbackError) {
        debugPrint('فشل إرسال الإشعار بالطريقة البسيطة أيضاً: $fallbackError');
        return false;
      }
    }
  }

  /// جدولة إشعار تذكير بالعودة
  Future<bool> scheduleReturnReminderNotification() async {
    try {
      // الحصول على نشاط المستخدم
      final UserActivity activity = await _getUserActivity();

      // التحقق مما إذا كان المستخدم غير نشط
      if (activity.daysSinceLastUsed < 3) {
        debugPrint('المستخدم نشط، لا داعي لإشعارات العودة');
        return false;
      }

      // تحديد نوع الغياب
      String messageType;
      if (activity.daysSinceLastUsed < 7) {
        messageType = 'recent_absence';
      } else if (activity.daysSinceLastUsed < 14) {
        messageType = 'short_absence';
      } else if (activity.daysSinceLastUsed < 30) {
        messageType = 'medium_absence';
      } else {
        messageType = 'long_absence';
      }

      // اختيار رسالة مناسبة
      final String message = _getReturnMessage(messageType);

      // زيادة عداد الإشعارات باستخدام copyWith لأن الحقول final
      final updatedActivity =
          activity.copyWith(notificationCount: activity.notificationCount + 1);

      // إنشاء معرف فريد للإشعار باستخدام العداد *قبل* الزيادة (لأن الزيادة قد تفشل)
      // تم تغيير النطاق لتجنب التعارض مع النظام القديم
      final int notificationId = 14000 + activity.notificationCount;

      // تحديد وقت الإشعار (بعد ساعة من الوقت الحالي)
      final DateTime now = DateTime.now();
      final DateTime scheduledTime = now.add(const Duration(hours: 1));

      // بيانات الإشعار
      final Map<String, dynamic> payload = {
        'type': 'tasbih_return',
        'message_type': messageType,
        'days_inactive': activity.daysSinceLastUsed.toString(),
      };

      // جدولة الإشعار
      final bool success = await _scheduleReturnReminderNotification(
        notificationId,
        'وهج السالك', // العنوان الأصلي
        message,
        scheduledTime,
        payload,
      );

      if (success) {
        // تسجيل معلومات الإشعار
        final NotificationInfo notificationInfo = NotificationInfo(
          id: notificationId,
          title: 'وهج السالك', // العنوان الأصلي
          body: message,
          channelKey: _returnReminderChannelId,
          displayTime: scheduledTime,
          payload: payload,
        );

        await _logNotification(notificationInfo);

        // حفظ نشاط المستخدم المحدث
        final SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.setString(_userActivityKey,
            json.encode(updatedActivity.toJson())); // حفظ النسخة المحدثة

        debugPrint(
            'تمت جدولة إشعار تذكير بالعودة في: ${scheduledTime.toString()}');
      } else {
        debugPrint('فشلت جدولة إشعار تذكير بالعودة');
      }

      return success;
    } catch (e) {
      debugPrint('خطأ في جدولة إشعار تذكير بالعودة: $e');
      return false;
    }
  }

  /// الحصول على رسالة العودة المناسبة
  String _getReturnMessage(String messageType) {
    final Random random = Random();

    // قائمة رسائل العودة الفاخرة حسب نوع الغياب
    final Map<String, List<String>> messagesByType = {
      'recent_absence': [
        'اشتقنا لك! 🌟 عد إلى المسبحة لتجديد الذكر والدعاء',
        'لحظات من السكينة تنتظرك في المسبحة 🕌 عد إلينا',
        'مرت أيام قليلة منذ آخر ذكر، هل تعود للمسبحة الآن؟ 📿',
        '"من ذكرني في نفسه ذكرته في نفسي" ✨ عد للذكر والدعاء',
      ],
      'short_absence': [
        'أسبوع دون ذكر! 🌙 عد إلى المسبحة لتجديد صلتك بالله',
        'قال تعالى: "ألا بذكر الله تطمئن القلوب" 💫 عد للذكر والدعاء',
        'أيام من الغياب عن الذكر، هل تعود للمسبحة الآن؟ 🤲',
        'اشتاقت المسبحة إليك! 🌟 عد لتنعم بلحظات من السكينة والروحانية',
      ],
      'medium_absence': [
        'مضى وقت طويل منذ آخر ذكر! 🕌 عد إلى المسبحة لتجديد روحك',
        'قال ﷺ: "مثل الذي يذكر ربه والذي لا يذكر ربه مثل الحي والميت" 📿 عد للذكر',
        'أسبوعان دون ذكر، ألا تشتاق للحظات الروحانية؟ ✨ عد للمسبحة',
        'الذكر حياة للقلوب 💫 عد إلى المسبحة لتحيي قلبك بالذكر',
      ],
      'long_absence': [
        'مضى شهر دون ذكر! 🌙 عد إلى المسبحة لتجديد صلتك بالله',
        'قال تعالى: "فاذكروني أذكركم" 🤲 عد للذكر والدعاء',
        'اشتقنا إليك كثيراً! 🌟 عد إلى المسبحة لتنعم بلحظات من السكينة',
        'الذكر جنة القلوب ونور الأرواح ✨ عد إلى المسبحة لتنعم بهذا النور',
      ],
    };

    // الحصول على قائمة الرسائل المناسبة لنوع الغياب
    final List<String> messages =
        messagesByType[messageType] ?? messagesByType['recent_absence']!;

    // اختيار رسالة عشوائية
    return messages[random.nextInt(messages.length)];
  }

  /// تحديث إعدادات الإشعارات
  Future<void> updateNotificationSettings({
    bool? morningEnabled,
    TimeOfDay? morningTime,
    bool? eveningEnabled,
    TimeOfDay? eveningTime,
  }) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();

      // تحديث إعدادات إشعارات الصباح
      if (morningEnabled != null) {
        await prefs.setBool('local_morning_azkar_enabled', morningEnabled);
      }

      if (morningTime != null) {
        final int minutes = morningTime.hour * 60 + morningTime.minute;
        await prefs.setInt('local_morning_azkar_time', minutes);
      }

      // تحديث إعدادات إشعارات المساء
      if (eveningEnabled != null) {
        await prefs.setBool('local_evening_azkar_enabled', eveningEnabled);
      }

      if (eveningTime != null) {
        final int minutes = eveningTime.hour * 60 + eveningTime.minute;
        await prefs.setInt('local_evening_azkar_time', minutes);
      }

      // تحديث جدولة الإشعارات
      final bool morningEnabledValue =
          morningEnabled ?? await isMorningAzkarEnabled();
      final bool eveningEnabledValue =
          eveningEnabled ?? await isEveningAzkarEnabled();

      if (morningEnabledValue) {
        final TimeOfDay morningTimeValue =
            morningTime ?? await getMorningAzkarTime();
        await scheduleMorningAzkarNotification(morningTimeValue);
      } else {
        await cancelMorningAzkarNotification();
      }

      if (eveningEnabledValue) {
        final TimeOfDay eveningTimeValue =
            eveningTime ?? await getEveningAzkarTime();
        await scheduleEveningAzkarNotification(eveningTimeValue);
      } else {
        await cancelEveningAzkarNotification();
      }
    } catch (e) {
      debugPrint('خطأ في تحديث إعدادات الإشعارات: $e');
    }
  }

  /// التحقق من تفعيل إشعارات أذكار الصباح
  Future<bool> isMorningAzkarEnabled() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getBool('local_morning_azkar_enabled') ?? false;
    } catch (e) {
      debugPrint('خطأ في التحقق من تفعيل إشعارات أذكار الصباح: $e');
      return false;
    }
  }

  /// الحصول على وقت إشعارات أذكار الصباح
  Future<TimeOfDay> getMorningAzkarTime() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final int minutes = prefs.getInt('local_morning_azkar_time') ??
          (7 * 60); // الافتراضي: 7:00 صباحًا

      return TimeOfDay(
        hour: minutes ~/ 60,
        minute: minutes % 60,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على وقت إشعارات أذكار الصباح: $e');
      return const TimeOfDay(hour: 7, minute: 0); // الافتراضي: 7:00 صباحًا
    }
  }

  /// التحقق من تفعيل إشعارات أذكار المساء
  Future<bool> isEveningAzkarEnabled() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getBool('local_evening_azkar_enabled') ?? false;
    } catch (e) {
      debugPrint('خطأ في التحقق من تفعيل إشعارات أذكار المساء: $e');
      return false;
    }
  }

  /// الحصول على وقت إشعارات أذكار المساء
  Future<TimeOfDay> getEveningAzkarTime() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final int minutes = prefs.getInt('local_evening_azkar_time') ??
          (19 * 60); // الافتراضي: 7:00 مساءً

      return TimeOfDay(
        hour: minutes ~/ 60,
        minute: minutes % 60,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على وقت إشعارات أذكار المساء: $e');
      return const TimeOfDay(hour: 19, minute: 0); // الافتراضي: 7:00 مساءً
    }
  }

  /// تهيئة الإشعارات عند بدء التطبيق
  Future<void> initializeNotifications(
      {bool showTestNotification = false}) async {
    await init(showTestNotification: showTestNotification);

    // تحديث نشاط المستخدم
    await _updateUserActivity();

    try {
      // تم تعليق التحقق من أذونات الإشعارات عند تثبيت التطبيق
      /*
      // التحقق من أذونات الإشعارات أولاً
      final bool hasPermission = await checkNotificationPermissions();
      if (!hasPermission) {
        debugPrint('لا توجد أذونات للإشعارات، لن يتم جدولة الإشعارات');
        return;
      }
      */

      // تم تعطيل جدولة إشعارات أذكار الصباح والمساء مؤقتاً
      debugPrint('تم تعطيل جدولة إشعارات أذكار الصباح والمساء مؤقتاً');

      // إلغاء الإشعارات القديمة
      await cancelMorningAzkarNotification();
      await cancelEveningAzkarNotification();

      /* تم تعليق الكود التالي مؤقتاً
      // تفعيل الإشعارات المجدولة
      final bool morningEnabled = await isMorningAzkarEnabled();
      final bool eveningEnabled = await isEveningAzkarEnabled();

      // إلغاء الإشعارات القديمة أولاً
      if (morningEnabled) {
        await cancelMorningAzkarNotification();
      }

      if (eveningEnabled) {
        await cancelEveningAzkarNotification();
      }

      // جدولة الإشعارات الجديدة
      if (morningEnabled) {
        debugPrint('جدولة إشعارات أذكار الصباح...');
        final TimeOfDay morningTime = await getMorningAzkarTime();
        final success = await scheduleMorningAzkarNotification(morningTime);

        if (!success) {
          debugPrint(
              'فشلت جدولة إشعار أذكار الصباح، سيتم المحاولة مرة أخرى لاحقاً');
          // جدولة محاولة أخرى بعد فترة قصيرة
          Future.delayed(const Duration(minutes: 2), () async {
            debugPrint('محاولة ثانية لجدولة إشعار أذكار الصباح');
            await scheduleMorningAzkarNotification(morningTime);
          });

          // محاولة ثالثة بعد فترة أطول
          Future.delayed(const Duration(minutes: 5), () async {
            debugPrint('محاولة ثالثة لجدولة إشعار أذكار الصباح');
            await scheduleMorningAzkarNotification(morningTime);
          });
        }
      }

      if (eveningEnabled) {
        debugPrint('جدولة إشعارات أذكار المساء...');
        final TimeOfDay eveningTime = await getEveningAzkarTime();
        final success = await scheduleEveningAzkarNotification(eveningTime);

        if (!success) {
          debugPrint(
              'فشلت جدولة إشعار أذكار المساء، سيتم المحاولة مرة أخرى لاحقاً');
          // جدولة محاولة أخرى بعد فترة قصيرة
          Future.delayed(const Duration(minutes: 2), () async {
            debugPrint('محاولة ثانية لجدولة إشعار أذكار المساء');
            await scheduleEveningAzkarNotification(eveningTime);
          });

          // محاولة ثالثة بعد فترة أطول
          Future.delayed(const Duration(minutes: 5), () async {
            debugPrint('محاولة ثالثة لجدولة إشعار أذكار المساء');
            await scheduleEveningAzkarNotification(eveningTime);
          });
        }
      }

      // جدولة تحقق إضافي بعد فترة للتأكد من أن الإشعارات تعمل
      Future.delayed(const Duration(minutes: 5), () {
        _verifyScheduledNotifications();
      });

      // جدولة تحقق إضافي بعد فترة أطول
      Future.delayed(const Duration(minutes: 30), () {
        _verifyScheduledNotifications();
      });
      */
    } catch (e) {
      debugPrint('خطأ في تهيئة الإشعارات: $e');
    }

    // التحقق من نشاط المستخدم وجدولة إشعارات العودة إذا لزم الأمر
    await scheduleReturnReminderNotification();
  }

  /// تهيئة خدمة الإشعارات
  /// يجب استدعاء هذه الدالة عند بدء التطبيق
  Future<void> init({bool showTestNotification = false}) async {
    if (_initialized) {
      debugPrint('خدمة الإشعارات مهيأة بالفعل');
      return;
    }

    try {
      // تهيئة منطقة التوقيت - تحسين: تهيئة مرة واحدة فقط
      if (!_timeZoneInitialized) {
        tz.initializeTimeZones();

        // تعيين منطقة التوقيت المحلية بدلاً من UTC
        try {
          // محاولة الحصول على منطقة التوقيت المحلية من النظام
          tz.setLocalLocation(
              tz.getLocation('Asia/Riyadh')); // منطقة توقيت الرياض كافتراضي
          debugPrint('تم تعيين منطقة التوقيت إلى: Asia/Riyadh');
        } catch (e) {
          debugPrint('خطأ في تعيين منطقة التوقيت المحلية: $e');
          // استخدام منطقة توقيت افتراضية في حالة الخطأ
          try {
            tz.setLocalLocation(
                tz.getLocation('Asia/Riyadh')); // منطقة توقيت الرياض كافتراضي
          } catch (e2) {
            debugPrint('خطأ في تعيين منطقة التوقيت الافتراضية: $e2');
          }
        }

        _timeZoneInitialized = true;
        final String timeZoneName = tz.local.name;
        debugPrint('تم تهيئة منطقة التوقيت المحلية: $timeZoneName');
      }

      // إعدادات تهيئة الإشعارات لنظام Android
      const AndroidInitializationSettings androidInitializationSettings =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // إعدادات تهيئة الإشعارات لنظام iOS
      const DarwinInitializationSettings darwinInitializationSettings =
          DarwinInitializationSettings(
        requestAlertPermission: false,
        requestBadgePermission: false,
        requestSoundPermission: false,
      );

      // إعدادات تهيئة الإشعارات لجميع الأنظمة
      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: androidInitializationSettings,
        iOS: darwinInitializationSettings,
      );

      // تهيئة مكتبة الإشعارات
      await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onDidReceiveNotificationResponse,
        // إضافة رد الاتصال القديم هنا إذا كان مدعومًا (قد يكون غير ضروري للإصدارات الحديثة)
        // onDidReceiveBackgroundNotificationResponse: ..., // (غير مستخدم في الكود الأصلي)
        // onDidReceiveLocalNotification: _onDidReceiveLocalNotification, // <= هذا خاص بـ iOS القديم، قد يتم تجاهله
      );

      // إنشاء قنوات الإشعارات
      await _createNotificationChannels();

      // تم تعليق طلب أذونات الإشعارات عند تثبيت التطبيق
      // await requestNotificationPermissions();

      // بدء مؤقت التحقق من الإشعارات
      _startVerificationTimer();

      _initialized = true;
      debugPrint('تم تهيئة خدمة الإشعارات بنجاح');

      // إرسال إشعار اختباري إذا طلب ذلك
      if (showTestNotification) {
        await _showTestNotification();
      }
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة الإشعارات: $e');
      _initialized = false;
    }
  }

  /// إنشاء قنوات الإشعارات
  Future<void> _createNotificationChannels() async {
    // لا حاجة لإنشاء قنوات في نظام iOS لأنه يتعامل معها تلقائيًا
    if (!Platform.isAndroid) return;

    // قناة أذكار الصباح
    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(
          AndroidNotificationChannel(
            _morningAzkarChannelId,
            _morningAzkarChannelName,
            description: _morningAzkarChannelDesc,
            importance: Importance.high,
            // تعطيل الصوت بناءً على طلب المستخدم
            playSound: false,
            enableVibration: true,
            vibrationPattern:
                Int64List.fromList([0, 100, 200, 300, 400, 300, 200, 100]),
            enableLights: true,
            ledColor: TasbihColors.primary,
          ),
        );

    // قناة أذكار المساء
    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(
          AndroidNotificationChannel(
            _eveningAzkarChannelId,
            _eveningAzkarChannelName,
            description: _eveningAzkarChannelDesc,
            importance: Importance.high,
            // تعطيل الصوت بناءً على طلب المستخدم
            playSound: false,
            enableVibration: true,
            vibrationPattern:
                Int64List.fromList([0, 100, 200, 300, 400, 300, 200, 100]),
            enableLights: true,
            ledColor: TasbihColors.secondary,
          ),
        );

    // قناة تذكيرات الأوراد
    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(
          AndroidNotificationChannel(
            _wirdReminderChannelId,
            _wirdReminderChannelName,
            description: _wirdReminderChannelDesc,
            importance: Importance.high,
            // تعطيل الصوت بناءً على طلب المستخدم
            playSound: false,
            enableVibration: true,
            vibrationPattern:
                Int64List.fromList([0, 100, 200, 300, 400, 300, 200, 100]),
            enableLights: true,
            ledColor: TasbihColors.tertiary,
          ),
        );

    // قناة تذكيرات العودة
    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(
          AndroidNotificationChannel(
            _returnReminderChannelId,
            _returnReminderChannelName,
            description: _returnReminderChannelDesc,
            importance: Importance.high,
            // تعطيل الصوت بناءً على طلب المستخدم
            playSound: false,
            enableVibration: true,
            vibrationPattern: Int64List.fromList(
                [0, 100, 200, 300, 400, 500, 400, 300, 200, 100]),
            enableLights: true,
            ledColor: Colors.amber,
          ),
        );
  }

  /// طلب أذونات الإشعارات
  Future<bool> requestNotificationPermissions() async {
    try {
      bool permissionGranted = false;

      // طلب أذونات الإشعارات لنظام Android
      if (Platform.isAndroid) {
        final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
            _flutterLocalNotificationsPlugin
                .resolvePlatformSpecificImplementation<
                    AndroidFlutterLocalNotificationsPlugin>();

        try {
          // محاولة استخدام الدالة الجديدة أولاً
          final bool? granted =
              await androidImplementation?.requestNotificationsPermission();
          permissionGranted = granted ?? false;
          debugPrint(
              'تم منح أذونات الإشعارات لنظام Android: $permissionGranted');
        } catch (e) {
          debugPrint('خطأ في طلب أذونات الإشعارات باستخدام الدالة الجديدة: $e');

          // محاولة استخدام الدالة القديمة كخطة بديلة
          try {
            // استخدام الدالة القديمة إذا كانت متوفرة
            final bool? granted =
                await androidImplementation?.areNotificationsEnabled();
            permissionGranted = granted ?? false;
            debugPrint(
                'تم التحقق من أذونات الإشعارات باستخدام الدالة القديمة: $permissionGranted');
          } catch (fallbackError) {
            debugPrint(
                'خطأ في التحقق من أذونات الإشعارات باستخدام الدالة القديمة: $fallbackError');
            permissionGranted = false;
          }
        }
      }

      // طلب أذونات الإشعارات لنظام iOS
      else if (Platform.isIOS) {
        try {
          final bool result = await _flutterLocalNotificationsPlugin
                  .resolvePlatformSpecificImplementation<
                      IOSFlutterLocalNotificationsPlugin>()
                  ?.requestPermissions(
                    alert: true,
                    badge: true,
                    sound: false, // تعطيل الصوت بناءً على طلب المستخدم
                    critical: true,
                  ) ??
              false;
          permissionGranted = result;
          debugPrint('تم منح أذونات الإشعارات لنظام iOS: $result');
        } catch (e) {
          debugPrint('خطأ في طلب أذونات الإشعارات لنظام iOS: $e');
          permissionGranted = false;
        }
      }

      // حفظ حالة الأذونات في الإعدادات المشتركة
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setBool('notification_permission_requested', true);
      await prefs.setBool('notification_permission_granted', permissionGranted);

      return permissionGranted;
    } catch (e) {
      debugPrint('خطأ عام في طلب أذونات الإشعارات: $e');

      // حفظ حالة الطلب على الأقل
      try {
        final SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.setBool('notification_permission_requested', true);
      } catch (_) {}

      return false;
    }
  }

  /// التحقق من أذونات الإشعارات
  Future<bool> checkNotificationPermissions() async {
    try {
      if (Platform.isAndroid) {
        final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
            _flutterLocalNotificationsPlugin
                .resolvePlatformSpecificImplementation<
                    AndroidFlutterLocalNotificationsPlugin>();

        // التحقق من إصدار Android
        final bool? granted =
            await androidImplementation?.areNotificationsEnabled();

        // إذا كانت النتيجة null، نتحقق من الإعدادات المشتركة
        if (granted == null) {
          final SharedPreferences prefs = await SharedPreferences.getInstance();
          return prefs.getBool('notification_permission_requested') ?? false;
        }

        return granted;
      }

      // لا توجد طريقة مباشرة للتحقق من أذونات iOS، لذا نتحقق من الإعدادات المشتركة
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getBool('notification_permission_requested') ?? false;
    } catch (e) {
      debugPrint('خطأ في التحقق من أذونات الإشعارات: $e');
      // في حالة الخطأ، نتحقق من الإعدادات المشتركة
      try {
        final SharedPreferences prefs = await SharedPreferences.getInstance();
        return prefs.getBool('notification_permission_requested') ?? false;
      } catch (_) {
        return false;
      }
    }
  }

  // تم إزالة دالة _onDidReceiveLocalNotification غير المستخدمة

  /// معالجة النقر على الإشعارات
  void _onDidReceiveNotificationResponse(NotificationResponse response) {
    debugPrint('تم النقر على إشعار: ${response.id}, ${response.payload}');
    if (response.payload != null) {
      _handleNotificationPayload(response.payload!);
    }
  }

  /// معالجة بيانات الإشعار
  void _handleNotificationPayload(String payload) {
    try {
      final Map<String, dynamic> data = json.decode(payload);
      final String type = data['type'] ?? '';

      switch (type) {
        case 'morning_azkar':
          _navigateToScreen(AppRoutes.azkarRoute, {'category': 'morning'});
          break;
        case 'evening_azkar':
          _navigateToScreen(AppRoutes.azkarRoute, {'category': 'evening'});
          break;
        case 'wird_reminder':
          // الانتقال إلى قائمة الأوراد أولاً
          _navigateToScreen(
              AppRoutes.wirdListRoute, {'wird_id': data['wird_id']});
          // يمكن تعديل هذا لينتقل مباشرة إلى الورد إذا كان ID متوفراً
          break;
        case 'tasbih_return':
          _navigateToScreen(AppRoutes.tasbihRoute);
          break;
        default:
          _navigateToScreen(AppRoutes.homeRoute);
      }
    } catch (e) {
      debugPrint('خطأ في معالجة بيانات الإشعار: $e');
      _navigateToScreen(AppRoutes.homeRoute);
    }
  }

  /// الانتقال إلى شاشة معينة
  void _navigateToScreen(String route, [Map<String, dynamic>? arguments]) {
    // التأكد من أن المفتاح ليس null قبل الاستخدام
    MyApp.navigatorKey.currentState?.pushNamed(route, arguments: arguments);
  }

  /// إرسال إشعار اختباري
  Future<void> _showTestNotification() async {
    const AndroidNotificationDetails androidDetails =
        AndroidNotificationDetails(
      'test_channel', // استخدام معرف قناة مختلف للاختبار
      'قناة الاختبار',
      channelDescription: 'قناة للإشعارات الاختبارية',
      importance: Importance.max,
      priority: Priority.high,
      ticker: 'ticker',
      // يمكنك إضافة صوت واهتزاز هنا إذا أردت
    );

    const NotificationDetails notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      ),
    );

    await _flutterLocalNotificationsPlugin.show(
      0, // استخدام id = 0 للإشعار الاختباري
      'اختبار الإشعارات',
      'هذا إشعار اختباري للتأكد من عمل نظام الإشعارات بشكل صحيح.',
      notificationDetails,
      payload: json.encode({'type': 'test'}), // بيانات اختبارية
    );

    debugPrint('تم إرسال إشعار اختباري');
  }

  /// بدء مؤقت التحقق من الإشعارات
  void _startVerificationTimer() {
    // إلغاء المؤقت الحالي إذا كان موجودًا
    _verificationTimer?.cancel();

    // إنشاء مؤقت جديد للتحقق من الإشعارات كل فترة محددة
    _verificationTimer = Timer.periodic(
      const Duration(minutes: 15), // تقليل المدة لزيادة فرص إعادة المحاولة
      (_) => _verifyScheduledNotifications(),
    );

    // التحقق من الإشعارات فور بدء المؤقت
    _verifyScheduledNotifications();

    // جدولة تحقق إضافي بعد دقيقة واحدة للتأكد من أن الإشعارات تعمل بشكل صحيح بعد بدء التطبيق
    Future.delayed(const Duration(minutes: 1), () {
      if (_verificationTimer != null &&
          !(_verificationTimer?.isActive ?? false)) {
        // إذا تم إلغاء المؤقت لأي سبب، أعد تشغيله
        _startVerificationTimer();
      } else {
        // إذا كان المؤقت نشطًا، قم بتشغيل التحقق فقط
        _verifyScheduledNotifications();
      }
    });
  }

  /// التحقق من الإشعارات المجدولة - تم تحسين نظام التشخيص
  Future<void> _verifyScheduledNotifications() async {
    try {
      debugPrint('🔍 جاري التحقق من الإشعارات المجدولة...');

      // التحقق من صحة التاريخ والوقت
      final DateTime validNow = _getValidDateTime();
      debugPrint('📅 التاريخ والوقت الحالي: ${validNow.toString()}');
      debugPrint('📅 السنة الحالية: ${validNow.year}');

      // التحقق من منطقة التوقيت
      debugPrint('🌐 منطقة التوقيت المحلية: ${tz.local.name}');

      // تم تعليق التحقق من أذونات الإشعارات
      /*
      // التحقق من أذونات الإشعارات أولاً
      final bool hasPermission = await checkNotificationPermissions();
      if (!hasPermission) {
        debugPrint(
            '⚠️ لا توجد أذونات للإشعارات، لن يتم التحقق من الإشعارات المجدولة');
        return;
      }

      debugPrint('✅ تم منح أذونات الإشعارات');
      */

      // الحصول على الإشعارات المجدولة
      final List<PendingNotificationRequest> pendingNotifications =
          await _flutterLocalNotificationsPlugin.pendingNotificationRequests();

      debugPrint('📊 عدد الإشعارات المجدولة: ${pendingNotifications.length}');

      // طباعة تفاصيل الإشعارات المجدولة
      if (pendingNotifications.isNotEmpty) {
        debugPrint('📋 قائمة الإشعارات المجدولة:');
        for (final notification in pendingNotifications) {
          debugPrint(
              '  - معرف: ${notification.id}, عنوان: ${notification.title}');
        }
      } else {
        debugPrint('⚠️ لا توجد إشعارات مجدولة حالياً');
      }

      // الحصول على الإشعارات الفاشلة
      final List<NotificationInfo> failedNotifications =
          await _getFailedNotifications();

      if (failedNotifications.isNotEmpty) {
        debugPrint('⚠️ عدد الإشعارات الفاشلة: ${failedNotifications.length}');

        // طباعة تفاصيل الإشعارات الفاشلة
        debugPrint('📋 قائمة الإشعارات الفاشلة:');
        for (final notification in failedNotifications) {
          debugPrint(
              '  - معرف: ${notification.id}, عنوان: ${notification.title}, وقت العرض: ${notification.displayTime}');
        }

        // إعادة جدولة الإشعارات الفاشلة
        // استخدام نسخة من القائمة لتجنب التعديل أثناء التكرار
        for (final notification
            in List<NotificationInfo>.from(failedNotifications)) {
          final bool success = await _retryFailedNotification(notification);
          debugPrint(
              '🔄 إعادة محاولة إشعار ${notification.id}: ${success ? "✅ نجاح" : "❌ فشل"}');
        }
      }

      // التحقق من إشعارات أذكار الصباح والمساء
      await _verifyAzkarNotifications(pendingNotifications);

      // التحقق من تفعيل الإشعارات
      final bool morningEnabled = await isMorningAzkarEnabled();
      final bool eveningEnabled = await isEveningAzkarEnabled();

      // طباعة معلومات التشخيص
      debugPrint(
          'حالة إشعارات أذكار الصباح: ${morningEnabled ? "مفعلة" : "معطلة"}');
      debugPrint(
          'حالة إشعارات أذكار المساء: ${eveningEnabled ? "مفعلة" : "معطلة"}');

      // طباعة معلومات الإشعارات المجدولة
      if (pendingNotifications.isNotEmpty) {
        debugPrint('قائمة الإشعارات المجدولة:');
        for (final notification in pendingNotifications) {
          debugPrint(
              '- معرف: ${notification.id}, عنوان: ${notification.title}');
        }
      }
    } catch (e, stackTrace) {
      debugPrint('خطأ في التحقق من الإشعارات المجدولة: $e');
      debugPrint('تفاصيل الخطأ: $stackTrace');

      // محاولة إعادة تشغيل المؤقت في حالة حدوث خطأ
      Future.delayed(const Duration(minutes: 5), () {
        if (_verificationTimer == null ||
            !(_verificationTimer?.isActive ?? false)) {
          debugPrint('إعادة تشغيل مؤقت التحقق بعد الخطأ');
          _startVerificationTimer();
        }
      });
    }
  }

  /// التحقق من إشعارات أذكار الصباح والمساء والورد وإعادة جدولتها إذا لزم الأمر - تم تحسين معالجة التوقيت
  Future<void> _verifyAzkarNotifications(
      List<PendingNotificationRequest> pendingNotifications) async {
    try {
      // تم تعطيل التحقق من إشعارات أذكار الصباح والمساء مؤقتاً
      debugPrint('تم تعطيل التحقق من إشعارات أذكار الصباح والمساء مؤقتاً');

      // إلغاء أي إشعارات قديمة
      await cancelMorningAzkarNotification();
      await cancelEveningAzkarNotification();

      /* تم تعليق الكود التالي مؤقتاً
      // التحقق من تفعيل إشعارات أذكار الصباح
      final bool morningEnabled = await isMorningAzkarEnabled();
      if (morningEnabled) {
        // التحقق من وجود إشعار أذكار الصباح
        final bool hasMorningNotification = pendingNotifications
            .any((notification) => notification.id == 10001);

        debugPrint(
            'التحقق من وجود إشعار أذكار الصباح: ${hasMorningNotification ? "موجود" : "غير موجود"}');

        if (!hasMorningNotification) {
          debugPrint('إشعار أذكار الصباح غير موجود، إعادة جدولة...');
          final TimeOfDay morningTime = await getMorningAzkarTime();

          // طباعة معلومات التوقيت للتشخيص
          debugPrint(
              'وقت إشعار أذكار الصباح: ${morningTime.hour}:${morningTime.minute}');

          // محاولة إعادة جدولة الإشعار
          final success = await scheduleMorningAzkarNotification(morningTime);
          debugPrint(
              'نتيجة إعادة جدولة إشعار أذكار الصباح: ${success ? "نجاح" : "فشل"}');
        }
      }

      // التحقق من تفعيل إشعارات أذكار المساء
      final bool eveningEnabled = await isEveningAzkarEnabled();
      if (eveningEnabled) {
        // التحقق من وجود إشعار أذكار المساء
        final bool hasEveningNotification = pendingNotifications
            .any((notification) => notification.id == 10002);

        debugPrint(
            'التحقق من وجود إشعار أذكار المساء: ${hasEveningNotification ? "موجود" : "غير موجود"}');

        if (!hasEveningNotification) {
          debugPrint('إشعار أذكار المساء غير موجود، إعادة جدولة...');
          final TimeOfDay eveningTime = await getEveningAzkarTime();

          // طباعة معلومات التوقيت للتشخيص
          debugPrint(
              'وقت إشعار أذكار المساء: ${eveningTime.hour}:${eveningTime.minute}');

          // محاولة إعادة جدولة الإشعار
          final success = await scheduleEveningAzkarNotification(eveningTime);
          debugPrint(
              'نتيجة إعادة جدولة إشعار أذكار المساء: ${success ? "نجاح" : "فشل"}');
        }
      }
      */

      // التحقق من إشعارات الورد
      await _verifyWirdNotifications(pendingNotifications);
    } catch (e) {
      debugPrint('خطأ في التحقق من إشعارات الأذكار: $e');
    }
  }

  /// التحقق من إشعارات الورد وإعادة جدولتها إذا لزم الأمر
  Future<void> _verifyWirdNotifications(
      List<PendingNotificationRequest> pendingNotifications) async {
    try {
      // الحصول على قائمة الإشعارات الفاشلة
      final List<NotificationInfo> failedNotifications =
          await _getFailedNotifications();

      // البحث عن إشعارات الورد الفاشلة
      final List<NotificationInfo> failedWirdNotifications = failedNotifications
          .where((notification) =>
              notification.channelKey == _wirdReminderChannelId &&
              notification.payload != null &&
              notification.payload!['type'] == 'wird_reminder')
          .toList();

      // إعادة جدولة إشعارات الورد الفاشلة
      for (final notification in failedWirdNotifications) {
        debugPrint('إعادة محاولة جدولة إشعار الورد الفاشل: ${notification.id}');

        // التحقق من أن وقت الإشعار لم يمر بعد
        final DateTime now = DateTime.now();
        DateTime scheduledTime = notification.displayTime;

        // إذا كان الوقت قد مر، نجدوله لليوم التالي بنفس الوقت
        if (scheduledTime.isBefore(now)) {
          scheduledTime = DateTime(
            now.year,
            now.month,
            now.day + 1,
            scheduledTime.hour,
            scheduledTime.minute,
          );
        }

        // محاولة إعادة جدولة الإشعار
        final bool success = await _scheduleWirdReminderNotification(
          notification.id,
          notification.title,
          notification.body,
          scheduledTime,
          notification.payload,
        );

        if (success) {
          debugPrint('تمت إعادة جدولة إشعار الورد بنجاح: ${notification.id}');
          await _removeFailedNotification(notification.id);
        }
      }

      // التحقق من إشعارات الورد المفقودة (التي تم إلغاؤها بطريقة غير متوقعة)
      // هذا يتطلب الوصول إلى قائمة الأوراد المخزنة، وهو خارج نطاق هذه الدالة
      // يمكن تنفيذه في مستوى أعلى (مثل WirdProvider)
    } catch (e) {
      debugPrint('خطأ في التحقق من إشعارات الورد: $e');
    }
  }

  /// الحصول على الإشعارات الفاشلة
  Future<List<NotificationInfo>> _getFailedNotifications() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final String? failedNotificationsJson =
          prefs.getString(_failedNotificationsKey);

      if (failedNotificationsJson == null || failedNotificationsJson.isEmpty) {
        return [];
      }

      final List<dynamic> decodedList = json.decode(failedNotificationsJson);
      // التأكد من أن كل عنصر هو Map قبل محاولة التحويل
      return decodedList
          .whereType<Map<String, dynamic>>() // فلترة العناصر غير الصالحة
          .map((item) => NotificationInfo.fromJson(item))
          .toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على الإشعارات الفاشلة: $e');
      return [];
    }
  }

  // تم حذف الدالة القديمة _saveFailedNotification واستبدالها بـ _saveFailedNotificationSimple

  /// حذف الإشعار الفاشل
  Future<void> _removeFailedNotification(int id) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final List<NotificationInfo> failedNotifications =
          await _getFailedNotifications();

      final int initialLength = failedNotifications.length;
      failedNotifications.removeWhere((n) => n.id == id);

      // التأكد من أنه تم الحذف فعلاً قبل إعادة الحفظ
      if (failedNotifications.length < initialLength) {
        final List<Map<String, dynamic>> notificationsJson =
            failedNotifications.map((n) => n.toJson()).toList();

        await prefs.setString(
            _failedNotificationsKey, json.encode(notificationsJson));
        debugPrint('تم حذف الإشعار الفاشل: $id');
      } else {
        debugPrint('لم يتم العثور على الإشعار الفاشل لحذفه: $id');
      }
    } catch (e) {
      debugPrint('خطأ في حذف الإشعار الفاشل: $e');
    }
  }

  /// إعادة محاولة جدولة الإشعار الفاشل - تم تحسين معالجة التوقيت
  Future<bool> _retryFailedNotification(NotificationInfo notification) async {
    try {
      debugPrint('إعادة محاولة جدولة الإشعار: ${notification.id}');

      // التحقق من أن وقت الإشعار لم يمر بعد
      final DateTime now = DateTime.now();
      debugPrint('التوقيت الحالي: ${now.toString()}');

      // إذا كان الإشعار لأذكار الصباح أو المساء، نتحقق مما إذا كان يجب إعادة جدولته للغد
      final Map<String, dynamic>? payload = notification.payload;
      final String type = payload?['type'] ?? '';
      debugPrint('نوع الإشعار: $type');

      DateTime adjustedDisplayTime = notification.displayTime;
      debugPrint('وقت الإشعار الأصلي: ${adjustedDisplayTime.toString()}');

      // إذا كان الإشعار لأذكار الصباح أو المساء وقد مر وقته، نجدوله لليوم التالي
      if ((type == 'morning_azkar' || type == 'evening_azkar') &&
          adjustedDisplayTime.isBefore(now)) {
        // إعادة جدولة للغد بنفس الوقت
        adjustedDisplayTime = DateTime(
          now.year,
          now.month,
          now.day + 1,
          adjustedDisplayTime.hour,
          adjustedDisplayTime.minute,
          0, // ثواني
        );
        debugPrint(
            'تم تعديل وقت الإشعار ${notification.id} إلى: $adjustedDisplayTime');
      }
      // للإشعارات الأخرى، إذا مر وقتها، نحذفها
      else if (adjustedDisplayTime
          .isBefore(now.subtract(const Duration(minutes: 1)))) {
        await _removeFailedNotification(notification.id);
        debugPrint('تم تخطي الإشعار لأن وقته قد مر: ${notification.id}');
        return false; // فشل لأن الوقت قد مر
      }

      bool success = false;

      // استخدام الدوال المعاد جدولتها
      switch (type) {
        case 'morning_azkar':
          // تم تعطيل إعادة جدولة إشعارات أذكار الصباح مؤقتاً
          debugPrint('تم تعطيل إعادة جدولة إشعارات أذكار الصباح مؤقتاً');
          await _removeFailedNotification(notification.id);
          success = false;
          break;
        case 'evening_azkar':
          // تم تعطيل إعادة جدولة إشعارات أذكار المساء مؤقتاً
          debugPrint('تم تعطيل إعادة جدولة إشعارات أذكار المساء مؤقتاً');
          await _removeFailedNotification(notification.id);
          success = false;
          break;
        case 'wird_reminder':
          success = await _scheduleWirdReminderNotification(
            notification.id,
            notification.title,
            notification.body,
            adjustedDisplayTime,
            payload,
          );
          break;
        case 'tasbih_return':
          success = await _scheduleReturnReminderNotification(
            notification.id,
            notification.title,
            notification.body,
            adjustedDisplayTime,
            payload,
          );
          break;
        default:
          debugPrint(
              'نوع إشعار غير معروف لإعادة الجدولة: $type, ID: ${notification.id}');
          // حذف الإشعار غير المعروف من قائمة الفاشلة
          await _removeFailedNotification(notification.id);
          success = false; // اعتبارها فشل
      }

      if (success) {
        // إذا نجحت إعادة الجدولة، نحذف الإشعار من قائمة الإشعارات الفاشلة
        await _removeFailedNotification(notification.id);
        debugPrint('✅ تمت إعادة جدولة الإشعار بنجاح: ${notification.id}');
        return true;
      } else {
        // إضافة منطق لعدد المحاولات
        // نحاول إعادة جدولة الإشعار مرة أخرى بعد فترة قصيرة
        debugPrint(
            '❌ فشلت إعادة جدولة الإشعار (سيتم المحاولة مرة أخرى لاحقاً): ${notification.id}');

        // إذا كان الإشعار مهمًا (أذكار الصباح أو المساء)، نحاول مرة أخرى بعد فترة قصيرة
        if (type == 'morning_azkar' || type == 'evening_azkar') {
          Future.delayed(const Duration(minutes: 5), () async {
            // التحقق مما إذا كان الإشعار لا يزال في قائمة الفاشلة
            final failedNotifications = await _getFailedNotifications();
            final stillFailed =
                failedNotifications.any((n) => n.id == notification.id);

            if (stillFailed) {
              debugPrint(
                  '🔄 محاولة فورية لإعادة جدولة الإشعار المهم: ${notification.id}');
              await _retryFailedNotification(notification);
            }
          });
        }
        return false;
      }
    } catch (e, stackTrace) {
      debugPrint(
          'خطأ في إعادة محاولة جدولة الإشعار: $e, ID: ${notification.id}');
      debugPrint('تفاصيل الخطأ: $stackTrace');

      // في حالة حدوث خطأ، نحاول مرة أخرى بعد فترة
      Future.delayed(const Duration(minutes: 3), () async {
        try {
          // التحقق مما إذا كان الإشعار لا يزال في قائمة الفاشلة
          final failedNotifications = await _getFailedNotifications();
          final stillFailed =
              failedNotifications.any((n) => n.id == notification.id);

          if (stillFailed) {
            debugPrint(
                'محاولة جديدة بعد الخطأ لإعادة جدولة الإشعار: ${notification.id}');
            await _retryFailedNotification(notification);
          }
        } catch (retryError) {
          debugPrint('خطأ في محاولة إعادة الجدولة بعد الخطأ: $retryError');
        }
      });

      return false; // فشل بسبب الخطأ
    }
  }

  /// تسجيل معلومات الإشعار
  Future<void> _logNotification(NotificationInfo notification) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final String? logJson = prefs.getString(_notificationLogKey);

      List<Map<String, dynamic>> log = [];

      if (logJson != null && logJson.isNotEmpty) {
        try {
          final List<dynamic> decodedLog = json.decode(logJson);
          // التأكد من أن كل عنصر هو Map
          log = List<Map<String, dynamic>>.from(
              decodedLog.whereType<Map<String, dynamic>>());
        } catch (e) {
          debugPrint("Error decoding notification log, resetting log: $e");
          log = []; // إعادة تعيين السجل إذا كان تالفًا
        }
      }

      // إضافة معلومات الإشعار إلى السجل
      log.add({
        ...notification.toJson(),
        'timestamp': DateTime.now().toIso8601String(),
      });

      // الاحتفاظ بآخر 100 إشعار فقط
      if (log.length > 100) {
        log = log.sublist(log.length - 100);
      }

      await prefs.setString(_notificationLogKey, json.encode(log));
    } catch (e) {
      debugPrint('خطأ في تسجيل معلومات الإشعار: $e');
    }
  }

  /// تحديث نشاط المستخدم
  Future<void> _updateUserActivity() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final String? activityJson = prefs.getString(_userActivityKey);

      UserActivity activity;

      if (activityJson != null && activityJson.isNotEmpty) {
        try {
          final Map<String, dynamic> decodedActivity =
              json.decode(activityJson);
          activity = UserActivity.fromJson(decodedActivity);
        } catch (e) {
          debugPrint("Error decoding user activity, creating new activity: $e");
          activity = UserActivity(
            // إنشاء نشاط جديد إذا كان المخزن تالفًا
            lastUsedDate: DateTime.now(),
            daysSinceLastUsed: 0,
            notificationCount: 0,
          );
        }
      } else {
        activity = UserActivity(
          lastUsedDate: DateTime.now(),
          daysSinceLastUsed: 0,
          notificationCount: 0,
        );
      }

      // تحديث نشاط المستخدم باستخدام copyWith
      final updatedActivity = activity.copyWith(
        lastUsedDate: DateTime.now(),
        daysSinceLastUsed: 0,
      );

      // حفظ النسخة المحدثة
      await prefs.setString(
          _userActivityKey, json.encode(updatedActivity.toJson()));

      // تحديث حالة أذونات الإشعارات أيضًا
      final bool hasPermission = await checkNotificationPermissions();
      if (hasPermission) {
        await prefs.setBool('notification_permission_requested', true);
      }
    } catch (e) {
      debugPrint('خطأ في تحديث نشاط المستخدم: $e');
    }
  }

  /// الحصول على نشاط المستخدم
  Future<UserActivity> _getUserActivity() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final String? activityJson = prefs.getString(_userActivityKey);

      if (activityJson != null && activityJson.isNotEmpty) {
        UserActivity activity;
        try {
          final Map<String, dynamic> decodedActivity =
              json.decode(activityJson);
          activity = UserActivity.fromJson(decodedActivity);
        } catch (e) {
          debugPrint(
              "Error decoding stored user activity, creating default: $e");
          // إرجاع نشاط افتراضي في حالة خطأ فك التشفير
          return UserActivity(
            lastUsedDate: DateTime.now().subtract(
                const Duration(days: 100)), // افتراضي قديم لتشغيل إشعار العودة
            daysSinceLastUsed: 100,
            notificationCount: 0,
          );
        }

        // حساب عدد الأيام منذ آخر استخدام وتحديث الكائن
        final DateTime now = DateTime.now();
        final DateTime lastUsed = activity.lastUsedDate;
        final int daysDifference = now.difference(lastUsed).inDays;

        // إنشاء نسخة جديدة مع تحديث عدد الأيام
        return activity.copyWith(daysSinceLastUsed: daysDifference);
      }

      // إذا لم يكن هناك نشاط مسجل، إنشاء نشاط جديد
      debugPrint("No user activity found, creating default.");
      return UserActivity(
        lastUsedDate:
            DateTime.now().subtract(const Duration(days: 100)), // افتراضي قديم
        daysSinceLastUsed: 100,
        notificationCount: 0,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على نشاط المستخدم: $e');

      // إرجاع نشاط افتراضي في حالة حدوث خطأ
      return UserActivity(
        lastUsedDate:
            DateTime.now().subtract(const Duration(days: 100)), // افتراضي قديم
        daysSinceLastUsed: 100,
        notificationCount: 0,
      );
    }
  }

  /// تنظيف موارد الخدمة
  void dispose() {
    _verificationTimer?.cancel();
    _initialized = false;
    debugPrint("LocalNotificationService disposed."); // إضافة للتأكيد
  }

  /// الحصول على تاريخ صالح للاستخدام (معالجة مشكلة التاريخ المستقبلي)
  DateTime _getValidDateTime() {
    try {
      final DateTime now = DateTime.now();

      // التحقق من صحة السنة (إذا كانت أكبر من السنة الحالية + 1، فهناك مشكلة)
      final int currentYear = DateTime.now().year;
      if (now.year > currentYear + 1) {
        debugPrint(
            '⚠️ تم اكتشاف تاريخ مستقبلي: ${now.year}، استخدام السنة الحالية: $currentYear');

        // إنشاء تاريخ جديد باستخدام السنة الحالية
        return DateTime(
          currentYear,
          now.month,
          now.day,
          now.hour,
          now.minute,
          now.second,
          now.millisecond,
          now.microsecond,
        );
      }

      return now;
    } catch (e) {
      debugPrint('⚠️ خطأ في الحصول على تاريخ صالح: $e');

      // في حالة حدوث أي خطأ، استخدم DateTime.now() مباشرة
      return DateTime.now();
    }
  }

  // --- الدوال المساعدة لجدولة الإشعارات (تبقى كما هي مع إصلاح uiLocal...) ---

  /// جدولة إشعار أذكار الصباح (مساعد داخلي) - تم تحسين معالجة التوقيت
  Future<bool> _scheduleMorningAzkarNotification(
    int id,
    String title,
    String body,
    DateTime scheduledTime,
    Map<String, dynamic>? payload,
  ) async {
    try {
      // تأكد من تهيئة منطقة التوقيت
      if (!_timeZoneInitialized) {
        tz.initializeTimeZones();
        _timeZoneInitialized = true;
      }

      // تحويل التوقيت المحلي إلى TZDateTime بشكل صحيح
      // استخدام tz.TZDateTime.from لتجنب مشاكل التحويل
      final tz.TZDateTime tzScheduledTime =
          tz.TZDateTime.from(scheduledTime, tz.local);

      // طباعة معلومات التوقيت للتشخيص
      debugPrint(
          'تحويل التوقيت: ${scheduledTime.toString()} -> ${tzScheduledTime.toString()}');
      debugPrint('منطقة التوقيت المحلية: ${tz.local.name}');

      final AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
        _morningAzkarChannelId,
        _morningAzkarChannelName,
        channelDescription: _morningAzkarChannelDesc,
        importance: Importance.high,
        priority: Priority.high,
        // تعطيل الصوت بناءً على طلب المستخدم
        playSound: false,
        enableVibration: true,
        vibrationPattern:
            Int64List.fromList([0, 100, 200, 300, 400, 300, 200, 100]),
        enableLights: true,
        ledColor: TasbihColors.primary,
        // إضافة وقت تشغيل وإيقاف LED لتجنب الخطأ
        // استخدام قيم آمنة لجميع الأجهزة
        ledOnMs: 1000,
        ledOffMs: 500,
        category: AndroidNotificationCategory.reminder,
        // fullScreenIntent: true, // قد يسبب إزعاجاً، تم التعليق في الكود الأصلي
        visibility: NotificationVisibility.public,
      );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: false, // تعطيل الصوت بناءً على طلب المستخدم
        interruptionLevel: InterruptionLevel.active,
        categoryIdentifier: 'morning_azkar',
      );

      final NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _flutterLocalNotificationsPlugin.zonedSchedule(
        id,
        title,
        body,
        tzScheduledTime,
        notificationDetails,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        matchDateTimeComponents: DateTimeComponents.time, // للتكرار اليومي
        payload: payload != null ? json.encode(payload) : null,
      );

      // طباعة معلومات مفصلة للتشخيص
      debugPrint('تمت جدولة إشعار أذكار الصباح داخلياً: $id');
      debugPrint('التوقيت المحلي: ${scheduledTime.toString()}');
      debugPrint('التوقيت المحول: ${tzScheduledTime.toString()}');
      debugPrint('الساعة: ${tzScheduledTime.hour}:${tzScheduledTime.minute}');

      // جدولة تحقق من الإشعار بعد فترة قصيرة
      Future.delayed(const Duration(seconds: 5), () async {
        final pendingNotifications = await _flutterLocalNotificationsPlugin
            .pendingNotificationRequests();
        final exists =
            pendingNotifications.any((notification) => notification.id == id);
        debugPrint(
            'التحقق من وجود إشعار أذكار الصباح $id: ${exists ? "موجود" : "غير موجود"}');
      });

      return true;
    } catch (e) {
      debugPrint('خطأ في الجدولة الداخلية لإشعار أذكار الصباح: $e');
      // حفظ الإشعار الفاشل للمحاولة لاحقاً
      await _saveFailedNotificationSimple(NotificationInfo(
          id: id,
          title: title,
          body: body,
          channelKey: _morningAzkarChannelId,
          displayTime: scheduledTime,
          payload: payload));
      return false;
    }
  }

  /// جدولة إشعار أذكار المساء (مساعد داخلي) - تم تحسين معالجة التوقيت
  Future<bool> _scheduleEveningAzkarNotification(
    int id,
    String title,
    String body,
    DateTime scheduledTime,
    Map<String, dynamic>? payload,
  ) async {
    try {
      // تأكد من تهيئة منطقة التوقيت
      if (!_timeZoneInitialized) {
        tz.initializeTimeZones();
        _timeZoneInitialized = true;
      }

      // تحويل التوقيت المحلي إلى TZDateTime بشكل صحيح
      // استخدام tz.TZDateTime.from لتجنب مشاكل التحويل
      final tz.TZDateTime tzScheduledTime =
          tz.TZDateTime.from(scheduledTime, tz.local);

      // طباعة معلومات التوقيت للتشخيص
      debugPrint(
          'تحويل التوقيت: ${scheduledTime.toString()} -> ${tzScheduledTime.toString()}');
      debugPrint('منطقة التوقيت المحلية: ${tz.local.name}');

      final AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
        _eveningAzkarChannelId,
        _eveningAzkarChannelName,
        channelDescription: _eveningAzkarChannelDesc,
        importance: Importance.high,
        priority: Priority.high,
        // تعطيل الصوت بناءً على طلب المستخدم
        playSound: false,
        enableVibration: true,
        vibrationPattern:
            Int64List.fromList([0, 100, 200, 300, 400, 300, 200, 100]),
        enableLights: true,
        ledColor: TasbihColors.secondary,
        // إضافة وقت تشغيل وإيقاف LED لتجنب الخطأ
        ledOnMs: 500,
        ledOffMs: 500,
        category: AndroidNotificationCategory.reminder,
        // fullScreenIntent: true,
        visibility: NotificationVisibility.public,
      );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: false, // تعطيل الصوت بناءً على طلب المستخدم
        interruptionLevel: InterruptionLevel.active,
        categoryIdentifier: 'evening_azkar',
      );

      final NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _flutterLocalNotificationsPlugin.zonedSchedule(
        id,
        title,
        body,
        tzScheduledTime,
        notificationDetails,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        matchDateTimeComponents: DateTimeComponents.time, // للتكرار اليومي
        payload: payload != null ? json.encode(payload) : null,
      );

      // طباعة معلومات مفصلة للتشخيص
      debugPrint('تمت جدولة إشعار أذكار المساء داخلياً: $id');
      debugPrint('التوقيت المحلي: ${scheduledTime.toString()}');
      debugPrint('التوقيت المحول: ${tzScheduledTime.toString()}');
      debugPrint('الساعة: ${tzScheduledTime.hour}:${tzScheduledTime.minute}');

      // جدولة تحقق من الإشعار بعد فترة قصيرة
      Future.delayed(const Duration(seconds: 5), () async {
        final pendingNotifications = await _flutterLocalNotificationsPlugin
            .pendingNotificationRequests();
        final exists =
            pendingNotifications.any((notification) => notification.id == id);
        debugPrint(
            'التحقق من وجود إشعار أذكار المساء $id: ${exists ? "موجود" : "غير موجود"}');
      });

      return true;
    } catch (e) {
      debugPrint('خطأ في الجدولة الداخلية لإشعار أذكار المساء: $e');
      await _saveFailedNotificationSimple(NotificationInfo(
          id: id,
          title: title,
          body: body,
          channelKey: _eveningAzkarChannelId,
          displayTime: scheduledTime,
          payload: payload));
      return false;
    }
  }

  /// جدولة إشعار تذكير بالورد (مساعد داخلي) - تم تحسين معالجة التوقيت
  Future<bool> _scheduleWirdReminderNotification(
    int id,
    String title,
    String body,
    DateTime scheduledTime,
    Map<String, dynamic>? payload,
  ) async {
    try {
      // تأكد من تهيئة منطقة التوقيت
      if (!_timeZoneInitialized) {
        tz.initializeTimeZones();
        _timeZoneInitialized = true;
      }

      // تحويل التوقيت المحلي إلى TZDateTime بشكل صحيح
      // استخدام tz.TZDateTime.from لتجنب مشاكل التحويل
      final tz.TZDateTime tzScheduledTime =
          tz.TZDateTime.from(scheduledTime, tz.local);

      // طباعة معلومات التوقيت للتشخيص
      debugPrint(
          'تحويل التوقيت: ${scheduledTime.toString()} -> ${tzScheduledTime.toString()}');
      debugPrint('منطقة التوقيت المحلية: ${tz.local.name}');

      final AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
        _wirdReminderChannelId,
        _wirdReminderChannelName,
        channelDescription: _wirdReminderChannelDesc,
        importance: Importance.high,
        priority: Priority.high,
        // تعطيل الصوت بناءً على طلب المستخدم
        playSound: false,
        enableVibration: true,
        vibrationPattern:
            Int64List.fromList([0, 100, 200, 300, 400, 300, 200, 100]),
        enableLights: true,
        ledColor: TasbihColors.tertiary,
        // إضافة وقت تشغيل وإيقاف LED لتجنب الخطأ
        ledOnMs: 500,
        ledOffMs: 500,
        category: AndroidNotificationCategory.reminder,
        // fullScreenIntent: true,
        visibility: NotificationVisibility.public,
      );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: false, // تعطيل الصوت بناءً على طلب المستخدم
        interruptionLevel: InterruptionLevel.active,
        categoryIdentifier: 'wird_reminder',
      );

      final NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // تحسين: استخدام وضع الضبط الدقيق للإشعارات
      await _flutterLocalNotificationsPlugin.zonedSchedule(
        id,
        title,
        body,
        tzScheduledTime,
        notificationDetails,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        matchDateTimeComponents: DateTimeComponents.time, // للتكرار اليومي
        payload: payload != null ? json.encode(payload) : null,
      );

      // طباعة معلومات مفصلة للتشخيص
      debugPrint('تمت جدولة إشعار تذكير بالورد داخلياً: $id');
      debugPrint('التوقيت المحلي: ${scheduledTime.toString()}');
      debugPrint('التوقيت المحول: ${tzScheduledTime.toString()}');
      debugPrint('الساعة: ${tzScheduledTime.hour}:${tzScheduledTime.minute}');

      // جدولة تحقق من الإشعار بعد فترة قصيرة
      Future.delayed(const Duration(seconds: 5), () async {
        final pendingNotifications = await _flutterLocalNotificationsPlugin
            .pendingNotificationRequests();
        final exists =
            pendingNotifications.any((notification) => notification.id == id);
        debugPrint(
            'التحقق من وجود الإشعار $id: ${exists ? "موجود" : "غير موجود"}');
      });

      return true;
    } catch (e, stackTrace) {
      debugPrint('خطأ في الجدولة الداخلية لإشعار تذكير بالورد: $e');
      debugPrint('تفاصيل الخطأ: $stackTrace');

      // حفظ الإشعار الفاشل للمحاولة لاحقاً
      await _saveFailedNotificationSimple(NotificationInfo(
          id: id,
          title: title,
          body: body,
          channelKey: _wirdReminderChannelId,
          displayTime: scheduledTime,
          payload: payload));

      // محاولة إعادة الجدولة بعد فترة قصيرة
      Future.delayed(const Duration(minutes: 2), () async {
        try {
          debugPrint('محاولة إعادة جدولة إشعار الورد: $id');

          // التحقق مما إذا كان الإشعار لا يزال في قائمة الفاشلة
          final failedNotifications = await _getFailedNotifications();
          final stillFailed = failedNotifications.any((n) => n.id == id);

          if (stillFailed) {
            // محاولة إعادة الجدولة مرة أخرى
            final success = await _scheduleWirdReminderNotification(
                id, title, body, scheduledTime, payload);

            if (success) {
              debugPrint('نجحت إعادة جدولة إشعار الورد: $id');
              await _removeFailedNotification(id);
            }
          }
        } catch (retryError) {
          debugPrint('خطأ في محاولة إعادة جدولة إشعار الورد: $retryError');
        }
      });

      return false;
    }
  }

  /// جدولة إشعار تذكير بالعودة (مساعد داخلي)
  Future<bool> _scheduleReturnReminderNotification(
    int id,
    String title,
    String body,
    DateTime scheduledTime,
    Map<String, dynamic>? payload,
  ) async {
    try {
      final tz.TZDateTime tzScheduledTime =
          tz.TZDateTime.from(scheduledTime, tz.local);

      final AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
        _returnReminderChannelId,
        _returnReminderChannelName,
        channelDescription: _returnReminderChannelDesc,
        importance:
            Importance.defaultImportance, // استخدام أهمية أقل لهذا النوع
        priority: Priority.defaultPriority,
        // تعطيل الصوت بناءً على طلب المستخدم
        playSound: false,
        enableVibration: true, // الاهتزاز قد يكون مزعجاً للبعض هنا
        vibrationPattern: Int64List.fromList([0, 100, 200, 100]), // اهتزاز أخف
        enableLights: true,
        ledColor: Colors.blue, // لون مختلف
        // إضافة وقت تشغيل وإيقاف LED لتجنب الخطأ
        ledOnMs: 500,
        ledOffMs: 500,
        category: AndroidNotificationCategory.reminder,
        // fullScreenIntent: false, // لا حاجة لشاشة كاملة هنا
        visibility: NotificationVisibility.public,
      );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: false, // لا حاجة لتغيير الشارة لهذا النوع
        presentSound: false, // تعطيل الصوت بناءً على طلب المستخدم
        interruptionLevel: InterruptionLevel.passive, // مستوى أقل إزعاجاً
        categoryIdentifier: 'return_reminder',
      );

      final NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _flutterLocalNotificationsPlugin.zonedSchedule(
        id,
        title,
        body,
        tzScheduledTime,
        notificationDetails,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        // لا يوجد تكرار يومي لهذا النوع
        payload: payload != null ? json.encode(payload) : null,
      );

      debugPrint(
          'تمت جدولة إشعار تذكير بالعودة داخلياً: $id في $tzScheduledTime');
      return true;
    } catch (e) {
      debugPrint('خطأ في الجدولة الداخلية لإشعار تذكير بالعودة: $e');
      await _saveFailedNotificationSimple(NotificationInfo(
          id: id,
          title: title,
          body: body,
          channelKey: _returnReminderChannelId,
          displayTime: scheduledTime,
          payload: payload));
      return false;
    }
  }

  /// تأجيل إشعار لوقت لاحق
  Future<bool> snoozeNotification(int originalId, int snoozeMinutes) async {
    try {
      // الحصول على الإشعارات المجدولة
      final List<PendingNotificationRequest> pendingNotifications =
          await _flutterLocalNotificationsPlugin.pendingNotificationRequests();

      // البحث عن الإشعار الأصلي
      PendingNotificationRequest? originalNotification;
      for (var notification in pendingNotifications) {
        if (notification.id == originalId) {
          originalNotification = notification;
          break;
        }
      }

      if (originalNotification == null) {
        debugPrint('لم يتم العثور على الإشعار الأصلي بمعرف: $originalId');
        return false;
      }

      // إلغاء الإشعار الأصلي
      await _flutterLocalNotificationsPlugin.cancel(originalId);

      // إنشاء وقت التأجيل - استخدام DateTime.now() مباشرة
      final DateTime realNow = DateTime.now();
      final DateTime snoozeTime = realNow.add(Duration(minutes: snoozeMinutes));

      // إنشاء معرف جديد للإشعار المؤجل
      final int snoozeId =
          originalId + 50000; // استخدام نطاق مختلف للإشعارات المؤجلة

      // إنشاء تفاصيل الإشعار
      final AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
        _returnReminderChannelId, // استخدام قناة تذكيرات العودة
        _returnReminderChannelName,
        channelDescription: _returnReminderChannelDesc,
        importance: Importance.high,
        priority: Priority.high,
        playSound: false,
        enableVibration: true,
        vibrationPattern:
            Int64List.fromList([0, 100, 200, 300, 400, 300, 200, 100]),
        enableLights: true,
        ledColor: TasbihColors.primary,
        ledOnMs: 500,
        ledOffMs: 500,
        category: AndroidNotificationCategory.reminder,
        visibility: NotificationVisibility.public,
      );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: false,
        interruptionLevel: InterruptionLevel.active,
      );

      final NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // جدولة الإشعار المؤجل
      await _flutterLocalNotificationsPlugin.zonedSchedule(
        snoozeId,
        'تذكير مؤجل',
        'تم تأجيل هذا التذكير لمدة $snoozeMinutes دقيقة',
        tz.TZDateTime.from(snoozeTime, tz.local),
        notificationDetails,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        payload: json.encode({
          'type': 'snoozed_notification',
          'original_id': originalId,
          'snooze_minutes': snoozeMinutes,
        }),
      );

      debugPrint(
          'تم تأجيل الإشعار $originalId لمدة $snoozeMinutes دقيقة، معرف جديد: $snoozeId');
      return true;
    } catch (e) {
      debugPrint('خطأ في تأجيل الإشعار: $e');
      return false;
    }
  }
} // <<< --- نهاية تعريف الكلاس المفقود --- >>>
