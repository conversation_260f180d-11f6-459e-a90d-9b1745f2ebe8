//  ملف لقسم الإشعارات

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../services/notification_manager.dart'; // استخدام مدير الإشعارات الجديد
import '../../../screens/tasbih/providers/wird_provider.dart'; // مزود الورد
import '../../../screens/tasbih/providers/tasbih_provider.dart'; // مزود المسبحة
import 'package:provider/provider.dart'; // للوصول إلى المزودين

import '../../../utils/app_colors.dart';
import '../utils/time_picker_utils.dart';
import 'dart:math' as math;
import 'package:flutter_svg/flutter_svg.dart';

class NotificationSection extends StatefulWidget {
  final Function(Widget, {double delay}) buildSlideAnimation;
  final Function(BuildContext, String, IconData) buildSectionTitle;

  const NotificationSection({
    super.key,
    required this.buildSlideAnimation,
    required this.buildSectionTitle,
  });

  @override
  State<NotificationSection> createState() => _NotificationSectionState();
}

class _NotificationSectionState extends State<NotificationSection>
    with SingleTickerProviderStateMixin {
  final NotificationManager _notificationManager = NotificationManager();
  TimeOfDay? _morningTime;
  TimeOfDay? _eveningTime;
  bool _morningEnabled = false;
  bool _eveningEnabled = false;
  int _snoozeMinutes = 10;
  bool _wirdRemindersDisabled = false;
  bool _returnNotificationsDisabled = false;
  late AnimationController _animationController;
  late Animation<double> _animation;

  // مزودات البيانات
  late WirdProvider _wirdProvider;
  late TasbihProvider _tasbihProvider;

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _loadTemporaryDisableSettings();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 20),
    )..repeat(reverse: true);
    _animation = Tween<double>(begin: 0, end: 1).animate(_animationController);

    // تهيئة مزودات البيانات
    _wirdProvider = Provider.of<WirdProvider>(context, listen: false);
    _tasbihProvider = Provider.of<TasbihProvider>(context, listen: false);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم مع تحسينات بصرية
        widget.buildSectionTitle(
            context, 'إشعارات الأذكار', Icons.notifications_outlined),

        // زخرفة إسلامية متطورة
        widget.buildSlideAnimation(
          _buildEnhancedIslamicDecoration(context),
          delay: 0.5,
        ),

        // تم تعطيل بطاقات إشعارات أذكار الصباح والمساء مؤقتاً
        /* تم تعليق الكود التالي مؤقتاً
        // بطاقة إشعارات أذكار الصباح
        widget.buildSlideAnimation(
          _buildEnhancedNotificationCard(
            title: 'أذكار الصباح',
            icon: Icons.wb_sunny_outlined,
            iconColor: Colors.amber,
            time: _morningTime,
            isEnabled: _morningEnabled,
            onToggle: (value) => _toggleNotification(true, value),
            onTap: () => _selectTime(context, true),
            isDarkMode: isDarkMode,
            gradientColors: const [Color(0xFFFFA000), Color(0xFFFFD54F)],
            subtitle: 'تذكير يومي بأذكار الصباح',
          ),
          delay: 0.6,
        ),

        // بطاقة إشعارات أذكار المساء
        widget.buildSlideAnimation(
          _buildEnhancedNotificationCard(
            title: 'أذكار المساء',
            icon: Icons.nightlight_round,
            iconColor: Colors.indigo,
            time: _eveningTime,
            isEnabled: _eveningEnabled,
            onToggle: (value) => _toggleNotification(false, value),
            onTap: () => _selectTime(context, false),
            isDarkMode: isDarkMode,
            gradientColors: const [Color(0xFF3949AB), Color(0xFF5C6BC0)],
            subtitle: 'تذكير يومي بأذكار المساء',
          ),
          delay: 0.7,
        ),
        */

        // إعدادات تأجيل الإشعارات
        widget.buildSlideAnimation(
          _buildEnhancedSnoozeCard(
            snoozeMinutes: _snoozeMinutes,
            onTap: _showSnoozeDurationPicker,
            isDarkMode: isDarkMode,
          ),
          delay: 0.8,
        ),

        // زر اختبار الإشعارات
        widget.buildSlideAnimation(
          _buildTestNotificationsCard(isDarkMode),
          delay: 0.9,
        ),

        // قسم تعطيل الإشعارات مؤقتاً - مخفي عن المستخدم العادي
        // widget.buildSlideAnimation(
        //   _buildTemporaryDisableSection(isDarkMode),
        //   delay: 1.0,
        // ),

        // تم تعطيل زر إعادة ضبط الإشعارات مؤقتاً
        /* تم تعليق الكود التالي مؤقتاً
        // إعادة ضبط الإشعارات
        if (_morningEnabled || _eveningEnabled)
          widget.buildSlideAnimation(
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
              child: ElevatedButton.icon(
                onPressed: _resetNotifications,
                icon: const Icon(Icons.refresh_rounded, size: 18),
                label: const Text('إعادة ضبط الإشعارات'),
                style: ElevatedButton.styleFrom(
                  foregroundColor: Colors.white,
                  backgroundColor: AppColors.azkarColor,
                  elevation: 2,
                  shadowColor:
                      AppColors.azkarColor.withAlpha(77), // 0.3 * 255 = 77
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding:
                      const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
                ),
              ),
            ),
            delay: 1.1,
          ),
        */
      ],
    );
  }

  // زخرفة إسلامية محسنة
  Widget _buildEnhancedIslamicDecoration(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      width: double.infinity,
      height: 60,
      child: Stack(
        children: [
          // الزخرفة المتحركة اليمنى
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Positioned(
                right: -20,
                top: -10 + (10 * _animation.value),
                child: Opacity(
                  opacity: 0.12,
                  child: Transform.rotate(
                    angle:
                        -0.05 * math.pi + (0.03 * math.pi * _animation.value),
                    child: SvgPicture.asset(
                      'assets/images/p2.svg',
                      width: 80,
                      height: 80,
                      colorFilter: const ColorFilter.mode(
                        AppColors.azkarColor,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),

          // الزخرفة المتحركة اليسرى
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Positioned(
                left: -15,
                bottom: -15 + (8 * _animation.value),
                child: Opacity(
                  opacity: 0.08,
                  child: Transform.rotate(
                    angle: 0.08 * math.pi * (1 - _animation.value * 0.3),
                    child: SvgPicture.asset(
                      'assets/images/p2.svg',
                      width: 60,
                      height: 60,
                      colorFilter: const ColorFilter.mode(
                        AppColors.azkarColor,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),

          // خط مركزي متدرج
          Center(
            child: Container(
              height: 1.2,
              width: screenWidth * 0.75,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    AppColors.azkarColor.withAlpha(128), // 0.5 * 255 = ~128
                    AppColors.azkarColor.withAlpha(179), // 0.7 * 255 = ~179
                    AppColors.azkarColor.withAlpha(128), // 0.5 * 255 = ~128
                    Colors.transparent,
                  ],
                  stops: const [0.0, 0.2, 0.5, 0.8, 1.0],
                ),
              ),
            ),
          ),

          // نقاط ضوئية متناثرة
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return CustomPaint(
                size: Size(screenWidth, 60),
                painter: LightDotsPainter(
                  progress: _animation.value,
                  color: AppColors.azkarColor,
                  isDarkMode: isDarkMode,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  // بطاقة إشعارات محسنة
  Widget _buildEnhancedNotificationCard({
    required String title,
    required IconData icon,
    required Color iconColor,
    required TimeOfDay? time,
    required bool isEnabled,
    required Function(bool) onToggle,
    required VoidCallback onTap,
    required bool isDarkMode,
    required List<Color> gradientColors,
    required String subtitle,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        decoration: BoxDecoration(
          color: isDarkMode
              ? Theme.of(context).cardColor.withAlpha(242) // 0.95 * 255 = 242
              : Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(8), // 0.03 * 255 = 8
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
          border: Border.all(
            color: isEnabled
                ? AppColors.azkarColor.withAlpha(77) // 0.3 * 255 = 77
                : Colors.grey.withAlpha(26), // 0.1 * 255 = 26
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // أيقونة مع خلفية متدرجة
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: isEnabled
                      ? LinearGradient(
                          colors: gradientColors,
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        )
                      : null,
                  color: isEnabled
                      ? null
                      : Colors.grey.withAlpha(51), // 0.2 * 255 = 51
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: isEnabled
                      ? [
                          BoxShadow(
                            color: gradientColors[0]
                                .withAlpha(77), // 0.3 * 255 = 77
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ]
                      : null,
                ),
                child: Icon(
                  icon,
                  color: isEnabled ? Colors.white : Colors.grey,
                  size: 26,
                ),
              ),
              const SizedBox(width: 16),
              // معلومات الإشعار
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: isEnabled
                            ? AppColors.azkarColor
                            : Theme.of(context)
                                .textTheme
                                .bodyLarge
                                ?.color
                                ?.withAlpha(179), // 0.7 * 255 = 179
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).textTheme.bodySmall?.color,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      time != null
                          ? 'الساعة ${time.hour}:${time.minute.toString().padLeft(2, '0')}'
                          : 'اضغط لضبط الوقت',
                      style: TextStyle(
                        fontSize: 14,
                        color: isEnabled
                            ? Theme.of(context).textTheme.bodyMedium?.color
                            : Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.color
                                ?.withAlpha(179), // 0.7 * 255 = 179
                      ),
                    ),
                  ],
                ),
              ),
              // زر التفعيل
              Switch(
                value: isEnabled,
                onChanged: onToggle,
                activeColor: AppColors.azkarColor,
                activeTrackColor:
                    AppColors.azkarColor.withAlpha(77), // 0.3 * 255 = 77
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بطاقة تأجيل محسنة
  Widget _buildEnhancedSnoozeCard({
    required int snoozeMinutes,
    required VoidCallback onTap,
    required bool isDarkMode,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        decoration: BoxDecoration(
          color: isDarkMode
              ? Theme.of(context).cardColor.withAlpha(242) // 0.95 * 255 = 242
              : Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(8), // 0.03 * 255 = 8
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
          border: Border.all(
            color: Colors.grey.withAlpha(26), // 0.1 * 255 = 26
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // أيقونة
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.blue.shade700,
                      Colors.blue.shade400,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withAlpha(77), // 0.3 * 255 = 77
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.snooze_outlined,
                  color: Colors.white,
                  size: 26,
                ),
              ),
              const SizedBox(width: 16),
              // معلومات التأجيل
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'مدة تأجيل الإشعارات',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'تأجيل تذكيرات الإشعارات',
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).textTheme.bodySmall?.color,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      '$snoozeMinutes دقيقة',
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).textTheme.bodyMedium?.color,
                      ),
                    ),
                  ],
                ),
              ),
              // سهم
              Icon(
                Icons.arrow_forward_ios,
                size: 18,
                color: Colors.grey.withAlpha(179), // 0.7 * 255 = 179
              ),
            ],
          ),
        ),
      ),
    );
  }

  // تحميل إعدادات الإشعارات
  Future<void> _loadSettings() async {
    try {
      // تحميل إعدادات الإشعارات من النظام الجديد
      final morningEnabled = await _notificationManager.isMorningAzkarEnabled();
      final morningTime = await _notificationManager.getMorningAzkarTime();
      final eveningEnabled = await _notificationManager.isEveningAzkarEnabled();
      final eveningTime = await _notificationManager.getEveningAzkarTime();

      // تحديث حالة الواجهة
      setState(() {
        _morningTime = morningTime;
        _morningEnabled = morningEnabled;
        _eveningTime = eveningTime;
        _eveningEnabled = eveningEnabled;
        _snoozeMinutes = 10; // قيمة افتراضية
      });
    } catch (e) {
      debugPrint('خطأ في تحميل الإعدادات: $e');
    }
  }

  // تحميل إعدادات التعطيل المؤقت
  Future<void> _loadTemporaryDisableSettings() async {
    try {
      // تحميل إعدادات التعطيل المؤقت من مزودات البيانات
      final wirdProvider = Provider.of<WirdProvider>(context, listen: false);
      final tasbihProvider =
          Provider.of<TasbihProvider>(context, listen: false);

      final wirdRemindersDisabled =
          await wirdProvider.areWirdRemindersTemporarilyDisabled();
      final returnNotificationsDisabled =
          await tasbihProvider.areReturnNotificationsTemporarilyDisabled();

      // تحديث حالة الواجهة
      setState(() {
        _wirdRemindersDisabled = wirdRemindersDisabled;
        _returnNotificationsDisabled = returnNotificationsDisabled;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل إعدادات التعطيل المؤقت: $e');
    }
  }

  // تبديل حالة تعطيل تذكيرات الأوراد مؤقتاً
  Future<void> _toggleWirdRemindersDisabled(bool value) async {
    try {
      final wirdProvider = Provider.of<WirdProvider>(context, listen: false);
      await wirdProvider.toggleWirdRemindersTemporarilyDisabled(value);

      setState(() {
        _wirdRemindersDisabled = value;
      });
    } catch (e) {
      debugPrint('خطأ في تبديل حالة تعطيل تذكيرات الأوراد: $e');
    }
  }

  // تبديل حالة تعطيل إشعارات العودة مؤقتاً
  Future<void> _toggleReturnNotificationsDisabled(bool value) async {
    try {
      final tasbihProvider =
          Provider.of<TasbihProvider>(context, listen: false);
      await tasbihProvider.toggleReturnNotificationsTemporarilyDisabled(value);

      setState(() {
        _returnNotificationsDisabled = value;
      });
    } catch (e) {
      debugPrint('خطأ في تبديل حالة تعطيل إشعارات العودة: $e');
    }
  }

  // تفعيل أو تعطيل الإشعارات
  Future<void> _toggleNotification(bool isMorning, bool value) async {
    setState(() {
      if (isMorning) {
        _morningEnabled = value;
      } else {
        _eveningEnabled = value;
      }
    });

    // تحديث إعدادات الإشعارات باستخدام النظام الجديد
    await _notificationManager.updateNotificationSettings(
      morningEnabled: isMorning ? value : null,
      morningTime: isMorning
          ? (_morningTime ?? const TimeOfDay(hour: 6, minute: 0))
          : null,
      eveningEnabled: !isMorning ? value : null,
      eveningTime: !isMorning
          ? (_eveningTime ?? const TimeOfDay(hour: 17, minute: 0))
          : null,
    );
  }

  // اختيار وقت الإشعار
  Future<void> _selectTime(BuildContext context, bool isMorning) async {
    final TimeOfDay? currentTime = isMorning ? _morningTime : _eveningTime;
    final TimeOfDay? pickedTime = await TimePickerUtils.showCustomTimePicker(
      context: context,
      initialTime: currentTime ??
          (isMorning
              ? const TimeOfDay(hour: 6, minute: 0)
              : const TimeOfDay(hour: 17, minute: 0)),
    );

    if (pickedTime != null) {
      setState(() {
        if (isMorning) {
          _morningTime = pickedTime;
        } else {
          _eveningTime = pickedTime;
        }
      });

      // تحديث إعدادات الإشعارات باستخدام النظام الجديد
      await _notificationManager.updateNotificationSettings(
        morningEnabled: isMorning ? _morningEnabled : null,
        morningTime: isMorning ? pickedTime : null,
        eveningEnabled: !isMorning ? _eveningEnabled : null,
        eveningTime: !isMorning ? pickedTime : null,
      );
    }
  }

  // إعادة ضبط الإشعارات
  Future<void> _resetNotifications() async {
    // إعادة تهيئة الإشعارات باستخدام النظام الجديد

    // إلغاء الإشعارات الحالية
    if (_morningEnabled) {
      await _notificationManager.cancelMorningAzkarNotification();
    }

    if (_eveningEnabled) {
      await _notificationManager.cancelEveningAzkarNotification();
    }

    // إعادة جدولة الإشعارات
    if (_morningEnabled && _morningTime != null) {
      await _notificationManager
          .scheduleMorningAzkarNotification(_morningTime!);
    }

    if (_eveningEnabled && _eveningTime != null) {
      await _notificationManager
          .scheduleEveningAzkarNotification(_eveningTime!);
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('تم إعادة ضبط الإشعارات'),
          backgroundColor: AppColors.azkarColor,
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    }
  }

  // إظهار منتقي مدة التأجيل
  void _showSnoozeDurationPicker() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        final isDarkMode = Theme.of(context).brightness == Brightness.dark;

        return Container(
          height: MediaQuery.of(context).size.height * 0.4,
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(26), // 0.1 * 255 = 26
                blurRadius: 10,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Column(
            children: [
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.grey.withAlpha(77), // 0.3 * 255 = 77
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.snooze_outlined,
                      color: AppColors.azkarColor,
                      size: 22,
                    ),
                    SizedBox(width: 8),
                    Text(
                      'مدة تأجيل الإشعارات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.azkarColor,
                      ),
                    ),
                  ],
                ),
              ),
              Divider(
                color: Colors.grey.withAlpha(51), // 0.2 * 255 = 51
                thickness: 1,
                indent: 30,
                endIndent: 30,
              ),
              const SizedBox(height: 8),
              Expanded(
                child: ListView(
                  children: [5, 10, 15, 20, 30, 45, 60].map((minutes) {
                    final isSelected = _snoozeMinutes == minutes;
                    return Container(
                      margin: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 4),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? AppColors.azkarColor.withAlpha(isDarkMode
                                ? 38
                                : 26) // 0.15 * 255 = 38, 0.1 * 255 = 26
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                        border: isSelected
                            ? Border.all(
                                color: AppColors.azkarColor
                                    .withAlpha(77), // 0.3 * 255 = 77
                                width: 1,
                              )
                            : null,
                      ),
                      child: ListTile(
                        title: Text(
                          '$minutes دقيقة',
                          style: TextStyle(
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal,
                            color: isSelected
                                ? AppColors.azkarColor
                                : Theme.of(context).textTheme.bodyLarge?.color,
                          ),
                        ),
                        leading: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: isSelected
                                ? AppColors.azkarColor
                                    .withAlpha(38) // 0.15 * 255 = 38
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Icon(
                            isSelected
                                ? Icons.check_circle
                                : Icons.circle_outlined,
                            color:
                                isSelected ? AppColors.azkarColor : Colors.grey,
                          ),
                        ),
                        onTap: () {
                          // Primero cerramos el diálogo
                          Navigator.pop(context);
                          // Luego actualizamos los minutos de snooze
                          updateSnoozeMinutes(minutes);
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> updateSnoozeMinutes(int minutes) async {
    setState(() {
      _snoozeMinutes = minutes;
    });

    // ملاحظة: النظام الجديد لا يدعم حاليًا تخزين وقت التأجيل
    // يمكن إضافة هذه الميزة لاحقًا

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم تحديث مدة التأجيل إلى $minutes دقيقة',
          ),
          backgroundColor: AppColors.azkarColor,
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  // بطاقة اختبار الإشعارات
  Widget _buildTestNotificationsCard(bool isDarkMode) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Theme.of(context).cardColor.withAlpha(242) // 0.95 * 255 = 242
            : Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(8), // 0.03 * 255 = 8
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: Colors.grey.withAlpha(26), // 0.1 * 255 = 26
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // أيقونة
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.purple.shade700,
                        Colors.purple.shade400,
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.purple.withAlpha(77), // 0.3 * 255 = 77
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.notifications_active,
                    color: Colors.white,
                    size: 26,
                  ),
                ),
                const SizedBox(width: 16),
                // معلومات الاختبار
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'اختبار الإشعارات',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'اختبار إشعارات أذكار الصباح والمساء',
                        style: TextStyle(
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // أزرار الاختبار
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
            child: Row(
              children: [
                // زر اختبار إشعارات الصباح
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _testMorningNotification,
                    icon: const Icon(Icons.wb_sunny_outlined, size: 18),
                    label: const Text('أذكار الصباح'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.amber.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      elevation: 4,
                      shadowColor: Colors.amber.withAlpha(100),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                // زر اختبار إشعارات المساء
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _testEveningNotification,
                    icon: const Icon(Icons.nightlight_round, size: 18),
                    label: const Text('أذكار المساء'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.indigo,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      elevation: 4,
                      shadowColor: Colors.indigo.withAlpha(100),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // قسم تعطيل الإشعارات مؤقتاً
  Widget _buildTemporaryDisableSection(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم
        Padding(
          padding: const EdgeInsets.only(
              right: 16.0, left: 16.0, top: 16.0, bottom: 8.0),
          child: Text(
            'تعطيل الإشعارات مؤقتاً',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.azkarColor,
            ),
          ),
        ),

        // بطاقة تعطيل تذكيرات الأوراد
        Container(
          margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          decoration: BoxDecoration(
            color: isDarkMode
                ? Theme.of(context).cardColor.withAlpha(242) // 0.95 * 255 = 242
                : Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(8), // 0.03 * 255 = 8
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
            border: Border.all(
              color: _wirdRemindersDisabled
                  ? Colors.red.withAlpha(77) // 0.3 * 255 = 77
                  : Colors.grey.withAlpha(26), // 0.1 * 255 = 26
              width: 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // أيقونة
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: _wirdRemindersDisabled
                          ? [Colors.red.shade700, Colors.red.shade400]
                          : [Colors.purple.shade700, Colors.purple.shade400],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: _wirdRemindersDisabled
                            ? Colors.red.withAlpha(77) // 0.3 * 255 = 77
                            : Colors.purple.withAlpha(77), // 0.3 * 255 = 77
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    _wirdRemindersDisabled
                        ? Icons.notifications_off
                        : Icons.notifications_active,
                    color: Colors.white,
                    size: 26,
                  ),
                ),
                const SizedBox(width: 16),
                // معلومات
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'تذكيرات الأوراد',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: _wirdRemindersDisabled ? Colors.red : null,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _wirdRemindersDisabled
                            ? 'تذكيرات الأوراد معطلة مؤقتاً'
                            : 'تذكيرات الأوراد مفعلة',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).textTheme.bodySmall?.color,
                        ),
                      ),
                    ],
                  ),
                ),
                // زر التفعيل
                Switch(
                  value: _wirdRemindersDisabled,
                  onChanged: _toggleWirdRemindersDisabled,
                  activeColor: Colors.red,
                  activeTrackColor: Colors.red.withAlpha(77), // 0.3 * 255 = 77
                ),
              ],
            ),
          ),
        ),

        // بطاقة تعطيل إشعارات العودة
        Container(
          margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          decoration: BoxDecoration(
            color: isDarkMode
                ? Theme.of(context).cardColor.withAlpha(242) // 0.95 * 255 = 242
                : Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(8), // 0.03 * 255 = 8
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
            border: Border.all(
              color: _returnNotificationsDisabled
                  ? Colors.red.withAlpha(77) // 0.3 * 255 = 77
                  : Colors.grey.withAlpha(26), // 0.1 * 255 = 26
              width: 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // أيقونة
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: _returnNotificationsDisabled
                          ? [Colors.red.shade700, Colors.red.shade400]
                          : [Colors.teal.shade700, Colors.teal.shade400],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: _returnNotificationsDisabled
                            ? Colors.red.withAlpha(77) // 0.3 * 255 = 77
                            : Colors.teal.withAlpha(77), // 0.3 * 255 = 77
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    _returnNotificationsDisabled
                        ? Icons.notifications_off
                        : Icons.notifications_active,
                    color: Colors.white,
                    size: 26,
                  ),
                ),
                const SizedBox(width: 16),
                // معلومات
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'إشعارات العودة',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color:
                              _returnNotificationsDisabled ? Colors.red : null,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _returnNotificationsDisabled
                            ? 'إشعارات العودة معطلة مؤقتاً'
                            : 'إشعارات العودة مفعلة',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).textTheme.bodySmall?.color,
                        ),
                      ),
                    ],
                  ),
                ),
                // زر التفعيل
                Switch(
                  value: _returnNotificationsDisabled,
                  onChanged: _toggleReturnNotificationsDisabled,
                  activeColor: Colors.red,
                  activeTrackColor: Colors.red.withAlpha(77), // 0.3 * 255 = 77
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // اختبار إشعارات أذكار الصباح
  Future<void> _testMorningNotification() async {
    // إظهار مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Center(
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(50),
                blurRadius: 10,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                valueColor:
                    AlwaysStoppedAnimation<Color>(Colors.amber.shade600),
              ),
              const SizedBox(height: 16),
              const Text(
                'جاري إرسال إشعار اختباري...',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
      ),
    );

    try {
      // تأخير قصير لتحسين تجربة المستخدم
      await Future.delayed(const Duration(milliseconds: 800));

      // إرسال إشعار اختباري فوري لأذكار الصباح
      final success =
          await _notificationManager.sendTestMorningAzkarNotification();

      // إغلاق مؤشر التحميل
      if (mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }

      if (mounted) {
        // تأثير اهتزاز للتفاعل
        HapticFeedback.mediumImpact();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  success ? Icons.check_circle : Icons.error,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    success
                        ? 'تم إرسال إشعار اختباري لأذكار الصباح'
                        : 'فشل إرسال الإشعار',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            backgroundColor: success ? Colors.amber.shade600 : Colors.red,
            behavior: SnackBarBehavior.floating,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            duration: const Duration(seconds: 3),
            action: success
                ? SnackBarAction(
                    label: 'تحقق',
                    textColor: Colors.white,
                    onPressed: () {
                      // لا شيء، فقط للإشارة إلى أن المستخدم يجب أن يتحقق من الإشعار
                    },
                  )
                : null,
          ),
        );
      }
    } catch (e) {
      // إغلاق مؤشر التحميل
      if (mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }

      if (mounted) {
        // تأثير اهتزاز للتفاعل
        HapticFeedback.vibrate();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.error,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'خطأ في إرسال الإشعار',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Text(
                        e.toString(),
                        style: const TextStyle(fontSize: 12),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  // اختبار إشعارات أذكار المساء
  Future<void> _testEveningNotification() async {
    // إظهار مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Center(
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(50),
                blurRadius: 10,
                spreadRadius: 1,
              ),
            ],
          ),
          child: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.indigo),
              ),
              SizedBox(height: 16),
              Text(
                'جاري إرسال إشعار اختباري...',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
      ),
    );

    try {
      // تأخير قصير لتحسين تجربة المستخدم
      await Future.delayed(const Duration(milliseconds: 800));

      // إرسال إشعار اختباري فوري لأذكار المساء
      final success =
          await _notificationManager.sendTestEveningAzkarNotification();

      // إغلاق مؤشر التحميل
      if (mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }

      if (mounted) {
        // تأثير اهتزاز للتفاعل
        HapticFeedback.mediumImpact();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  success ? Icons.check_circle : Icons.error,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    success
                        ? 'تم إرسال إشعار اختباري لأذكار المساء'
                        : 'فشل إرسال الإشعار',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            backgroundColor: success ? Colors.indigo : Colors.red,
            behavior: SnackBarBehavior.floating,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            duration: const Duration(seconds: 3),
            action: success
                ? SnackBarAction(
                    label: 'تحقق',
                    textColor: Colors.white,
                    onPressed: () {
                      // لا شيء، فقط للإشارة إلى أن المستخدم يجب أن يتحقق من الإشعار
                    },
                  )
                : null,
          ),
        );
      }
    } catch (e) {
      // إغلاق مؤشر التحميل
      if (mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }

      if (mounted) {
        // تأثير اهتزاز للتفاعل
        HapticFeedback.vibrate();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.error,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'خطأ في إرسال الإشعار',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Text(
                        e.toString(),
                        style: const TextStyle(fontSize: 12),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }
}

// رسام نقاط الضوء - نسخة أكثر صرامة
class LightDotsPainter extends CustomPainter {
  final double progress;
  final Color color;
  final bool isDarkMode;

  LightDotsPainter(
      { //
      required this.progress,
      required this.color,
      required this.isDarkMode});

  @override
  void paint(Canvas canvas, Size size) {
    final random = math.Random(42); // ثابت للحصول على نفس النمط دائمًا
    final paint = Paint()..style = PaintingStyle.fill;

    // رسم النقاط المضيئة المتحركة
    for (int i = 0; i < 12; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final radius = 1.0 + random.nextDouble() * 2.0;

      // حركة النقاط
      final offsetX = math.sin((progress * math.pi * 2) + i) * 5.0;
      final offsetY = math.cos((progress * math.pi * 2) + i * 0.7) * 3.0;

      // حساب قيمة الشفافية بطريقة آمنة تماماً
      double safeOpacity;

      try {
        // محاولة لحساب التأثير المتموج بشكل آمن
        final sinValue = math.sin((progress * math.pi * 2) + i * 0.5);
        // نتأكد من أن القيمة بين -1.0 و 1.0
        final clampedSinValue = sinValue.clamp(-1.0, 1.0);
        // حساب القيمة الأساسية بين 0.05 و 0.15
        safeOpacity = 0.1 + (0.05 * clampedSinValue);
        // نتأكد مرة أخرى من أن القيمة النهائية بين 0.0 و 1.0
        safeOpacity = safeOpacity.clamp(0.0, 1.0);

        // تعديل للوضع المظلم
        if (isDarkMode) {
          safeOpacity = (safeOpacity * 0.8).clamp(0.0, 1.0);
        }
      } catch (e) {
        // إذا حدث أي خطأ، نضع قيمة ثابتة آمنة
        safeOpacity = isDarkMode ? 0.08 : 0.1;
      }

      // نتأكد للمرة الأخيرة من أن القيمة آمنة
      final finalOpacity = safeOpacity.clamp(0.0, 1.0);

      paint.color = color.withAlpha(
          (finalOpacity * 255).toInt()); // Convertir de 0.0-1.0 a 0-255
      canvas.drawCircle(Offset(x + offsetX, y + offsetY), radius, paint);
    }
  }

  @override
  bool shouldRepaint(LightDotsPainter oldDelegate) =>
      oldDelegate.progress != progress ||
      oldDelegate.color != color ||
      oldDelegate.isDarkMode != isDarkMode;
}
