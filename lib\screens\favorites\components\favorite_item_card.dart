import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../models/favorite_item.dart';
import '../../../models/dua.dart';

class FavoriteItemCard extends StatelessWidget {
  final FavoriteItem item;
  final Function(FavoriteItem) onTap;
  final Function(FavoriteItem) onShare;
  final Function(FavoriteItem) onDelete;
  final Function(DateTime) formatDate;

  const FavoriteItemCard({
    Key? key,
    required this.item,
    required this.onTap,
    required this.onShare,
    required this.onDelete,
    required this.formatDate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // استخدام الطرق المساعدة في كائن FavoriteItem
    final itemColor = item.color;
    final itemIcon = item.icon;
    final itemTypeLabel = item.typeLabel;

    // الحصول على ثيم التطبيق لاستخدامه في التصميم
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // إنشاء بطاقة بتصميم فاخر
    return Card(
      elevation: _isLuxuryItem(item.type) ? 6 : 4,
      shadowColor: itemColor.withAlpha(_isLuxuryItem(item.type) ? 60 : 40),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24),
        side: BorderSide(
          color: isDark
              ? (_isLuxuryItem(item.type)
                  ? itemColor.withAlpha(30)
                  : Colors.transparent)
              : itemColor.withAlpha(_isLuxuryItem(item.type) ? 40 : 25),
          width: _isLuxuryItem(item.type) ? 1.5 : 1.0,
        ),
      ),
      color: isDark
          ? Color.lerp(theme.cardColor, itemColor,
              _isLuxuryItem(item.type) ? 0.08 : 0.05)
          : theme.cardColor,
      child: InkWell(
        onTap: () {
          // حفظ مرجع للعنصر والدالة
          final currentItem = item;
          // استخدام Future.microtask بدلاً من addPostFrameCallback لتجنب مشاكل البناء
          Future.microtask(() {
            onTap(currentItem);
          });
        },
        borderRadius: BorderRadius.circular(20),
        splashColor: itemColor.withAlpha(50),
        highlightColor: itemColor.withAlpha(20),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                textDirection:
                    TextDirection.rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                children: [
                  // أيقونة محسنة بتأثيرات ظلال
                  _buildItemIcon(itemIcon, itemColor, isDark),
                  const SizedBox(width: 16),

                  // محتوى العنصر
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      textDirection: TextDirection
                          .rtl, // تحديد اتجاه العمود من اليمين إلى اليسار
                      children: [
                        // العنوان بخط محسن
                        Text(
                          item.title,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: isDark ? Colors.white : Colors.black87,
                            fontSize: 18,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          textDirection: TextDirection
                              .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                          textAlign: TextAlign.right, // محاذاة النص إلى اليمين
                        ),
                        const SizedBox(height: 6),
                        // العنوان الفرعي مع شارة النوع
                        Row(
                          textDirection: TextDirection
                              .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: itemColor.withAlpha(20),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                itemTypeLabel,
                                style: TextStyle(
                                  fontSize: 11,
                                  color: itemColor,
                                  fontWeight: FontWeight.w500,
                                ),
                                textDirection: TextDirection
                                    .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                textAlign:
                                    TextAlign.right, // محاذاة النص إلى اليمين
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                item.subtitle,
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: theme.textTheme.bodySmall?.color,
                                  fontWeight: FontWeight.w500,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                textDirection: TextDirection
                                    .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                                textAlign:
                                    TextAlign.right, // محاذاة النص إلى اليمين
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        // تاريخ الإضافة
                        Row(
                          textDirection: TextDirection
                              .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                          children: [
                            Icon(
                              Icons.access_time_rounded,
                              size: 14,
                              color: theme.textTheme.bodySmall?.color,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              formatDate(item.addedDate),
                              style: theme.textTheme.bodySmall?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                              textDirection: TextDirection
                                  .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                              textAlign:
                                  TextAlign.right, // محاذاة النص إلى اليمين
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // أزرار الإجراءات محسنة
                  Column(
                    textDirection: TextDirection
                        .rtl, // تحديد اتجاه العمود من اليمين إلى اليسار
                    children: [
                      // زر المشاركة بتصميم فاخر
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment
                                .topRight, // تغيير اتجاه التدرج ليتناسب مع اللغة العربية
                            end: Alignment.bottomLeft,
                            colors: [
                              itemColor.withAlpha(
                                  _isLuxuryItem(item.type) ? 20 : 15),
                              itemColor
                                  .withAlpha(_isLuxuryItem(item.type) ? 10 : 5),
                            ],
                          ),
                          shape: BoxShape.circle,
                          boxShadow: _isLuxuryItem(item.type)
                              ? [
                                  BoxShadow(
                                    color: itemColor.withAlpha(15),
                                    blurRadius: 4,
                                    spreadRadius: 0,
                                    offset: const Offset(0, 2),
                                  ),
                                ]
                              : null,
                        ),
                        child: Material(
                          color: Colors.transparent,
                          shape: const CircleBorder(),
                          child: InkWell(
                            customBorder: const CircleBorder(),
                            onTap: () {
                              HapticFeedback.mediumImpact();
                              // حفظ مرجع للعنصر
                              final currentItem = item;
                              // استخدام Future.microtask لتجنب مشاكل البناء
                              Future.microtask(() {
                                onShare(currentItem);
                              });
                            },
                            child: Padding(
                              padding: const EdgeInsets.all(12.0),
                              child: Icon(
                                Icons.share_rounded,
                                color: itemColor,
                                size: 20,
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      // زر الحذف بتصميم فاخر
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment
                                .topRight, // تغيير اتجاه التدرج ليتناسب مع اللغة العربية
                            end: Alignment.bottomLeft,
                            colors: [
                              Colors.red.withAlpha(
                                  _isLuxuryItem(item.type) ? 20 : 15),
                              Colors.red
                                  .withAlpha(_isLuxuryItem(item.type) ? 10 : 5),
                            ],
                          ),
                          shape: BoxShape.circle,
                          boxShadow: _isLuxuryItem(item.type)
                              ? [
                                  BoxShadow(
                                    color: Colors.red.withAlpha(15),
                                    blurRadius: 4,
                                    spreadRadius: 0,
                                    offset: const Offset(0, 2),
                                  ),
                                ]
                              : null,
                        ),
                        child: Material(
                          color: Colors.transparent,
                          shape: const CircleBorder(),
                          child: InkWell(
                            customBorder: const CircleBorder(),
                            onTap: () {
                              HapticFeedback.mediumImpact();
                              // حفظ مرجع للعنصر
                              final currentItem = item;
                              // استخدام Future.microtask لتجنب مشاكل البناء
                              Future.microtask(() {
                                onDelete(currentItem);
                              });
                            },
                            child: const Padding(
                              padding: EdgeInsets.all(12.0),
                              child: Icon(
                                Icons.delete_rounded,
                                color: Colors.redAccent,
                                size: 20,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              // محتوى إضافي للدعاء أو الصلاة على النبي
              if ((item.type == 'dua' || item.type == 'prophet_prayer') &&
                  item.item != null)
                Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          itemColor.withAlpha(isDark ? 25 : 15),
                          itemColor.withAlpha(isDark ? 15 : 8),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: itemColor.withAlpha(30),
                        width: 1.5,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: itemColor.withAlpha(15),
                          blurRadius: 5,
                          spreadRadius: 0,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Text(
                      item.type == 'dua'
                          ? (item.item as Dua).text
                          : (item.item as Dua)
                              .text, // Asumiendo que Dua y ProphetPrayer tienen la misma estructura
                      style: TextStyle(
                        fontSize: 15,
                        height: 1.6,
                        color: isDark ? Colors.white70 : Colors.black87,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.right,
                      textDirection: TextDirection.rtl,
                    ),
                  ),
                ),

              // شريط ملون في أسفل البطاقة بتصميم فاخر - متوافق مع اتجاه اللغة العربية
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: Container(
                  height: _isLuxuryItem(item.type) ? 5 : 4,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: _isLuxuryItem(item.type)
                          ? [
                              itemColor.withAlpha(220),
                              itemColor.withAlpha(120),
                              itemColor.withAlpha(30),
                              Colors.transparent,
                            ]
                          : [
                              itemColor.withAlpha(200),
                              itemColor.withAlpha(50),
                              Colors.transparent,
                            ],
                      stops: _isLuxuryItem(item.type)
                          ? const [0.0, 0.4, 0.8, 1.0]
                          : const [0.0, 0.7, 1.0],
                      begin: Alignment
                          .centerRight, // تغيير اتجاه التدرج ليتناسب مع اللغة العربية
                      end: Alignment.centerLeft,
                    ),
                    borderRadius:
                        BorderRadius.circular(_isLuxuryItem(item.type) ? 3 : 2),
                    boxShadow: _isLuxuryItem(item.type)
                        ? [
                            BoxShadow(
                              color: itemColor.withAlpha(30),
                              blurRadius: 3,
                              spreadRadius: 0,
                              offset: const Offset(0, 1),
                            ),
                          ]
                        : null,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // التحقق مما إذا كان العنصر من النوع الفاخر (دعاء أو صلاة على النبي)
  bool _isLuxuryItem(String type) {
    return type == 'dua' || type == 'prophet_prayer';
  }

  Widget _buildItemIcon(IconData icon, Color color, bool isDark) {
    // تحسين تأثير الأيقونة للدعاء والصلاة على النبي
    if (_isLuxuryItem(item.type)) {
      return Stack(
        children: [
          // تأثير الهالة المتحركة - مُحسّن للأداء
          RepaintBoundary(
            child: TweenAnimationBuilder<double>(
              tween: Tween<double>(begin: 0.9, end: 1.1),
              // تقليل مدة الرسوم المتحركة لتحسين الأداء
              duration: const Duration(seconds: 2),
              // استخدام منحنى أبسط لتحسين الأداء
              curve: Curves.easeInOut,
              builder: (context, value, child) {
                return Opacity(
                  // تقليل مدى تغير الشفافية لتحسين الأداء
                  opacity: (1.1 - value) * 0.3,
                  child: Transform.scale(
                    scale: value,
                    child: Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            color.withAlpha(80),
                            color.withAlpha(0),
                          ],
                          stops: const [0.2, 1.0],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

          // الأيقونة الرئيسية مع تأثير نبض - مُحسّن للأداء
          RepaintBoundary(
            child: TweenAnimationBuilder<double>(
              tween: Tween<double>(begin: 0.97, end: 1.03),
              // تقليل مدة الرسوم المتحركة لتحسين الأداء
              duration: const Duration(milliseconds: 1500),
              // استخدام منحنى أبسط لتحسين الأداء
              curve: Curves.easeInOut,
              builder: (context, scale, child) {
                return Transform.scale(
                  scale: scale,
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: color.withAlpha(isDark ? 40 : 30),
                      shape: BoxShape.circle,
                      // تبسيط الظلال لتحسين الأداء
                      boxShadow: [
                        BoxShadow(
                          color: color.withAlpha(40),
                          // تقليل قيمة التمويه لتحسين الأداء
                          blurRadius: 6,
                          spreadRadius: 0.5,
                          offset: const Offset(0, 2),
                        ),
                      ],
                      border: Border.all(
                        color: color.withAlpha(60),
                        width: 1.5,
                      ),
                    ),
                    child: Center(
                      // استخدام أيقونة بسيطة بدلاً من ShaderMask لتحسين الأداء
                      child: Icon(
                        icon,
                        color: color,
                        size: 28,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      );
    }
    // تأثير الأيقونة العادية للعناصر الأخرى - مُحسّن للأداء
    else {
      return RepaintBoundary(
        child: TweenAnimationBuilder<double>(
          tween: Tween<double>(begin: 0.95, end: 1.0),
          // تقليل مدة الرسوم المتحركة لتحسين الأداء
          duration: const Duration(milliseconds: 1200),
          // استخدام منحنى أبسط لتحسين الأداء
          curve: Curves.easeOut,
          builder: (context, scale, child) {
            return Transform.scale(
              scale: scale,
              child: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: color.withAlpha(isDark ? 40 : 30),
                  shape: BoxShape.circle,
                  // تبسيط الظلال لتحسين الأداء
                  boxShadow: [
                    BoxShadow(
                      color: color.withAlpha(40),
                      // تقليل قيمة التمويه لتحسين الأداء
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Center(
                  child: Icon(
                    icon,
                    color: color,
                    size: 28,
                  ),
                ),
              ),
            );
          },
        ),
      );
    }
  }
}
