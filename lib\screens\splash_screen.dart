import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/app_colors.dart';
import 'home_screen.dart';
import 'welcome_screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  // التحكم في الرسوم المتحركة
  late AnimationController _animationController;
  late Animation<double> _logoFadeAnimation;
  late Animation<double> _logoScaleAnimation;
  late Animation<double> _titleFadeAnimation;
  late Animation<double> _titleSlideAnimation;
  late Animation<double> _subtitleFadeAnimation;
  late Animation<double> _backgroundOpacityAnimation;
  late Animation<double> _decorationFadeAnimation;

  // للانتقال المتميز
  bool _startTransition = false;
  bool _showLogoOnly = false;

  @override
  void initState() {
    super.initState();

    // إعداد التحريكات
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2500),
    );

    _backgroundOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.4, curve: Curves.easeOut),
      ),
    );

    _decorationFadeAnimation = Tween<double>(begin: 0.0, end: 0.8).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 0.6, curve: Curves.easeOut),
      ),
    );

    _logoFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.3, 0.6, curve: Curves.easeOut),
      ),
    );

    _logoScaleAnimation = Tween<double>(begin: 0.4, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.3, 0.7, curve: Curves.elasticOut),
      ),
    );

    _titleFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.6, 0.8, curve: Curves.easeOut),
      ),
    );

    _titleSlideAnimation = Tween<double>(begin: 50.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.6, 0.8, curve: Curves.easeOut),
      ),
    );

    _subtitleFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.7, 0.9, curve: Curves.easeOut),
      ),
    );

    // بدء التحريكات
    _animationController.forward();

    // جدولة الانتقال المتميز
    _scheduleAdvancedTransition();
  }

  // دالة لجدولة الانتقال المتميز
  void _scheduleAdvancedTransition() {
    // المرحلة 1: عرض الشاشة الكاملة
    Timer(const Duration(milliseconds: 2800), () {
      if (mounted) {
        setState(() {
          _startTransition = true;
        });
      }
    });

    // المرحلة 2: إظهار الشعار فقط
    Timer(const Duration(milliseconds: 3200), () {
      if (mounted) {
        setState(() {
          _showLogoOnly = true;
        });
      }
    });

    // المرحلة 3: الانتقال إلى الصفحة الرئيسية
    Timer(const Duration(milliseconds: 3600), () {
      _navigateToHome();
    });
  }

  // دالة الانتقال إلى الصفحة الرئيسية أو شاشة الترحيب
  void _navigateToHome() async {
    try {
      // التحقق مما إذا كان المستخدم قد رأى شاشة الترحيب من قبل
      final prefs = await SharedPreferences.getInstance();
      final welcomeScreenShown = prefs.getBool('welcome_screen_shown') ?? false;

      // تحديد الشاشة المستهدفة
      final Widget targetScreen =
          welcomeScreenShown ? const HomeScreen() : const WelcomeScreen();

      // التحقق من أن الـ widget لا يزال مثبتًا في الشجرة
      if (!mounted) return;

      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => targetScreen,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            // تأثير مزيج من التلاشي والتكبير
            var fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
              CurvedAnimation(
                parent: animation,
                curve: Curves.easeOut,
              ),
            );

            var scaleAnimation = Tween<double>(begin: 1.1, end: 1.0).animate(
              CurvedAnimation(
                parent: animation,
                curve: Curves.easeOut,
              ),
            );

            return FadeTransition(
              opacity: fadeAnimation,
              child: ScaleTransition(
                scale: scaleAnimation,
                child: child,
              ),
            );
          },
          transitionDuration: const Duration(milliseconds: 800),
        ),
      );
    } catch (e) {
      debugPrint('خطأ أثناء الانتقال إلى الصفحة الرئيسية: $e');
      // محاولة بديلة بطريقة أبسط
      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/home');
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    const appColor = AppColors.azkarColor;

    return Scaffold(
      body: Stack(
        fit: StackFit.expand,
        children: [
          // خلفية متدرجة
          FadeTransition(
            opacity: _backgroundOpacityAnimation,
            child: AnimatedOpacity(
              duration: const Duration(milliseconds: 400),
              opacity: _startTransition ? 0.0 : 1.0,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      isDarkMode
                          ? const Color(0xFF1A1A2E)
                          : const Color(0xFFF8F9FA),
                      isDarkMode
                          ? const Color(0xFF121212)
                          : const Color(0xFFECF0F1),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // زخارف إسلامية في الخلفية
          Positioned(
            top: -100,
            right: -100,
            child: FadeTransition(
              opacity: _decorationFadeAnimation,
              child: AnimatedOpacity(
                duration: const Duration(milliseconds: 400),
                opacity: _startTransition ? 0.0 : 1.0,
                child: SvgPicture.asset(
                  'assets/images/p2.svg',
                  width: 300,
                  height: 300,
                  colorFilter: ColorFilter.mode(
                    appColor.withAlpha(75),
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
          ),

          Positioned(
            bottom: -80,
            left: -80,
            child: FadeTransition(
              opacity: _decorationFadeAnimation,
              child: AnimatedOpacity(
                duration: const Duration(milliseconds: 400),
                opacity: _startTransition ? 0.0 : 1.0,
                child: SvgPicture.asset(
                  'assets/images/p2.svg',
                  width: 250,
                  height: 250,
                  colorFilter: ColorFilter.mode(
                    appColor.withAlpha(50),
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
          ),

          // المحتوى الرئيسي
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // شعار التطبيق - يظل مرئيًا خلال الانتقال
                FadeTransition(
                  opacity: _logoFadeAnimation,
                  child: ScaleTransition(
                    scale: _logoScaleAnimation,
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 500),
                      curve: Curves.easeOutCubic,
                      width: _showLogoOnly ? 80 : 120,
                      height: _showLogoOnly ? 80 : 120,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: appColor.withAlpha(_showLogoOnly ? 100 : 75),
                            blurRadius: _showLogoOnly ? 30 : 20,
                            spreadRadius: _showLogoOnly ? 2 : 5,
                          ),
                        ],
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            appColor.withAlpha(230),
                            appColor.withAlpha(180),
                          ],
                        ),
                      ),
                      child: Center(
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 500),
                          width: _showLogoOnly ? 50 : 70,
                          height: _showLogoOnly ? 50 : 70,
                          child: SvgPicture.asset(
                            'assets/images/p2.svg',
                            colorFilter: const ColorFilter.mode(
                              Colors.white,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  height: _showLogoOnly ? 0 : 40,
                ),

                // عنوان التطبيق - يختفي خلال الانتقال
                if (!_showLogoOnly)
                  FadeTransition(
                    opacity: _titleFadeAnimation,
                    child: AnimatedOpacity(
                      duration: const Duration(milliseconds: 300),
                      opacity: _startTransition ? 0.0 : 1.0,
                      child: SlideTransition(
                        position: Tween<Offset>(
                          begin: Offset(0, _titleSlideAnimation.value / 100),
                          end: Offset.zero,
                        ).animate(_animationController),
                        child: Column(
                          children: [
                          const  Text(
                              'وهج السالك',
                              style:  TextStyle(
                                color: appColor,
                                fontSize: 32,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 1.2,
                              ),
                            ),

                            const SizedBox(height: 12),

                            // وصف التطبيق
                            FadeTransition(
                              opacity: _subtitleFadeAnimation,
                              child: Text(
                                'ينابيع الحكمة وأنوار المعرفة',
                                style: TextStyle(
                                  color: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.color
                                      ?.withAlpha(204), // 0.8 * 255 = 204
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  height: _showLogoOnly ? 0 : 80,
                ),

                // مؤشر التحميل - يختفي خلال الانتقال
                if (!_showLogoOnly)
                  FadeTransition(
                    opacity: _subtitleFadeAnimation,
                    child: AnimatedOpacity(
                      duration: const Duration(milliseconds: 300),
                      opacity: _startTransition ? 0.0 : 1.0,
                      child: const SizedBox(
                        width: 40,
                        height: 40,
                        child: CircularProgressIndicator(
                          strokeWidth: 3,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // معلومات الإصدار - تختفي خلال الانتقال
          if (!_showLogoOnly)
            Positioned(
              bottom: 20,
              left: 0,
              right: 0,
              child: FadeTransition(
                opacity: _subtitleFadeAnimation,
                child: AnimatedOpacity(
                  duration: const Duration(milliseconds: 300),
                  opacity: _startTransition ? 0.0 : 1.0,
                  child: Text(
                    'الإصدار 1.0.0',
                    style: TextStyle(
                      color: Theme.of(context)
                          .textTheme
                          .bodySmall
                          ?.color
                          ?.withAlpha(153), // 0.6 * 255 = 153
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
