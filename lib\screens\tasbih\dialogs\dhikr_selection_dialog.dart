// حوار اختيار الذكر

import 'package:flutter/material.dart';
import '../models/dhikr_model.dart';
import '../utils/tasbih_colors.dart';

Future<void> showDhikrSelectionDialog(
  BuildContext context, {
  required List<DhikrModel> dhikrs,
  required DhikrModel selectedDhikr,
  required ValueChanged<DhikrModel> onDhikrSelected,
  required Function(int, int) onUpdateDhikrTarget,
  required Function(int, String) onUpdateDhikrName,
  required VoidCallback onAddNew,
}) async {
  final theme = Theme.of(context);
  final isDarkMode = theme.brightness == Brightness.dark;

  return showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: isDarkMode ? TasbihColors.darkCardColor : Colors.white,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    builder: (context) {
      return Container(
        padding: const EdgeInsets.all(20),
        height: MediaQuery.of(context).size.height * 0.6,
        child: Column(
          children: [
            Container(
              height: 4,
              width: 40,
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: Colors.grey[400],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Text(
              'اختر الذكر',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: TasbihColors.primary,
              ),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: ListView.builder(
                  physics: const BouncingScrollPhysics(),
                  itemCount: dhikrs.length,
                  itemBuilder: (context, index) {
                    final dhikr = dhikrs[index];
                    final isSelected = selectedDhikr.id == dhikr.id;

                    return Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? TasbihColors.primary
                                .withAlpha(isDarkMode ? 40 : 20)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: ListTile(
                        onTap: () {
                          onDhikrSelected(dhikr);
                          Navigator.pop(context);
                        },
                        title: Text(
                          dhikr.name,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal,
                            color: isSelected
                                ? TasbihColors.primary
                                : theme.textTheme.titleMedium?.color,
                          ),
                        ),
                        subtitle: Text(
                          'الهدف: ${dhikr.count} مرة',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: isSelected
                                ? TasbihColors.primary.withAlpha(200)
                                : theme.textTheme.bodySmall?.color,
                          ),
                        ),
                        leading: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? TasbihColors.primary.withAlpha(40)
                                : TasbihColors.primary.withAlpha(20),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.menu_book_rounded,
                            color: isSelected
                                ? TasbihColors.primary
                                : TasbihColors.primary.withAlpha(150),
                            size: 20,
                          ),
                        ),
                        // إظهار أزرار التعديل بناءً على نوع الذكر
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // زر تعديل اسم الذكر (للأذكار المخصصة فقط)
                            if (!dhikr.isDefault)
                              IconButton(
                                icon: Icon(
                                  Icons.edit_note,
                                  color: Colors.grey[600],
                                  size: 20,
                                ),
                                onPressed: () {
                                  Navigator.pop(context);
                                  _showEditNameDialog(
                                    context,
                                    dhikr.id,
                                    dhikr.name,
                                    onUpdateDhikrName,
                                  );
                                },
                                tooltip: 'تعديل اسم الذكر',
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints.tightFor(
                                  width: 30,
                                  height: 30,
                                ),
                              ),
                            // زر تعديل العدد المستهدف (لجميع الأذكار)
                            IconButton(
                              icon: Icon(
                                Icons.edit,
                                color: Colors.grey[600],
                                size: 20,
                              ),
                              onPressed: () {
                                Navigator.pop(context);
                                _showEditTargetDialog(
                                  context,
                                  dhikr.id,
                                  dhikr.count,
                                  onUpdateDhikrTarget,
                                );
                              },
                              tooltip: 'تعديل العدد المستهدف',
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints.tightFor(
                                width: 30,
                                height: 30,
                              ),
                            ),
                            // إظهار علامة التحديد للذكر المحدد
                            if (isSelected)
                              const Padding(
                                padding: EdgeInsets.only(right: 8),
                                child: Icon(
                                  Icons.check_circle,
                                  color: TasbihColors.primary,
                                  size: 20,
                                ),
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                onAddNew();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: TasbihColors.primary,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children:  [
                  Icon(Icons.add),
                  SizedBox(width: 8),
                  Text('إضافة ذكر جديد'),
                ],
              ),
            ),
          ],
        ),
      );
    },
  );
}

Future<int?> _showEditTargetDialog(
  BuildContext context,
  int dhikrId,
  int currentCount,
  Function(int, int) onUpdateDhikrTarget,
) async {
  final TextEditingController controller =
      TextEditingController(text: currentCount.toString());
  final FocusNode focusNode = FocusNode();

  // استخدام showGeneralDialog بدلاً من showDialog لتجنب مشكلة الـ overflow
  return showGeneralDialog<int>(
    context: context,
    barrierDismissible: true,
    barrierLabel: 'تعديل العدد المستهدف',
    transitionDuration: const Duration(milliseconds: 250),
    pageBuilder: (context, animation1, animation2) {
      return Container(); // لن يتم استخدامه
    },
    transitionBuilder: (context, animation, secondaryAnimation, child) {
      // تأثير الظهور
      final curvedAnimation = CurvedAnimation(
        parent: animation,
        curve: Curves.easeInOut,
      );

      return ScaleTransition(
        scale: Tween<double>(begin: 0.8, end: 1.0).animate(curvedAnimation),
        child: FadeTransition(
          opacity: curvedAnimation,
          child: SafeArea(
            child: Center(
              child: Material(
                color: Colors.transparent,
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.9,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Theme.of(context).dialogBackgroundColor,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // العنوان
                      const Text(
                        'تعديل العدد المستهدف',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: TasbihColors.primary,
                        ),
                      ),
                      const SizedBox(height: 20),

                      // حقل إدخال العدد
                      TextField(
                        controller: controller,
                        focusNode: focusNode,
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          hintText: 'أدخل العدد المستهدف',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                            borderSide: const BorderSide(
                              color: TasbihColors.primary,
                              width: 2,
                            ),
                          ),
                          // إضافة تعبئة أكبر لتسهيل النقر
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 16,
                          ),
                        ),
                        textAlign: TextAlign.center,
                        onEditingComplete: () {
                          // إخفاء لوحة المفاتيح عند الانتهاء من التحرير
                          focusNode.unfocus();

                          // محاولة تحديث العدد
                          final customCount = int.tryParse(controller.text);
                          if (customCount != null && customCount > 0) {
                            onUpdateDhikrTarget(dhikrId, customCount);
                            Navigator.pop(context, customCount);
                          }
                        },
                      ),
                      const SizedBox(height: 20),

                      // أزرار الإجراءات
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          TextButton(
                            style: TextButton.styleFrom(
                              foregroundColor: Colors.grey[600],
                            ),
                            onPressed: () {
                              // إخفاء لوحة المفاتيح
                              focusNode.unfocus();
                              Navigator.pop(context);
                            },
                            child: const Text('إلغاء'),
                          ),
                          const SizedBox(width: 16),
                          TextButton(
                            style: TextButton.styleFrom(
                              foregroundColor: TasbihColors.primary,
                            ),
                            onPressed: () {
                              // إخفاء لوحة المفاتيح
                              focusNode.unfocus();

                              final customCount = int.tryParse(controller.text);
                              if (customCount != null && customCount > 0) {
                                onUpdateDhikrTarget(dhikrId, customCount);
                                Navigator.pop(context, customCount);
                              }
                            },
                            child: const Text('تحديث'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      );
    },
  );
}

// حوار تعديل اسم الذكر
Future<String?> _showEditNameDialog(
  BuildContext context,
  int dhikrId,
  String currentName,
  Function(int, String) onUpdateDhikrName,
) async {
  final TextEditingController controller =
      TextEditingController(text: currentName);
  final FocusNode focusNode = FocusNode();

  // استخدام showGeneralDialog بدلاً من showDialog لتجنب مشكلة الـ overflow
  return showGeneralDialog<String>(
    context: context,
    barrierDismissible: true,
    barrierLabel: 'تعديل اسم الذكر',
    transitionDuration: const Duration(milliseconds: 250),
    pageBuilder: (context, animation1, animation2) {
      return Container(); // لن يتم استخدامه
    },
    transitionBuilder: (context, animation, secondaryAnimation, child) {
      // تأثير الظهور
      final curvedAnimation = CurvedAnimation(
        parent: animation,
        curve: Curves.easeInOut,
      );

      return ScaleTransition(
        scale: Tween<double>(begin: 0.8, end: 1.0).animate(curvedAnimation),
        child: FadeTransition(
          opacity: curvedAnimation,
          child: SafeArea(
            child: Center(
              child: Material(
                color: Colors.transparent,
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.9,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Theme.of(context).dialogBackgroundColor,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // العنوان
                      const Text(
                        'تعديل اسم الذكر',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: TasbihColors.primary,
                        ),
                      ),
                      const SizedBox(height: 20),

                      // حقل إدخال الاسم
                      TextField(
                        controller: controller,
                        focusNode: focusNode,
                        decoration: InputDecoration(
                          hintText: 'أدخل اسم الذكر',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                            borderSide: const BorderSide(
                              color: TasbihColors.primary,
                              width: 2,
                            ),
                          ),
                          // إضافة تعبئة أكبر لتسهيل النقر
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 16,
                          ),
                        ),
                        textAlign: TextAlign.center,
                        textDirection: TextDirection.rtl,
                        onEditingComplete: () {
                          // إخفاء لوحة المفاتيح عند الانتهاء من التحرير
                          focusNode.unfocus();

                          // محاولة تحديث الاسم
                          final newName = controller.text.trim();
                          if (newName.isNotEmpty) {
                            onUpdateDhikrName(dhikrId, newName);
                            Navigator.pop(context, newName);
                          }
                        },
                      ),
                      const SizedBox(height: 20),

                      // أزرار الإجراءات
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          TextButton(
                            style: TextButton.styleFrom(
                              foregroundColor: Colors.grey[600],
                            ),
                            onPressed: () {
                              // إخفاء لوحة المفاتيح
                              focusNode.unfocus();
                              Navigator.pop(context);
                            },
                            child: const Text('إلغاء'),
                          ),
                          const SizedBox(width: 16),
                          TextButton(
                            style: TextButton.styleFrom(
                              foregroundColor: TasbihColors.primary,
                            ),
                            onPressed: () {
                              // إخفاء لوحة المفاتيح
                              focusNode.unfocus();

                              final newName = controller.text.trim();
                              if (newName.isNotEmpty) {
                                onUpdateDhikrName(dhikrId, newName);
                                Navigator.pop(context, newName);
                              }
                            },
                            child: const Text('تحديث'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      );
    },
  );
}
