part of '../azkar_details_screen.dart';

SliverAppBar _buildAzkarAppBar({
  required Zikr category,
  required bool isSearchMode,
  required Function() onSearchModeToggle,
  required Function(String) onSearchQueryChanged,
  required String searchQuery,
  required BuildContext context,
  bool showTitle = true,
}) {
  final isDarkMode = Theme.of(context).brightness == Brightness.dark;
  final screenSize = MediaQuery.of(context).size;
  final isTablet = screenSize.shortestSide >= 600;

  // مقاسات متناسبة مع حجم الشاشة
  final double expandedHeight =
      isSearchMode ? screenSize.height * 0.15 : screenSize.height * 0.28;
  // final double titleFontSize = isTablet ? 24.0 : 20.0; // No se utiliza
  final double iconSize = isTablet ? 28.0 : 24.0;

  // ألوان ديناميكية
  final Color primaryColor = isDarkMode
      ? AppColors.getAzkarColor(isDarkMode).withAlpha(217) // 0.85 * 255 = 217
      : AppColors.getAzkarColor(isDarkMode);
  const Color accentColor = Colors.white;

  return SliverAppBar(
    expandedHeight: expandedHeight,
    pinned: true,
    floating: false,
    stretch: true,
    title: null,
    // تأثير محسن لتمدد شريط العنوان
    flexibleSpace: LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        // حساب نسبة التمدد (1.0 = ممدد بالكامل، 0.0 = مطوي بالكامل)
        final FlexibleSpaceBarSettings? settings = context
            .dependOnInheritedWidgetOfExactType<FlexibleSpaceBarSettings>();

        // إذا كانت الإعدادات فارغة، استخدم قيمة افتراضية
        if (settings == null) {
          debugPrint(
              'تحذير: FlexibleSpaceBarSettings فارغة في _buildAzkarAppBar');
          return FlexibleSpaceBar(
            title: null,
            titlePadding: EdgeInsets.zero,
            centerTitle: false,
            background: isSearchMode
                ? null
                : _buildEnhancedBackground(category, context, 0.0, showTitle),
          );
        }

        final double deltaExtent = settings.maxExtent - settings.minExtent;
        final double t =
            (1.0 - (settings.currentExtent - settings.minExtent) / deltaExtent)
                .clamp(0.0, 1.0);

        // استخدام نسبة التمدد للرسوم المتحركة
        return FlexibleSpaceBar(
          title: null,
          titlePadding: EdgeInsets.zero,
          centerTitle: false,
          background: isSearchMode
              ? null
              : _buildEnhancedBackground(category, context, t, showTitle),
        );
      },
    ),
    backgroundColor: isDarkMode
        ? const Color(0xFF1A1A2E) // TasbihColors.darkBackground
        : primaryColor,
    elevation: 0,
    shadowColor: Colors.black26,
    // شريط أدوات مع تأثيرات حركية - مُحسّن للأداء
    leading: RepaintBoundary(
      child: Container(
        // إزالة الرسوم المتحركة لتحسين الأداء
        decoration: BoxDecoration(
          color: isSearchMode
              ? Colors.transparent
              : Colors.white.withAlpha(38), // 0.15 * 255 = 38
          shape: BoxShape.circle,
          boxShadow: isSearchMode
              ? null
              : [
                  BoxShadow(
                    color: Colors.black.withAlpha(26), // 0.1 * 255 = 26
                    // تقليل قيمة التمويه لتحسين الأداء
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ],
        ),
        margin: const EdgeInsets.all(8),
        child: IconButton(
          icon: const Icon(Icons.arrow_back),
          color: accentColor,
          tooltip: 'العودة',
          splashRadius: 24,
          onPressed: isSearchMode
              ? onSearchModeToggle
              : () => Navigator.of(context).pop(),
        ),
      ),
    ),
    actions: isSearchMode
        ? []
        : [
            // زر البحث المحسن
            _buildActionButton(
              icon: Icons.search,
              tooltip: 'بحث',
              onPressed: onSearchModeToggle,
              iconSize: iconSize * 0.9,
            ),
            // زر المشاركة
            _buildActionButton(
              icon: Icons.share,
              tooltip: 'مشاركة',
              onPressed: () => _shareCategory(context, category),
              iconSize: iconSize * 0.9,
            ),
            // زر المفضلة مع تأثير نبض
            _buildFavoriteButton(context, category),
            const SizedBox(width: 8),
          ],
    bottom: isSearchMode
        ? PreferredSize(
            preferredSize: Size.fromHeight(screenSize.height * 0.08),
            child: _buildEnhancedSearchField(
              context,
              onSearchQueryChanged,
              searchQuery,
              isDarkMode,
            ),
          )
        : null,
  );
}

Widget _buildEnhancedBackground(
    Zikr category, BuildContext context, double scrollRatio, bool showTitle) {
  final isDarkMode = Theme.of(context).brightness == Brightness.dark;
  final screenSize = MediaQuery.of(context).size;
  final categoryIcon = IconHelper.getIconForCategory(
    category.name,
    iconName: category.iconName,
  );

  return Stack(
    fit: StackFit.expand,
    children: [
      // خلفية متدرجة متقدمة مع تدرجات متعددة
      Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            colors: isDarkMode
                ? [
                    AppColors.getAzkarColor(isDarkMode)
                        .withAlpha(242), // 0.95 * 255 = 242
                    AppColors.getAzkarColor(isDarkMode)
                        .withAlpha(217), // 0.85 * 255 = 217
                    AppColors.getAzkarColor(isDarkMode)
                        .withAlpha(191), // 0.75 * 255 = 191
                  ]
                : [
                    AppColors.getAzkarColor(isDarkMode),
                    AppColors.getAzkarColor(isDarkMode)
                        .withAlpha(230), // 0.9 * 255 = 230
                    AppColors.getAzkarColor(isDarkMode)
                        .withAlpha(204), // 0.8 * 255 = 204
                  ],
            stops: const [0.2, 0.6, 1.0],
          ),
        ),
      ),

      // طبقة إضاءة متقدمة - مُحسّنة للأداء
      RepaintBoundary(
        child: CustomPaint(
          painter: AzkarLightingEffect(
            progress: 1.0 - scrollRatio,
            isDarkMode: isDarkMode,
          ),
          child: const SizedBox.expand(),
        ),
      ),

      // طبقة موجات متحركة محسنة - مُحسّنة للأداء
      RepaintBoundary(
        child: CustomPaint(
          painter: WavePainter(
            animationValue: scrollRatio,
            isDarkMode: isDarkMode,
            // تقليل قيمة الشفافية لتحسين الأداء
            waveOpacity: 0.06,
          ),
          size: Size(screenSize.width, screenSize.height),
        ),
      ),

      // زخرفة إسلامية رئيسية متقدمة - مُحسّنة للأداء وتجنب مشكلة ParentDataWidget
      Positioned(
        top: -20 - (scrollRatio * 10),
        right: -20 - (scrollRatio * 5),
        child: RepaintBoundary(
          child: Opacity(
            // تبسيط حساب الشفافية لتحسين الأداء
            opacity: math.max(0.18 - (scrollRatio * 0.05), 0),
            child: SvgPicture.asset(
              'assets/images/p2.svg',
              width: screenSize.width * 0.55,
              height: screenSize.width * 0.55,
              colorFilter: ColorFilter.mode(
                Colors.white.withAlpha(isDarkMode
                    ? 217
                    : 242), // 0.85 * 255 = 217, 0.95 * 255 = 242
                BlendMode.srcIn,
              ),
            ),
          ),
        ),
      ),

      // زخرفة إسلامية ثانوية للعمق - مُحسّنة للأداء وتجنب مشكلة ParentDataWidget
      Positioned(
        bottom: -40 + (scrollRatio * 10),
        left: -25 - (scrollRatio * 5),
        child: RepaintBoundary(
          child: Opacity(
            // تبسيط حساب الشفافية لتحسين الأداء
            opacity: math.max(0.12 - (scrollRatio * 0.04), 0),
            child: SvgPicture.asset(
              'assets/images/p2.svg',
              width: screenSize.width * 0.45,
              height: screenSize.width * 0.45,
              colorFilter: ColorFilter.mode(
                Colors.white.withAlpha(
                    isDarkMode ? 179 : 204), // 0.7 * 255 = 179, 0.8 * 255 = 204
                BlendMode.srcIn,
              ),
            ),
          ),
        ),
      ),

      // تأثير خفيف من الأعلى
      Positioned(
        top: 0,
        left: 0,
        right: 0,
        child: Container(
          height: 80,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.white.withAlpha(38), // 0.15 * 255 = 38
                Colors.transparent,
              ],
            ),
          ),
        ),
      ),

      // معلومات الفئة مع تأثيرات حركية متقدمة
      if (showTitle)
        Positioned(
          bottom: 55 - (scrollRatio * 15),
          left: 16,
          right: 16,
          child: Opacity(
            opacity: math.max(1.0 - (scrollRatio * 1.2), 0),
            child: Transform.translate(
              offset: Offset(0, scrollRatio * 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // عنوان أكبر مع ظل متقدم
                  Text(
                    category.name,
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 30,
                      height: 1.2,
                      shadows: [
                        Shadow(
                          color: Colors.black.withAlpha(102), // 0.4 * 255 = 102
                          offset: const Offset(0, 2),
                          blurRadius: 5,
                        ),
                        Shadow(
                          color: Colors.black.withAlpha(51), // 0.2 * 255 = 51
                          offset: const Offset(0, 1),
                          blurRadius: 3,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 10),

                  // خط فاصل للتمييز - متحرك بشكل متقدم
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 500),
                    height: 2.5,
                    width: 45 + (scrollRatio * 20),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(191), // 0.75 * 255 = 191
                      borderRadius: BorderRadius.circular(1.5),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.white.withAlpha(77), // 0.3 * 255 = 77
                          blurRadius: 2,
                          spreadRadius: 0.5,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 10),

                  // وصف محسن مع ظلال متعددة
                  Text(
                    category.description,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14.5,
                      fontWeight: FontWeight.w400,
                      letterSpacing: 0.3,
                      height: 1.4,
                      shadows: [
                        Shadow(
                          color: Colors.black.withAlpha(89), // 0.35 * 255 = 89
                          offset: const Offset(0, 1.5),
                          blurRadius: 3,
                        ),
                        Shadow(
                          color: Colors.black.withAlpha(51), // 0.2 * 255 = 51
                          offset: const Offset(0, 0.5),
                          blurRadius: 1,
                        ),
                      ],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
        ),

      // ديكور إضافي في الخلفية - مع تحسينات التدرج
      Positioned(
        bottom: 0,
        left: 0,
        right: 0,
        child: Container(
          height: 45,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.transparent,
                isDarkMode
                    ? Colors.black.withAlpha(64) // 0.25 * 255 = 64
                    : Colors.black.withAlpha(31), // 0.12 * 255 = 31
                isDarkMode
                    ? Colors.black.withAlpha(89) // 0.35 * 255 = 89
                    : Colors.black.withAlpha(46), // 0.18 * 255 = 46
              ],
              stops: const [0.0, 0.6, 1.0],
            ),
          ),
        ),
      ),

      // معلومات إضافية مع أيقونة - تحسينات متقدمة
      Positioned(
        bottom: 16,
        left: 16,
        child: Row(
          children: [
            // أيقونة الفئة - مع تأثيرات متقدمة
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white.withAlpha(64), // 0.25 * 255 = 64
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(38), // 0.15 * 255 = 38
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(
                  color: Colors.white.withAlpha(77), // 0.3 * 255 = 77
                  width: 0.5,
                ),
              ),
              child: Icon(
                categoryIcon,
                color: Colors.white,
                size: 24,
                shadows: [
                  Shadow(
                    color: Colors.black.withAlpha(77), // 0.3 * 255 = 77
                    blurRadius: 2,
                  ),
                ],
              ),
            ),
            const SizedBox(width: 10),

            // عدد الأذكار - مع تحسينات متقدمة
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 7),
              decoration: BoxDecoration(
                color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
                borderRadius: BorderRadius.circular(18),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(26), // 0.1 * 255 = 26
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(
                  color: Colors.white.withAlpha(64), // 0.25 * 255 = 64
                  width: 0.5,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.format_list_numbered,
                    color: Colors.white,
                    size: 16,
                    shadows: [
                      Shadow(
                        color: Colors.black.withAlpha(77), // 0.3 * 255 = 77
                        blurRadius: 2,
                      ),
                    ],
                  ),
                  const SizedBox(width: 6),
                  Text(
                    '${category.items?.length ?? 0} ذكر',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12.5,
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          color: Colors.black.withAlpha(77), // 0.3 * 255 = 77
                          blurRadius: 2,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ],
  );
}

// مربع بحث محسن - مُحسّن للأداء
Widget _buildEnhancedSearchField(
  BuildContext context,
  Function(String) onSearchQueryChanged,
  String searchQuery,
  bool isDarkMode,
) {
  // استخدام RepaintBoundary لتحسين الأداء
  return RepaintBoundary(
    child: Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      decoration: BoxDecoration(
        color: isDarkMode
            ? const Color(0xFF252A34).withAlpha(
                128) /* TasbihColors.darkCardColor, 0.5 * 255 = 128 */
            : Colors.white24,
        borderRadius: BorderRadius.circular(16),
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            // تقليل قيمة التمويه لتحسين الأداء
            blurRadius: 6,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          // تقليل قيمة التمويه لتحسين الأداء
          filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
          child: TextField(
            autofocus: true,
            onChanged: onSearchQueryChanged,
            style: const TextStyle(color: Colors.white),
            cursorColor: Colors.white70,
            textInputAction: TextInputAction.search,
            decoration: InputDecoration(
              hintText: 'البحث في الأذكار...',
              hintStyle: TextStyle(
                  color: isDarkMode
                      ? const Color(
                          0xFFAAAAAA) /* TasbihColors.darkTextSecondary */
                      : Colors.white70),
              prefixIcon: Container(
                // إزالة الرسوم المتحركة لتحسين الأداء
                padding: const EdgeInsets.all(12),
                child: Icon(
                  Icons.search,
                  color: isDarkMode
                      ? const Color(
                          0xFFAAAAAA) /* TasbihColors.darkTextSecondary */
                      : Colors.white70,
                ),
              ),
              suffixIcon: searchQuery.isNotEmpty
                  ? IconButton(
                      icon: Icon(Icons.clear,
                          color: isDarkMode
                              ? const Color(
                                  0xFFAAAAAA) /* TasbihColors.darkTextSecondary */
                              : Colors.white70),
                      onPressed: () => onSearchQueryChanged(''),
                    )
                  : null,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 14,
              ),
            ),
          ),
        ),
      ),
    ),
  );
}

// أزرار الإجراءات المحسنة
Widget _buildActionButton({
  required IconData icon,
  required String tooltip,
  required VoidCallback onPressed,
  required double iconSize,
}) {
  // استخدام RepaintBoundary لتحسين الأداء
  return RepaintBoundary(
    child: Container(
      // إزالة الرسوم المتحركة لتحسين الأداء
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: Colors.white.withAlpha(38), // 0.15 * 255 = 38
        shape: BoxShape.circle,
      ),
      child: IconButton(
        icon: Icon(icon),
        color: Colors.white,
        tooltip: tooltip,
        splashRadius: 24,
        iconSize: iconSize,
        onPressed: onPressed,
      ),
    ),
  );
}

// زر المفضلة مع تأثير نبض
Widget _buildFavoriteButton(BuildContext context, Zikr category) {
  // الحصول على حالة المفضلة من الحالة الرئيسية
  final _AzkarDetailsScreenState? state =
      context.findAncestorStateOfType<_AzkarDetailsScreenState>();

  // إذا كانت الحالة فارغة، استخدم قيمة افتراضية
  if (state == null) {
    debugPrint('تحذير: _AzkarDetailsScreenState فارغة في _buildFavoriteButton');
    return const SizedBox.shrink(); // إرجاع مساحة فارغة
  }

  final bool isCategoryFavorite = state._isCategoryFavorite;

  return StatefulBuilder(builder: (context, setState) {
    // استخدام RepaintBoundary لتحسين الأداء
    return RepaintBoundary(
      child: Container(
        // إزالة الرسوم المتحركة لتحسين الأداء
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: isCategoryFavorite
              ? Colors.pink.withAlpha(77) // 0.3 * 255 = 77
              : Colors.white.withAlpha(38), // 0.15 * 255 = 38
          shape: BoxShape.circle,
        ),
        child: TweenAnimationBuilder<double>(
          // تقليل مدة الرسوم المتحركة لتحسين الأداء
          tween: Tween<double>(begin: 1.0, end: 1.0),
          duration: const Duration(milliseconds: 200),
          // استخدام منحنى أبسط لتحسين الأداء
          curve: Curves.easeOut,
          builder: (context, value, child) {
            return Transform.scale(
              scale: value,
              child: IconButton(
                icon: Icon(
                  isCategoryFavorite ? Icons.favorite : Icons.favorite_border,
                ),
                color: Colors.white,
                tooltip:
                    isCategoryFavorite ? 'إزالة من المفضلة' : 'إضافة للمفضلة',
                splashRadius: 24,
                onPressed: () {
                  // استدعاء دالة تبديل حالة المفضلة للقسم كاملاً
                  state._toggleCategoryFavorite(category);

                  // تحديث حالة الزر
                  setState(() {});

                  // تأثير اهتزاز للتفاعل - استخدام اهتزاز أخف لتحسين الأداء
                  HapticFeedback.lightImpact();
                },
              ),
            );
          },
        ),
      ),
    );
  });
}

// مشاركة الفئة
void _shareCategory(BuildContext context, Zikr category) {
  // تجميع نصوص جميع الأذكار في الفئة
  String allAzkarText = '';

  // إضافة اسم الفئة والوصف
  allAzkarText += '${category.name}\n';
  if (category.description.isNotEmpty) {
    allAzkarText += '${category.description}\n\n';
  } else {
    allAzkarText += '\n';
  }

  // إضافة نصوص الأذكار مع المصدر والفضل
  if (category.items != null && category.items!.isNotEmpty) {
    for (int i = 0; i < category.items!.length; i++) {
      final zikr = category.items![i];
      allAzkarText += '${i + 1}. ${zikr.text}';

      // إضافة عدد التكرار إذا كان أكثر من 1
      if (zikr.count > 1) {
        allAzkarText += ' (${zikr.count} مرات)';
      }

      // إضافة المصدر إذا كان متوفراً
      if (zikr.source != null && zikr.source!.isNotEmpty) {
        allAzkarText += '\nالمصدر: ${zikr.source}';
      }

      // إضافة الفضل إذا كان متوفراً
      if (zikr.fadl != null && zikr.fadl!.isNotEmpty) {
        allAzkarText += '\nالفضل: ${zikr.fadl}';
      }

      // إضافة سطر جديد بين الأذكار
      if (i < category.items!.length - 1) {
        allAzkarText += '\n\n';
      }
    }
  } else if (category.subcategories != null &&
      category.subcategories!.isNotEmpty) {
    // في حالة وجود فئات فرعية
    for (int i = 0; i < category.subcategories!.length; i++) {
      final subcat = category.subcategories![i];
      allAzkarText += '${subcat.name}:\n\n';

      if (subcat.items != null && subcat.items!.isNotEmpty) {
        for (int j = 0; j < subcat.items!.length; j++) {
          final zikr = subcat.items![j];
          allAzkarText += '${j + 1}. ${zikr.text}';

          // إضافة عدد التكرار إذا كان أكثر من 1
          if (zikr.count > 1) {
            allAzkarText += ' (${zikr.count} مرات)';
          }

          // إضافة المصدر إذا كان متوفراً
          if (zikr.source != null && zikr.source!.isNotEmpty) {
            allAzkarText += '\nالمصدر: ${zikr.source}';
          }

          // إضافة الفضل إذا كان متوفراً
          if (zikr.fadl != null && zikr.fadl!.isNotEmpty) {
            allAzkarText += '\nالفضل: ${zikr.fadl}';
          }

          // إضافة سطر جديد بين الأذكار
          if (j < subcat.items!.length - 1) {
            allAzkarText += '\n\n';
          }
        }
      }

      // إضافة سطر جديد بين الفئات الفرعية
      if (i < category.subcategories!.length - 1) {
        allAzkarText += '\n\n';
      }
    }
  }

  // عرض حوار المشاركة المتقدم للمحتوى الكامل
  showDialog(
    context: context,
    builder: (context) => ShareDialog(
      title: category.name,
      content: allAzkarText,
      isZikr: false,
      isFullContent: true, // تحديد أن هذا محتوى كامل
    ),
  );
}

// رسام موجات متقدم للخلفية
class WavePainter extends CustomPainter {
  final double animationValue;
  final bool isDarkMode;
  final double waveOpacity;

  WavePainter({
    required this.animationValue,
    required this.isDarkMode,
    this.waveOpacity = 0.05,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final baseOpacity = isDarkMode ? waveOpacity * 0.8 : waveOpacity;

    // موجة أولى متقدمة - مُحسّنة للأداء
    _drawEnhancedWave(
      canvas: canvas,
      size: size,
      height: size.height * 0.8,
      controlPoint1: size.width * 0.25,
      // تقليل مدى حركة نقاط التحكم لتحسين الأداء
      controlHeight1: size.height * (0.7 + animationValue * 0.05),
      controlPoint2: size.width * 0.75,
      // تقليل مدى حركة نقاط التحكم لتحسين الأداء
      controlHeight2: size.height * (0.9 - animationValue * 0.05),
      opacity: baseOpacity,
      isDarkMode: isDarkMode,
    );

    // موجة ثانية متقدمة - مُحسّنة للأداء
    _drawEnhancedWave(
      canvas: canvas,
      size: size,
      height: size.height * 0.9,
      controlPoint1: size.width * 0.25,
      // تقليل مدى حركة نقاط التحكم لتحسين الأداء
      controlHeight1: size.height * (0.8 - animationValue * 0.06),
      controlPoint2: size.width * 0.75,
      // تقليل مدى حركة نقاط التحكم لتحسين الأداء
      controlHeight2: size.height * (1.0 + animationValue * 0.04),
      opacity: baseOpacity + 0.02,
      isDarkMode: isDarkMode,
    );

    // إزالة الموجة الثالثة لتحسين الأداء
  }

  void _drawEnhancedWave({
    required Canvas canvas,
    required Size size,
    required double height,
    required double controlPoint1,
    required double controlHeight1,
    required double controlPoint2,
    required double controlHeight2,
    required double opacity,
    required bool isDarkMode,
  }) {
    final path = Path();

    path.moveTo(0, height);
    path.quadraticBezierTo(
      controlPoint1,
      controlHeight1,
      size.width * 0.5,
      height,
    );
    path.quadraticBezierTo(
      controlPoint2,
      controlHeight2,
      size.width,
      height,
    );
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    final wavePaint = Paint()
      ..color = Colors.white.withAlpha((opacity * 255).round())
      ..style = PaintingStyle.fill
      // تقليل قيمة التمويه لتحسين الأداء
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 1.0);

    canvas.drawPath(path, wavePaint);
  }

  @override
  bool shouldRepaint(WavePainter oldDelegate) {
    // تحسين شرط إعادة الرسم لتجنب إعادة الرسم غير الضرورية
    return (oldDelegate.animationValue - animationValue).abs() > 0.01 ||
        oldDelegate.isDarkMode != isDarkMode ||
        oldDelegate.waveOpacity != waveOpacity;
  }
}

// رسام تأثيرات الإضاءة المتقدمة
class AzkarLightingEffect extends CustomPainter {
  final double progress;
  final bool isDarkMode;

  AzkarLightingEffect({
    required this.progress,
    required this.isDarkMode,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);

    // تأثير وهج في الزاوية العلوية اليمنى - مُحسّن للأداء
    final topRightGlow = RadialGradient(
      center: const Alignment(0.8, -0.8),
      // تبسيط الحساب الرياضي لتحسين الأداء
      radius: 1.0,
      colors: [
        Colors.white.withAlpha((0.15 * progress * 255).round()),
        Colors.white.withAlpha((0.05 * progress * 255).round()),
        Colors.transparent,
      ],
      stops: const [0.0, 0.4, 0.8],
    ).createShader(rect);

    canvas.drawRect(rect, Paint()..shader = topRightGlow);

    // تأثير وهج في الزاوية السفلية اليسرى - مُحسّن للأداء
    final bottomLeftGlow = RadialGradient(
      center: const Alignment(-0.6, 0.8),
      // تبسيط الحساب الرياضي لتحسين الأداء
      radius: 0.8,
      colors: [
        Colors.white.withAlpha((0.08 * progress * 255).round()),
        Colors.white.withAlpha((0.03 * progress * 255).round()),
        Colors.transparent,
      ],
      stops: const [0.0, 0.4, 0.8],
    ).createShader(rect);

    canvas.drawRect(rect, Paint()..shader = bottomLeftGlow);

    // تقليل عدد نقاط الضوء المتوهجة لتحسين الأداء
    _drawLightSpot(canvas, size, 0.7, -0.3, 0.25 * progress);
    _drawLightSpot(canvas, size, -0.5, 0.6, 0.2 * progress);
  }

  void _drawLightSpot(
      Canvas canvas, Size size, double dx, double dy, double intensity) {
    final center = Offset(
      size.width * 0.5 + (dx * size.width * 0.5),
      size.height * 0.5 + (dy * size.height * 0.5),
    );

    // تقليل حجم نقطة الضوء لتحسين الأداء
    final radius = size.width * 0.12;

    final spotGradient = RadialGradient(
      colors: [
        Colors.white.withAlpha((intensity * 255).round()),
        Colors.white.withAlpha((intensity * 0.3 * 255).round()),
        Colors.transparent,
      ],
      stops: const [0.0, 0.3, 1.0],
    ).createShader(Rect.fromCircle(center: center, radius: radius));

    canvas.drawCircle(
      center,
      radius,
      Paint()..shader = spotGradient,
    );
  }

  @override
  bool shouldRepaint(AzkarLightingEffect oldDelegate) {
    // تحسين شرط إعادة الرسم لتجنب إعادة الرسم غير الضرورية
    return (oldDelegate.progress - progress).abs() > 0.01 ||
        oldDelegate.isDarkMode != isDarkMode;
  }
}
