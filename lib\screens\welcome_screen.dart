import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/app_colors.dart';
import '../utils/constants.dart';
import 'dart:math' as math;

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final PageController _pageController = PageController();
  int _currentPage = 0;
  final int _totalPages = 3;
  bool _isLastPage = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 20),
    )..repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
      _isLastPage = page == _totalPages - 1;
    });
  }

  void _markWelcomeScreenShown() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('welcome_screen_shown', true);
  }

  void _navigateToHome() {
    _markWelcomeScreenShown();
    Navigator.pushReplacementNamed(context, AppConstants.homeRoute);
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: Stack(
        children: [
          // خلفية متحركة
          AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return CustomPaint(
                size: Size(size.width, size.height),
                painter: BackgroundPainter(
                  animation: _animationController,
                  isDarkMode: isDarkMode,
                ),
              );
            },
          ),

          // زخارف إسلامية
          Positioned(
            top: -50,
            right: -50,
            child: Opacity(
              opacity: 0.1,
              child: SvgPicture.asset(
                'assets/images/p2.svg',
                width: 200,
                height: 200,
                colorFilter: ColorFilter.mode(
                  AppColors.getAzkarColor(isDarkMode),
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),

          Positioned(
            bottom: -50,
            left: -50,
            child: Opacity(
              opacity: 0.1,
              child: SvgPicture.asset(
                'assets/images/p2.svg',
                width: 200,
                height: 200,
                colorFilter: ColorFilter.mode(
                  AppColors.getAzkarColor(isDarkMode),
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),

          // محتوى الصفحة
          SafeArea(
            child: Column(
              children: [
                // شعار التطبيق
                Padding(
                  padding: const EdgeInsets.only(top: 40.0),
                  child: Center(
                    child: Column(
                      children: [
                        Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: AppColors.getAzkarColor(isDarkMode)
                                .withAlpha(26),
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.getAzkarColor(isDarkMode)
                                    .withAlpha(51),
                                blurRadius: 20,
                                spreadRadius: 5,
                              ),
                            ],
                          ),
                          child: Center(
                            child: Icon(
                              Icons.auto_awesome,
                              size: 50,
                              color: AppColors.getAzkarColor(isDarkMode),
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'وهج السالك',
                          style: TextStyle(
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                            color: isDarkMode ? Colors.white : Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'رفيقك في طريق الذكر والعبادة',
                          style: TextStyle(
                            fontSize: 18,
                            color: isDarkMode
                                ? Colors.white.withAlpha(179)
                                : Colors.black87.withAlpha(179),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // صفحات الترحيب
                Expanded(
                  child: PageView(
                    controller: _pageController,
                    onPageChanged: _onPageChanged,
                    children: [
                      _buildWelcomePage(
                        context,
                        isDarkMode,
                        'مرحباً بك في وهج السالك',
                        'تطبيق إسلامي متكامل يساعدك على الاستمرار في ذكر الله وتسبيحه في كل وقت وحين.',
                        Icons.favorite,
                      ),
                      _buildWelcomePage(
                        context,
                        isDarkMode,
                        'أقسام التطبيق الحالية',
                        'يحتوي التطبيق حالياً على قسم الأذكار والمسبحة الإلكترونية والأوراد اليومية و الأدعية و صيغ الصلاة على النبي محمد ﷺ  لمساعدتك في رحلتك الروحانية.',
                        Icons.auto_awesome_motion,
                      ),
                      _buildWelcomePage(
                        context,
                        isDarkMode,
                        'أقسام قادمة قريباً',
                        'نعمل حالياً على تطوير أقسام جديدة مثل الكتب والقصائد لإثراء تجربتك مع التطبيق. ترقب المزيد من المميزات قريباً!',
                        Icons.update,
                      ),
                    ],
                  ),
                ),

                // مؤشرات الصفحات
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      _totalPages,
                      (index) => _buildDotIndicator(index),
                    ),
                  ),
                ),

                // زر التخطي أو البدء
                Padding(
                  padding: const EdgeInsets.only(
                    bottom: 40.0,
                    left: 24.0,
                    right: 24.0,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      if (!_isLastPage)
                        TextButton(
                          onPressed: _navigateToHome,
                          child: Text(
                            'تخطي',
                            style: TextStyle(
                              fontSize: 16,
                              color: AppColors.getAzkarColor(isDarkMode),
                            ),
                          ),
                        )
                      else
                        const SizedBox.shrink(),
                      ElevatedButton(
                        onPressed: () {
                          if (_isLastPage) {
                            _navigateToHome();
                          } else {
                            _pageController.nextPage(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.getAzkarColor(isDarkMode),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 32,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                          elevation: 4,
                        ),
                        child: Text(
                          _isLastPage ? 'ابدأ الآن' : 'التالي',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomePage(
    BuildContext context,
    bool isDarkMode,
    String title,
    String description,
    IconData icon,
  ) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: AppColors.getAzkarColor(isDarkMode).withAlpha(26),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              size: 40,
              color: AppColors.getAzkarColor(isDarkMode),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            title,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            description,
            style: TextStyle(
              fontSize: 16,
              height: 1.5,
              color: isDarkMode
                  ? Colors.white.withAlpha(204)
                  : Colors.black87.withAlpha(204),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDotIndicator(int index) {
    final isActive = index == _currentPage;
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      height: 8,
      width: isActive ? 24 : 8,
      decoration: BoxDecoration(
        color: isActive
            ? AppColors.getAzkarColor(
                Theme.of(context).brightness == Brightness.dark)
            : Colors.grey.withAlpha(102),
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }
}

class BackgroundPainter extends CustomPainter {
  final Animation<double> animation;
  final bool isDarkMode;

  BackgroundPainter({
    required this.animation,
    required this.isDarkMode,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    // لون الخلفية
    final backgroundColor = isDarkMode
        ? const Color(0xFF1A1A2E) // TasbihColors.darkBackground
        : const Color(0xFFF8F8F8);

    // رسم الخلفية
    final backgroundPaint = Paint()..color = backgroundColor;
    canvas.drawRect(Rect.fromLTWH(0, 0, width, height), backgroundPaint);

    // لون الزخارف
    final decorationColor = AppColors.getAzkarColor(isDarkMode);

    // رسم تأثير توهج في الزاوية العلوية
    final topGlowPaint = Paint()
      ..shader = RadialGradient(
        colors: [
          decorationColor.withAlpha(13), // 0.05 opacity
          decorationColor.withAlpha(0),
        ],
        stops: const [0.0, 1.0],
        radius: 0.8,
      ).createShader(
        Rect.fromCircle(
          center: Offset(width * 0.2, height * 0.2),
          radius: width * 0.8,
        ),
      );

    canvas.drawCircle(
      Offset(width * 0.2, height * 0.2),
      width * 0.8,
      topGlowPaint,
    );

    // رسم تأثير توهج في الزاوية السفلية
    final bottomGlowPaint = Paint()
      ..shader = RadialGradient(
        colors: [
          decorationColor.withAlpha(13), // 0.05 opacity
          decorationColor.withAlpha(0),
        ],
        stops: const [0.0, 1.0],
        radius: 0.8,
      ).createShader(
        Rect.fromCircle(
          center: Offset(width * 0.8, height * 0.8),
          radius: width * 0.8,
        ),
      );

    canvas.drawCircle(
      Offset(width * 0.8, height * 0.8),
      width * 0.8,
      bottomGlowPaint,
    );

    // رسم أنماط هندسية متحركة
    final patternPaint = Paint()
      ..color = decorationColor.withAlpha(5) // 0.02 opacity
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // رسم دوائر متحركة
    for (int i = 0; i < 5; i++) {
      final radius = 50.0 + i * 40.0;
      final offset = 10.0 * math.sin(animation.value * math.pi * 2 + i);
      canvas.drawCircle(
        Offset(width * 0.5, height * 0.5),
        radius + offset,
        patternPaint,
      );
    }

    // رسم نمط نجمي
    final starPath = Path();
    const points = 8;
    const angleStep = 2 * math.pi / points;
    const outerRadius = 120.0;
    final innerRadius = 60.0 *
        (0.9 + 0.1 * math.sin(animation.value * math.pi * 2 + math.pi / 2));

    for (int i = 0; i < points; i++) {
      final outerAngle = i * angleStep;
      final innerAngle = outerAngle + angleStep / 2;

      final outerX = width * 0.5 + math.cos(outerAngle) * outerRadius;
      final outerY = height * 0.5 + math.sin(outerAngle) * outerRadius;
      final innerX = width * 0.5 + math.cos(innerAngle) * innerRadius;
      final innerY = height * 0.5 + math.sin(innerAngle) * innerRadius;

      if (i == 0) {
        starPath.moveTo(outerX, outerY);
      } else {
        starPath.lineTo(outerX, outerY);
      }
      starPath.lineTo(innerX, innerY);
    }
    starPath.close();
    canvas.drawPath(starPath, patternPaint);
  }

  @override
  bool shouldRepaint(BackgroundPainter oldDelegate) => true;
}
